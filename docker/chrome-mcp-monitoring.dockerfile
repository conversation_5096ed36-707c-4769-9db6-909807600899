# Chrome MCP Monitoring Dashboard Dockerfile
# Extends the main Strapi application with Chrome MCP monitoring capabilities
# Implements Story 2.1: Chrome MCP Server Health Monitoring Dashboard

FROM node:18-alpine

# Set working directory
WORKDIR /opt/app

# Install system dependencies for Chrome MCP monitoring
RUN apk add --no-cache \
    chromium \
    nss \
    freetype \
    freetype-dev \
    harfbuzz \
    ca-certificates \
    ttf-freefont \
    curl \
    wget

# Set Chrome executable path for Chrome MCP
ENV CHROME_BIN=/usr/bin/chromium-browser
ENV CHROME_PATH=/usr/bin/chromium-browser

# Copy package files
COPY package*.json ./
COPY yarn.lock ./

# Install dependencies
RUN yarn install --frozen-lockfile --production=false

# Copy application code
COPY . .

# Build the application with Chrome MCP monitoring
RUN yarn build

# Create monitoring data directory
RUN mkdir -p /opt/app/data/monitoring

# Set environment variables for Chrome MCP monitoring
ENV NODE_ENV=production
ENV CHROME_MCP_MONITORING_ENABLED=true
ENV CHROME_MCP_MONITORING_INTERVAL=30000
ENV CHROME_MCP_MONITORING_AUTO_START=true
ENV CHROME_MCP_MONITORING_DATA_DIR=/opt/app/data/monitoring

# Health check for Chrome MCP monitoring
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:1337/api/chrome-mcp-monitoring/status || exit 1

# Expose port
EXPOSE 1337

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs
RUN adduser -S strapi -u 1001

# Change ownership of app directory
RUN chown -R strapi:nodejs /opt/app

# Switch to non-root user
USER strapi

# Start command
CMD ["yarn", "start"]
