{
	// Use IntelliSense to learn about possible attributes.
	// Hover to view descriptions of existing attributes.
	// For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
	"version": "0.2.0",
	"configurations": [
		{
			"name": "Debug Strapi Server",
			"request": "launch",
			"runtimeArgs": ["develop", "--debug"],
			"runtimeExecutable": "yarn",
			"skipFiles": ["<node_internals>/**", "node_modules/**"],
			"type": "node",
			"env": {
				"NODE_ENV": "development",
				"GENERATE_SOURCEMAP": "true",
				"TS_NODE_OPTIONS": "--transpile-only --files"
			},
			"sourceMaps": true,
			"outFiles": ["${workspaceFolder}/dist/**/*.js"],
			"resolveSourceMapLocations": [
				"${workspaceFolder}/**",
				"!**/node_modules/**"
			],
			"console": "integratedTerminal",
			"restart": true,
			"protocol": "inspector"
		},
		{
			"name": "Debug Strapi with Source Maps",
			"request": "launch",
			"runtimeArgs": ["dev:debug"],
			"runtimeExecutable": "yarn",
			"skipFiles": ["<node_internals>/**", "node_modules/**"],
			"type": "node",
			"env": {
				"NODE_ENV": "development",
				"GENERATE_SOURCEMAP": "true",
				"DEV_SERVER_SOURCEMAP": "true"
			},
			"sourceMaps": true,
			"smartStep": true,
			"console": "integratedTerminal",
			"restart": true
		},
		{
			"name": "Attach to Strapi Process",
			"request": "attach",
			"port": 9229,
			"type": "node",
			"sourceMaps": true,
			"outFiles": ["${workspaceFolder}/dist/**/*.js"],
			"skipFiles": ["<node_internals>/**", "node_modules/**"]
		}
	]
}
