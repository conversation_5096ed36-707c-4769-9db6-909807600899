{
	// TypeScript configuration
	"typescript.preferences.includePackageJsonAutoImports": "on",
	"typescript.suggest.autoImports": true,
	"typescript.updateImportsOnFileMove.enabled": "always",

	// Enable source map support
	"debug.javascript.usePreview": true,
	"debug.javascript.sourceMaps": true,

	// File associations
	"files.associations": {
		"*.json": "jsonc"
	},

	// Exclude build artifacts from search
	"search.exclude": {
		"**/node_modules": true,
		"**/dist": true,
		"**/.tmp": true,
		"**/.cache": true,
		"**/build": true
	},

	// File watcher excludes for better performance
	"files.watcherExclude": {
		"**/node_modules/**": true,
		"**/dist/**": true,
		"**/.tmp/**": true,
		"**/.cache/**": true,
		"**/build/**": true
	},

	// ESLint configuration
	"eslint.workingDirectories": ["src"],

	// Auto-save configuration
	"files.autoSave": "onFocusChange",

	// Format on save
	"editor.formatOnSave": true,
	"editor.codeActionsOnSave": {
		"source.fixAll.eslint": "explicit"
	},

	// Terminal configuration
	"terminal.integrated.env.osx": {
		"NODE_ENV": "development"
	}
}
