# Design Document

## Overview

This design outlines a comprehensive Content Management System (CMS) built on Strapi that integrates with Adapty's server API to manage DIY paywall configurations. The system provides a user-friendly interface for marketing teams, product managers, and content creators to manage paywall content, A/B tests, and localization without requiring technical expertise. The architecture ensures seamless synchronization with Adapty's subscription platform while providing robust content management, workflow approval, and analytics capabilities.

## Architecture

### High-Level System Architecture

```mermaid
graph TB
    subgraph "Strapi CMS Core"
        A[Admin Dashboard]
        B[Content Management]
        C[User Management]
        D[API Layer]
        E[Database Layer]
    end
    
    subgraph "Adapty Integration"
        F[Adapty API Client]
        G[Sync Service]
        H[Webhook Handler]
        I[Remote Config Manager]
    end
    
    subgraph "Mobile Applications"
        J[React Native App]
        K[iOS App]
        L[Android App]
    end
    
    subgraph "External Services"
        M[Adapty Server API]
        N[CDN Service]
        O[Analytics Service]
        P[Translation Service]
    end
    
    A --> B
    B --> D
    D --> E
    D --> F
    F --> G
    G --> H
    H --> I
    
    D --> J
    D --> K
    D --> L
    
    F --> M
    B --> N
    G --> O
    B --> P
    
    I --> M
```

### Strapi Content Type Architecture

```mermaid
graph TB
    subgraph "Core Content Types"
        A[Paywall]
        B[PaywallVariation]
        C[Product]
        D[Placement]
        E[ABTest]
    end
    
    subgraph "Content Components"
        F[Theme]
        G[Feature]
        H[Testimonial]
        I[ProductLabel]
        J[MediaAsset]
    end
    
    subgraph "Configuration Types"
        K[AdaptyConfig]
        L[LocaleConfig]
        M[AnalyticsConfig]
        N[WorkflowConfig]
    end
    
    A --> B
    A --> F
    A --> G
    A --> H
    B --> I
    B --> J
    
    D --> A
    E --> B
    C --> I
    
    K --> D
    L --> A
    M --> E
    N --> A
```

## Components and Interfaces

### Core Strapi Content Types

#### Paywall Content Type
```typescript
interface PaywallContentType {
  // Basic Information
  id: number;
  name: string;
  description: string;
  placementId: string;
  status: 'draft' | 'review' | 'approved' | 'published' | 'archived';
  
  // Content Fields
  title: string;
  subtitle: string;
  description_text: string;
  cta_text: string;
  cta_secondary_text?: string;
  
  // Relationships
  theme: ThemeComponent;
  features: FeatureComponent[];
  testimonials: TestimonialComponent[];
  variations: PaywallVariation[];
  placement: Placement;
  
  // Localization
  localizations: PaywallContentType[];
  locale: string;
  
  // Metadata
  created_at: Date;
  updated_at: Date;
  published_at?: Date;
  created_by: User;
  updated_by: User;
  
  // Adapty Integration
  adapty_sync_status: 'pending' | 'synced' | 'error';
  adapty_last_sync: Date;
  adapty_remote_config_id?: string;
}
```

#### Theme Component
```typescript
interface ThemeComponent {
  id: number;
  name: string;
  primary_color: string;
  background_color: string;
  text_color: string;
  button_style: 'rounded' | 'square';
  gradient_colors?: string[];
  
  // Layout Configuration
  header_style: 'minimal' | 'hero' | 'gradient';
  product_display_style: 'list' | 'grid' | 'carousel';
  show_features: boolean;
  show_testimonials: boolean;
  
  // Custom CSS
  custom_styles?: string;
  
  // Media Assets
  background_image?: MediaAsset;
  logo?: MediaAsset;
}
```

#### Feature Component
```typescript
interface FeatureComponent {
  id: number;
  icon: string;
  title: string;
  description: string;
  order: number;
  is_highlighted: boolean;
  
  // Media
  icon_image?: MediaAsset;
  
  // Localization
  localizations: FeatureComponent[];
}
```

#### Product Label Component
```typescript
interface ProductLabelComponent {
  id: number;
  product_id: string; // Adapty product ID
  badge_text?: string;
  badge_color?: string;
  subtitle?: string;
  highlight: boolean;
  savings_percentage?: number;
  
  // Custom positioning
  badge_position: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  
  // Localization
  localizations: ProductLabelComponent[];
}
```

### Adapty Integration Services

#### Adapty API Client
```typescript
interface AdaptyAPIClient {
  // Authentication
  authenticate(apiKey: string): Promise<void>;
  
  // Placement Management
  getPlacement(placementId: string): Promise<AdaptyPlacement>;
  listPlacements(): Promise<AdaptyPlacement[]>;
  
  // Product Management
  getProducts(placementId: string): Promise<AdaptyProduct[]>;
  getProduct(productId: string): Promise<AdaptyProduct>;
  
  // Remote Config Management
  updateRemoteConfig(placementId: string, config: any): Promise<void>;
  getRemoteConfig(placementId: string): Promise<any>;
  
  // A/B Testing
  createABTest(config: ABTestConfig): Promise<AdaptyABTest>;
  updateABTest(testId: string, config: ABTestConfig): Promise<AdaptyABTest>;
  getABTestResults(testId: string): Promise<ABTestResults>;
  
  // Analytics
  getAnalytics(placementId: string, dateRange: DateRange): Promise<AnalyticsData>;
}
```

#### Sync Service
```typescript
interface SyncService {
  // Full Synchronization
  syncAllPlacements(): Promise<SyncResult>;
  syncPlacement(placementId: string): Promise<SyncResult>;
  
  // Incremental Sync
  syncChanges(since: Date): Promise<SyncResult>;
  
  // Conflict Resolution
  resolveConflicts(conflicts: SyncConflict[]): Promise<void>;
  
  // Status Monitoring
  getSyncStatus(): Promise<SyncStatus>;
  
  // Event Handling
  onSyncComplete(callback: (result: SyncResult) => void): void;
  onSyncError(callback: (error: SyncError) => void): void;
}
```

### API Endpoints for Mobile Apps

#### Paywall Content API
```typescript
interface PaywallContentAPI {
  // Get paywall configuration
  GET('/api/paywalls/:placementId'): Promise<PaywallResponse>;
  GET('/api/paywalls/:placementId/locale/:locale'): Promise<PaywallResponse>;
  
  // Get A/B test variation
  GET('/api/paywalls/:placementId/variation'): Promise<PaywallVariationResponse>;
  
  // Analytics tracking
  POST('/api/analytics/paywall-shown'): Promise<void>;
  POST('/api/analytics/product-selected'): Promise<void>;
  POST('/api/analytics/purchase-initiated'): Promise<void>;
  
  // Cache management
  GET('/api/paywalls/:placementId/version'): Promise<VersionResponse>;
  GET('/api/paywalls/:placementId/etag'): Promise<ETagResponse>;
}

interface PaywallResponse {
  paywall: {
    id: string;
    name: string;
    placement_id: string;
    content: {
      title: string;
      subtitle: string;
      description: string;
      cta_text: string;
      cta_secondary_text?: string;
    };
    theme: ThemeConfiguration;
    features: FeatureConfiguration[];
    testimonials: TestimonialConfiguration[];
    product_labels: Record<string, ProductLabelConfiguration>;
    layout: LayoutConfiguration;
  };
  ab_test: {
    test_id: string;
    variation_id: string;
    test_name: string;
    audience_name: string;
  };
  cache_info: {
    version: number;
    etag: string;
    expires_at: string;
  };
}
```

## Data Models

### Database Schema Design

#### Paywall Table
```sql
CREATE TABLE paywalls (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  placement_id VARCHAR(100) NOT NULL UNIQUE,
  status VARCHAR(20) DEFAULT 'draft',
  
  -- Content Fields
  title VARCHAR(255) NOT NULL,
  subtitle VARCHAR(500),
  description_text TEXT,
  cta_text VARCHAR(100) NOT NULL,
  cta_secondary_text VARCHAR(200),
  
  -- Adapty Integration
  adapty_sync_status VARCHAR(20) DEFAULT 'pending',
  adapty_last_sync TIMESTAMP,
  adapty_remote_config_id VARCHAR(100),
  
  -- Localization
  locale VARCHAR(10) DEFAULT 'en',
  
  -- Metadata
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  published_at TIMESTAMP,
  created_by INTEGER REFERENCES users(id),
  updated_by INTEGER REFERENCES users(id)
);
```

#### Theme Components Table
```sql
CREATE TABLE themes (
  id SERIAL PRIMARY KEY,
  paywall_id INTEGER REFERENCES paywalls(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  
  -- Colors
  primary_color VARCHAR(7) NOT NULL,
  background_color VARCHAR(7) NOT NULL,
  text_color VARCHAR(7) NOT NULL,
  gradient_colors JSON,
  
  -- Styles
  button_style VARCHAR(20) DEFAULT 'rounded',
  header_style VARCHAR(20) DEFAULT 'hero',
  product_display_style VARCHAR(20) DEFAULT 'list',
  
  -- Layout Flags
  show_features BOOLEAN DEFAULT true,
  show_testimonials BOOLEAN DEFAULT false,
  
  -- Custom Styles
  custom_styles TEXT,
  
  -- Media
  background_image_id INTEGER REFERENCES files(id),
  logo_id INTEGER REFERENCES files(id),
  
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### A/B Test Configuration
```sql
CREATE TABLE ab_tests (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  placement_id VARCHAR(100) NOT NULL,
  
  -- Test Configuration
  status VARCHAR(20) DEFAULT 'draft',
  traffic_allocation DECIMAL(5,2) DEFAULT 50.00,
  start_date TIMESTAMP,
  end_date TIMESTAMP,
  
  -- Success Metrics
  primary_metric VARCHAR(50) DEFAULT 'conversion_rate',
  secondary_metrics JSON,
  
  -- Adapty Integration
  adapty_test_id VARCHAR(100),
  adapty_sync_status VARCHAR(20) DEFAULT 'pending',
  
  -- Results
  statistical_significance DECIMAL(5,4),
  confidence_level DECIMAL(5,2) DEFAULT 95.00,
  winner_variation_id INTEGER,
  
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  created_by INTEGER REFERENCES users(id)
);
```

## Implementation Strategy

### Phase 1: Core Strapi Setup
1. **Strapi Installation and Configuration**
   - Set up Strapi with PostgreSQL database
   - Configure authentication and user roles
   - Set up basic content types and components

2. **Adapty Integration Foundation**
   - Implement Adapty API client
   - Create basic sync service
   - Set up webhook handlers

### Phase 2: Content Management
1. **Paywall Content Types**
   - Implement all content types and components
   - Create admin panel interfaces
   - Add validation and error handling

2. **Media Management**
   - Set up CDN integration
   - Implement image optimization
   - Create media library interface

### Phase 3: Advanced Features
1. **A/B Testing System**
   - Implement A/B test management
   - Create variation comparison tools
   - Add statistical analysis

2. **Localization Support**
   - Set up multi-language content
   - Implement translation workflows
   - Add locale-specific configurations

### Phase 4: API and Mobile Integration
1. **REST API Development**
   - Create mobile-optimized endpoints
   - Implement caching strategies
   - Add authentication and rate limiting

2. **Real-time Updates**
   - Implement webhook notifications
   - Add real-time sync capabilities
   - Create conflict resolution

## Security Considerations

### Authentication and Authorization
```typescript
interface SecurityConfig {
  // API Authentication
  jwt_secret: string;
  jwt_expiration: string;
  
  // Role-based Access Control
  roles: {
    admin: Permission[];
    editor: Permission[];
    viewer: Permission[];
  };
  
  // Adapty API Security
  adapty_api_key: string;
  adapty_webhook_secret: string;
  
  // Rate Limiting
  rate_limits: {
    api_calls_per_minute: number;
    sync_operations_per_hour: number;
  };
}
```

### Data Protection
- Encrypt sensitive configuration data
- Implement audit logging for all changes
- Use HTTPS for all API communications
- Validate and sanitize all user inputs
- Implement proper CORS policies

## Performance Optimizations

### Caching Strategy
```typescript
interface CacheConfig {
  // Redis Configuration
  redis_url: string;
  
  // Cache TTL Settings
  paywall_content_ttl: number; // 5 minutes
  product_data_ttl: number;    // 15 minutes
  analytics_ttl: number;       // 1 hour
  
  // CDN Settings
  cdn_url: string;
  cache_headers: {
    max_age: number;
    stale_while_revalidate: number;
  };
}
```

### Database Optimization
- Index frequently queried fields
- Implement connection pooling
- Use read replicas for analytics queries
- Optimize N+1 query problems with proper relations

## Monitoring and Analytics

### System Monitoring
```typescript
interface MonitoringConfig {
  // Health Checks
  health_check_interval: number;
  
  // Performance Metrics
  response_time_threshold: number;
  error_rate_threshold: number;
  
  // Adapty Sync Monitoring
  sync_failure_alerts: boolean;
  sync_performance_tracking: boolean;
  
  // User Activity Tracking
  content_modification_logs: boolean;
  api_usage_analytics: boolean;
}
```

This comprehensive design provides a robust foundation for building a Strapi-based CMS that seamlessly integrates with Adapty's server API, enabling non-technical team members to manage sophisticated DIY paywall configurations with full A/B testing, localization, and analytics capabilities.