---
inclusion: always
---

# Project Structure & Conventions

## Strapi Content Type Architecture

### Standard Content Type Structure
Every content type follows this exact pattern in `/src/api/[content-type]/`:
```
/content-types/[content-type]/schema.json  # Content type definition
/controllers/[content-type].ts             # HTTP request handlers
/routes/[content-type].ts                  # Route definitions (optional)
/services/[content-type].ts                # Business logic
/middlewares/                              # Custom middleware (optional)
```

### Key Content Types in This Project
- **Paywall System**: `paywall`, `paywall-variation`, `paywall-metrics`, `ab-test`
- **Mobile Integration**: `mobile-app`, `mobile-api-usage`, `remote-config-version`
- **Analytics**: `api-performance`, `user-engagement`, `system-health`, `performance-alert`
- **Content**: `article`, `author`, `category`, `global`
- **Sync & Operations**: `sync-operation`, `sync-status`, `deployment-log`

## Service Layer Organization

### Core Services (`/src/services/`)
- **`adapty/`**: External API integration with Adapty platform
- **`analytics/`**: Data collection, reporting, and metrics
- **`caching/`**: Redis cache and CDN management
- **`localization/`**: Multi-language and regional support
- **`monitoring/`**: Performance monitoring and alerting
- **`ab-testing/`**: A/B test management and statistical analysis

### Mobile API Structure (`/src/api/mobile/v1/`)
- **Controllers**: Handle mobile-specific endpoints
- **Services**: Business logic for mobile operations
- **Middlewares**: Authentication, rate limiting, caching, analytics
- **Routes**: Versioned API endpoints for mobile clients

## Admin Panel Customization (`/src/admin/`)

### Extensions (`/src/admin/extensions/components/`)
Custom React components for enhanced admin functionality:
- `ABTestDashboard.tsx`, `ABTestManager.tsx` - A/B testing interface
- `AnalyticsDashboard.tsx`, `PerformanceMonitoringDashboard.tsx` - Analytics views
- `PaywallPreview.tsx`, `BulkPaywallOperations.tsx` - Paywall management
- `FeatureManager.tsx`, `ProductLabelManager.tsx` - Feature toggles

## File Naming & Location Rules

### TypeScript Files
- Controllers: `[content-type].ts` in respective controllers folder
- Services: `[content-type].ts` in respective services folder
- Use kebab-case for file and folder names
- Use PascalCase for React components

### Schema Files
- Always `schema.json` in `/content-types/[content-type]/` folder
- Follow Strapi v5 schema format

### Test Files
- Location: `/tests/[feature-area]/[test-name].test.ts`
- Use descriptive names: `ab-test-manager.test.ts`, `adapty-integration.test.ts`

## Code Organization Patterns

### When Creating New Content Types
1. Generate schema in `/src/api/[name]/content-types/[name]/schema.json`
2. Implement controller in `/src/api/[name]/controllers/[name].ts`
3. Add business logic in `/src/api/[name]/services/[name].ts`
4. Create routes if custom routing needed
5. Add tests in `/tests/[feature-area]/`

### When Adding Mobile API Endpoints
1. Place in `/src/api/mobile/v1/` structure
2. Use middleware for common concerns (auth, rate limiting, caching)
3. Follow versioning pattern for backward compatibility
4. Add analytics tracking middleware

### When Extending Admin Interface
1. Add React components to `/src/admin/extensions/components/`
2. Register components in `/src/admin/app.tsx`
3. Use TypeScript for all admin customizations

## Configuration Management

### Environment-Specific Config
- Development: Use `.env` file
- Production: Set environment variables directly
- Database config: `/config/database.ts`
- Server config: `/config/server.ts`
- Plugin config: `/config/plugins.ts`

### Middleware Configuration
- Global middleware: `/config/middlewares.ts`
- Custom middleware: `/src/middlewares/` or within specific API folders
- Mobile API middleware: `/src/api/mobile/v1/middlewares/`

## Data & Asset Management

### Media Files
- Development uploads: `/data/uploads/`
- Production uploads: `/public/uploads/`
- Use media validation middleware for security

### Database
- SQLite for development: `/.tmp/data.db`
- Migrations: `/database/migrations/`
- Seed data: `/data/data.json` and `/scripts/seed.js`

## Import Path Conventions

### Strapi Core Imports
```typescript
import { factories } from '@strapi/strapi'
import type { Core } from '@strapi/strapi'
```

### Internal Service Imports
```typescript
import { adaptyClient } from '../../../services/adapty'
import { analyticsService } from '../../../services/analytics'
```

### Type Imports
```typescript
import type { PaywallAttributes } from '../../../../types/generated/contentTypes'
```

## Critical Rules

1. **Never modify core Strapi files** - Always extend through controllers, services, and middleware
2. **Use TypeScript strictly** - No `any` types, proper interfaces for all data structures
3. **Follow Strapi lifecycle hooks** - Use `beforeCreate`, `afterUpdate`, etc. in services
4. **Implement proper error handling** - Use Strapi's error utilities and HTTP status codes
5. **Cache strategically** - Use Redis for frequently accessed data, implement cache invalidation
6. **Validate all inputs** - Use Strapi's validation or custom validation middleware
7. **Log appropriately** - Use `strapi.log` for consistent logging across the application