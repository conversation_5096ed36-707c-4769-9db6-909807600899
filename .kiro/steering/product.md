---
inclusion: always
---

# Strapi Mobile App Paywall Management System

This is a Strapi-based headless CMS application designed to manage mobile app paywalls, A/B testing, and subscription monetization through Adapty integration. The system provides comprehensive paywall configuration, analytics tracking, and content management capabilities for mobile applications.

## Core Features

### Paywall Management
- Dynamic paywall configuration with localization support
- A/B testing for paywall variations and pricing strategies
- Real-time analytics and performance monitoring
- Adapty API integration for subscription management

### Mobile API
- Secure mobile app authentication and authorization
- Rate limiting and caching for optimal performance
- Versioned API endpoints for mobile clients
- Enhanced caching with CDN integration

### Content Management
- Blog articles, authors, and categories for marketing content
- Media management with validation and optimization
- Dynamic content blocks (rich text, media, quotes, sliders)
- SEO optimization and draft/publish workflows

### Analytics & Monitoring
- Performance monitoring with alerting
- User engagement tracking
- Revenue metrics and cohort analysis
- System health monitoring

## Architecture Patterns

### Content Type Structure
Follow Strapi conventions with controllers, services, and routes:
```
/src/api/[content-type]/
  /content-types/[content-type]/schema.json
  /controllers/[content-type].ts
  /services/[content-type].ts
  /routes/[content-type].ts
```

### Service Layer Organization
- **Adapty Services**: External API integration and sync operations
- **Analytics Services**: Data collection and reporting
- **Caching Services**: Redis and CDN management
- **Localization Services**: Multi-language support and regional customization

### Mobile API Design
- Versioned endpoints under `/api/mobile/v1/`
- Middleware for authentication, rate limiting, and caching
- Standardized response formats with error handling

## Key Content Types

### Paywall System
- **Paywall**: Core paywall configuration with Adapty integration
- **Paywall Variation**: A/B test variations with traffic allocation
- **Paywall Metrics**: Performance analytics and conversion tracking
- **AB Test**: Test configuration with statistical analysis

### Mobile Integration
- **Mobile App**: App registration and API access management
- **Mobile API Usage**: Usage tracking and rate limiting
- **Remote Config Version**: Configuration versioning for mobile clients

### Analytics & Monitoring
- **API Performance**: Performance metrics and monitoring
- **User Engagement**: User interaction tracking
- **System Health**: System status and alerting
- **Performance Alert**: Automated alerting system

### Content Management
- **Article**: Marketing content and blog posts
- **Author**: Content creator management
- **Category**: Content classification
- **Global**: Site-wide settings and configuration

## Code Style Conventions

### TypeScript Usage
- Use strict TypeScript for all new code
- Define proper interfaces for API responses and data models
- Leverage Strapi's generated types from `types/generated/`

### Error Handling
- Implement consistent error responses across all endpoints
- Use proper HTTP status codes
- Log errors with appropriate detail levels

### API Design
- Follow RESTful conventions for standard CRUD operations
- Use custom controllers for complex business logic
- Implement proper validation and sanitization

### Performance Considerations
- Implement caching strategies for frequently accessed data
- Use database relations efficiently
- Monitor and optimize query performance

## Integration Guidelines

### Adapty Integration
- Use the centralized `AdaptyApiClient` for all external API calls
- Implement proper error handling and retry logic
- Maintain sync operations for data consistency

### Mobile API Security
- Validate mobile app authentication tokens
- Implement rate limiting per app/user
- Use HTTPS for all mobile communications

### Localization Support
- Use Strapi's i18n plugin for content localization
- Implement regional customization for paywalls
- Support multiple currencies and pricing strategies

## Data Flow

1. **Mobile Apps** register and authenticate through the mobile API
2. **Paywall configurations** are managed through Strapi admin and synced with Adapty
3. **A/B tests** distribute traffic between paywall variations
4. **Analytics data** is collected and processed for performance insights
5. **Content** is managed through Strapi admin and delivered via API endpoints