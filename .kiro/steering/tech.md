---
inclusion: always
---

# Technology Stack & Development Guidelines

## Core Technologies
- **Strapi**: v5.18.1 - Headless CMS framework (use `@strapi/strapi` imports)
- **Node.js**: >=18.0.0 <=22.x.x
- **TypeScript**: ^5.x (strict mode enabled - no `any` types)
- **React**: ^18.0.0 (admin interface extensions only)

## Database & Storage
- **Primary**: SQLite (better-sqlite3 v11.3.0) - located at `.tmp/data.db`
- **Production**: Configurable for MySQL/PostgreSQL via `config/database.ts`
- **Media**: Development uploads in `/data/uploads/`, production in `/public/uploads/`

## Critical Dependencies
- `@strapi/plugin-users-permissions`: Authentication (use for mobile API auth)
- `@strapi/plugin-cloud`: Deployment capabilities
- `fs-extra`: File operations (prefer over native `fs`)
- `mime-types`: File upload validation

## TypeScript Standards
- Use strict TypeScript configuration
- Import generated types: `import type { PaywallAttributes } from '../../../types/generated/contentTypes'`
- Define interfaces for all API responses and service methods
- Use Strapi's Core types: `import type { Core } from '@strapi/strapi'`

## Development Commands

### Primary Development
```bash
yarn develop    # Start with auto-reload (preferred)
yarn build      # Build admin panel
yarn start      # Production server
```

### Data Operations
```bash
yarn seed:example    # Load sample data
yarn console        # Access Strapi console
```

### Testing & Quality
```bash
yarn test           # Run test suite
yarn upgrade:dry    # Check for updates
```

## Environment Configuration Rules
- **Never commit** `.env` - use `.env.example` as template
- Database config: `config/database.ts` (TypeScript, not JSON)
- Server config: `config/server.ts` (includes CORS, rate limiting)
- Plugin config: `config/plugins.ts` (Adapty, analytics integrations)

## API Development Patterns

### Standard Strapi APIs
- Auto-generated REST endpoints for all content types
- Use `factories.createCoreController()` for custom logic
- Implement lifecycle hooks in services, not controllers

### Mobile API (`/src/api/mobile/v1/`)
- Always version mobile endpoints
- Use middleware stack: auth → rate-limit → cache → analytics
- Return consistent JSON structure with error handling

### Custom Controllers
```typescript
import { factories } from '@strapi/strapi'

export default factories.createCoreController('api::paywall.paywall', ({ strapi }) => ({
  async customMethod(ctx) {
    // Implementation
  }
}))
```

## Performance & Caching
- Use Redis for session/API caching (see `/src/services/caching/`)
- Implement CDN integration for media files
- Cache frequently accessed paywall configurations
- Monitor API performance via `/src/services/monitoring/`

## Security Requirements
- Validate all inputs using Strapi validators or custom middleware
- Use `@strapi/plugin-users-permissions` for authentication
- Implement rate limiting for mobile APIs
- Sanitize file uploads with media validation middleware

## Integration Guidelines

### Adapty Integration
- Use centralized client: `/src/services/adapty/client.ts`
- Implement retry logic and error handling
- Sync operations should be idempotent

### Analytics & Monitoring
- Log using `strapi.log` for consistency
- Track performance metrics automatically
- Implement alerting for critical failures

## File Organization Rules
- Controllers: Handle HTTP requests only
- Services: Contain business logic and external API calls
- Middlewares: Cross-cutting concerns (auth, logging, validation)
- Extensions: Admin panel customizations in `/src/admin/extensions/`