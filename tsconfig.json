{
	"compilerOptions": {
		"module": "CommonJS",
		"moduleResolution": "Node",
		"lib": [
			"ES2020"
		],
		"types": [],
		"target": "ES2019",
		"strict": false,
		"noEmit": true,
		"skipLibCheck": true,
		"forceConsistentCasingInFileNames": true,
		"incremental": true,
		"esModuleInterop": true,
		"resolveJsonModule": true,
		"noEmitOnError": false,
		"noImplicitThis": true,
		"outDir": "dist",
		"rootDir": ".",
		"typeRoots": [
			"./node_modules/@types",
			"./types"
		],
		// Enable source maps for server-side debugging (but not for config files)
		"sourceMap": false,
		"inlineSourceMap": false,
		"inlineSources": false,
		// Path mapping for better imports
		"baseUrl": ".",
		"paths": {
			"@/*": [
				"./src/*"
			],
			"@api/*": [
				"./src/api/*"
			],
			"@services/*": [
				"./src/services/*"
			],
			"@middlewares/*": [
				"./src/middlewares/*"
			],
			"@components/*": [
				"./src/components/*"
			],
			"@config/*": [
				"./config/*"
			]
		}
	},
	"include": [
		// Include root files
		"./",
		// Include all ts files
		"./**/*.ts",
		// Include all js files
		"./**/*.js",
		// Force the JSON files in the src folder to be included
		"src/**/*.json",
		// Include generated type definitions
		"types/**/*.d.ts"
	],
	"exclude": [
		"node_modules/",
		"build/",
		"dist/",
		".cache/",
		".tmp/",
		// Do not include admin files in the server compilation
		"src/admin/",
		// Do not include test files
		"**/*.js.map",
		"**/*.test.*",
		// Do not include plugins in the server compilation
		"src/plugins/**"
	]
}