/**
 * Global type declarations for missing modules
 * Fixes TypeScript compilation issues in Strapi-Adapty CMS
 */

// Minimatch library type declarations
declare module "minimatch" {
	interface IOptions {
		debug?: boolean;
		nobrace?: boolean;
		noglobstar?: boolean;
		dot?: boolean;
		noext?: boolean;
		nocase?: boolean;
		nonull?: boolean;
		matchBase?: boolean;
		nocomment?: boolean;
		nonegate?: boolean;
		flipNegate?: boolean;
		partial?: boolean;
		allowWindowsEscape?: boolean;
	}

	function minimatch(
		target: string,
		pattern: string,
		options?: IOptions,
	): boolean;

	namespace minimatch {
		function filter(
			pattern: string,
			options?: IOptions,
		): (target: string) => boolean;
		function match(
			list: string[],
			pattern: string,
			options?: IOptions,
		): string[];
		function makeRe(pattern: string, options?: IOptions): RegExp | false;
		const Minimatch: {
			new (pattern: string, options?: IOptions): any;
		};
	}

	export = minimatch;
}

// Sharp library type declarations
declare module "sharp" {
	interface ResizeOptions {
		width?: number;
		height?: number;
		fit?: "cover" | "contain" | "fill" | "inside" | "outside";
		position?: string;
		background?: string;
		kernel?: string;
		withoutEnlargement?: boolean;
		withoutReduction?: boolean;
		fastShrinkOnLoad?: boolean;
	}

	interface JpegOptions {
		quality?: number;
		progressive?: boolean;
		mozjpeg?: boolean;
		trellisQuantisation?: boolean;
		overshootDeringing?: boolean;
		optimiseScans?: boolean;
		optimizeCoding?: boolean;
		quantisationTable?: number;
		force?: boolean;
	}

	interface PngOptions {
		progressive?: boolean;
		compressionLevel?: number;
		adaptiveFiltering?: boolean;
		force?: boolean;
		palette?: boolean;
		quality?: number;
		effort?: number;
		colours?: number;
		colors?: number;
		dither?: number;
	}

	interface Sharp {
		resize(width?: number, height?: number, options?: ResizeOptions): Sharp;
		resize(options: ResizeOptions): Sharp;
		jpeg(options?: JpegOptions): Sharp;
		png(options?: PngOptions): Sharp;
		webp(options?: any): Sharp;
		toBuffer(): Promise<Buffer>;
		toFile(fileOut: string): Promise<any>;
		metadata(): Promise<any>;
		clone(): Sharp;
		rotate(angle?: number): Sharp;
		flip(flip?: boolean): Sharp;
		flop(flop?: boolean): Sharp;
		crop(left: number, top: number, width: number, height: number): Sharp;
	}

	function sharp(input?: string | Buffer | Uint8Array): Sharp;

	namespace sharp {
		const format: {
			jpeg: any;
			png: any;
			webp: any;
		};
	}

	export = sharp;
}

// Strapi generated types placeholder
declare module "generated" {
	// This will be replaced by actual Strapi generated types
	export interface ContentTypes {
		[key: string]: any;
	}

	export interface Components {
		[key: string]: any;
	}
}

// Additional Strapi-specific type declarations
declare namespace Strapi {
	interface EntityService {
		findMany(uid: string, params?: any): Promise<any>;
		findOne(uid: string, id: string | number, params?: any): Promise<any>;
		create(uid: string, params: any): Promise<any>;
		update(uid: string, id: string | number, params: any): Promise<any>;
		delete(uid: string, id: string | number): Promise<any>;
	}

	interface Context {
		request: {
			body: any;
			query: any;
			params: any;
		};
		response: any;
		params: any;
		query: any;
		badRequest: (message: string) => any;
		notFound: (message: string) => any;
	}
}

// Global Strapi instance type
declare const strapi: {
	entityService: Strapi.EntityService;
	cache: {
		get: (key: string) => Promise<any>;
		set: (key: string, value: any, ttl?: number) => Promise<void>;
		del: (key: string) => Promise<void>;
	};
	[key: string]: any;
};

// Extend Strapi namespace for better typing
declare namespace Strapi {
	interface Strapi {
		cache: {
			get: (key: string) => Promise<any>;
			set: (key: string, value: any, ttl?: number) => Promise<void>;
			del: (key: string) => Promise<void>;
		};
	}
}

// Jest global types for tests
declare global {
	function beforeEach(fn: () => void | Promise<void>): void;
	function afterEach(fn: () => void | Promise<void>): void;

	namespace jest {
		function clearAllMocks(): void;
		function resetAllMocks(): void;
		function restoreAllMocks(): void;
		function spyOn(object: any, method: string): any;
	}
}

// Additional type definitions for missing interfaces
interface UnifiedReporting {
	[key: string]: any;
}

interface QualityIssue {
	type: string;
	message: string;
	severity: "low" | "medium" | "high";
}

// Export types that are used but not exported
export interface SystemHealthMetrics {
	[key: string]: any;
}

export interface APIPerformanceMetrics {
	[key: string]: any;
}

export interface SyncOperationMetrics {
	[key: string]: any;
}

export interface UserActivityMetrics {
	[key: string]: any;
}

export interface PerformanceThresholds {
	[key: string]: any;
}

export interface TranslationStatus {
	[key: string]: any;
}

export interface LocalizationMetrics {
	[key: string]: any;
}

// Utility function to ensure array type from Strapi entity service
declare global {
	function ensureArray<T>(value: T | T[]): T[];
}
