import type { Attribute, Schema } from '@strapi/strapi';

export interface SharedFeature extends Schema.Component {
  collectionName: 'components_shared_features';
  info: {
    description: 'Feature item for paywall feature lists';
    displayName: 'Feature';
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    description: Attribute.Text &
      Attribute.Required &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    icon: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        maxLength: 100;
      }>;
    icon_image: Attribute.Media<'images'>;
    is_highlighted: Attribute.Boolean & Attribute.DefaultTo<false>;
    order: Attribute.Integer & Attribute.Required & Attribute.DefaultTo<0>;
    title: Attribute.String &
      Attribute.Required &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }> &
      Attribute.SetMinMaxLength<{
        maxLength: 255;
      }>;
  };
}

export interface SharedMedia extends Schema.Component {
  collectionName: 'components_shared_media';
  info: {
    displayName: 'Media';
    icon: 'file-video';
  };
  attributes: {
    file: Attribute.Media<'images' | 'files' | 'videos'>;
  };
}

export interface SharedProductLabel extends Schema.Component {
  collectionName: 'components_shared_product_labels';
  info: {
    description: 'Custom product badges and highlights for paywall products';
    displayName: 'Product Label';
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    badge_color: Attribute.String;
    badge_position: Attribute.Enumeration<
      ['top-left', 'top-right', 'bottom-left', 'bottom-right']
    > &
      Attribute.DefaultTo<'top-right'>;
    badge_text: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }> &
      Attribute.SetMinMaxLength<{
        maxLength: 50;
      }>;
    highlight: Attribute.Boolean & Attribute.DefaultTo<false>;
    product_id: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        maxLength: 100;
      }>;
    savings_percentage: Attribute.Integer &
      Attribute.SetMinMax<
        {
          max: 100;
          min: 0;
        },
        number
      >;
    subtitle: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }> &
      Attribute.SetMinMaxLength<{
        maxLength: 255;
      }>;
  };
}

export interface SharedQuote extends Schema.Component {
  collectionName: 'components_shared_quotes';
  info: {
    displayName: 'Quote';
    icon: 'indent';
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    body: Attribute.Text &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    title: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
  };
}

export interface SharedRichText extends Schema.Component {
  collectionName: 'components_shared_rich_texts';
  info: {
    description: '';
    displayName: 'Rich text';
    icon: 'align-justify';
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    body: Attribute.RichText &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
  };
}

export interface SharedSeo extends Schema.Component {
  collectionName: 'components_shared_seos';
  info: {
    description: '';
    displayName: 'Seo';
    icon: 'allergies';
    name: 'Seo';
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    metaDescription: Attribute.Text &
      Attribute.Required &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    metaTitle: Attribute.String &
      Attribute.Required &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    shareImage: Attribute.Media<'images'>;
  };
}

export interface SharedSlider extends Schema.Component {
  collectionName: 'components_shared_sliders';
  info: {
    description: '';
    displayName: 'Slider';
    icon: 'address-book';
  };
  attributes: {
    files: Attribute.Media<'images', true>;
  };
}

export interface SharedTestimonial extends Schema.Component {
  collectionName: 'components_shared_testimonials';
  info: {
    description: 'Customer testimonial for paywall social proof';
    displayName: 'Testimonial';
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    author_avatar: Attribute.Media<'images'>;
    author_name: Attribute.String &
      Attribute.Required &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }> &
      Attribute.SetMinMaxLength<{
        maxLength: 255;
      }>;
    author_title: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }> &
      Attribute.SetMinMaxLength<{
        maxLength: 255;
      }>;
    company: Attribute.String &
      Attribute.SetMinMaxLength<{
        maxLength: 255;
      }>;
    content: Attribute.Text &
      Attribute.Required &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    order: Attribute.Integer & Attribute.Required & Attribute.DefaultTo<0>;
    rating: Attribute.Integer &
      Attribute.SetMinMax<
        {
          max: 5;
          min: 1;
        },
        number
      >;
  };
}

export interface SharedTheme extends Schema.Component {
  collectionName: 'components_shared_themes';
  info: {
    description: 'Theme configuration for paywall appearance and styling';
    displayName: 'Theme';
  };
  attributes: {
    background_color: Attribute.String & Attribute.Required;
    background_image: Attribute.Media<'images'>;
    button_style: Attribute.Enumeration<['rounded', 'square']> &
      Attribute.DefaultTo<'rounded'>;
    custom_styles: Attribute.Text;
    gradient_colors: Attribute.JSON;
    header_style: Attribute.Enumeration<['minimal', 'hero', 'gradient']> &
      Attribute.DefaultTo<'hero'>;
    logo: Attribute.Media<'images'>;
    name: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        maxLength: 255;
      }>;
    primary_color: Attribute.String & Attribute.Required;
    product_display_style: Attribute.Enumeration<['list', 'grid', 'carousel']> &
      Attribute.DefaultTo<'list'>;
    show_features: Attribute.Boolean & Attribute.DefaultTo<true>;
    show_testimonials: Attribute.Boolean & Attribute.DefaultTo<false>;
    text_color: Attribute.String & Attribute.Required;
  };
}

declare module '@strapi/types' {
  export module Shared {
    export interface Components {
      'shared.feature': SharedFeature;
      'shared.media': SharedMedia;
      'shared.product-label': SharedProductLabel;
      'shared.quote': SharedQuote;
      'shared.rich-text': SharedRichText;
      'shared.seo': SharedSeo;
      'shared.slider': SharedSlider;
      'shared.testimonial': SharedTestimonial;
      'shared.theme': SharedTheme;
    }
  }
}
