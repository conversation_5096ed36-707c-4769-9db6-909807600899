import type { Attribute, Schema } from '@strapi/strapi';

export interface AdminApiToken extends Schema.CollectionType {
  collectionName: 'strapi_api_tokens';
  info: {
    description: '';
    displayName: 'Api Token';
    name: 'Api Token';
    pluralName: 'api-tokens';
    singularName: 'api-token';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    accessKey: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'admin::api-token',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    description: Attribute.String &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }> &
      Attribute.DefaultTo<''>;
    expiresAt: Attribute.DateTime;
    lastUsedAt: Attribute.DateTime;
    lifespan: Attribute.BigInteger;
    name: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    permissions: Attribute.Relation<
      'admin::api-token',
      'oneToMany',
      'admin::api-token-permission'
    >;
    type: Attribute.Enumeration<['read-only', 'full-access', 'custom']> &
      Attribute.Required &
      Attribute.DefaultTo<'read-only'>;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'admin::api-token',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface AdminApiTokenPermission extends Schema.CollectionType {
  collectionName: 'strapi_api_token_permissions';
  info: {
    description: '';
    displayName: 'API Token Permission';
    name: 'API Token Permission';
    pluralName: 'api-token-permissions';
    singularName: 'api-token-permission';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'admin::api-token-permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    token: Attribute.Relation<
      'admin::api-token-permission',
      'manyToOne',
      'admin::api-token'
    >;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'admin::api-token-permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface AdminPermission extends Schema.CollectionType {
  collectionName: 'admin_permissions';
  info: {
    description: '';
    displayName: 'Permission';
    name: 'Permission';
    pluralName: 'permissions';
    singularName: 'permission';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    actionParameters: Attribute.JSON & Attribute.DefaultTo<{}>;
    conditions: Attribute.JSON & Attribute.DefaultTo<[]>;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'admin::permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    properties: Attribute.JSON & Attribute.DefaultTo<{}>;
    role: Attribute.Relation<'admin::permission', 'manyToOne', 'admin::role'>;
    subject: Attribute.String &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'admin::permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface AdminRole extends Schema.CollectionType {
  collectionName: 'admin_roles';
  info: {
    description: '';
    displayName: 'Role';
    name: 'Role';
    pluralName: 'roles';
    singularName: 'role';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    code: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<'admin::role', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    description: Attribute.String;
    name: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    permissions: Attribute.Relation<
      'admin::role',
      'oneToMany',
      'admin::permission'
    >;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<'admin::role', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    users: Attribute.Relation<'admin::role', 'manyToMany', 'admin::user'>;
  };
}

export interface AdminTransferToken extends Schema.CollectionType {
  collectionName: 'strapi_transfer_tokens';
  info: {
    description: '';
    displayName: 'Transfer Token';
    name: 'Transfer Token';
    pluralName: 'transfer-tokens';
    singularName: 'transfer-token';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    accessKey: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'admin::transfer-token',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    description: Attribute.String &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }> &
      Attribute.DefaultTo<''>;
    expiresAt: Attribute.DateTime;
    lastUsedAt: Attribute.DateTime;
    lifespan: Attribute.BigInteger;
    name: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    permissions: Attribute.Relation<
      'admin::transfer-token',
      'oneToMany',
      'admin::transfer-token-permission'
    >;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'admin::transfer-token',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface AdminTransferTokenPermission extends Schema.CollectionType {
  collectionName: 'strapi_transfer_token_permissions';
  info: {
    description: '';
    displayName: 'Transfer Token Permission';
    name: 'Transfer Token Permission';
    pluralName: 'transfer-token-permissions';
    singularName: 'transfer-token-permission';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'admin::transfer-token-permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    token: Attribute.Relation<
      'admin::transfer-token-permission',
      'manyToOne',
      'admin::transfer-token'
    >;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'admin::transfer-token-permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface AdminUser extends Schema.CollectionType {
  collectionName: 'admin_users';
  info: {
    description: '';
    displayName: 'User';
    name: 'User';
    pluralName: 'users';
    singularName: 'user';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    blocked: Attribute.Boolean & Attribute.Private & Attribute.DefaultTo<false>;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<'admin::user', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    email: Attribute.Email &
      Attribute.Required &
      Attribute.Private &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    firstname: Attribute.String &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    isActive: Attribute.Boolean &
      Attribute.Private &
      Attribute.DefaultTo<false>;
    lastname: Attribute.String &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    password: Attribute.Password &
      Attribute.Private &
      Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    preferedLanguage: Attribute.String;
    registrationToken: Attribute.String & Attribute.Private;
    resetPasswordToken: Attribute.String & Attribute.Private;
    roles: Attribute.Relation<'admin::user', 'manyToMany', 'admin::role'> &
      Attribute.Private;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<'admin::user', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    username: Attribute.String;
  };
}

export interface ApiAbTestAbTest extends Schema.CollectionType {
  collectionName: 'ab_tests';
  info: {
    description: 'A/B test configuration with metrics and audience targeting';
    displayName: 'A/B Test';
    pluralName: 'ab-tests';
    singularName: 'ab-test';
  };
  options: {
    draftAndPublish: true;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    adapty_last_sync: Attribute.DateTime &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }>;
    adapty_sync_status: Attribute.Enumeration<['pending', 'synced', 'error']> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<'pending'>;
    adapty_test_id: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.SetMinMaxLength<{
        maxLength: 100;
      }>;
    audience_targeting: Attribute.JSON &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }>;
    auto_promote_winner: Attribute.Boolean &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<false>;
    confidence_interval: Attribute.JSON &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }>;
    confidence_level: Attribute.Decimal &
      Attribute.Required &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.SetMinMax<
        {
          max: 99.9;
          min: 80;
        },
        number
      > &
      Attribute.DefaultTo<95>;
    created_by_user: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }>;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::ab-test.ab-test',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    current_results: Attribute.JSON &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }>;
    description: Attribute.Text &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    device_targeting: Attribute.JSON &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }>;
    duration_days: Attribute.Integer &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.SetMinMax<
        {
          max: 365;
          min: 1;
        },
        number
      >;
    effect_size: Attribute.Decimal &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }>;
    end_date: Attribute.DateTime &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }>;
    geographic_targeting: Attribute.JSON &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }>;
    hypothesis: Attribute.Text &
      Attribute.Required &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    locale: Attribute.String;
    localizations: Attribute.Relation<
      'api::ab-test.ab-test',
      'oneToMany',
      'api::ab-test.ab-test'
    >;
    minimum_detectable_effect: Attribute.Decimal &
      Attribute.Required &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.SetMinMax<
        {
          max: 100;
          min: 0.1;
        },
        number
      > &
      Attribute.DefaultTo<5>;
    minimum_sample_size: Attribute.Integer &
      Attribute.Required &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.SetMinMax<
        {
          min: 100;
        },
        number
      > &
      Attribute.DefaultTo<1000>;
    name: Attribute.String &
      Attribute.Required &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.SetMinMaxLength<{
        maxLength: 255;
      }>;
    notes: Attribute.Text &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    p_value: Attribute.Decimal &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.SetMinMax<
        {
          max: 1;
          min: 0;
        },
        number
      >;
    primary_metric: Attribute.Enumeration<
      [
        'conversion_rate',
        'revenue_per_user',
        'trial_conversion',
        'retention_rate',
        'ltv'
      ]
    > &
      Attribute.Required &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<'conversion_rate'>;
    publishedAt: Attribute.DateTime;
    secondary_metrics: Attribute.JSON &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }>;
    start_date: Attribute.DateTime &
      Attribute.Required &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }>;
    statistical_power: Attribute.Decimal &
      Attribute.Required &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.SetMinMax<
        {
          max: 99;
          min: 50;
        },
        number
      > &
      Attribute.DefaultTo<80>;
    statistical_significance: Attribute.Decimal &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.SetMinMax<
        {
          max: 100;
          min: 0;
        },
        number
      >;
    status: Attribute.Enumeration<
      ['draft', 'scheduled', 'running', 'paused', 'completed', 'cancelled']
    > &
      Attribute.Required &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<'draft'>;
    test_type: Attribute.Enumeration<
      ['paywall_content', 'pricing', 'theme', 'features', 'full_paywall']
    > &
      Attribute.Required &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<'paywall_content'>;
    traffic_allocation: Attribute.Decimal &
      Attribute.Required &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.SetMinMax<
        {
          max: 100;
          min: 0.1;
        },
        number
      > &
      Attribute.DefaultTo<100>;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::ab-test.ab-test',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    user_segment_filters: Attribute.JSON &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }>;
    variations: Attribute.Relation<
      'api::ab-test.ab-test',
      'oneToMany',
      'api::paywall-variation.paywall-variation'
    >;
    winner_selected_at: Attribute.DateTime &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }>;
    winner_selection_method: Attribute.Enumeration<
      ['manual', 'automatic', 'scheduled']
    > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<'manual'>;
    winner_variation: Attribute.Relation<
      'api::ab-test.ab-test',
      'oneToOne',
      'api::paywall-variation.paywall-variation'
    >;
  };
}

export interface ApiAboutAbout extends Schema.SingleType {
  collectionName: 'abouts';
  info: {
    description: 'Write about yourself and the content you create';
    displayName: 'About';
    pluralName: 'abouts';
    singularName: 'about';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    blocks: Attribute.DynamicZone<
      ['shared.media', 'shared.quote', 'shared.rich-text', 'shared.slider']
    >;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::about.about',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    title: Attribute.String;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::about.about',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiAnalyticsAnalytics extends Schema.CollectionType {
  collectionName: 'analytics';
  info: {
    description: 'General analytics data storage for various metrics and events';
    displayName: 'Analytics';
    pluralName: 'analytics-entries';
    singularName: 'analytics';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    app_id: Attribute.String;
    count: Attribute.Integer & Attribute.DefaultTo<1>;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::analytics.analytics',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    device_type: Attribute.Enumeration<['mobile', 'tablet', 'desktop']> &
      Attribute.Required;
    event_type: Attribute.Enumeration<
      [
        'paywall_impression',
        'user_engagement',
        'conversion',
        'interaction',
        'page_view',
        'session_start',
        'session_end'
      ]
    > &
      Attribute.Required;
    locale: Attribute.String & Attribute.Required & Attribute.DefaultTo<'en'>;
    metadata: Attribute.JSON;
    paywall_id: Attribute.String;
    placement_id: Attribute.String;
    platform: Attribute.Enumeration<['ios', 'android', 'web']> &
      Attribute.Required;
    region: Attribute.String & Attribute.Required;
    session_id: Attribute.String;
    timestamp: Attribute.DateTime & Attribute.Required;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::analytics.analytics',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    user_id: Attribute.String;
    value: Attribute.Decimal & Attribute.DefaultTo<0>;
  };
}

export interface ApiApiPerformanceApiPerformance extends Schema.CollectionType {
  collectionName: 'api_performance';
  info: {
    description: 'API endpoint performance monitoring';
    displayName: 'API Performance';
    pluralName: 'api-performances';
    singularName: 'api-performance';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::api-performance.api-performance',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    endpoint: Attribute.String & Attribute.Required;
    errorMessage: Attribute.Text;
    ipAddress: Attribute.String;
    method: Attribute.Enumeration<
      ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS', 'HEAD']
    > &
      Attribute.Required;
    requestSize: Attribute.Integer & Attribute.DefaultTo<0>;
    responseSize: Attribute.Integer & Attribute.DefaultTo<0>;
    responseTime: Attribute.Integer &
      Attribute.Required &
      Attribute.DefaultTo<0>;
    statusCode: Attribute.Integer & Attribute.Required;
    timestamp: Attribute.DateTime & Attribute.Required;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::api-performance.api-performance',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    userAgent: Attribute.Text;
  };
}

export interface ApiArticleArticle extends Schema.CollectionType {
  collectionName: 'articles';
  info: {
    description: 'Create your blog content';
    displayName: 'Article';
    pluralName: 'articles';
    singularName: 'article';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    author: Attribute.Relation<
      'api::article.article',
      'manyToOne',
      'api::author.author'
    >;
    blocks: Attribute.DynamicZone<
      ['shared.media', 'shared.quote', 'shared.rich-text', 'shared.slider']
    >;
    category: Attribute.Relation<
      'api::article.article',
      'manyToOne',
      'api::category.category'
    >;
    cover: Attribute.Media<'images' | 'files' | 'videos'>;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::article.article',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    description: Attribute.Text &
      Attribute.SetMinMaxLength<{
        maxLength: 80;
      }>;
    publishedAt: Attribute.DateTime;
    slug: Attribute.UID<'api::article.article', 'title'>;
    title: Attribute.String;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::article.article',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiAuthorAuthor extends Schema.CollectionType {
  collectionName: 'authors';
  info: {
    description: 'Create authors for your content';
    displayName: 'Author';
    pluralName: 'authors';
    singularName: 'author';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    articles: Attribute.Relation<
      'api::author.author',
      'oneToMany',
      'api::article.article'
    >;
    avatar: Attribute.Media<'images' | 'files' | 'videos'>;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::author.author',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    email: Attribute.String;
    name: Attribute.String;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::author.author',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiCacheInvalidationCacheInvalidation
  extends Schema.CollectionType {
  collectionName: 'cache_invalidations';
  info: {
    description: 'Tracking cache invalidation events and management operations';
    displayName: 'Cache Invalidation';
    pluralName: 'cache-invalidation-entries';
    singularName: 'cache-invalidation';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    cache_keys: Attribute.JSON;
    content_type: Attribute.String & Attribute.Required;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::cache-invalidation.cache-invalidation',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    error_message: Attribute.Text;
    event: Attribute.Enumeration<
      [
        'afterCreate',
        'afterUpdate',
        'afterDelete',
        'afterPublish',
        'afterUnpublish',
        'bulk_update',
        'manual_invalidation'
      ]
    > &
      Attribute.Required;
    processing_time: Attribute.Integer & Attribute.DefaultTo<0>;
    reason: Attribute.Text;
    success: Attribute.Boolean & Attribute.DefaultTo<true>;
    tags_invalidated: Attribute.Integer &
      Attribute.Required &
      Attribute.DefaultTo<0>;
    timestamp: Attribute.DateTime & Attribute.Required;
    triggered_by: Attribute.String;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::cache-invalidation.cache-invalidation',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiCacheMetricsCacheMetrics extends Schema.CollectionType {
  collectionName: 'cache_metrics';
  info: {
    description: 'Analytics data for cache performance tracking and monitoring';
    displayName: 'Cache Metrics';
    pluralName: 'cache-metrics-entries';
    singularName: 'cache-metrics';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    app_id: Attribute.String;
    cache_hit: Attribute.Boolean & Attribute.DefaultTo<false>;
    cache_size: Attribute.Integer & Attribute.DefaultTo<0>;
    cache_ttl: Attribute.Integer &
      Attribute.Required &
      Attribute.DefaultTo<300>;
    cache_type: Attribute.Enumeration<['redis', 'memory', 'cdn', 'database']> &
      Attribute.Required;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::cache-metrics.cache-metrics',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    endpoint: Attribute.String & Attribute.Required;
    method: Attribute.Enumeration<['GET', 'POST', 'PUT', 'DELETE', 'PATCH']> &
      Attribute.Required;
    response_time: Attribute.Integer &
      Attribute.Required &
      Attribute.DefaultTo<0>;
    timestamp: Attribute.DateTime & Attribute.Required;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::cache-metrics.cache-metrics',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiCategoryCategory extends Schema.CollectionType {
  collectionName: 'categories';
  info: {
    description: 'Organize your content into categories';
    displayName: 'Category';
    pluralName: 'categories';
    singularName: 'category';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    articles: Attribute.Relation<
      'api::category.category',
      'oneToMany',
      'api::article.article'
    >;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::category.category',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    description: Attribute.Text;
    name: Attribute.String;
    slug: Attribute.UID;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::category.category',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiCohortAnalysisCohortAnalysis extends Schema.CollectionType {
  collectionName: 'cohort_analysis';
  info: {
    description: 'User cohort retention and revenue analysis';
    displayName: 'Cohort Analysis';
    pluralName: 'cohort-analyses';
    singularName: 'cohort-analysis';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    churnAnalysis: Attribute.JSON & Attribute.Required;
    cohortName: Attribute.String & Attribute.Required;
    cohortStartDate: Attribute.Date & Attribute.Required;
    conversionMetrics: Attribute.JSON & Attribute.Required;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::cohort-analysis.cohort-analysis',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    retentionRates: Attribute.JSON & Attribute.Required;
    revenueMetrics: Attribute.JSON & Attribute.Required;
    timestamp: Attribute.DateTime & Attribute.Required;
    totalUsers: Attribute.Integer & Attribute.Required & Attribute.DefaultTo<0>;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::cohort-analysis.cohort-analysis',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiDeploymentLogDeploymentLog extends Schema.CollectionType {
  collectionName: 'deployment_logs';
  info: {
    description: 'Track deployment and rollback actions for remote configurations';
    displayName: 'Deployment Log';
    pluralName: 'deployment-logs';
    singularName: 'deployment-log';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    action: Attribute.Enumeration<['deploy', 'rollback', 'preview']> &
      Attribute.Required;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::deployment-log.deployment-log',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    data: Attribute.JSON;
    error_message: Attribute.Text;
    locale: Attribute.String & Attribute.DefaultTo<'en'>;
    occurred_at: Attribute.DateTime;
    performed_by: Attribute.String;
    placement_id: Attribute.String;
    previous_version_id: Attribute.String;
    reason: Attribute.Text;
    retry_count: Attribute.Integer & Attribute.DefaultTo<0>;
    status: Attribute.String;
    success: Attribute.Boolean & Attribute.DefaultTo<true>;
    sync_type: Attribute.Enumeration<['status', 'metrics', 'full']>;
    test_id: Attribute.Integer;
    timestamp: Attribute.DateTime & Attribute.Required;
    type: Attribute.Enumeration<
      [
        'deployment',
        'rollback',
        'configuration',
        'maintenance',
        'ab_test_sync_failure',
        'quality_report',
        'translation_activity'
      ]
    > &
      Attribute.Required &
      Attribute.DefaultTo<'deployment'>;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::deployment-log.deployment-log',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    version_id: Attribute.String & Attribute.Required;
  };
}

export interface ApiGlobalGlobal extends Schema.SingleType {
  collectionName: 'globals';
  info: {
    description: 'Define global settings';
    displayName: 'Global';
    pluralName: 'globals';
    singularName: 'global';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::global.global',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    defaultSeo: Attribute.Component<'shared.seo'>;
    favicon: Attribute.Media<'images' | 'files' | 'videos'>;
    siteDescription: Attribute.Text & Attribute.Required;
    siteName: Attribute.String & Attribute.Required;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::global.global',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiMobileApiUsageMobileApiUsage extends Schema.CollectionType {
  collectionName: 'mobile_api_usages';
  info: {
    description: 'Tracks mobile API usage and performance metrics';
    displayName: 'Mobile API Usage';
    pluralName: 'mobile-api-usages';
    singularName: 'mobile-api-usage';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    app_id: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        maxLength: 50;
      }>;
    app_version: Attribute.String &
      Attribute.SetMinMaxLength<{
        maxLength: 20;
      }>;
    cache_hit: Attribute.Boolean & Attribute.DefaultTo<false>;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::mobile-api-usage.mobile-api-usage',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    endpoint: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        maxLength: 200;
      }>;
    error_message: Attribute.Text;
    ip_address: Attribute.String &
      Attribute.SetMinMaxLength<{
        maxLength: 45;
      }>;
    method: Attribute.Enumeration<['GET', 'POST', 'PUT', 'DELETE', 'PATCH']> &
      Attribute.Required;
    platform: Attribute.Enumeration<
      ['ios', 'android', 'react_native', 'unknown']
    > &
      Attribute.Required;
    request_params: Attribute.JSON;
    request_size: Attribute.Integer & Attribute.DefaultTo<0>;
    response_size: Attribute.Integer & Attribute.DefaultTo<0>;
    response_time: Attribute.Integer & Attribute.Required;
    session_id: Attribute.String &
      Attribute.SetMinMaxLength<{
        maxLength: 100;
      }>;
    status_code: Attribute.Integer & Attribute.Required;
    timestamp: Attribute.DateTime & Attribute.Required;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::mobile-api-usage.mobile-api-usage',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    user_agent: Attribute.String &
      Attribute.SetMinMaxLength<{
        maxLength: 500;
      }>;
    user_id: Attribute.String &
      Attribute.SetMinMaxLength<{
        maxLength: 100;
      }>;
  };
}

export interface ApiMobileAppMobileApp extends Schema.CollectionType {
  collectionName: 'mobile_apps';
  info: {
    description: 'Mobile application registrations for API access';
    displayName: 'Mobile App';
    pluralName: 'mobile-apps';
    singularName: 'mobile-app';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    api_key: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        maxLength: 100;
      }>;
    app_id: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        maxLength: 50;
      }>;
    bundle_id: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        maxLength: 200;
      }>;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::mobile-app.mobile-app',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    description: Attribute.Text;
    is_active: Attribute.Boolean &
      Attribute.Required &
      Attribute.DefaultTo<true>;
    last_used: Attribute.DateTime;
    max_version: Attribute.String &
      Attribute.SetMinMaxLength<{
        maxLength: 20;
      }>;
    min_version: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        maxLength: 20;
      }>;
    name: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        maxLength: 100;
      }>;
    permissions: Attribute.JSON &
      Attribute.Required &
      Attribute.DefaultTo<['paywall:read']>;
    platform: Attribute.Enumeration<['ios', 'android', 'react_native']> &
      Attribute.Required;
    rate_limits: Attribute.JSON &
      Attribute.Required &
      Attribute.DefaultTo<{
        burst_limit: 100;
        requests_per_minute: 60;
      }>;
    secret_key: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        maxLength: 200;
      }>;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::mobile-app.mobile-app',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    usage_stats: Attribute.JSON &
      Attribute.DefaultTo<{
        error_rate: 0;
        last_24h_requests: 0;
        total_requests: 0;
      }>;
    webhook_secret: Attribute.String &
      Attribute.SetMinMaxLength<{
        maxLength: 100;
      }>;
    webhook_url: Attribute.String &
      Attribute.SetMinMaxLength<{
        maxLength: 500;
      }>;
  };
}

export interface ApiPaywallMetricsPaywallMetrics extends Schema.CollectionType {
  collectionName: 'paywall_metrics';
  info: {
    description: 'Analytics data for paywall performance tracking';
    displayName: 'Paywall Metrics';
    pluralName: 'paywall-metrics-entries';
    singularName: 'paywall-metrics';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    averageRevenuePerUser: Attribute.Decimal &
      Attribute.Required &
      Attribute.DefaultTo<0>;
    bounceRate: Attribute.Decimal & Attribute.Required & Attribute.DefaultTo<0>;
    conversionRate: Attribute.Decimal &
      Attribute.Required &
      Attribute.DefaultTo<0>;
    conversions: Attribute.Integer &
      Attribute.Required &
      Attribute.DefaultTo<0>;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::paywall-metrics.paywall-metrics',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    deviceType: Attribute.Enumeration<['mobile', 'tablet', 'desktop']> &
      Attribute.Required;
    impressions: Attribute.Integer &
      Attribute.Required &
      Attribute.DefaultTo<0>;
    locale: Attribute.String & Attribute.Required;
    paywallId: Attribute.String & Attribute.Required;
    platform: Attribute.Enumeration<['ios', 'android', 'web']> &
      Attribute.Required;
    region: Attribute.String & Attribute.Required;
    revenue: Attribute.Decimal & Attribute.Required & Attribute.DefaultTo<0>;
    timestamp: Attribute.DateTime & Attribute.Required;
    timeToConversion: Attribute.Integer &
      Attribute.Required &
      Attribute.DefaultTo<0>;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::paywall-metrics.paywall-metrics',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    userSegment: Attribute.String & Attribute.Required;
  };
}

export interface ApiPaywallVariationPaywallVariation
  extends Schema.CollectionType {
  collectionName: 'paywall_variations';
  info: {
    description: 'Individual variations for A/B testing with traffic allocation and performance tracking';
    displayName: 'Paywall Variation';
    pluralName: 'paywall-variations';
    singularName: 'paywall-variation';
  };
  options: {
    draftAndPublish: true;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    ab_test: Attribute.Relation<
      'api::paywall-variation.paywall-variation',
      'manyToOne',
      'api::ab-test.ab-test'
    >;
    adapty_remote_config_id: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.SetMinMaxLength<{
        maxLength: 100;
      }>;
    adapty_variation_id: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.SetMinMaxLength<{
        maxLength: 100;
      }>;
    base_paywall: Attribute.Relation<
      'api::paywall-variation.paywall-variation',
      'manyToOne',
      'api::paywall.paywall'
    >;
    confidence_interval_lower: Attribute.Decimal &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }>;
    confidence_interval_upper: Attribute.Decimal &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }>;
    conversion_rate: Attribute.Decimal &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.SetMinMax<
        {
          max: 100;
          min: 0;
        },
        number
      >;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::paywall-variation.paywall-variation',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    cta_secondary_text_override: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }> &
      Attribute.SetMinMaxLength<{
        maxLength: 200;
      }>;
    cta_text_override: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }> &
      Attribute.SetMinMaxLength<{
        maxLength: 100;
      }>;
    custom_properties: Attribute.JSON &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }>;
    description: Attribute.Text &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    description_override: Attribute.Text &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    features_override: Attribute.Component<'shared.feature', true>;
    is_winner: Attribute.Boolean &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<false>;
    last_metrics_update: Attribute.DateTime &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }>;
    locale: Attribute.String;
    localizations: Attribute.Relation<
      'api::paywall-variation.paywall-variation',
      'oneToMany',
      'api::paywall-variation.paywall-variation'
    >;
    name: Attribute.String &
      Attribute.Required &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.SetMinMaxLength<{
        maxLength: 255;
      }>;
    notes: Attribute.Text &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    performance_metrics: Attribute.JSON &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }>;
    pricing_override: Attribute.JSON &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }>;
    product_labels_override: Attribute.Component<'shared.product-label', true>;
    promoted_at: Attribute.DateTime &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }>;
    publishedAt: Attribute.DateTime;
    revenue_per_user: Attribute.Decimal &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.SetMinMax<
        {
          min: 0;
        },
        number
      >;
    statistical_significance: Attribute.Decimal &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.SetMinMax<
        {
          max: 100;
          min: 0;
        },
        number
      >;
    subtitle_override: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }> &
      Attribute.SetMinMaxLength<{
        maxLength: 500;
      }>;
    testimonials_override: Attribute.Component<'shared.testimonial', true>;
    theme_override: Attribute.Component<'shared.theme'>;
    title_override: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }> &
      Attribute.SetMinMaxLength<{
        maxLength: 255;
      }>;
    total_conversions: Attribute.BigInteger &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.SetMinMax<
        {
          min: 0;
        },
        number
      >;
    total_impressions: Attribute.BigInteger &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.SetMinMax<
        {
          min: 0;
        },
        number
      >;
    total_revenue: Attribute.Decimal &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.SetMinMax<
        {
          min: 0;
        },
        number
      >;
    traffic_percentage: Attribute.Decimal &
      Attribute.Required &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.SetMinMax<
        {
          max: 100;
          min: 0.1;
        },
        number
      > &
      Attribute.DefaultTo<50>;
    unique_users: Attribute.BigInteger &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.SetMinMax<
        {
          min: 0;
        },
        number
      >;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::paywall-variation.paywall-variation',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    variation_type: Attribute.Enumeration<
      ['control', 'variant', 'challenger']
    > &
      Attribute.Required &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<'variant'>;
  };
}

export interface ApiPaywallPaywall extends Schema.CollectionType {
  collectionName: 'paywalls';
  info: {
    description: 'Paywall configuration for Adapty integration';
    displayName: 'Paywall';
    pluralName: 'paywalls';
    singularName: 'paywall';
  };
  options: {
    draftAndPublish: true;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    adapty_last_sync: Attribute.DateTime &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }>;
    adapty_remote_config_id: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.SetMinMaxLength<{
        maxLength: 100;
      }>;
    adapty_sync_status: Attribute.Enumeration<['pending', 'synced', 'error']> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<'pending'>;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::paywall.paywall',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    cta_secondary_text: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }> &
      Attribute.SetMinMaxLength<{
        maxLength: 200;
      }>;
    cta_text: Attribute.String &
      Attribute.Required &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }> &
      Attribute.SetMinMaxLength<{
        maxLength: 100;
      }>;
    description: Attribute.Text &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    description_text: Attribute.Text &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    features: Attribute.Component<'shared.feature', true>;
    locale: Attribute.String;
    localizations: Attribute.Relation<
      'api::paywall.paywall',
      'oneToMany',
      'api::paywall.paywall'
    >;
    name: Attribute.String &
      Attribute.Required &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.SetMinMaxLength<{
        maxLength: 255;
      }>;
    placement_id: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.SetMinMaxLength<{
        maxLength: 100;
      }>;
    product_labels: Attribute.Component<'shared.product-label', true>;
    publishedAt: Attribute.DateTime;
    remote_config_versions: Attribute.Relation<
      'api::paywall.paywall',
      'oneToMany',
      'api::remote-config-version.remote-config-version'
    >;
    status: Attribute.Enumeration<
      ['draft', 'review', 'approved', 'published', 'archived']
    > &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<'draft'>;
    subtitle: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }> &
      Attribute.SetMinMaxLength<{
        maxLength: 500;
      }>;
    testimonials: Attribute.Component<'shared.testimonial', true>;
    theme: Attribute.Component<'shared.theme'> & Attribute.Required;
    title: Attribute.String &
      Attribute.Required &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }> &
      Attribute.SetMinMaxLength<{
        maxLength: 255;
      }>;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::paywall.paywall',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiPerformanceAlertPerformanceAlert
  extends Schema.CollectionType {
  collectionName: 'performance_alerts';
  info: {
    description: 'Automated performance alerts from Adapty analytics integration';
    displayName: 'Performance Alert';
    pluralName: 'performance-alerts';
    singularName: 'performance-alert';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    acknowledgedAt: Attribute.DateTime;
    acknowledgedBy: Attribute.String;
    affectedMetrics: Attribute.JSON & Attribute.Required;
    alertId: Attribute.String & Attribute.Required & Attribute.Unique;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::performance-alert.performance-alert',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    currentValue: Attribute.Decimal & Attribute.Required;
    message: Attribute.Text & Attribute.Required;
    notes: Attribute.Text;
    previousValue: Attribute.Decimal & Attribute.Required;
    recommendedActions: Attribute.JSON & Attribute.Required;
    resolvedAt: Attribute.DateTime;
    resolvedBy: Attribute.String;
    severity: Attribute.Enumeration<['low', 'medium', 'high', 'critical']> &
      Attribute.Required;
    status: Attribute.Enumeration<
      ['active', 'acknowledged', 'resolved', 'dismissed']
    > &
      Attribute.Required &
      Attribute.DefaultTo<'active'>;
    threshold: Attribute.Decimal & Attribute.Required;
    timestamp: Attribute.DateTime & Attribute.Required;
    type: Attribute.Enumeration<
      [
        'conversion_drop',
        'revenue_spike',
        'revenue_drop',
        'churn_increase',
        'trial_conversion_drop'
      ]
    > &
      Attribute.Required;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::performance-alert.performance-alert',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiPreviewConfigPreviewConfig extends Schema.CollectionType {
  collectionName: 'preview_configs';
  info: {
    description: 'Store preview configurations for testing remote configs before deployment';
    displayName: 'Preview Config';
    pluralName: 'preview-configs';
    singularName: 'preview-config';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    access_count: Attribute.Integer & Attribute.DefaultTo<0>;
    config_data: Attribute.JSON & Attribute.Required;
    created_by: Attribute.String;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::preview-config.preview-config',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    expires_at: Attribute.DateTime & Attribute.Required;
    last_accessed: Attribute.DateTime;
    locale: Attribute.String & Attribute.DefaultTo<'en'>;
    paywall: Attribute.Relation<
      'api::preview-config.preview-config',
      'manyToOne',
      'api::paywall.paywall'
    >;
    preview_id: Attribute.String & Attribute.Required & Attribute.Unique;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::preview-config.preview-config',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiProductProduct extends Schema.CollectionType {
  collectionName: 'products';
  info: {
    description: 'Products synced from Adapty for paywall configuration';
    displayName: 'Product';
    pluralName: 'products';
    singularName: 'product';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    adapty_product_id: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        maxLength: 100;
      }>;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::product.product',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    is_active: Attribute.Boolean & Attribute.DefaultTo<true>;
    last_synced: Attribute.DateTime;
    metadata: Attribute.JSON;
    name: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        maxLength: 255;
      }>;
    price_amount: Attribute.Decimal;
    price_currency: Attribute.String &
      Attribute.SetMinMaxLength<{
        maxLength: 3;
      }>;
    price_localized: Attribute.String &
      Attribute.SetMinMaxLength<{
        maxLength: 50;
      }>;
    store: Attribute.Enumeration<['app_store', 'play_store', 'stripe']> &
      Attribute.Required;
    subscription_period_count: Attribute.Integer &
      Attribute.SetMinMax<
        {
          min: 1;
        },
        number
      >;
    subscription_period_unit: Attribute.Enumeration<
      ['day', 'week', 'month', 'year']
    >;
    type: Attribute.Enumeration<
      ['subscription', 'consumable', 'non_consumable']
    > &
      Attribute.Required;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::product.product',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    vendor_product_id: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        maxLength: 255;
      }>;
  };
}

export interface ApiRemoteConfigVersionRemoteConfigVersion
  extends Schema.CollectionType {
  collectionName: 'remote_config_versions';
  info: {
    description: 'Track versions of remote configurations for Adapty deployment';
    displayName: 'Remote Config Version';
    pluralName: 'remote-config-versions';
    singularName: 'remote-config-version';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    config_data: Attribute.JSON & Attribute.Required;
    created_by: Attribute.String & Attribute.Required;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::remote-config-version.remote-config-version',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    deployed_at: Attribute.DateTime;
    deployment_notes: Attribute.Text;
    locale: Attribute.String & Attribute.DefaultTo<'en'>;
    paywall: Attribute.Relation<
      'api::remote-config-version.remote-config-version',
      'manyToOne',
      'api::paywall.paywall'
    >;
    placement_id: Attribute.String & Attribute.Required;
    status: Attribute.Enumeration<['draft', 'deployed', 'rolled_back']> &
      Attribute.Required &
      Attribute.DefaultTo<'draft'>;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::remote-config-version.remote-config-version',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    version_id: Attribute.String & Attribute.Required & Attribute.Unique;
    version_number: Attribute.String & Attribute.Required;
  };
}

export interface ApiSubscriptionMetricsSubscriptionMetrics
  extends Schema.CollectionType {
  collectionName: 'subscription_metrics';
  info: {
    description: 'Subscription lifecycle data from Adapty integration';
    displayName: 'Subscription Metrics';
    pluralName: 'subscription-metrics-entries';
    singularName: 'subscription-metrics';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    averageRevenuePerUser: Attribute.Decimal &
      Attribute.Required &
      Attribute.DefaultTo<0>;
    conversionRate: Attribute.Decimal &
      Attribute.Required &
      Attribute.DefaultTo<0>;
    conversions: Attribute.Integer &
      Attribute.Required &
      Attribute.DefaultTo<0>;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::subscription-metrics.subscription-metrics',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    identifier: Attribute.String & Attribute.Required;
    impressions: Attribute.Integer &
      Attribute.Required &
      Attribute.DefaultTo<0>;
    revenue: Attribute.Decimal & Attribute.Required & Attribute.DefaultTo<0>;
    subscriptions: Attribute.Integer &
      Attribute.Required &
      Attribute.DefaultTo<0>;
    timestamp: Attribute.DateTime & Attribute.Required;
    trialConversionRate: Attribute.Decimal &
      Attribute.Required &
      Attribute.DefaultTo<0>;
    trials: Attribute.Integer & Attribute.Required & Attribute.DefaultTo<0>;
    type: Attribute.Enumeration<
      ['overall', 'country', 'product', 'variation']
    > &
      Attribute.Required;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::subscription-metrics.subscription-metrics',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiSyncOperationSyncOperation extends Schema.CollectionType {
  collectionName: 'sync_operations';
  info: {
    description: 'Synchronization operation monitoring';
    displayName: 'Sync Operation';
    pluralName: 'sync-operations';
    singularName: 'sync-operation';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::sync-operation.sync-operation',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    duration: Attribute.Integer;
    endTime: Attribute.DateTime;
    errorCount: Attribute.Integer & Attribute.Required & Attribute.DefaultTo<0>;
    errorDetails: Attribute.JSON;
    operationId: Attribute.String & Attribute.Required & Attribute.Unique;
    operationType: Attribute.Enumeration<
      ['adapty_sync', 'remote_config_deploy', 'ab_test_sync', 'analytics_sync']
    > &
      Attribute.Required;
    recordsProcessed: Attribute.Integer &
      Attribute.Required &
      Attribute.DefaultTo<0>;
    startTime: Attribute.DateTime & Attribute.Required;
    status: Attribute.Enumeration<
      ['running', 'completed', 'failed', 'timeout']
    > &
      Attribute.Required &
      Attribute.DefaultTo<'running'>;
    successRate: Attribute.Decimal &
      Attribute.Required &
      Attribute.DefaultTo<0>;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::sync-operation.sync-operation',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiSyncStatusSyncStatus extends Schema.CollectionType {
  collectionName: 'sync_statuses';
  info: {
    description: 'Track synchronization status between Strapi and Adapty';
    displayName: 'Sync Status';
    pluralName: 'sync-statuses';
    singularName: 'sync-status';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    adapty_last_modified: Attribute.DateTime;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::sync-status.sync-status',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    entity_id: Attribute.String & Attribute.Required;
    entity_type: Attribute.Enumeration<['paywall', 'product', 'placement']> &
      Attribute.Required;
    error_message: Attribute.Text;
    last_sync: Attribute.DateTime & Attribute.Required;
    retry_count: Attribute.Integer &
      Attribute.Required &
      Attribute.DefaultTo<0>;
    strapi_last_modified: Attribute.DateTime;
    sync_status: Attribute.Enumeration<
      ['pending', 'synced', 'error', 'conflict']
    > &
      Attribute.Required &
      Attribute.DefaultTo<'pending'>;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::sync-status.sync-status',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiSystemHealthSystemHealth extends Schema.CollectionType {
  collectionName: 'system_health';
  info: {
    description: 'System health monitoring metrics';
    displayName: 'System Health';
    pluralName: 'system-health-records';
    singularName: 'system-health';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    activeConnections: Attribute.Integer &
      Attribute.Required &
      Attribute.DefaultTo<0>;
    cpuUsage: Attribute.Decimal & Attribute.Required & Attribute.DefaultTo<0>;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::system-health.system-health',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    errorRate: Attribute.Decimal & Attribute.Required & Attribute.DefaultTo<0>;
    memoryPercentage: Attribute.Decimal &
      Attribute.Required &
      Attribute.DefaultTo<0>;
    memoryTotal: Attribute.BigInteger &
      Attribute.Required &
      Attribute.DefaultTo<0>;
    memoryUsed: Attribute.BigInteger &
      Attribute.Required &
      Attribute.DefaultTo<0>;
    responseTime: Attribute.Integer &
      Attribute.Required &
      Attribute.DefaultTo<0>;
    status: Attribute.Enumeration<['healthy', 'warning', 'critical', 'down']> &
      Attribute.Required &
      Attribute.DefaultTo<'healthy'>;
    timestamp: Attribute.DateTime & Attribute.Required;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::system-health.system-health',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    uptime: Attribute.BigInteger & Attribute.Required & Attribute.DefaultTo<0>;
  };
}

export interface ApiTranslationAssignmentTranslationAssignment
  extends Schema.CollectionType {
  collectionName: 'translation_assignments';
  info: {
    description: 'Manages translation assignments and progress tracking';
    displayName: 'Translation Assignment';
    pluralName: 'translation-assignments';
    singularName: 'translation-assignment';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    actual_words: Attribute.Integer &
      Attribute.SetMinMax<
        {
          min: 0;
        },
        number
      >;
    approved_at: Attribute.DateTime;
    assigned_reviewer: Attribute.Relation<
      'api::translation-assignment.translation-assignment',
      'manyToOne',
      'admin::user'
    >;
    assigned_translator: Attribute.Relation<
      'api::translation-assignment.translation-assignment',
      'manyToOne',
      'admin::user'
    >;
    completed_at: Attribute.DateTime;
    completed_fields: Attribute.JSON;
    content_type: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        maxLength: 255;
      }>;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::translation-assignment.translation-assignment',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    deadline: Attribute.DateTime;
    entity_id: Attribute.Integer & Attribute.Required;
    estimated_words: Attribute.Integer &
      Attribute.SetMinMax<
        {
          min: 0;
        },
        number
      >;
    external_service_data: Attribute.JSON;
    fields_to_translate: Attribute.JSON;
    notes: Attribute.Text;
    priority: Attribute.Enumeration<['low', 'medium', 'high', 'urgent']> &
      Attribute.DefaultTo<'medium'>;
    progress_percentage: Attribute.Integer &
      Attribute.SetMinMax<
        {
          max: 100;
          min: 0;
        },
        number
      > &
      Attribute.DefaultTo<0>;
    quality_score: Attribute.Integer &
      Attribute.SetMinMax<
        {
          max: 10;
          min: 1;
        },
        number
      >;
    reviewed_at: Attribute.DateTime;
    reviewer_notes: Attribute.Text;
    source_locale: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        maxLength: 10;
      }>;
    started_at: Attribute.DateTime;
    status: Attribute.Enumeration<
      [
        'pending',
        'assigned',
        'in_progress',
        'completed',
        'reviewed',
        'approved',
        'rejected'
      ]
    > &
      Attribute.DefaultTo<'pending'>;
    target_locale: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        maxLength: 10;
      }>;
    translation_memory_matches: Attribute.JSON;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::translation-assignment.translation-assignment',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiTranslationMemoryTranslationMemory
  extends Schema.CollectionType {
  collectionName: 'translation_memories';
  info: {
    description: 'Stores translation pairs for consistency and reuse';
    displayName: 'Translation Memory';
    pluralName: 'translation-memories';
    singularName: 'translation-memory';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    approved_by: Attribute.Relation<
      'api::translation-memory.translation-memory',
      'manyToOne',
      'admin::user'
    >;
    content_type: Attribute.String &
      Attribute.SetMinMaxLength<{
        maxLength: 255;
      }>;
    context: Attribute.String &
      Attribute.SetMinMaxLength<{
        maxLength: 500;
      }>;
    created_by_translator: Attribute.Relation<
      'api::translation-memory.translation-memory',
      'manyToOne',
      'admin::user'
    >;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::translation-memory.translation-memory',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    field_name: Attribute.String &
      Attribute.SetMinMaxLength<{
        maxLength: 255;
      }>;
    is_approved: Attribute.Boolean & Attribute.DefaultTo<false>;
    is_fuzzy_match: Attribute.Boolean & Attribute.DefaultTo<false>;
    last_used: Attribute.DateTime;
    quality_score: Attribute.Integer &
      Attribute.SetMinMax<
        {
          max: 10;
          min: 1;
        },
        number
      > &
      Attribute.DefaultTo<5>;
    similarity_score: Attribute.Decimal &
      Attribute.SetMinMax<
        {
          max: 1;
          min: 0;
        },
        number
      >;
    source_locale: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        maxLength: 10;
      }>;
    source_text: Attribute.Text & Attribute.Required;
    tags: Attribute.JSON;
    target_locale: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        maxLength: 10;
      }>;
    target_text: Attribute.Text & Attribute.Required;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::translation-memory.translation-memory',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    usage_count: Attribute.Integer &
      Attribute.SetMinMax<
        {
          min: 0;
        },
        number
      > &
      Attribute.DefaultTo<0>;
  };
}

export interface ApiUserEngagementUserEngagement extends Schema.CollectionType {
  collectionName: 'user_engagement';
  info: {
    description: 'User interaction and engagement metrics';
    displayName: 'User Engagement';
    pluralName: 'user-engagement-records';
    singularName: 'user-engagement';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    clickThroughRate: Attribute.Decimal &
      Attribute.Required &
      Attribute.DefaultTo<0>;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::user-engagement.user-engagement',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    heatmapData: Attribute.JSON & Attribute.Required;
    interactionEvents: Attribute.JSON & Attribute.Required;
    pageViews: Attribute.Integer & Attribute.Required & Attribute.DefaultTo<0>;
    paywallId: Attribute.String & Attribute.Required;
    scrollDepth: Attribute.Decimal &
      Attribute.Required &
      Attribute.DefaultTo<0>;
    sessionDuration: Attribute.Integer &
      Attribute.Required &
      Attribute.DefaultTo<0>;
    timestamp: Attribute.DateTime & Attribute.Required;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::user-engagement.user-engagement',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginContentReleasesRelease extends Schema.CollectionType {
  collectionName: 'strapi_releases';
  info: {
    displayName: 'Release';
    pluralName: 'releases';
    singularName: 'release';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    actions: Attribute.Relation<
      'plugin::content-releases.release',
      'oneToMany',
      'plugin::content-releases.release-action'
    >;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::content-releases.release',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    name: Attribute.String & Attribute.Required;
    releasedAt: Attribute.DateTime;
    scheduledAt: Attribute.DateTime;
    status: Attribute.Enumeration<
      ['ready', 'blocked', 'failed', 'done', 'empty']
    > &
      Attribute.Required;
    timezone: Attribute.String;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'plugin::content-releases.release',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginContentReleasesReleaseAction
  extends Schema.CollectionType {
  collectionName: 'strapi_release_actions';
  info: {
    displayName: 'Release Action';
    pluralName: 'release-actions';
    singularName: 'release-action';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    contentType: Attribute.String & Attribute.Required;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::content-releases.release-action',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    entry: Attribute.Relation<
      'plugin::content-releases.release-action',
      'morphToOne'
    >;
    isEntryValid: Attribute.Boolean;
    locale: Attribute.String;
    release: Attribute.Relation<
      'plugin::content-releases.release-action',
      'manyToOne',
      'plugin::content-releases.release'
    >;
    type: Attribute.Enumeration<['publish', 'unpublish']> & Attribute.Required;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'plugin::content-releases.release-action',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginI18NLocale extends Schema.CollectionType {
  collectionName: 'i18n_locale';
  info: {
    collectionName: 'locales';
    description: '';
    displayName: 'Locale';
    pluralName: 'locales';
    singularName: 'locale';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    code: Attribute.String & Attribute.Unique;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::i18n.locale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    name: Attribute.String &
      Attribute.SetMinMax<
        {
          max: 50;
          min: 1;
        },
        number
      >;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'plugin::i18n.locale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginUploadFile extends Schema.CollectionType {
  collectionName: 'files';
  info: {
    description: '';
    displayName: 'File';
    pluralName: 'files';
    singularName: 'file';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    alternativeText: Attribute.String;
    caption: Attribute.String;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::upload.file',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    ext: Attribute.String;
    folder: Attribute.Relation<
      'plugin::upload.file',
      'manyToOne',
      'plugin::upload.folder'
    > &
      Attribute.Private;
    folderPath: Attribute.String &
      Attribute.Required &
      Attribute.Private &
      Attribute.SetMinMax<
        {
          min: 1;
        },
        number
      >;
    formats: Attribute.JSON;
    hash: Attribute.String & Attribute.Required;
    height: Attribute.Integer;
    mime: Attribute.String & Attribute.Required;
    name: Attribute.String & Attribute.Required;
    previewUrl: Attribute.String;
    provider: Attribute.String & Attribute.Required;
    provider_metadata: Attribute.JSON;
    related: Attribute.Relation<'plugin::upload.file', 'morphToMany'>;
    size: Attribute.Decimal & Attribute.Required;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'plugin::upload.file',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    url: Attribute.String & Attribute.Required;
    width: Attribute.Integer;
  };
}

export interface PluginUploadFolder extends Schema.CollectionType {
  collectionName: 'upload_folders';
  info: {
    displayName: 'Folder';
    pluralName: 'folders';
    singularName: 'folder';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    children: Attribute.Relation<
      'plugin::upload.folder',
      'oneToMany',
      'plugin::upload.folder'
    >;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::upload.folder',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    files: Attribute.Relation<
      'plugin::upload.folder',
      'oneToMany',
      'plugin::upload.file'
    >;
    name: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMax<
        {
          min: 1;
        },
        number
      >;
    parent: Attribute.Relation<
      'plugin::upload.folder',
      'manyToOne',
      'plugin::upload.folder'
    >;
    path: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMax<
        {
          min: 1;
        },
        number
      >;
    pathId: Attribute.Integer & Attribute.Required & Attribute.Unique;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'plugin::upload.folder',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginUsersPermissionsPermission
  extends Schema.CollectionType {
  collectionName: 'up_permissions';
  info: {
    description: '';
    displayName: 'Permission';
    name: 'permission';
    pluralName: 'permissions';
    singularName: 'permission';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Attribute.String & Attribute.Required;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::users-permissions.permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    role: Attribute.Relation<
      'plugin::users-permissions.permission',
      'manyToOne',
      'plugin::users-permissions.role'
    >;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'plugin::users-permissions.permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginUsersPermissionsRole extends Schema.CollectionType {
  collectionName: 'up_roles';
  info: {
    description: '';
    displayName: 'Role';
    name: 'role';
    pluralName: 'roles';
    singularName: 'role';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::users-permissions.role',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    description: Attribute.String;
    name: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 3;
      }>;
    permissions: Attribute.Relation<
      'plugin::users-permissions.role',
      'oneToMany',
      'plugin::users-permissions.permission'
    >;
    type: Attribute.String & Attribute.Unique;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'plugin::users-permissions.role',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    users: Attribute.Relation<
      'plugin::users-permissions.role',
      'oneToMany',
      'plugin::users-permissions.user'
    >;
  };
}

export interface PluginUsersPermissionsUser extends Schema.CollectionType {
  collectionName: 'up_users';
  info: {
    description: '';
    displayName: 'User';
    name: 'user';
    pluralName: 'users';
    singularName: 'user';
  };
  options: {
    draftAndPublish: false;
    timestamps: true;
  };
  attributes: {
    blocked: Attribute.Boolean & Attribute.DefaultTo<false>;
    confirmationToken: Attribute.String & Attribute.Private;
    confirmed: Attribute.Boolean & Attribute.DefaultTo<false>;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::users-permissions.user',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    email: Attribute.Email &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    password: Attribute.Password &
      Attribute.Private &
      Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    provider: Attribute.String;
    resetPasswordToken: Attribute.String & Attribute.Private;
    role: Attribute.Relation<
      'plugin::users-permissions.user',
      'manyToOne',
      'plugin::users-permissions.role'
    >;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'plugin::users-permissions.user',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    username: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 3;
      }>;
  };
}

declare module '@strapi/types' {
  export module Shared {
    export interface ContentTypes {
      'admin::api-token': AdminApiToken;
      'admin::api-token-permission': AdminApiTokenPermission;
      'admin::permission': AdminPermission;
      'admin::role': AdminRole;
      'admin::transfer-token': AdminTransferToken;
      'admin::transfer-token-permission': AdminTransferTokenPermission;
      'admin::user': AdminUser;
      'api::ab-test.ab-test': ApiAbTestAbTest;
      'api::about.about': ApiAboutAbout;
      'api::analytics.analytics': ApiAnalyticsAnalytics;
      'api::api-performance.api-performance': ApiApiPerformanceApiPerformance;
      'api::article.article': ApiArticleArticle;
      'api::author.author': ApiAuthorAuthor;
      'api::cache-invalidation.cache-invalidation': ApiCacheInvalidationCacheInvalidation;
      'api::cache-metrics.cache-metrics': ApiCacheMetricsCacheMetrics;
      'api::category.category': ApiCategoryCategory;
      'api::cohort-analysis.cohort-analysis': ApiCohortAnalysisCohortAnalysis;
      'api::deployment-log.deployment-log': ApiDeploymentLogDeploymentLog;
      'api::global.global': ApiGlobalGlobal;
      'api::mobile-api-usage.mobile-api-usage': ApiMobileApiUsageMobileApiUsage;
      'api::mobile-app.mobile-app': ApiMobileAppMobileApp;
      'api::paywall-metrics.paywall-metrics': ApiPaywallMetricsPaywallMetrics;
      'api::paywall-variation.paywall-variation': ApiPaywallVariationPaywallVariation;
      'api::paywall.paywall': ApiPaywallPaywall;
      'api::performance-alert.performance-alert': ApiPerformanceAlertPerformanceAlert;
      'api::preview-config.preview-config': ApiPreviewConfigPreviewConfig;
      'api::product.product': ApiProductProduct;
      'api::remote-config-version.remote-config-version': ApiRemoteConfigVersionRemoteConfigVersion;
      'api::subscription-metrics.subscription-metrics': ApiSubscriptionMetricsSubscriptionMetrics;
      'api::sync-operation.sync-operation': ApiSyncOperationSyncOperation;
      'api::sync-status.sync-status': ApiSyncStatusSyncStatus;
      'api::system-health.system-health': ApiSystemHealthSystemHealth;
      'api::translation-assignment.translation-assignment': ApiTranslationAssignmentTranslationAssignment;
      'api::translation-memory.translation-memory': ApiTranslationMemoryTranslationMemory;
      'api::user-engagement.user-engagement': ApiUserEngagementUserEngagement;
      'plugin::content-releases.release': PluginContentReleasesRelease;
      'plugin::content-releases.release-action': PluginContentReleasesReleaseAction;
      'plugin::i18n.locale': PluginI18NLocale;
      'plugin::upload.file': PluginUploadFile;
      'plugin::upload.folder': PluginUploadFolder;
      'plugin::users-permissions.permission': PluginUsersPermissionsPermission;
      'plugin::users-permissions.role': PluginUsersPermissionsRole;
      'plugin::users-permissions.user': PluginUsersPermissionsUser;
    }
  }
}
