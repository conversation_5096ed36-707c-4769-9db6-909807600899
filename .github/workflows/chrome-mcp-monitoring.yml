# GitHub Actions CI/CD Pipeline for Chrome MCP Monitoring Dashboard
# Implements Story 2.1: Chrome MCP Server Health Monitoring Dashboard

name: Chrome MCP Monitoring Dashboard CI/CD

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'src/admin/extensions/components/ChromeMCPDashboard.tsx'
      - 'src/admin/extensions/components/HealthStatusCard.tsx'
      - 'src/admin/extensions/components/MetricsChart.tsx'
      - 'src/admin/extensions/components/AlertConfiguration.tsx'
      - 'src/api/chrome-mcp-monitoring/**'
      - 'src/plugins/chrome-mcp-monitoring/**'
      - 'tests/e2e/chrome-mcp-**'
      - 'docker/chrome-mcp-monitoring.dockerfile'
      - 'docker-compose.monitoring.yml'
      - '.github/workflows/chrome-mcp-monitoring.yml'
  pull_request:
    branches: [ main ]
    paths:
      - 'src/admin/extensions/components/ChromeMCPDashboard.tsx'
      - 'src/admin/extensions/components/HealthStatusCard.tsx'
      - 'src/admin/extensions/components/MetricsChart.tsx'
      - 'src/admin/extensions/components/AlertConfiguration.tsx'
      - 'src/api/chrome-mcp-monitoring/**'
      - 'src/plugins/chrome-mcp-monitoring/**'
      - 'tests/e2e/chrome-mcp-**'

env:
  NODE_VERSION: '18'
  DOCKER_REGISTRY: ghcr.io
  IMAGE_NAME: adapty-cms/chrome-mcp-monitoring

jobs:
  # Test Chrome MCP monitoring components
  test-monitoring:
    runs-on: ubuntu-latest
    name: Test Chrome MCP Monitoring
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'yarn'
        
    - name: Install dependencies
      run: yarn install --frozen-lockfile
      
    - name: Run TypeScript checks
      run: yarn type-check
      
    - name: Run ESLint on monitoring components
      run: |
        yarn lint src/admin/extensions/components/ChromeMCPDashboard.tsx
        yarn lint src/admin/extensions/components/HealthStatusCard.tsx
        yarn lint src/admin/extensions/components/MetricsChart.tsx
        yarn lint src/admin/extensions/components/AlertConfiguration.tsx
        yarn lint src/api/chrome-mcp-monitoring/
        yarn lint src/plugins/chrome-mcp-monitoring/
      
    - name: Run unit tests for monitoring components
      run: yarn test --testPathPattern="chrome-mcp"
      
    - name: Build application with monitoring
      run: yarn build
      env:
        CHROME_MCP_MONITORING_ENABLED: true

  # E2E tests for Chrome MCP monitoring
  e2e-monitoring:
    runs-on: ubuntu-latest
    name: E2E Tests - Chrome MCP Monitoring
    needs: test-monitoring
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: strapi
          POSTGRES_USER: strapi
          POSTGRES_DB: strapi
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
          
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'yarn'
        
    - name: Install dependencies
      run: yarn install --frozen-lockfile
      
    - name: Setup Chrome for testing
      uses: browser-actions/setup-chrome@latest
      
    - name: Build application
      run: yarn build
      env:
        DATABASE_CLIENT: postgres
        DATABASE_HOST: localhost
        DATABASE_PORT: 5432
        DATABASE_NAME: strapi
        DATABASE_USERNAME: strapi
        DATABASE_PASSWORD: strapi
        CHROME_MCP_MONITORING_ENABLED: true
        
    - name: Start Strapi server
      run: |
        yarn start &
        sleep 30
      env:
        DATABASE_CLIENT: postgres
        DATABASE_HOST: localhost
        DATABASE_PORT: 5432
        DATABASE_NAME: strapi
        DATABASE_USERNAME: strapi
        DATABASE_PASSWORD: strapi
        CHROME_MCP_MONITORING_ENABLED: true
        CHROME_MCP_MONITORING_AUTO_START: true
        
    - name: Run Chrome MCP E2E tests
      run: |
        node tests/e2e/chrome-mcp-story-2.1-validation.js
        node tests/e2e/chrome-mcp-live-test.js
        
    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: chrome-mcp-test-results
        path: |
          test-results/
          screenshots/
          
  # Build and push Docker image
  build-docker:
    runs-on: ubuntu-latest
    name: Build Docker Image
    needs: [test-monitoring, e2e-monitoring]
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.DOCKER_REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
        
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.DOCKER_REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}
          
    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: docker/chrome-mcp-monitoring.dockerfile
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        
  # Deploy to staging environment
  deploy-staging:
    runs-on: ubuntu-latest
    name: Deploy to Staging
    needs: build-docker
    if: github.ref == 'refs/heads/main'
    environment: staging
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Deploy to staging
      run: |
        echo "Deploying Chrome MCP Monitoring Dashboard to staging..."
        # Add your deployment commands here
        # Example: kubectl apply -f k8s/staging/
        # Example: docker-compose -f docker-compose.monitoring.yml up -d
        
    - name: Run health checks
      run: |
        echo "Running health checks..."
        # Wait for deployment to be ready
        sleep 60
        
        # Check Chrome MCP monitoring endpoint
        curl -f http://staging.example.com/api/chrome-mcp-monitoring/status || exit 1
        
        # Check dashboard accessibility
        curl -f http://staging.example.com/admin/plugins/chrome-mcp-monitoring || exit 1
        
    - name: Run smoke tests
      run: |
        echo "Running smoke tests..."
        # Add smoke tests for Chrome MCP monitoring
        node tests/e2e/chrome-mcp-smoke-test.js
        
  # Deploy to production environment
  deploy-production:
    runs-on: ubuntu-latest
    name: Deploy to Production
    needs: deploy-staging
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Deploy to production
      run: |
        echo "Deploying Chrome MCP Monitoring Dashboard to production..."
        # Add your production deployment commands here
        
    - name: Run production health checks
      run: |
        echo "Running production health checks..."
        # Wait for deployment to be ready
        sleep 60
        
        # Check Chrome MCP monitoring endpoint
        curl -f https://production.example.com/api/chrome-mcp-monitoring/status || exit 1
        
        # Check dashboard accessibility
        curl -f https://production.example.com/admin/plugins/chrome-mcp-monitoring || exit 1
        
    - name: Notify deployment success
      run: |
        echo "Chrome MCP Monitoring Dashboard deployed successfully to production!"
        # Add notification logic (Slack, email, etc.)
        
  # Security scan
  security-scan:
    runs-on: ubuntu-latest
    name: Security Scan
    needs: build-docker
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: ${{ env.DOCKER_REGISTRY }}/${{ env.IMAGE_NAME }}:latest
        format: 'sarif'
        output: 'trivy-results.sarif'
        
    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v3
      with:
        sarif_file: 'trivy-results.sarif'
