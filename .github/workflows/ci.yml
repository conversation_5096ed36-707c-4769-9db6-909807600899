name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  NODE_VERSION: '18'
  POSTGRES_VERSION: '15'

jobs:
  test:
    name: Test
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: strapi_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'yarn'

    - name: Install dependencies
      run: yarn install --frozen-lockfile

    - name: Run linting
      run: yarn lint || echo "Linting not configured yet"

    - name: Run type checking
      run: yarn type-check || echo "Type checking not configured yet"

    - name: Run tests
      run: yarn test || echo "Tests not configured yet"
      env:
        NODE_ENV: test
        DATABASE_CLIENT: postgres
        DATABASE_HOST: localhost
        DATABASE_PORT: 5432
        DATABASE_NAME: strapi_test
        DATABASE_USERNAME: postgres
        DATABASE_PASSWORD: postgres
        REDIS_URL: redis://localhost:6379

    - name: Build application
      run: yarn build
      env:
        NODE_ENV: production

  security:
    name: Security Audit
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'yarn'

    - name: Install dependencies
      run: yarn install --frozen-lockfile

    - name: Run security audit
      run: yarn audit --level moderate

    - name: Run dependency check
      run: npx audit-ci --moderate

  build-and-deploy:
    name: Build and Deploy
    runs-on: ubuntu-latest
    needs: [test, security]
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'yarn'

    - name: Install dependencies
      run: yarn install --frozen-lockfile

    - name: Build application
      run: yarn build
      env:
        NODE_ENV: production

    - name: Build Docker image
      run: |
        docker build -t strapi-adapty-cms:${{ github.sha }} .
        docker tag strapi-adapty-cms:${{ github.sha }} strapi-adapty-cms:latest

    # Add deployment steps here based on your deployment target
    # Example for Docker registry:
    # - name: Push to registry
    #   run: |
    #     echo ${{ secrets.DOCKER_PASSWORD }} | docker login -u ${{ secrets.DOCKER_USERNAME }} --password-stdin
    #     docker push strapi-adapty-cms:${{ github.sha }}
    #     docker push strapi-adapty-cms:latest