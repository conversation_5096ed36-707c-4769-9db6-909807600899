version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: strapi-adapty-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: strapi_adapty_cms
      POSTGRES_USER: strapi
      POSTGRES_PASSWORD: strapi
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - strapi-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U strapi -d strapi_adapty_cms"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: strapi-adapty-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - strapi-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Strapi Application
  strapi:
    build: .
    container_name: strapi-adapty-cms
    restart: unless-stopped
    environment:
      NODE_ENV: development
      DATABASE_CLIENT: postgres
      DATABASE_HOST: postgres
      DATABASE_PORT: 5432
      DATABASE_NAME: strapi_adapty_cms
      DATABASE_USERNAME: strapi
      DATABASE_PASSWORD: strapi
      REDIS_URL: redis://redis:6379
    volumes:
      - ./:/app
      - /app/node_modules
      - uploads:/app/public/uploads
    ports:
      - "1337:1337"
    networks:
      - strapi-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:1337/_health"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Development tools (optional)
  adminer:
    image: adminer:latest
    container_name: strapi-adapty-adminer
    restart: unless-stopped
    ports:
      - "8080:8080"
    networks:
      - strapi-network
    depends_on:
      - postgres

volumes:
  postgres_data:
  redis_data:
  uploads:

networks:
  strapi-network:
    driver: bridge