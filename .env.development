# Development-specific environment configuration
# This file is automatically loaded when NODE_ENV=development

# Enable development mode features
NODE_ENV=development

# Enable detailed logging for debugging
LOG_LEVEL=debug

# Disable caching in development for real-time changes
CACHE_ENABLED=false

# Enable source map support
GENERATE_SOURCEMAP=true

# Development server configuration
DEV_SERVER_SOURCEMAP=true
DEV_SERVER_HOT_RELOAD=true

# Disable minification in development
MINIFY=false

# Enable TypeScript strict mode warnings
TS_STRICT_MODE=true

# Development database settings (faster for development)
DATABASE_DEBUG=true
DATABASE_POOL_MIN=1
DATABASE_POOL_MAX=5

# Disable external service calls in development (optional)
# ADAPTY_MOCK_MODE=true
# REDIS_MOCK_MODE=true

# Enable admin panel development features
ADMIN_DEV_MODE=true
ADMIN_WATCH_IGNORE_FILES=node_modules/**,dist/**,.tmp/**

# Webpack/Vite development server settings
WEBPA<PERSON>K_DEV_SERVER=true
VITE_DEV_SERVER=true

# Database Configuration
DATABASE_CLIENT=sqlite
DATABASE_FILENAME=.tmp/data.db
