{"config": {"configFile": "/Users/<USER>/Documents/Code/admin-strapi/playwright.config.js", "rootDir": "/Users/<USER>/Documents/Code/admin-strapi/tests/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": "/Users/<USER>/Documents/Code/admin-strapi/tests/e2e/global-setup.js", "globalTeardown": "/Users/<USER>/Documents/Code/admin-strapi/tests/e2e/global-teardown.js", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/results.json"}], ["junit", {"outputFile": "test-results/results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/Documents/Code/admin-strapi/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/Documents/Code/admin-strapi/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.2", "workers": 5, "webServer": {"command": "npm run develop", "url": "http://localhost:1337", "reuseExistingServer": true, "timeout": 120000}}, "suites": [], "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at /Users/<USER>/Library/Caches/ms-playwright/chromium_headless_shell-1181/chrome-mac/headless_shell\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at /Users/<USER>/Library/Caches/ms-playwright/chromium_headless_shell-1181/chrome-mac/headless_shell\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝\n    at globalSetup (/Users/<USER>/Documents/Code/admin-strapi/tests/e2e/global-setup.js:11:34)", "location": {"file": "/Users/<USER>/Documents/Code/admin-strapi/tests/e2e/global-setup.js", "column": 34, "line": 11}, "snippet": "\u001b[90m   at \u001b[39mglobal-setup.js:11\n\n   9 |   console.log('🚀 Starting global E2E test setup...');\n  10 |   \n> 11 |   const browser = await chromium.launch();\n     |                                  ^\n  12 |   const context = await browser.newContext();\n  13 |   const page = await context.newPage();\n  14 |   "}], "stats": {"startTime": "2025-08-03T05:17:07.735Z", "duration": 27247.316000000003, "expected": 0, "skipped": 0, "unexpected": 0, "flaky": 0}}