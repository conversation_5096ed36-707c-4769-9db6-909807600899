I'll use the desktop-commander to save these documentation files to the `docs/` directory.

#desktop-commander

Please save the following files to the `docs/` directory:

1. **docs/development-guide.md** - Complete development setup and workflow guide
2. **docs/deployment-guide.md** - Comprehensive production deployment guide  
3. **docs/README.md** - Documentation index and overview

Here are the file contents:

**File 1: docs/development-guide.md**
```markdown
# Development Guide - Strapi Adapty CMS

## Table of Contents
- [Prerequisites](#prerequisites)
- [Environment Setup](#environment-setup)
- [Development Workflow](#development-workflow)
- [Database Management](#database-management)
- [Testing](#testing)
- [Troubleshooting](#troubleshooting)

## Prerequisites

### System Requirements
- **Node.js**: 18.0.0 - 22.x.x (LTS recommended)
- **Yarn**: 1.22.x or higher
- **Docker**: 20.10+ (for containerized development)
- **Docker Compose**: 2.0+
- **Git**: 2.30+

### Development Tools (Recommended)
- **VS Code** with extensions:
  - TypeScript and JavaScript Language Features
  - Strapi Snippets
  - Docker
  - GitLens
- **Postman** or **Insomnia** for API testing
- **TablePlus** or **pgAdmin** for database management

## Environment Setup

### 1. Clone and Install Dependencies

```bash
# Clone the repository
git clone <repository-url>
cd strapi-adapty-cms

# Install dependencies
yarn install

# Copy environment configuration
cp .env.example .env
```

### 2. Environment Configuration

Edit `.env` file with your configuration:

```bash
# Application
NODE_ENV=development
HOST=0.0.0.0
PORT=1337

# Database (PostgreSQL recommended for production)
DATABASE_CLIENT=postgres
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=strapi_adapty_cms
DATABASE_USERNAME=strapi
DATABASE_PASSWORD=your_password
DATABASE_SSL=false

# Secrets (Generate new ones for each environment)
APP_KEYS=your_app_keys_here
API_TOKEN_SALT=your_api_token_salt
ADMIN_JWT_SECRET=your_admin_jwt_secret
TRANSFER_TOKEN_SALT=your_transfer_token_salt
JWT_SECRET=your_jwt_secret

# Adapty Integration
ADAPTY_API_KEY=your_adapty_api_key
ADAPTY_WEBHOOK_SECRET=your_webhook_secret
ADAPTY_BASE_URL=https://api.adapty.io/api/v1

# File Upload (Optional - defaults to local)
UPLOAD_PROVIDER=local
# For AWS S3:
# UPLOAD_PROVIDER=aws-s3
# AWS_ACCESS_KEY_ID=your_key
# AWS_ACCESS_SECRET=your_secret
# AWS_REGION=us-east-1
# AWS_BUCKET=your-bucket

# Redis (for caching and sessions)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Email (for notifications)
EMAIL_PROVIDER=sendmail
# For production, use SMTP:
# EMAIL_PROVIDER=smtp
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USERNAME=your_email
# SMTP_PASSWORD=your_password
```

### 3. Database Setup

#### Option A: Docker (Recommended)
```bash
# Start PostgreSQL and Redis with Docker
docker-compose -f docker-compose.dev.yml up -d postgres redis

# Wait for PostgreSQL to be ready
docker-compose exec postgres pg_isready -U strapi -d strapi_adapty_cms
```

#### Option B: Local Installation
```bash
# Install PostgreSQL locally
brew install postgresql  # macOS
sudo apt install postgresql  # Ubuntu

# Create database and user
createdb strapi_adapty_cms
createuser -P strapi  # Enter password when prompted
```

### 4. Initialize Strapi

```bash
# Run database migrations and setup
yarn strapi install

# Start development server
yarn develop
```

## Development Workflow

### Daily Development

```bash
# Start development environment
yarn develop

# In another terminal, start Docker services if needed
docker-compose -f docker-compose.dev.yml up -d postgres redis

# Access the application
# Admin Panel: http://localhost:1337/admin
# API: http://localhost:1337/api
# Documentation: http://localhost:1337/documentation
```

### Code Structure

```
src/
├── api/                    # Content types and API endpoints
│   ├── paywall/           # Paywall content management
│   ├── ab-test/           # A/B testing functionality
│   ├── mobile-api/        # Mobile API endpoints
│   └── analytics/         # Analytics and reporting
├── components/            # Reusable content components
├── extensions/            # Strapi core extensions
├── middlewares/           # Custom middleware
├── plugins/              # Custom plugins
└── services/             # Business logic services
```

### Creating New Content Types

```bash
# Generate new content type
yarn strapi generate

# Or manually create in src/api/your-type/
mkdir -p src/api/your-type/content-types/your-type
mkdir -p src/api/your-type/controllers
mkdir -p src/api/your-type/routes
mkdir -p src/api/your-type/services
```

### Working with Adapty Integration

```typescript
// Example: Using Adapty service
const adaptyService = strapi.service('api::adapty.adapty');

// Deploy paywall to Adapty
const result = await adaptyService.deployPaywall(paywallId, {
  placement: 'premium_paywall',
  config: paywallConfig
});
```

## Database Management

### Migrations

```bash
# Create new migration
yarn strapi generate migration create_new_table

# Run migrations
yarn strapi db:migrate

# Rollback last migration
yarn strapi db:migrate:down
```

### Seeding Data

```bash
# Seed example data
yarn seed:example

# Custom seed script
node scripts/seed-custom-data.js
```

### Database Backup and Restore

```bash
# Backup database
pg_dump -h localhost -U strapi strapi_adapty_cms > backup.sql

# Restore database
psql -h localhost -U strapi strapi_adapty_cms < backup.sql
```

## Testing

### Running Tests

```bash
# Run all tests
yarn test

# Run tests in watch mode
yarn test:watch

# Run tests with coverage
yarn test:coverage

# Run specific test file
yarn test src/api/paywall/tests/paywall.test.js
```

### Writing Tests

```typescript
// Example test file: src/api/paywall/tests/paywall.test.js
const request = require('supertest');

describe('Paywall API', () => {
  it('should create a new paywall', async () => {
    const response = await request(strapi.server.httpServer)
      .post('/api/paywalls')
      .send({
        data: {
          title: 'Test Paywall',
          placement: 'premium'
        }
      })
      .expect(200);

    expect(response.body.data.attributes.title).toBe('Test Paywall');
  });
});
```

## Troubleshooting

### Common Issues

#### 1. Port Already in Use
```bash
# Find process using port 1337
lsof -i :1337

# Kill the process
kill -9 <PID>
```

#### 2. Database Connection Issues
```bash
# Check PostgreSQL status
docker-compose ps postgres

# View PostgreSQL logs
docker-compose logs postgres

# Reset database
docker-compose down -v
docker-compose up -d postgres
```

#### 3. Node Modules Issues
```bash
# Clear node modules and reinstall
rm -rf node_modules yarn.lock
yarn install
```

#### 4. TypeScript Compilation Errors
```bash
# Check TypeScript compilation
npx tsc --noEmit --skipLibCheck

# Clear TypeScript cache
rm -rf .tsbuildinfo
```

#### 5. Strapi Cache Issues
```bash
# Clear Strapi cache
rm -rf .strapi
rm -rf dist
rm -rf build

# Restart development server
yarn develop
```

### Debug Mode

```bash
# Start with debug logging
DEBUG=strapi:* yarn develop

# Or with specific debug categories
DEBUG=strapi:database,strapi:server yarn develop
```

### Performance Monitoring

```bash
# Monitor memory usage
node --inspect yarn develop

# Profile performance
node --prof yarn develop
```

### Logs

```bash
# View application logs
tail -f logs/strapi.log

# View Docker logs
docker-compose logs -f strapi
```

## Development Best Practices

### 1. Code Quality
- Use TypeScript for type safety
- Follow ESLint and Prettier configurations
- Write unit tests for business logic
- Use meaningful commit messages

### 2. Environment Management
- Never commit `.env` files
- Use different configurations for dev/staging/prod
- Validate environment variables on startup

### 3. Database
- Always use migrations for schema changes
- Backup database before major changes
- Use transactions for complex operations

### 4. API Development
- Follow RESTful conventions
- Implement proper error handling
- Add request validation
- Document API endpoints

### 5. Security
- Keep dependencies updated
- Use HTTPS in production
- Implement rate limiting
- Validate all inputs

## Useful Commands Reference

```bash
# Development
yarn develop              # Start development server
yarn build               # Build for production
yarn start               # Start production server

# Database
yarn strapi db:migrate    # Run migrations
yarn strapi db:seed      # Seed database

# Content Types
yarn strapi generate     # Generate new content type
yarn strapi console      # Access Strapi console

# Docker
docker-compose up -d     # Start services
docker-compose down      # Stop services
docker-compose logs -f   # View logs

# Testing
yarn test               # Run tests
yarn test:watch         # Watch mode
yarn test:coverage      # With coverage

# Utilities
yarn lint              # Lint code
yarn format            # Format code
yarn type-check        # Check TypeScript
```

## Getting Help

- **Documentation**: Check `docs/` directory
- **Strapi Docs**: https://docs.strapi.io
- **Issues**: Create GitHub issue with reproduction steps
- **Community**: Strapi Discord and Forum
```

**File 2: docs/deployment-guide.md**
```markdown
# Deployment Guide - Strapi Adapty CMS

## Table of Contents
- [Deployment Overview](#deployment-overview)
- [Environment Preparation](#environment-preparation)
- [Docker Deployment](#docker-deployment)
- [Cloud Deployment](#cloud-deployment)
- [Database Setup](#database-setup)
- [SSL and Security](#ssl-and-security)
- [Monitoring and Maintenance](#monitoring-and-maintenance)
- [Rollback Procedures](#rollback-procedures)

## Deployment Overview

### Supported Deployment Methods
1. **Docker Compose** (Recommended for most use cases)
2. **Kubernetes** (For enterprise/scalable deployments)
3. **Cloud Platforms** (AWS, Google Cloud, Azure)
4. **Traditional VPS** (Ubuntu/CentOS servers)

### Architecture Components
- **Strapi Application** (Node.js)
- **PostgreSQL Database**
- **Redis** (Caching and sessions)
- **Nginx** (Reverse proxy and static files)
- **SSL Certificate** (Let's Encrypt or commercial)

## Environment Preparation

### 1. Server Requirements

#### Minimum Requirements
- **CPU**: 2 cores
- **RAM**: 4GB
- **Storage**: 20GB SSD
- **OS**: Ubuntu 20.04+ or CentOS 8+

#### Recommended for Production
- **CPU**: 4+ cores
- **RAM**: 8GB+
- **Storage**: 50GB+ SSD
- **OS**: Ubuntu 22.04 LTS

### 2. Domain and DNS Setup

```bash
# Point your domain to server IP
# A record: yourdomain.com -> YOUR_SERVER_IP
# A record: api.yourdomain.com -> YOUR_SERVER_IP (optional subdomain)
```

### 3. Server Preparation

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker and Docker Compose
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Install additional tools
sudo apt install -y git nginx certbot python3-certbot-nginx htop
```

## Docker Deployment

### 1. Production Docker Configuration

Create `docker-compose.prod.yml`:

```yaml
version: '3.8'

services:
  strapi:
    build:
      context: .
      dockerfile: Dockerfile
    environment:
      NODE_ENV: production
      DATABASE_CLIENT: postgres
      DATABASE_HOST: postgres
      DATABASE_PORT: 5432
      DATABASE_NAME: ${DATABASE_NAME}
      DATABASE_USERNAME: ${DATABASE_USERNAME}
      DATABASE_PASSWORD: ${DATABASE_PASSWORD}
      REDIS_HOST: redis
      REDIS_PORT: 6379
    volumes:
      - ./uploads:/app/public/uploads
      - ./logs:/app/logs
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    networks:
      - strapi-network

  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: ${DATABASE_NAME}
      POSTGRES_USER: ${DATABASE_USERNAME}
      POSTGRES_PASSWORD: ${DATABASE_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
    restart: unless-stopped
    networks:
      - strapi-network

  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - strapi-network

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/sites:/etc/nginx/sites-available
      - ./ssl:/etc/nginx/ssl
      - ./uploads:/var/www/uploads
    depends_on:
      - strapi
    restart: unless-stopped
    networks:
      - strapi-network

volumes:
  postgres_data:
  redis_data:

networks:
  strapi-network:
    driver: bridge
```

### 2. Production Environment File

Create `.env.production`:

```bash
# Application
NODE_ENV=production
HOST=0.0.0.0
PORT=1337
PUBLIC_URL=https://yourdomain.com

# Database
DATABASE_CLIENT=postgres
DATABASE_HOST=postgres
DATABASE_PORT=5432
DATABASE_NAME=strapi_adapty_cms_prod
DATABASE_USERNAME=strapi_prod
DATABASE_PASSWORD=your_secure_password_here
DATABASE_SSL=false

# Security Keys (Generate new secure keys)
APP_KEYS=key1,key2,key3,key4
API_TOKEN_SALT=your_secure_salt
ADMIN_JWT_SECRET=your_secure_admin_jwt_secret
TRANSFER_TOKEN_SALT=your_secure_transfer_salt
JWT_SECRET=your_secure_jwt_secret

# Adapty Integration
ADAPTY_API_KEY=your_production_adapty_api_key
ADAPTY_WEBHOOK_SECRET=your_production_webhook_secret
ADAPTY_BASE_URL=https://api.adapty.io/api/v1

# File Upload (AWS S3 recommended for production)
UPLOAD_PROVIDER=aws-s3
AWS_ACCESS_KEY_ID=your_aws_key
AWS_ACCESS_SECRET=your_aws_secret
AWS_REGION=us-east-1
AWS_BUCKET=your-production-bucket
AWS_CDN_URL=https://your-cloudfront-domain.com

# Redis
REDIS_HOST=redis
REDIS_PORT=6379

# Email
EMAIL_PROVIDER=smtp
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password

# Monitoring
SENTRY_DSN=your_sentry_dsn
LOG_LEVEL=info
```

### 3. Nginx Configuration

Create `nginx/nginx.conf`:

```nginx
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Logging
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';
    access_log /var/log/nginx/access.log main;

    # Performance
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=admin:10m rate=5r/s;

    # Upstream
    upstream strapi {
        server strapi:1337;
    }

    # HTTP to HTTPS redirect
    server {
        listen 80;
        server_name yourdomain.com www.yourdomain.com;
        return 301 https://$server_name$request_uri;
    }

    # Main server block
    server {
        listen 443 ssl http2;
        server_name yourdomain.com www.yourdomain.com;

        # SSL Configuration
        ssl_certificate /etc/nginx/ssl/fullchain.pem;
        ssl_certificate_key /etc/nginx/ssl/privkey.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;

        # Admin panel
        location /admin {
            limit_req zone=admin burst=20 nodelay;
            proxy_pass http://strapi;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
        }

        # API endpoints
        location /api {
            limit_req zone=api burst=50 nodelay;
            proxy_pass http://strapi;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Static files
        location /uploads {
            alias /var/www/uploads;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # Health check
        location /_health {
            proxy_pass http://strapi;
            access_log off;
        }

        # Root redirect
        location / {
            return 301 /admin;
        }
    }
}
```

### 4. Deployment Script

Create `scripts/deploy.sh`:

```bash
#!/bin/bash

# Production Deployment Script
set -e

echo "🚀 Starting deployment..."

# Configuration
DEPLOY_USER="deploy"
DEPLOY_HOST="your-server.com"
DEPLOY_PATH="/opt/strapi-adapty-cms"
BACKUP_PATH="/opt/backups"
DOCKER_COMPOSE_FILE="docker-compose.prod.yml"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Pre-deployment checks
log_info "Running pre-deployment checks..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    log_error "Docker is not running"
    exit 1
fi

# Check if environment file exists
if [ ! -f .env.production ]; then
    log_error ".env.production file not found"
    exit 1
fi

# Backup current deployment
log_info "Creating backup..."
BACKUP_NAME="backup-$(date +%Y%m%d-%H%M%S)"
mkdir -p $BACKUP_PATH/$BACKUP_NAME

# Backup database
docker-compose -f $DOCKER_COMPOSE_FILE exec -T postgres pg_dump -U $DATABASE_USERNAME $DATABASE_NAME > $BACKUP_PATH/$BACKUP_NAME/database.sql

# Backup uploads
cp -r ./uploads $BACKUP_PATH/$BACKUP_NAME/

log_info "Backup created: $BACKUP_PATH/$BACKUP_NAME"

# Build new images
log_info "Building application..."
docker-compose -f $DOCKER_COMPOSE_FILE build --no-cache

# Run database migrations
log_info "Running database migrations..."
docker-compose -f $DOCKER_COMPOSE_FILE run --rm strapi yarn strapi db:migrate

# Deploy new version
log_info "Deploying new version..."
docker-compose -f $DOCKER_COMPOSE_FILE up -d

# Wait for services to be ready
log_info "Waiting for services to be ready..."
sleep 30

# Health check
log_info "Running health checks..."
if curl -f http://localhost/_health > /dev/null 2>&1; then
    log_info "✅ Deployment successful!"
else
    log_error "❌ Health check failed, rolling back..."
    
    # Rollback
    docker-compose -f $DOCKER_COMPOSE_FILE down
    
    # Restore database
    docker-compose -f $DOCKER_COMPOSE_FILE up -d postgres
    sleep 10
    docker-compose -f $DOCKER_COMPOSE_FILE exec -T postgres psql -U $DATABASE_USERNAME -d $DATABASE_NAME < $BACKUP_PATH/$BACKUP_NAME/database.sql
    
    # Restore uploads
    rm -rf ./uploads
    cp -r $BACKUP_PATH/$BACKUP_NAME/uploads ./
    
    # Start previous version
    docker-compose -f $DOCKER_COMPOSE_FILE up -d
    
    log_error "Rollback completed"
    exit 1
fi

# Cleanup old backups (keep last 5)
log_info "Cleaning up old backups..."
cd $BACKUP_PATH
ls -t | tail -n +6 | xargs -r rm -rf

log_info "🎉 Deployment completed successfully!"
```

## SSL and Security

### SSL Certificate Setup (Let's Encrypt)

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Auto-renewal (add to crontab)
0 12 * * * /usr/bin/certbot renew --quiet
```

### Security Hardening

Create `scripts/security-hardening.sh`:

```bash
#!/bin/bash

# Security Hardening Script
set -e

echo "🔒 Applying security hardening..."

# Update system
sudo apt update && sudo apt upgrade -y

# Configure firewall
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw --force enable

# Disable root login
sudo sed -i 's/PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config
sudo systemctl restart ssh

# Install fail2ban
sudo apt install -y fail2ban
sudo systemctl enable fail2ban
sudo systemctl start fail2ban

# Configure automatic security updates
sudo apt install -y unattended-upgrades
echo 'Unattended-Upgrade::Automatic-Reboot "false";' | sudo tee -a /etc/apt/apt.conf.d/50unattended-upgrades

# Set up log rotation
sudo tee /etc/logrotate.d/strapi << EOF
/opt/strapi-adapty-cms/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 root root
}
EOF

echo "✅ Security hardening completed"
```

## Monitoring and Maintenance

### Health Check Endpoint

Create health check files:

`src/api/health/routes/health.ts`:
```typescript
export default {
  routes: [
    {
      method: 'GET',
      path: '/_health',
      handler: 'health.check',
      config: {
        auth: false,
      },
    },
  ],
};
```

`src/api/health/controllers/health.ts`:
```typescript
export default {
  async check(ctx) {
    try {
      // Check database connection
      await strapi.db.connection.raw('SELECT 1');
      
      // Check Redis connection
      const redis = strapi.redis;
      if (redis) {
        await redis.ping();
      }

      ctx.body = {
        status: 'ok',
        timestamp: new Date().toISOString(),
        version: process.env.npm_package_version,
        uptime: process.uptime(),
      };
    } catch (error) {
      ctx.status = 503;
      ctx.body = {
        status: 'error',
        error: error.message,
      };
    }
  },
};
```

### Monitoring Script

Create `scripts/monitor.sh`:

```bash
#!/bin/bash

# System Monitoring Script
set -e

# Configuration
ALERT_EMAIL="<EMAIL>"
LOG_FILE="/var/log/strapi-monitor.log"

# Functions
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a $LOG_FILE
}

send_alert() {
    echo "$1" | mail -s "Strapi Alert: $2" $ALERT_EMAIL
    log_message "ALERT: $2 - $1"
}

# Check disk space
DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    send_alert "Disk usage is at ${DISK_USAGE}%" "High Disk Usage"
fi

# Check memory usage
MEMORY_USAGE=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
if [ $MEMORY_USAGE -gt 90 ]; then
    send_alert "Memory usage is at ${MEMORY_USAGE}%" "High Memory Usage"
fi

# Check if Strapi is running
if ! curl -f http://localhost/_health > /dev/null 2>&1; then
    send_alert "Strapi health check failed" "Service Down"
    
    # Attempt to restart
    log_message "Attempting to restart Strapi..."
    docker-compose -f docker-compose.prod.yml restart strapi
    
    sleep 30
    
    if curl -f http://localhost/_health > /dev/null 2>&1; then
        log_message "Strapi restarted successfully"
    else
        send_alert "Failed to restart Strapi" "Critical Service Failure"
    fi
fi

# Check database connections
DB_CONNECTIONS=$(docker-compose exec -T postgres psql -U strapi_prod -d strapi_adapty_cms_prod -c "SELECT count(*) FROM pg_stat_activity;" | sed -n '3p' | xargs)
if [ $DB_CONNECTIONS -gt 150 ]; then
    send_alert "Database has ${DB_CONNECTIONS} active connections" "High Database Load"
fi

log_message "Monitoring check completed"
```

## Rollback Procedures

### Quick Rollback Script

Create `scripts/rollback.sh`:

```bash
#!/bin/bash

# Rollback Script
set -e

# Configuration
BACKUP_PATH="/opt/backups"
DOCKER_COMPOSE_FILE="docker-compose.prod.yml"

echo "🔄 Starting rollback procedure..."

# List available backups
echo "Available backups:"
ls -la $BACKUP_PATH/

# Get backup name from user
read -p "Enter backup name to restore (e.g., backup-20240101-120000): " BACKUP_NAME

if [ ! -d "$BACKUP_PATH/$BACKUP_NAME" ]; then
    echo "❌ Backup not found: $BACKUP_NAME"
    exit 1
fi

echo "⚠️  This will restore the system to backup: $BACKUP_NAME"
read -p "Are you sure? (yes/no): " CONFIRM

if [ "$CONFIRM" != "yes" ]; then
    echo "Rollback cancelled"
    exit 0
fi

# Stop current services
echo "Stopping current services..."
docker-compose -f $DOCKER_COMPOSE_FILE down

# Restore database
echo "Restoring database..."
docker-compose -f $DOCKER_COMPOSE_FILE up -d postgres
sleep 10

# Drop and recreate database
docker-compose -f $DOCKER_COMPOSE_FILE exec postgres psql -U postgres -c "DROP DATABASE IF EXISTS strapi_adapty_cms_prod;"
docker-compose -f $DOCKER_COMPOSE_FILE exec postgres psql -U postgres -c "CREATE DATABASE strapi_adapty_cms_prod OWNER strapi_prod;"

# Restore database data
docker-compose -f $DOCKER_COMPOSE_FILE exec -T postgres psql -U strapi_prod -d strapi_adapty_cms_prod < $BACKUP_PATH/$BACKUP_NAME/database.sql

# Restore uploads
echo "Restoring uploads..."
rm -rf ./uploads
cp -r $BACKUP_PATH/$BACKUP_NAME/uploads ./

# Start all services
echo "Starting services..."
docker-compose -f $DOCKER_COMPOSE_FILE up -d

# Wait and verify
sleep 30
if curl -f http://localhost/_health > /dev/null 2>&1; then
    echo "✅ Rollback completed successfully!"
else
    echo "❌ Rollback failed - service not responding"
    exit 1
fi
```

## Maintenance Tasks

### Regular Maintenance Checklist

1. **Daily**
   - Monitor system health
   - Check error logs
   - Verify backups

2. **Weekly**
   - Update system packages
   - Review performance metrics
   - Clean up old logs

3. **Monthly**
   - Security updates
   - Database optimization
   - Backup verification

4. **Quarterly**
   - Dependency updates
   - Security audit
   - Disaster recovery test

### Automated Maintenance Script

Create `scripts/maintenance.sh`:

```bash
#!/bin/bash

# Automated Maintenance Script
set -e

echo "🔧 Starting maintenance tasks..."

# Update system packages
echo "Updating system packages..."
sudo apt update && sudo apt upgrade -y

# Clean up Docker
echo "Cleaning up Docker..."
docker system prune -f
docker volume prune -f

# Optimize database
echo "Optimizing database..."
docker-compose exec postgres psql -U strapi_prod -d strapi_adapty_cms_prod -c "VACUUM ANALYZE;"

# Clean up old logs
echo "Cleaning up old logs..."
find /var/log -name "*.log" -mtime +30 -delete
find ./logs -name "*.log" -mtime +7 -delete

# Update SSL certificates
echo "Updating SSL certificates..."
sudo certbot renew --quiet

# Restart services for fresh start
echo "Restarting services..."
docker-compose -f docker-compose.prod.yml restart

echo "✅ Maintenance completed successfully!"
```

This comprehensive deployment guide covers all aspects of deploying your Strapi Adapty CMS to production, from basic Docker setups to enterprise Kubernetes deployments, with proper security, monitoring, and maintenance procedures.
```

**File 3: docs/README.md**
```markdown
# Documentation Index - Strapi Adapty CMS

Welcome to the comprehensive documentation for the Strapi Adapty CMS project. This documentation covers everything from development setup to production deployment and maintenance.

## 📚 Documentation Structure

### Getting Started
- **[Development Guide](./development-guide.md)** - Complete setup and development workflow
- **[Deployment Guide](./deployment-guide.md)** - Production deployment and infrastructure
- **[API Documentation](./mobile-api-v1-documentation.md)** - Mobile API endpoints and usage

### Feature Documentation
- **[Media Management](./media-management.md)** - File upload and optimization system
- **[A/B Testing](./ab-testing-guide.md)** - A/B test configuration and management
- **[Analytics](./analytics-guide.md)** - Performance monitoring and reporting
- **[Localization](./localization-guide.md)** - Multi-language content management

### Technical Guides
- **[Architecture Overview](./architecture.md)** - System design and components
- **[Database Schema](./database-schema.md)** - Data models and relationships
- **[Security Guide](./security-guide.md)** - Security best practices and configuration
- **[Performance Optimization](./performance-guide.md)** - Optimization techniques and monitoring

### Operations
- **[Monitoring Guide](./monitoring-guide.md)** - System monitoring and alerting
- **[Backup and Recovery](./backup-recovery.md)** - Data protection and disaster recovery
- **[Troubleshooting](./troubleshooting.md)** - Common issues and solutions

## 🚀 Quick Start

1. **Development Setup**
   ```bash
   # Clone and setup
   git clone <repository-url>
   cd strapi-adapty-cms
   yarn install
   cp .env.example .env
   
   # Start development
   yarn develop
   ```

2. **Production Deployment**
   ```bash
   # Deploy with Docker
   docker-compose -f docker-compose.prod.yml up -d
   ```

3. **Access Points**
   - Admin Panel: `http://localhost:1337/admin`
   - API: `http://localhost:1337/api`
   - Documentation: `http://localhost:1337/documentation`

## 🔧 Key Features

- **Paywall Management** - Visual paywall builder with themes and components
- **Adapty Integration** - Seamless subscription management integration
- **A/B Testing** - Built-in experimentation platform
- **Mobile API** - Optimized endpoints for mobile applications
- **Analytics** - Comprehensive performance monitoring
- **Localization** - Multi-language content support
- **Media Management** - Advanced file handling and optimization

## 📖 Additional Resources

- **[Strapi Documentation](https://docs.strapi.io)** - Official Strapi docs
- **[Adapty Documentation](https://docs.adapty.io)** - Adapty integration guide
- **[Project Repository](https://github.com/your-org/strapi-adapty-cms)** - Source code and issues

## 🆘 Getting Help

- **Issues**: Create a GitHub issue with detailed reproduction steps
- **Questions**: Check existing documentation or ask in team chat
- **Emergencies**: Contact the on-call engineer

## 📝 Contributing

Please read the development guide before contributing and ensure all tests pass:

```bash
yarn test
yarn lint
yarn type-check
```

---

*Last updated: $(date)*
```

Please save these three files to the `docs/` directory. They provide comprehensive guides for both development and production deployment of the Strapi Adapty CMS project.
