# TechDoc AI Agent - Project Structure

## 📁 Directory Layout

```
src/agents/techdoc-ai/
├── 📁 core/                    # Core agent functionality
│   ├── 📁 scanner/             # Code and schema analysis
│   │   ├── CodeScanner.ts      # TypeScript/JS file analysis
│   │   ├── SchemaAnalyzer.ts   # Strapi schema parsing
│   │   ├── ApiExtractor.ts     # REST/GraphQL endpoint discovery
│   │   ├── CommentParser.ts    # JSDoc and comment extraction
│   │   └── index.ts            # Scanner module exports
│   ├── 📁 generator/           # Content generation engine
│   │   ├── ContentGenerator.ts # AI-powered content creation
│   │   ├── TemplateEngine.ts   # Handlebars template processing
│   │   ├── FormatProcessor.ts  # Multi-format output handling
│   │   ├── ExampleGenerator.ts # Code example creation
│   │   └── index.ts            # Generator module exports
│   ├── 📁 quality/             # Quality assurance
│   │   ├── Validator.ts        # Documentation validation
│   │   ├── LinkChecker.ts      # Link verification
│   │   ├── CompletionChecker.ts# Coverage analysis
│   │   └── index.ts            # QA module exports
│   └── 📁 integrations/        # External service integrations
│       ├── StrapiPlugin.ts     # Strapi CMS integration
│       ├── GitManager.ts       # Git repository management
│       ├── DeploymentManager.ts# Website deployment
│       ├── NotificationManager.ts # Team notifications
│       └── index.ts            # Integration module exports
├── 📁 templates/               # Documentation templates
│   ├── 📁 api/                 # API documentation templates
│   │   ├── rest-endpoint.hbs   # REST API endpoint template
│   │   ├── graphql-schema.hbs  # GraphQL schema template
│   │   └── authentication.hbs # Auth documentation template
│   ├── 📁 content-types/       # Content type templates
│   │   ├── schema-overview.hbs # Schema documentation
│   │   ├── field-reference.hbs # Field type reference
│   │   └── relationships.hbs   # Relationship documentation
│   ├── 📁 guides/              # Integration guide templates
│   │   ├── setup-guide.hbs     # Setup instructions
│   │   ├── integration-guide.hbs # External API integration
│   │   └── deployment-guide.hbs # Deployment instructions
│   ├── 📁 components/          # Reusable template components
│   │   ├── code-block.hbs      # Code syntax highlighting
│   │   ├── api-table.hbs       # API parameter tables
│   │   ├── navigation.hbs      # Navigation menu
│   │   └── footer.hbs          # Documentation footer
│   └── 📁 layouts/             # Page layout templates
│       ├── main.hbs            # Main page layout
│       ├── api-reference.hbs   # API reference layout
│       └── guide.hbs           # Guide page layout
├── 📁 config/                  # Configuration files
│   ├── default.json            # Default configuration
│   ├── development.json        # Development settings
│   ├── production.json         # Production settings
│   ├── templates.json          # Template configuration
│   └── integrations.json       # External service config
├── 📁 types/                   # TypeScript type definitions
│   ├── agent.d.ts              # Core agent interfaces
│   ├── strapi.d.ts             # Strapi-specific types
│   ├── documentation.d.ts      # Documentation structure types
│   └── integrations.d.ts       # Integration service types
├── 📁 utils/                   # Utility functions
│   ├── file-utils.ts           # File system operations
│   ├── string-utils.ts         # String manipulation
│   ├── markdown-utils.ts       # Markdown processing
│   ├── git-utils.ts            # Git operations
│   └── logger.ts               # Logging utility
├── 📁 tests/                   # Test suite
│   ├── 📁 unit/                # Unit tests
│   │   ├── scanner.test.ts     # Scanner module tests
│   │   ├── generator.test.ts   # Generator module tests
│   │   └── integrations.test.ts # Integration tests
│   ├── 📁 integration/         # Integration tests
│   │   ├── full-workflow.test.ts # End-to-end tests
│   │   └── strapi-plugin.test.ts # Strapi integration tests
│   ├── 📁 fixtures/            # Test data
│   │   ├── sample-schemas/     # Sample Strapi schemas
│   │   ├── sample-apis/        # Sample API definitions
│   │   └── expected-outputs/   # Expected documentation outputs
│   └── setup.ts                # Test environment setup
├── 📁 docs/                    # Agent documentation
│   ├── README.md               # Main documentation
│   ├── api-reference.md        # API reference
│   ├── configuration.md        # Configuration guide
│   ├── templates.md            # Template development guide
│   └── troubleshooting.md      # Common issues and solutions
├── 📁 scripts/                 # Build and deployment scripts
│   ├── build.ts                # Build script
│   ├── test.ts                 # Test runner
│   ├── deploy.ts               # Deployment script
│   └── generate-docs.ts        # Documentation generation
├── package.json                # Package configuration
├── tsconfig.json               # TypeScript configuration
├── jest.config.js              # Jest test configuration
├── .eslintrc.js                # ESLint configuration
├── .prettierrc                 # Prettier configuration
├── .gitignore                  # Git ignore rules
└── README.md                   # Project README
```

## 🔧 Configuration Structure

### Main Configuration (`config/default.json`)
```json
{
  "agent": {
    "name": "TechDoc AI Agent",
    "version": "1.0.0",
    "description": "Automated technical documentation generation for Strapi projects"
  },
  "scanner": {
    "includePaths": ["src/api", "src/components", "src/extensions"],
    "excludePaths": ["node_modules", "build", ".tmp", "coverage"],
    "fileExtensions": [".ts", ".js", ".json", ".md"],
    "maxFileSize": "1MB",
    "enableWatch": true
  },
  "generator": {
    "aiProvider": "openai",
    "model": "gpt-4",
    "temperature": 0.3,
    "maxTokens": 2048,
    "templateEngine": "handlebars",
    "outputFormats": ["markdown", "html", "pdf"],
    "enableExamples": true,
    "enableBestPractices": true
  },
  "quality": {
    "enableValidation": true,
    "enableLinkChecking": true,
    "enableCompletionCheck": true,
    "minimumCoverage": 90,
    "enableSpellCheck": false
  },
  "output": {
    "baseDirectory": "docs",
    "staticAssets": "assets",
    "searchIndex": "search.json",
    "enableSitemap": true,
    "enableRSS": false
  }
}
```