# TechDoc AI Agent - Configuration & Deployment

## ⚙️ Configuration Management

### Environment-Based Configuration
```typescript
interface AgentConfig {
  // Environment
  environment: 'development' | 'staging' | 'production';
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  
  // Core Settings
  agent: {
    name: string;
    version: string;
    timeout: number;
    maxConcurrency: number;
    retryAttempts: number;
  };
  
  // Scanner Configuration
  scanner: {
    watchMode: boolean;
    includePaths: string[];
    excludePaths: string[];
    filePatterns: string[];
    maxFileSize: string;
    parallelScanning: boolean;
  };
  
  // AI Provider Settings
  ai: {
    provider: 'openai' | 'anthropic' | 'huggingface';
    model: string;
    apiKey: string;
    baseURL?: string;
    temperature: number;
    maxTokens: number;
    requestTimeout: number;
    rateLimiting: {
      requestsPerMinute: number;
      tokensPerMinute: number;
    };
  };
  
  // Output Configuration
  output: {
    formats: OutputFormat[];
    destination: string;
    theme: string;
    customCSS?: string;
    enableSearch: boolean;
    enableAnalytics: boolean;
  };
  
  // Integration Settings
  integrations: {
    strapi: StrapiIntegrationConfig;
    git: GitIntegrationConfig;
    deployment: DeploymentConfig;
    notifications: NotificationConfig;
  };
}
```

### Strapi Integration Configuration
```typescript
interface StrapiIntegrationConfig {
  enabled: boolean;
  pluginMode: boolean;
  apiUrl?: string;
  adminPath?: string;
  contentTypes: {
    includeAll: boolean;
    include: string[];
    exclude: string[];
  };
  components: {
    includeShared: boolean;
    includePrivate: boolean;
  };
  customFields: {
    documentCustomFields: boolean;
    includePluginFields: boolean;
  };
}
```

### Git Integration Configuration
```typescript
interface GitIntegrationConfig {
  enabled: boolean;
  autoCommit: boolean;
  autoPush: boolean;
  branch: string;
  commitMessage: string;
  pullRequestOnUpdate: boolean;
  webhooks: {
    enabled: boolean;
    secret: string;
    events: string[];
  };
}
```

### Deployment Configuration
```typescript
interface DeploymentConfig {
  enabled: boolean;
  provider: 'netlify' | 'vercel' | 'github-pages' | 'aws-s3' | 'custom';
  settings: {
    netlify?: {
      siteId: string;
      accessToken: string;
      buildCommand: string;
      publishDirectory: string;
    };
    vercel?: {
      projectId: string;
      accessToken: string;
      framework: string;
    };
    githubPages?: {
      repository: string;
      branch: string;
      token: string;
    };
    awsS3?: {
      bucket: string;
      region: string;
      accessKeyId: string;
      secretAccessKey: string;
      cloudFrontDistribution?: string;
    };
    custom?: {
      buildCommand: string;
      deployCommand: string;
      environment: Record<string, string>;
    };
  };
}
```

## 🚀 Deployment Strategies

### 1. Strapi Plugin Deployment
```typescript
// strapi-plugin-techdoc-ai/package.json
{
  "name": "strapi-plugin-techdoc-ai",
  "version": "1.0.0",
  "description": "AI-powered technical documentation generator for Strapi",
  "strapi": {
    "name": "techdoc-ai",
    "displayName": "TechDoc AI",
    "description": "Generate technical documentation automatically",
    "kind": "plugin"
  },
  "dependencies": {
    "@strapi/design-system": "^1.6.3",
    "@strapi/helper-plugin": "^4.6.0",
    "@strapi/icons": "^1.6.3"
  }
}

// Plugin Structure
src/plugins/techdoc-ai/
├── admin/           # Admin panel integration
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   └── index.js
│   └── package.json
├── server/          # Server-side functionality
│   ├── controllers/
│   ├── routes/
│   ├── services/
│   └── index.js
└── package.json
```

### 2. Standalone Service Deployment
```dockerfile
# Dockerfile
FROM node:20-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci --only=production

# Copy source code
COPY src/ ./src/
COPY config/ ./config/
COPY templates/ ./templates/

# Create non-root user
RUN addgroup -g 1001 -S techdoc && \
    adduser -S techdoc -u 1001
USER techdoc

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node src/health-check.js

EXPOSE 3000

CMD ["node", "src/index.js"]
```

### 3. Docker Compose Deployment
```yaml
# docker-compose.yml
version: '3.8'

services:
  techdoc-ai:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - STRAPI_URL=${STRAPI_URL}
    volumes:
      - ./docs:/app/docs
      - ./config:/app/config:ro
    depends_on:
      - redis
    restart: unless-stopped
  
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
  
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docs:/usr/share/nginx/html:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - techdoc-ai
    restart: unless-stopped

volumes:
  redis_data:
```

### 4. Kubernetes Deployment
```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: techdoc-ai
  labels:
    app: techdoc-ai
spec:
  replicas: 3
  selector:
    matchLabels:
      app: techdoc-ai
  template:
    metadata:
      labels:
        app: techdoc-ai
    spec:
      containers:
      - name: techdoc-ai
        image: techdoc-ai:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: techdoc-secrets
              key: openai-api-key
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: techdoc-ai-service
spec:
  selector:
    app: techdoc-ai
  ports:
  - protocol: TCP
    port: 80
    targetPort: 3000
  type: LoadBalancer
```