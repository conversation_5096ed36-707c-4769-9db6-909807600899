# Development Server Setup Guide

This guide will walk you through setting up the Strapi Mobile App Paywall Management System for local development.

## Prerequisites

Before starting, ensure you have the following installed:

- **Node.js**: Version 18.0.0 to 22.x.x (check with `node --version`)
- **Yarn**: Package manager (install with `npm install -g yarn`)
- **Git**: Version control system

## Step 1: Environment Configuration

### 1.1 Copy Environment Template
```bash
cp .env.example .env
```

### 1.2 Configure Environment Variables

Edit the `.env` file with your specific configuration:

#### Required Settings (Already Configured)
- `HOST=0.0.0.0` - Server host
- `PORT=1337` - Development server port
- App secrets (already generated)

#### Database Configuration
The project is configured to use PostgreSQL by default, but SQLite is available for development:

**For SQLite (Recommended for Development):**
```env
DATABASE_CLIENT=sqlite
DATABASE_FILENAME=.tmp/data.db
```

**For PostgreSQL (Production-like):**
```env
DATABASE_CLIENT=postgres
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=strapi_adapty_cms
DATABASE_USERNAME=strapi
DATABASE_PASSWORD=strapi
DATABASE_SSL=false
```

#### Optional Integrations
```env
# Adapty Integration (for paywall management)
ADAPTY_API_KEY=your_adapty_api_key_here
ADAPTY_WEBHOOK_SECRET=your_webhook_secret_here

# Redis Cache (for performance)
REDIS_URL=redis://localhost:6379

# Media Upload
UPLOAD_PROVIDER=local
UPLOAD_MAX_SIZE=52428800
```

## Step 2: Install Dependencies

```bash
yarn install
```

This will install all required dependencies including:
- Strapi core framework (v5.18.1)
- Database drivers (SQLite/PostgreSQL)
- TypeScript support
- Testing framework (Jest)

## Step 3: Database Setup

### 3.1 For SQLite (Default)
No additional setup required. The database file will be created automatically at `.tmp/data.db`.

### 3.2 For PostgreSQL (Optional)
If using PostgreSQL, ensure the database server is running and create the database:

```bash
# Install PostgreSQL (macOS with Homebrew)
brew install postgresql
brew services start postgresql

# Create database and user
createdb strapi_adapty_cms
createuser strapi -P  # Enter password: strapi
```

## Step 4: Start Development Server

### 4.1 First Time Setup
```bash
yarn develop
```

This command will:
- Build the admin panel
- Start the development server with auto-reload
- Create the admin user account (follow prompts)
- Initialize the database schema

### 4.2 Subsequent Starts
```bash
yarn develop
```

The server will be available at:
- **Admin Panel**: http://localhost:1337/admin
- **API Endpoints**: http://localhost:1337/api
- **Mobile API**: http://localhost:1337/api/mobile/v1

## Step 5: Load Sample Data (Optional)

To populate the system with example data:

```bash
yarn seed:example
```

This will create:
- Sample paywall configurations
- Test articles and authors
- Example A/B test setups
- Demo analytics data

## Step 6: Verify Installation

### 6.1 Check Admin Panel
1. Open http://localhost:1337/admin
2. Log in with your admin credentials
3. Verify content types are loaded:
   - Paywall
   - Articles
   - Authors
   - Categories

### 6.2 Test API Endpoints
```bash
# Test basic API
curl http://localhost:1337/api/articles

# Test mobile API
curl http://localhost:1337/api/mobile/v1/paywalls
```

## Development Commands

### Primary Development
```bash
yarn develop    # Start with auto-reload (recommended)
yarn build      # Build admin panel for production
yarn start      # Start production server
```

### Data Operations
```bash
yarn seed:example    # Load sample data
yarn console        # Access Strapi console for debugging
```

### Testing & Quality
```bash
yarn test           # Run test suite
yarn test:watch     # Run tests in watch mode
yarn test:coverage  # Generate coverage report
yarn upgrade:dry    # Check for available updates
```

## Project Structure Overview

```
├── .docs/                 # Documentation
├── config/                # Strapi configuration
├── src/
│   ├── api/              # Content types and APIs
│   │   ├── mobile/v1/    # Mobile-specific API endpoints
│   │   ├── paywall/      # Paywall management
│   │   └── article/      # Content management
│   ├── admin/            # Admin panel customizations
│   ├── services/         # Business logic services
│   └── middlewares/      # Custom middleware
├── data/                 # Upload storage (development)
├── database/             # Migrations and seeds
└── tests/                # Test files
```

## Troubleshooting

### Common Issues

#### Port Already in Use
```bash
# Kill process on port 1337
lsof -ti:1337 | xargs kill -9
```

#### Database Connection Issues
- For SQLite: Ensure `.tmp/` directory exists and is writable
- For PostgreSQL: Verify service is running with `brew services list`

#### Missing Dependencies
```bash
# Clear cache and reinstall
rm -rf node_modules yarn.lock
yarn install
```

#### Admin Panel Not Loading
```bash
# Rebuild admin panel
yarn build
yarn develop
```

### Development Tips

1. **Auto-reload**: The development server automatically restarts when you modify server files
2. **Admin Panel**: Changes to admin customizations require a browser refresh
3. **Database Changes**: Schema modifications may require restarting the server
4. **Environment Changes**: Restart the server after modifying `.env` file

## Next Steps

Once your development server is running:

1. **Explore Admin Panel**: Familiarize yourself with content types and configurations
2. **Review API Documentation**: Check `/docs` folder for API specifications
3. **Set up Integrations**: Configure Adapty and other external services
4. **Run Tests**: Ensure everything works with `yarn test`
5. **Start Development**: Begin implementing features according to project requirements

## Additional Resources

- [Strapi Documentation](https://docs.strapi.io/)
- [Project Architecture Guide](.docs/architecture.md)
- [API Documentation](.docs/api-reference.md)
- [Deployment Guide](.docs/deployment.md)