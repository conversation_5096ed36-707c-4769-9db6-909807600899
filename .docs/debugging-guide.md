# TypeScript Debugging Guide

This guide explains how to debug TypeScript files in the Strapi development environment with full source map support.

## Development Server with Source Maps

The project is configured to run a webpack dev server (via Vite for admin panel) with source maps enabled, allowing you to debug TypeScript files directly without compiled JavaScript.

### Quick Start

```bash
# Standard development server with Vite (recommended for debugging)
yarn develop

# Development server with webpack (alternative)
yarn dev:webpack

# Enhanced development server with debugging features
yarn dev:debug:legacy
```

### What's Configured

#### 1. Vite Configuration (`src/admin/vite.config.ts`)
- Source maps enabled for admin panel
- TypeScript debugging support
- Hot module replacement
- Path aliases for cleaner imports
- Strapi v5 supports both Vite and Webpack bundlers

#### 2. TypeScript Configuration
- **Admin Panel** (`src/admin/tsconfig.json`): ESNext target with source maps
- **Server** (`tsconfig.json`): CommonJS with source maps and path mapping

#### 3. Environment Configuration
- `.env.development`: Development-specific settings
- Source map generation enabled
- Debug logging enabled
- Caching disabled for real-time changes

## Debugging Methods

### 1. VS Code Debugging

Three launch configurations are available:

#### Debug Strapi Server
```json
"name": "Debug Strapi Server"
```
- Launches Strapi with debugging enabled
- Full TypeScript source map support
- Breakpoints work in `.ts` files

#### Debug with Enhanced Source Maps
```json
"name": "Debug Strapi with Source Maps"
```
- Uses the enhanced development script
- Additional debugging features enabled

#### Attach to Running Process
```json
"name": "Attach to Strapi Process"
```
- Attach debugger to already running Strapi process
- Useful for debugging long-running processes

### 2. Browser DevTools (Admin Panel)

The admin panel runs with Vite dev server:
- Source maps automatically loaded
- TypeScript files visible in Sources tab
- Breakpoints work in React components
- Hot reload preserves debugging session

### 3. Node.js Inspector

Start with inspector enabled:
```bash
node --inspect-brk=9229 node_modules/.bin/strapi develop
```

Then connect with Chrome DevTools or VS Code.

## File Structure for Debugging

```
src/
├── admin/                 # Admin panel (Vite + React)
│   ├── vite.config.ts    # Vite configuration with source maps
│   ├── tsconfig.json     # Admin TypeScript config
│   └── extensions/       # Custom admin components
├── api/                  # Strapi API endpoints
├── services/             # Business logic services
└── middlewares/          # Custom middleware

.vscode/
├── launch.json           # Debug configurations
└── settings.json         # TypeScript and debugging settings
```

## Debugging Best Practices

### 1. TypeScript Debugging
- Set breakpoints directly in `.ts` files
- Use path aliases for cleaner imports
- Leverage TypeScript strict mode for better error catching

### 2. Admin Panel Debugging
- Use React DevTools browser extension
- Debug custom components in `src/admin/extensions/`
- Source maps work for styled-components and other libraries

### 3. API Debugging
- Debug controllers, services, and middleware
- Use Strapi's built-in logging: `strapi.log.info()`
- Database queries can be debugged with `DATABASE_DEBUG=true`

### 4. Performance Debugging
- Source maps don't impact runtime performance
- Development server optimizes for debugging, not speed
- Use `yarn build` for production-optimized builds

## Troubleshooting

### Source Maps Not Working
1. Ensure `GENERATE_SOURCEMAP=true` in environment
2. Clear build artifacts: `rm -rf dist/ .cache/ build/`
3. Restart development server

### Breakpoints Not Hitting
1. Check file paths in debugger match source files
2. Verify TypeScript compilation is working
3. Ensure source map files are generated

### Admin Panel Debugging Issues
1. Check browser console for source map errors
2. Verify Vite dev server is running
3. Clear browser cache and restart

## Environment Variables

Key debugging-related environment variables:

```bash
# Enable source maps
GENERATE_SOURCEMAP=true
DEV_SERVER_SOURCEMAP=true

# Enable debug logging
LOG_LEVEL=debug
NODE_ENV=development

# TypeScript options
TS_NODE_OPTIONS="--transpile-only --files"

# Database debugging
DATABASE_DEBUG=true
```

## Scripts

- `yarn develop`: Standard development with source maps
- `yarn dev:debug`: Enhanced debugging features
- `./scripts/dev-with-sourcemaps.sh`: Direct script execution

## IDE Support

### VS Code
- Full TypeScript IntelliSense
- Integrated debugging
- Source map support
- Auto-import suggestions

### Other IDEs
- WebStorm: Built-in Node.js debugging
- Vim/Neovim: Use coc-tsserver or similar
- Emacs: Use lsp-mode with typescript-language-server

## Performance Notes

- Source maps increase initial build time but enable debugging
- Hot reload works with source maps enabled
- Production builds automatically disable source maps
- Development database uses SQLite for faster iteration