# Media Management System Documentation

## Overview

The Media Management System provides enhanced capabilities for handling paywall-related media assets in the Strapi Adapty CMS. It includes features for image optimization, CDN integration, folder organization, and validation specific to paywall content requirements.

## Features

### 1. Image Optimization

The system automatically creates optimized versions of uploaded images for different use cases:

- **Thumbnails**: Small, compressed versions for admin interface and previews
- **Mobile**: Optimized for mobile device display
- **Desktop**: Higher quality for desktop viewing
- **Field-specific optimizations**:
  - Background images: Hero-sized versions for paywall backgrounds
  - Logos: Vector-friendly formats with transparency
  - Icons: Multiple sizes for feature icons
  - Avatars: Cropped versions for testimonial authors

All optimized images are stored in an `optimized` subfolder and referenced in the file metadata.

### 2. CDN Integration

The system supports multiple CDN providers:

- **Local storage**: Default provider with no external dependencies
- **AWS S3**: Integration with Amazon S3 and CloudFront
- **Cloudinary**: Integration with Cloudinary's image optimization services

Configuration is done through environment variables:

```
UPLOAD_PROVIDER=aws-s3
AWS_ACCESS_KEY_ID=your_key
AWS_ACCESS_SECRET=your_secret
AWS_REGION=us-east-1
AWS_BUCKET=your-bucket
AWS_CDN_URL=https://your-cloudfront-domain.com
```

### 3. Folder Organization

Media assets are automatically organized into a structured folder hierarchy:

- **Paywalls/**
  - **Backgrounds/**: Background images for paywalls
  - **Logos/**: Brand logos
  - **Icons/**: Feature icons
  - **Testimonials/**: Author avatars and testimonial images
- **Themes/**: Theme-related media assets
- **General/**: General purpose media files

Each paywall's assets are further organized by placement ID for easy management.

### 4. Media Validation

The system includes comprehensive validation for uploaded files:

- **Size limits**: Field-specific size limits (e.g., 10MB for backgrounds, 2MB for logos)
- **Dimension requirements**: Minimum dimensions for each field type
- **Format validation**: Allowed file formats for each field type
- **Security checks**: Prevention of malicious file uploads

### 5. Admin Interface Extensions

The admin interface is enhanced with:

- **Folder navigation**: Easy browsing of the organized media structure
- **Field-specific uploads**: Automatic folder selection based on field type
- **Preview functionality**: View optimized versions of images
- **Bulk operations**: Manage multiple files efficiently

## Usage

### Uploading Media for Paywalls

When uploading media for paywall content, the system automatically:

1. Validates the file based on the field type
2. Places it in the appropriate folder
3. Creates optimized versions
4. Sets appropriate metadata (alt text, captions)

### Accessing Optimized Versions

Optimized versions can be accessed through the API:

```javascript
// Example: Get mobile-optimized version of an image
const file = await strapi.entityService.findOne('plugin::upload.file', fileId);
const mobileVersion = file.paywall_optimizations?.mobile;
const mobileUrl = mobileVersion ? mobileVersion.url : file.url;
```

### CDN URLs

When using a CDN, URLs are automatically transformed:

```javascript
// Example: Get CDN URL for an image
const cdnUrl = `${process.env.AWS_CDN_URL}${file.url}`;
```

## Configuration Options

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `UPLOAD_PROVIDER` | Storage provider (local, aws-s3, cloudinary) | local |
| `UPLOAD_MAX_SIZE` | Maximum file size in bytes | 52428800 (50MB) |
| `AWS_ACCESS_KEY_ID` | AWS access key for S3 | - |
| `AWS_ACCESS_SECRET` | AWS secret key for S3 | - |
| `AWS_REGION` | AWS region for S3 | us-east-1 |
| `AWS_BUCKET` | S3 bucket name | - |
| `AWS_CDN_URL` | CloudFront or other CDN URL | - |
| `CLOUDINARY_NAME` | Cloudinary cloud name | - |
| `CLOUDINARY_KEY` | Cloudinary API key | - |
| `CLOUDINARY_SECRET` | Cloudinary API secret | - |

### Plugin Configuration

Additional configuration options are available in `config/plugins.ts`:

```javascript
upload: {
  config: {
    breakpoints: {
      xlarge: 1920,
      large: 1000,
      medium: 750,
      small: 500,
      xsmall: 64,
    },
    // Image optimization settings
    responsive: true,
    quality: 85,
    progressive: true,
  }
}
```

## Best Practices

1. **Use appropriate file formats**:
   - JPG/WebP for photos and backgrounds
   - PNG/SVG for logos and icons with transparency
   - WebP for best compression/quality balance

2. **Optimize before upload**:
   - Resize large images to reasonable dimensions
   - Compress images when possible
   - Use vector formats (SVG) for logos and icons

3. **Use descriptive filenames**:
   - Include paywall name or placement ID
   - Describe the content (e.g., "premium-plan-background.jpg")
   - Use kebab-case for consistency

4. **Add proper metadata**:
   - Alt text for accessibility
   - Captions for context
   - Tags for searchability

## Troubleshooting

### Common Issues

1. **Upload fails with validation error**:
   - Check file size against limits
   - Verify dimensions meet minimum requirements
   - Ensure file format is allowed for the field

2. **Optimized versions not generated**:
   - Check Sharp library installation
   - Verify temporary directory permissions
   - Check server logs for errors

3. **CDN URLs not working**:
   - Verify CDN configuration in environment variables
   - Check CORS settings on CDN
   - Ensure CDN is properly configured for your domain

### Logs

Check the following logs for troubleshooting:

- Strapi server logs
- Upload plugin logs
- CDN provider logs (if applicable)

## API Reference

### Upload Service

```typescript
// Get optimized versions of a file
const optimizedVersions = await strapi.service('plugin::upload.upload').getOptimizedVersions(fileId);

// Validate a file for a specific field
const validation = await strapi.service('plugin::upload.upload').validatePaywallMedia(file, 'background_image');

// Get appropriate folder for a field
const folder = strapi.service('plugin::upload.upload').getFieldFolder('logo');
```

### Controllers

```typescript
// Custom upload with paywall-specific handling
POST /upload
// Body: { refId: 1, ref: 'api::paywall.paywall', field: 'background_image' }
```

## Future Enhancements

- Advanced image editing capabilities in admin interface
- AI-powered image tagging and categorization
- Automatic image quality assessment
- Video transcoding for video assets
- Responsive image sets with art direction