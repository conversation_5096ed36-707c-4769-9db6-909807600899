{
    "extends": "./tsconfig.json",
    "compilerOptions": {
        // Enable source maps for development debugging
        "sourceMap": true,
        "inlineSourceMap": false,
        "inlineSources": false
    },
    "include": [
        // Only include src files for source map generation
        "src/**/*.ts",
        "src/**/*.js",
        "src/**/*.json",
        "types/**/*.d.ts"
    ],
    "exclude": [
        "node_modules/",
        "build/",
        "dist/",
        ".cache/",
        ".tmp/",
        "config/",
        "src/admin/",
        "**/*.test.*",
        "src/plugins/**",
        "**/*.js.map"
    ]
}