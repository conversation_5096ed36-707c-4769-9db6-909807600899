#!/bin/bash

# Regenerate Strapi TypeScript types
# This script ensures that TypeScript types are up-to-date with schema changes

echo "🔄 Regenerating Strapi TypeScript types..."

# Clear existing generated types
echo "🧹 Cleaning existing types..."
rm -rf types/generated/
rm -rf .tmp/types/

# Clear build cache to force regeneration
rm -rf .cache/
rm -rf dist/
rm -rf build/

# Ensure .tmp directory exists
mkdir -p .tmp

echo "📦 Starting Strapi to regenerate types..."
echo "   This will start the server briefly to generate types, then exit"
echo ""

# Start Strapi briefly to generate types
# The --no-build flag prevents full build, just generates types
timeout 30s yarn strapi develop --no-build || true

echo ""
echo "✅ Type regeneration complete!"
echo "   Generated types should now be available in types/generated/"
echo "   You can now restart your development server"