#!/bin/bash

# Strapi Adapty CMS Development Setup Script
set -e

echo "🚀 Setting up Strapi Adapty CMS development environment..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Check if .env file exists
if [ ! -f .env ]; then
    echo "📝 Creating .env file from .env.example..."
    cp .env.example .env
    echo "⚠️  Please update the .env file with your configuration before continuing."
    echo "   Especially update the following variables:"
    echo "   - ADAPTY_API_KEY"
    echo "   - ADAPTY_WEBHOOK_SECRET"
    echo "   - APP_KEYS (generate new ones)"
    echo "   - JWT_SECRET (generate new one)"
    echo "   - ADMIN_JWT_SECRET (generate new one)"
    read -p "Press Enter to continue after updating .env file..."
fi

# Install dependencies
echo "📦 Installing dependencies..."
yarn install

# Start Docker services
echo "🐳 Starting Docker services..."
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d postgres redis

# Wait for PostgreSQL to be ready
echo "⏳ Waiting for PostgreSQL to be ready..."
until docker-compose exec postgres pg_isready -U strapi -d strapi_adapty_cms; do
    sleep 2
done

# Run database migrations
echo "🗄️  Running database setup..."
yarn strapi install

echo "✅ Development environment setup complete!"
echo ""
echo "🎯 Next steps:"
echo "   1. Start the development server: yarn develop"
echo "   2. Open http://localhost:1337/admin to access the admin panel"
echo "   3. Create your first admin user"
echo ""
echo "🐳 Docker services:"
echo "   - PostgreSQL: localhost:5432"
echo "   - Redis: localhost:6379"
echo "   - Adminer (DB admin): http://localhost:8080"
echo ""
echo "📚 Useful commands:"
echo "   - yarn develop: Start development server"
echo "   - yarn build: Build for production"
echo "   - yarn seed:example: Seed example data"
echo "   - docker-compose logs: View service logs"