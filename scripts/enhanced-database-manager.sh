#!/bin/bash

# Enhanced Database Manager for Strapi Adapty CMS
# Provides comprehensive database operations with error handling and validation

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${CYAN}$1${NC}"
}

log_divider() {
    echo "============================================================"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to load environment variables
load_environment() {
    if [ -f .env ]; then
        export $(grep -v '^#' .env | xargs)
        log_success "Environment variables loaded from .env"
    else
        log_error ".env file not found. Please run setup first."
        exit 1
    fi
}

# Function to detect database type
detect_database_type() {
    load_environment
    
    DB_CLIENT=${DATABASE_CLIENT:-sqlite}
    
    case $DB_CLIENT in
        sqlite)
            DB_TYPE="SQLite"
            DB_FILE=${DATABASE_FILENAME:-.tmp/data.db}
            ;;
        postgres)
            DB_TYPE="PostgreSQL"
            DB_HOST=${DATABASE_HOST:-localhost}
            DB_PORT=${DATABASE_PORT:-5432}
            DB_NAME=${DATABASE_NAME:-strapi_adapty_cms}
            DB_USER=${DATABASE_USERNAME:-strapi}
            ;;
        mysql)
            DB_TYPE="MySQL"
            DB_HOST=${DATABASE_HOST:-localhost}
            DB_PORT=${DATABASE_PORT:-3306}
            DB_NAME=${DATABASE_NAME:-strapi_adapty_cms}
            DB_USER=${DATABASE_USERNAME:-strapi}
            ;;
        *)
            log_error "Unsupported database client: $DB_CLIENT"
            exit 1
            ;;
    esac
    
    log_info "Database type: $DB_TYPE"
}

# Function to check database status
check_database_status() {
    log_info "Checking database status..."
    
    case $DB_CLIENT in
        sqlite)
            check_sqlite_status
            ;;
        postgres)
            check_postgres_status
            ;;
        mysql)
            check_mysql_status
            ;;
    esac
}

# Function to check SQLite status
check_sqlite_status() {
    local db_path="$DB_FILE"
    local db_dir=$(dirname "$db_path")
    
    # Ensure directory exists
    if [ ! -d "$db_dir" ]; then
        mkdir -p "$db_dir"
        log_success "Created database directory: $db_dir"
    fi
    
    if [ -f "$db_path" ]; then
        local size=$(du -h "$db_path" | cut -f1)
        log_success "SQLite database exists: $db_path ($size)"
        
        # Check if database is accessible
        if sqlite3 "$db_path" "SELECT 1;" >/dev/null 2>&1; then
            log_success "Database is accessible"
            
            # Count tables
            local table_count=$(sqlite3 "$db_path" "SELECT COUNT(*) FROM sqlite_master WHERE type='table';" 2>/dev/null || echo "0")
            log_info "Tables in database: $table_count"
        else
            log_warning "Database file exists but is not accessible"
        fi
    else
        log_warning "SQLite database does not exist: $db_path"
        log_info "Database will be created on first run"
    fi
}

# Function to check PostgreSQL status
check_postgres_status() {
    log_info "Checking PostgreSQL connection..."
    
    # Check if using Docker
    if command_exists docker-compose && docker-compose ps postgres >/dev/null 2>&1; then
        log_info "Using Docker PostgreSQL"
        
        if docker-compose exec -T postgres pg_isready -U "$DB_USER" -d "$DB_NAME" >/dev/null 2>&1; then
            log_success "PostgreSQL is running and accessible"
            
            # Get database info
            local db_size=$(docker-compose exec -T postgres psql -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT pg_size_pretty(pg_database_size('$DB_NAME'));" 2>/dev/null | xargs || echo "unknown")
            log_info "Database size: $db_size"
            
            local table_count=$(docker-compose exec -T postgres psql -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" 2>/dev/null | xargs || echo "0")
            log_info "Tables in database: $table_count"
        else
            log_error "PostgreSQL is not accessible"
            return 1
        fi
    else
        log_info "Using local PostgreSQL"
        
        # Check local PostgreSQL connection
        if command_exists psql; then
            if PGPASSWORD="$DATABASE_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" >/dev/null 2>&1; then
                log_success "Local PostgreSQL is accessible"
            else
                log_error "Cannot connect to local PostgreSQL"
                return 1
            fi
        else
            log_warning "psql command not found. Cannot verify local PostgreSQL connection"
        fi
    fi
}

# Function to check MySQL status
check_mysql_status() {
    log_info "Checking MySQL connection..."
    
    if command_exists mysql; then
        if mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DATABASE_PASSWORD" -e "USE $DB_NAME; SELECT 1;" >/dev/null 2>&1; then
            log_success "MySQL is accessible"
            
            local db_size=$(mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DATABASE_PASSWORD" -e "SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) AS 'DB Size in MB' FROM information_schema.tables WHERE table_schema='$DB_NAME';" -s -N 2>/dev/null || echo "unknown")
            log_info "Database size: ${db_size}MB"
        else
            log_error "Cannot connect to MySQL"
            return 1
        fi
    else
        log_warning "mysql command not found. Cannot verify MySQL connection"
    fi
}

# Function to reset database
reset_database() {
    log_warning "Resetting database..."
    
    # Confirm action
    read -p "Are you sure you want to reset the database? This will delete all data. (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Database reset cancelled"
        return 0
    fi
    
    case $DB_CLIENT in
        sqlite)
            reset_sqlite_database
            ;;
        postgres)
            reset_postgres_database
            ;;
        mysql)
            reset_mysql_database
            ;;
    esac
}

# Function to reset SQLite database
reset_sqlite_database() {
    local db_path="$DB_FILE"
    
    if [ -f "$db_path" ]; then
        rm -f "$db_path"
        log_success "SQLite database file removed: $db_path"
    else
        log_info "SQLite database file does not exist"
    fi
    
    # Ensure directory exists
    local db_dir=$(dirname "$db_path")
    mkdir -p "$db_dir"
    log_success "Database directory ready: $db_dir"
}

# Function to reset PostgreSQL database
reset_postgres_database() {
    if command_exists docker-compose && docker-compose ps postgres >/dev/null 2>&1; then
        log_info "Resetting Docker PostgreSQL..."
        
        # Stop and remove containers
        docker-compose down
        
        # Remove PostgreSQL volume
        local volume_name=$(docker volume ls -q | grep postgres | head -1)
        if [ -n "$volume_name" ]; then
            docker volume rm "$volume_name" 2>/dev/null || true
            log_success "PostgreSQL volume removed"
        fi
        
        # Start PostgreSQL again
        docker-compose up -d postgres
        
        # Wait for PostgreSQL to be ready
        log_info "Waiting for PostgreSQL to be ready..."
        local wait_attempts=0
        local max_wait=30
        
        while [ $wait_attempts -lt $max_wait ]; do
            if docker-compose exec -T postgres pg_isready -U "$DB_USER" -d postgres >/dev/null 2>&1; then
                log_success "PostgreSQL is ready"
                break
            else
                wait_attempts=$((wait_attempts + 1))
                sleep 1
            fi
        done
        
        if [ $wait_attempts -eq $max_wait ]; then
            log_error "PostgreSQL readiness check timed out"
            return 1
        fi
        
        # Create database
        docker-compose exec -T postgres createdb -U "$DB_USER" "$DB_NAME" 2>/dev/null || true
        log_success "PostgreSQL database reset complete"
    else
        log_info "Resetting local PostgreSQL..."
        
        if command_exists dropdb && command_exists createdb; then
            PGPASSWORD="$DATABASE_PASSWORD" dropdb -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" "$DB_NAME" 2>/dev/null || true
            PGPASSWORD="$DATABASE_PASSWORD" createdb -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" "$DB_NAME"
            log_success "Local PostgreSQL database reset complete"
        else
            log_error "dropdb/createdb commands not found. Please reset manually:"
            echo "dropdb -h $DB_HOST -p $DB_PORT -U $DB_USER $DB_NAME"
            echo "createdb -h $DB_HOST -p $DB_PORT -U $DB_USER $DB_NAME"
        fi
    fi
}

# Function to reset MySQL database
reset_mysql_database() {
    if command_exists mysql; then
        mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DATABASE_PASSWORD" -e "DROP DATABASE IF EXISTS $DB_NAME; CREATE DATABASE $DB_NAME;"
        log_success "MySQL database reset complete"
    else
        log_error "mysql command not found. Please reset manually:"
        echo "mysql -h $DB_HOST -P $DB_PORT -u $DB_USER -p"
        echo "DROP DATABASE IF EXISTS $DB_NAME;"
        echo "CREATE DATABASE $DB_NAME;"
    fi
}

# Function to backup database
backup_database() {
    local backup_dir="backups"
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local backup_file="$backup_dir/backup_$timestamp"
    
    log_info "Creating database backup..."
    
    # Create backup directory
    mkdir -p "$backup_dir"
    
    case $DB_CLIENT in
        sqlite)
            backup_sqlite_database "$backup_file"
            ;;
        postgres)
            backup_postgres_database "$backup_file"
            ;;
        mysql)
            backup_mysql_database "$backup_file"
            ;;
    esac
}

# Function to backup SQLite database
backup_sqlite_database() {
    local backup_file="$1.db"
    
    if [ -f "$DB_FILE" ]; then
        cp "$DB_FILE" "$backup_file"
        local size=$(du -h "$backup_file" | cut -f1)
        log_success "SQLite backup created: $backup_file ($size)"
    else
        log_warning "No SQLite database file to backup"
    fi
}

# Function to backup PostgreSQL database
backup_postgres_database() {
    local backup_file="$1.sql"
    
    if command_exists docker-compose && docker-compose ps postgres >/dev/null 2>&1; then
        docker-compose exec -T postgres pg_dump -U "$DB_USER" "$DB_NAME" > "$backup_file"
    else
        PGPASSWORD="$DATABASE_PASSWORD" pg_dump -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" "$DB_NAME" > "$backup_file"
    fi
    
    local size=$(du -h "$backup_file" | cut -f1)
    log_success "PostgreSQL backup created: $backup_file ($size)"
}

# Function to backup MySQL database
backup_mysql_database() {
    local backup_file="$1.sql"
    
    mysqldump -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DATABASE_PASSWORD" "$DB_NAME" > "$backup_file"
    
    local size=$(du -h "$backup_file" | cut -f1)
    log_success "MySQL backup created: $backup_file ($size)"
}

# Function to seed database
seed_database() {
    log_info "Seeding database with example data..."
    
    if [ -f "scripts/seed.js" ]; then
        if node scripts/seed.js; then
            log_success "Database seeded successfully"
        else
            log_error "Database seeding failed"
            return 1
        fi
    else
        log_warning "Seed script not found: scripts/seed.js"
    fi
}

# Function to show database info
show_database_info() {
    log_header "📊 Database Information"
    log_divider
    echo ""
    echo "Database Type: $DB_TYPE"
    echo "Client: $DB_CLIENT"
    
    case $DB_CLIENT in
        sqlite)
            echo "File: $DB_FILE"
            ;;
        postgres|mysql)
            echo "Host: $DB_HOST"
            echo "Port: $DB_PORT"
            echo "Database: $DB_NAME"
            echo "User: $DB_USER"
            ;;
    esac
    
    echo ""
    check_database_status
}

# Function to show help
show_help() {
    log_header "🗄️  Database Manager for Strapi Adapty CMS"
    echo ""
    echo "Usage: $0 <command>"
    echo ""
    echo "Commands:"
    echo "  status    Show database status and information"
    echo "  reset     Reset database (removes all data)"
    echo "  backup    Create database backup"
    echo "  seed      Seed database with example data"
    echo "  help      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 status"
    echo "  $0 backup"
    echo "  $0 reset"
    echo "  $0 seed"
    echo ""
}

# Main execution
main() {
    local command=${1:-help}
    
    log_header "🗄️  Enhanced Database Manager"
    log_divider
    echo ""
    
    # Detect database configuration
    detect_database_type
    echo ""
    
    case $command in
        status)
            show_database_info
            ;;
        reset)
            reset_database
            ;;
        backup)
            backup_database
            ;;
        seed)
            seed_database
            ;;
        help)
            show_help
            ;;
        *)
            log_error "Unknown command: $command"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# Execute main function
main "$@"