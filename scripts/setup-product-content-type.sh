#!/bin/bash

# Setup script to ensure product content type is properly registered
# This script handles the complete setup of the product content type

echo "🔧 Setting up product content type..."

# Clear all caches and build artifacts
echo "🧹 Cleaning caches and build artifacts..."
rm -rf .cache/
rm -rf .tmp/
rm -rf dist/
rm -rf build/
rm -rf types/generated/

# Ensure .tmp directory exists for SQLite
mkdir -p .tmp

# Check if product content type files exist
if [ ! -f "src/api/product/content-types/product/schema.json" ]; then
    echo "❌ Product schema file not found!"
    exit 1
fi

echo "✅ Product content type files found"

# Start Strapi briefly to register the new content type
echo "📦 Starting Strapi to register content types..."
echo "   This may take a moment as <PERSON><PERSON><PERSON> discovers the new content type..."
echo ""

# Use timeout to prevent hanging, but give enough time for registration
timeout 60s yarn strapi develop --no-build 2>/dev/null || {
    echo "⏰ Strapi startup timed out (this is expected)"
}

# Check if types were generated
if [ -f "types/generated/contentTypes.d.ts" ]; then
    echo "✅ TypeScript types generated successfully"
    
    # Check if product type is included
    if grep -q "product" types/generated/contentTypes.d.ts; then
        echo "✅ Product content type registered successfully"
    else
        echo "⚠️  Product content type may not be fully registered"
        echo "   Try running 'yarn develop' and check the admin panel"
    fi
else
    echo "⚠️  TypeScript types not generated"
    echo "   You may need to run 'yarn develop' manually"
fi

echo ""
echo "🚀 Setup complete! You can now run:"
echo "   yarn develop"
echo ""
echo "📝 Note: If you don't see the Product content type in the admin panel,"
echo "   restart the development server completely."