#!/bin/bash

# Development server with source maps enabled
# This script ensures proper TypeScript debugging support

echo "🚀 Starting Strapi development server with source maps..."

# Set development environment
export NODE_ENV=development
export GENERATE_SOURCEMAP=true
export DEV_SERVER_SOURCEMAP=true

# Enable TypeScript source map support
export TS_NODE_OPTIONS="--transpile-only --files"

# Clear any existing build artifacts and source maps
echo "🧹 Cleaning build artifacts..."
rm -rf dist/
rm -rf .cache/
rm -rf build/
rm -rf config/*.js.map
rm -rf config/*.d.ts

# Ensure .tmp directory exists for SQLite
mkdir -p .tmp

# Start Strapi in development mode with source maps
echo "📦 Starting Strapi development server..."
echo "   - Bundler: Vite (better source map support)"
echo "   - Source maps: ENABLED"
echo "   - TypeScript debugging: ENABLED"
echo "   - Hot reload: ENABLED"
echo "   - Admin panel: http://localhost:1337/admin"
echo ""

# Run Strapi develop command with Vite bundler for better source maps
yarn develop --bundler vite --watch-admin --debug