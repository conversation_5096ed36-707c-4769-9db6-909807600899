const fs = require("node:fs");
const path = require("node:path");

function findDuplicatePluralNames() {
	const apiDir = path.join(__dirname, "..", "src", "api");
	const pluralNames = new Map();
	const duplicates = [];

	if (!fs.existsSync(apiDir)) {
		console.log("API directory not found");
		return;
	}

	const apiDirs = fs.readdirSync(apiDir);

	for (const dir of apiDirs) {
		const schemaPath = path.join(
			apiDir,
			dir,
			"content-types",
			dir,
			"schema.json",
		);

		if (fs.existsSync(schemaPath)) {
			try {
				const schema = JSON.parse(fs.readFileSync(schemaPath, "utf8"));
				const pluralName = schema.info?.pluralName;

				if (pluralName) {
					console.log(
						`Found content type: ${dir} with pluralName: ${pluralName}`,
					);

					if (pluralNames.has(pluralName)) {
						duplicates.push({
							pluralName,
							directories: [pluralNames.get(pluralName), dir],
						});
					} else {
						pluralNames.set(pluralName, dir);
					}
				}
			} catch (error) {
				console.error(`Error reading schema for ${dir}:`, error.message);
			}
		}
	}

	if (duplicates.length > 0) {
		console.log("\n🚨 Found duplicate plural names:");
		duplicates.forEach((dup) => {
			console.log(
				`- "${dup.pluralName}" used in: ${dup.directories.join(", ")}`,
			);
		});
	} else {
		console.log("\n✅ No duplicate plural names found");
	}

	return duplicates;
}

findDuplicatePluralNames();
