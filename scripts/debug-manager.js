#!/usr/bin/env node

/**
 * Debug Manager - Intelligent debug mode with automatic fallback
 * Handles error detection and graceful degradation
 */

const { spawn } = require("node:child_process");
const fs = require("node:fs");
const _path = require("node:path");
const DebugFallbackManager = require("../config/debug-fallback");

class DebugManager {
	constructor() {
		this.fallbackManager = new DebugFallbackManager();
		this.maxRetries = 3;
		this.currentRetry = 0;
		this.processTimeout = 60000; // 60 seconds timeout for startup
	}

	/**
	 * Main debug session orchestrator
	 */
	async startDebugSession() {
		console.log("🚀 Strapi Debug Manager");
		console.log("=======================");

		// Pre-flight checks
		const preflightResult = await this.preflightChecks();
		if (!preflightResult.success) {
			console.error("❌ Pre-flight checks failed:", preflightResult.error);
			return false;
		}

		// Attempt debug mode with fallback
		return await this.attemptDebugWithFallback();
	}

	/**
	 * Pre-flight system checks
	 */
	async preflightChecks() {
		console.log("🔍 Running pre-flight checks...");

		try {
			// Check Node.js version
			const nodeVersion = process.version;
			const nodeMajor = parseInt(nodeVersion.slice(1).split(".")[0]);

			if (nodeMajor < 18) {
				return {
					success: false,
					error: `Node.js ${nodeVersion} is not supported. Please use Node.js 18.x or higher.`,
				};
			}
			console.log(`✅ Node.js ${nodeVersion} is supported`);

			// Check dependencies
			const depCheck = this.fallbackManager.checkDependencyCompatibility();
			if (!depCheck.compatible) {
				console.warn(`⚠️  Dependency warning: ${depCheck.reason}`);
				// Don't fail, but note the warning
			} else {
				console.log("✅ Dependencies are compatible");
			}

			// Check environment files
			if (!fs.existsSync(".env")) {
				if (fs.existsSync(".env.example")) {
					console.log("📋 Copying .env.example to .env");
					fs.copyFileSync(".env.example", ".env");
					console.warn("⚠️  Please configure your .env file");
				} else {
					return {
						success: false,
						error: "No .env or .env.example file found",
					};
				}
			}
			console.log("✅ Environment configuration found");

			// Check port availability
			const port = process.env.PORT || 1337;
			const portAvailable = await this.checkPortAvailability(port);
			if (!portAvailable) {
				console.warn(`⚠️  Port ${port} may be in use`);
			} else {
				console.log(`✅ Port ${port} is available`);
			}

			return { success: true };
		} catch (error) {
			return {
				success: false,
				error: `Pre-flight check failed: ${error.message}`,
			};
		}
	}

	/**
	 * Check if port is available
	 */
	async checkPortAvailability(port) {
		return new Promise((resolve) => {
			const net = require("node:net");
			const server = net.createServer();

			server.listen(port, () => {
				server.once("close", () => resolve(true));
				server.close();
			});

			server.on("error", () => resolve(false));
		});
	}

	/**
	 * Attempt debug mode with automatic fallback
	 */
	async attemptDebugWithFallback() {
		while (this.currentRetry < this.maxRetries) {
			console.log(
				`\n🧪 Attempt ${this.currentRetry + 1}/${this.maxRetries} - ${this.fallbackManager.currentLevel} mode`,
			);

			// Apply current configuration
			const config = this.fallbackManager.getConfiguration();
			const applied = this.fallbackManager.applyConfiguration(config);

			if (!applied) {
				console.error("❌ Failed to apply configuration");
				if (
					!this.fallbackManager.fallback("Configuration application failed")
				) {
					break;
				}
				this.currentRetry++;
				continue;
			}

			// Attempt to start debug mode
			const result = await this.startDebugProcess();

			if (result.success) {
				console.log("🎉 Debug mode started successfully!");
				this.fallbackManager.saveState();
				return true;
			}

			// Analyze failure and determine if fallback is appropriate
			const shouldFallback = this.analyzeFailure(result);

			if (shouldFallback) {
				if (!this.fallbackManager.fallback(result.reason)) {
					break;
				}
			} else {
				console.error("❌ Critical failure, cannot continue:", result.reason);
				break;
			}

			this.currentRetry++;
		}

		console.error("❌ All debug attempts failed");
		this.showTroubleshootingGuide();
		this.fallbackManager.saveState();
		return false;
	}

	/**
	 * Start the debug process
	 */
	async startDebugProcess() {
		return new Promise((resolve) => {
			console.log("🚀 Starting Strapi debug process...");

			const child = spawn("yarn", ["develop", "--debug"], {
				stdio: ["pipe", "pipe", "pipe"],
				env: { ...process.env },
			});

			let stdout = "";
			let stderr = "";
			let startupComplete = false;
			let hasErrors = false;
			const errorDetails = [];

			// Set up timeout
			const timeout = setTimeout(() => {
				child.kill("SIGTERM");
				resolve({
					success: false,
					reason: "Startup timeout exceeded",
					stdout,
					stderr,
					errorDetails,
				});
			}, this.processTimeout);

			child.stdout.on("data", (data) => {
				const output = data.toString();
				stdout += output;

				// Check for successful startup indicators
				if (
					output.includes("Server listening") ||
					output.includes("Admin panel available") ||
					output.includes("Welcome back!")
				) {
					startupComplete = true;
					clearTimeout(timeout);

					// Give it a moment to stabilize
					setTimeout(() => {
						child.kill("SIGTERM");
						resolve({
							success: true,
							reason: "Startup completed successfully",
							stdout,
							stderr,
						});
					}, 2000);
				}

				// Log important progress
				if (output.includes("Building build context")) {
					console.log("📦 Building context...");
				}
				if (output.includes("Creating admin")) {
					console.log("🎨 Creating admin panel...");
				}
			});

			child.stderr.on("data", (data) => {
				const output = data.toString();
				stderr += output;

				// Check for critical errors
				if (output.includes("callback is not a function")) {
					hasErrors = true;
					errorDetails.push("ESBuild callback error detected");
				}

				if (output.includes("EPIPE") || output.includes("ECONNRESET")) {
					hasErrors = true;
					errorDetails.push("Connection error detected");
				}

				if (output.includes("Error:") && !output.includes("[WARN]")) {
					hasErrors = true;
					errorDetails.push(`Error: ${output.trim()}`);
				}

				// Log warnings but don't treat as failures
				if (output.includes("[WARN]")) {
					console.log(`⚠️  ${output.trim()}`);
				}
			});

			child.on("close", (code) => {
				clearTimeout(timeout);

				if (startupComplete) {
					// Already resolved as success
					return;
				}

				resolve({
					success: false,
					reason: hasErrors
						? `Process failed with errors: ${errorDetails.join(", ")}`
						: `Process exited with code ${code}`,
					code,
					stdout,
					stderr,
					errorDetails,
				});
			});

			child.on("error", (error) => {
				clearTimeout(timeout);
				resolve({
					success: false,
					reason: `Process error: ${error.message}`,
					error: error.message,
					stdout,
					stderr,
				});
			});
		});
	}

	/**
	 * Analyze failure to determine if fallback is appropriate
	 */
	analyzeFailure(result) {
		const { reason, errorDetails = [], stderr = "" } = result;

		// Callback errors should be resolved by dependency updates
		if (reason.includes("callback is not a function")) {
			console.log("🔧 ESBuild callback error - dependency update needed");
			return false; // Don't fallback, this should be fixed
		}

		// Connection errors might be resolved by fallback
		if (reason.includes("EPIPE") || reason.includes("ECONNRESET")) {
			console.log("🔧 Connection error - trying fallback configuration");
			return true;
		}

		// Memory or performance issues
		if (stderr.includes("out of memory") || stderr.includes("heap")) {
			console.log("🔧 Memory issue - trying reduced configuration");
			return true;
		}

		// Build configuration issues
		if (reason.includes("webpack") || reason.includes("build")) {
			console.log("🔧 Build configuration issue - trying simpler setup");
			return true;
		}

		// Timeout issues
		if (reason.includes("timeout")) {
			console.log("🔧 Startup timeout - trying faster configuration");
			return true;
		}

		// Default to trying fallback for unknown issues
		console.log("🔧 Unknown issue - attempting fallback");
		return true;
	}

	/**
	 * Show troubleshooting guide
	 */
	showTroubleshootingGuide() {
		console.log("\n🛠️  Troubleshooting Guide");
		console.log("========================");
		console.log("");
		console.log("Common solutions:");
		console.log("1. Update dependencies: yarn install");
		console.log("2. Clear cache: rm -rf .cache dist node_modules/.cache");
		console.log("3. Check .env configuration");
		console.log("4. Verify port availability");
		console.log("5. Try without debug: yarn develop");
		console.log("");
		console.log("For more help:");
		console.log("- Check logs in .cache/debug-state.json");
		console.log("- Review the debug diagnosis report");
		console.log("- Try running with different Node.js version");
		console.log("");

		// Show current diagnostics
		const diagnostics = this.fallbackManager.getDiagnostics();
		console.log("Current system info:");
		console.log(`- Node.js: ${diagnostics.nodeVersion}`);
		console.log(`- Platform: ${diagnostics.platform} ${diagnostics.arch}`);
		console.log(
			`- Memory: ${Math.round(diagnostics.memoryUsage.heapUsed / 1024 / 1024)}MB used`,
		);
		console.log(`- Debug level attempted: ${diagnostics.currentLevel}`);
	}
}

// Main execution
async function main() {
	const debugManager = new DebugManager();
	const success = await debugManager.startDebugSession();
	process.exit(success ? 0 : 1);
}

// Run if called directly
if (require.main === module) {
	main().catch(console.error);
}

module.exports = DebugManager;
