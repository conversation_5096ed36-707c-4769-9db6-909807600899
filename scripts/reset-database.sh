#!/bin/bash

echo "🗄️ Resetting database..."

# Check if using Docker
if docker-compose ps postgres &>/dev/null; then
    echo "Found Docker PostgreSQL, resetting..."
    docker-compose down
    docker volume rm $(docker volume ls -q | grep postgres) 2>/dev/null || true
    docker-compose up -d postgres
    
    # Wait for PostgreSQL to be ready
    echo "Waiting for PostgreSQL to be ready..."
    until docker-compose exec postgres pg_isready -U strapi -d strapi_adapty_cms; do
        sleep 2
    done
    
    echo "✅ Database reset complete"
else
    echo "No Docker PostgreSQL found. If using local PostgreSQL, please run:"
    echo "dropdb strapi_adapty_cms && createdb strapi_adapty_cms"
fi