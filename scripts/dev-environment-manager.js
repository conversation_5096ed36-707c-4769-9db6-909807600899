#!/usr/bin/env node

/**
 * Development Environment Manager
 * Comprehensive tooling for development workflow management
 */

const fs = require("node:fs");
const path = require("node:path");
const { spawn, exec } = require("node:child_process");
const { promisify } = require("node:util");

const execAsync = promisify(exec);

// Color codes for output
const colors = {
	reset: "\x1b[0m",
	red: "\x1b[0;31m",
	green: "\x1b[0;32m",
	yellow: "\x1b[1;33m",
	blue: "\x1b[0;34m",
	magenta: "\x1b[0;35m",
	cyan: "\x1b[0;36m",
	white: "\x1b[1;37m",
};

// Logging functions
const log = {
	info: (msg) => console.log(`${colors.blue}[INFO]${colors.reset} ${msg}`),
	success: (msg) =>
		console.log(`${colors.green}[SUCCESS]${colors.reset} ${msg}`),
	warning: (msg) =>
		console.log(`${colors.yellow}[WARNING]${colors.reset} ${msg}`),
	error: (msg) => console.log(`${colors.red}[ERROR]${colors.reset} ${msg}`),
	debug: (msg) => console.log(`${colors.magenta}[DEBUG]${colors.reset} ${msg}`),
	header: (msg) => console.log(`${colors.cyan}${msg}${colors.reset}`),
	divider: () => console.log("=".repeat(60)),
};

class DevEnvironmentManager {
	constructor() {
		this.projectRoot = process.cwd();
		this.packageJson = this.loadPackageJson();
		this.envFile = path.join(this.projectRoot, ".env");
		this.commands = {
			setup: this.setupEnvironment.bind(this),
			check: this.checkEnvironment.bind(this),
			clean: this.cleanEnvironment.bind(this),
			reset: this.resetEnvironment.bind(this),
			doctor: this.runDiagnostics.bind(this),
			deps: this.manageDependencies.bind(this),
			db: this.manageDatabase.bind(this),
			help: this.showHelp.bind(this),
		};
	}

	/**
	 * Load package.json with error handling
	 */
	loadPackageJson() {
		try {
			return JSON.parse(
				fs.readFileSync(path.join(this.projectRoot, "package.json"), "utf8"),
			);
		} catch (_error) {
			log.error("Failed to load package.json");
			process.exit(1);
		}
	}

	/**
	 * Main entry point
	 */
	async run() {
		const command = process.argv[2] || "help";
		const subCommand = process.argv[3];

		log.header("🛠️  Strapi Development Environment Manager");
		log.divider();

		if (this.commands[command]) {
			try {
				await this.commands[command](subCommand);
			} catch (error) {
				log.error(`Command failed: ${error.message}`);
				process.exit(1);
			}
		} else {
			log.error(`Unknown command: ${command}`);
			this.showHelp();
			process.exit(1);
		}
	}

	/**
	 * Setup development environment
	 */
	async setupEnvironment(mode = "full") {
		log.info("Setting up development environment...");

		// Check prerequisites
		await this.checkPrerequisites();

		// Setup environment file
		await this.setupEnvironmentFile();

		// Install dependencies
		if (mode === "full" || mode === "deps") {
			await this.installDependencies();
		}

		// Setup database
		if (mode === "full" || mode === "db") {
			await this.setupDatabase();
		}

		// Validate setup
		await this.validateSetup();

		log.success("Development environment setup complete!");
		this.showNextSteps();
	}

	/**
	 * Check environment health
	 */
	async checkEnvironment() {
		log.info("Checking development environment...");

		const checks = [
			this.checkNodeVersion(),
			this.checkYarnVersion(),
			this.checkDependencies(),
			this.checkEnvironmentFile(),
			this.checkDatabaseConnection(),
			this.checkPortAvailability(),
			this.checkDiskSpace(),
			this.checkMemory(),
		];

		const results = await Promise.allSettled(checks);

		let passed = 0;
		let failed = 0;

		results.forEach((result, _index) => {
			if (result.status === "fulfilled" && result.value.success) {
				log.success(result.value.message);
				passed++;
			} else {
				const error =
					result.status === "rejected" ? result.reason : result.value.error;
				log.error(error);
				failed++;
			}
		});

		log.divider();
		log.info(`Environment check complete: ${passed} passed, ${failed} failed`);

		if (failed > 0) {
			log.warning(
				'Some checks failed. Run "yarn dev:env doctor" for detailed diagnostics.',
			);
		}

		return failed === 0;
	}

	/**
	 * Clean development environment
	 */
	async cleanEnvironment(target = "all") {
		log.info(`Cleaning development environment (${target})...`);

		const cleanTasks = {
			cache: () => this.cleanCache(),
			deps: () => this.cleanDependencies(),
			build: () => this.cleanBuildArtifacts(),
			logs: () => this.cleanLogs(),
			all: async () => {
				await this.cleanCache();
				await this.cleanBuildArtifacts();
				await this.cleanLogs();
			},
		};

		if (cleanTasks[target]) {
			await cleanTasks[target]();
			log.success(`Cleaning ${target} complete!`);
		} else {
			log.error(`Unknown clean target: ${target}`);
			log.info("Available targets: cache, deps, build, logs, all");
		}
	}

	/**
	 * Reset development environment
	 */
	async resetEnvironment(component = "all") {
		log.warning(`Resetting ${component}...`);

		const resetTasks = {
			db: () => this.resetDatabase(),
			cache: () => this.cleanCache(),
			deps: async () => {
				await this.cleanDependencies();
				await this.installDependencies();
			},
			all: async () => {
				await this.resetDatabase();
				await this.cleanCache();
				await this.cleanDependencies();
				await this.installDependencies();
			},
		};

		if (resetTasks[component]) {
			const confirmed = await this.confirmAction(
				`Reset ${component}? This action cannot be undone.`,
			);
			if (confirmed) {
				await resetTasks[component]();
				log.success(`Reset ${component} complete!`);
			} else {
				log.info("Reset cancelled.");
			}
		} else {
			log.error(`Unknown reset component: ${component}`);
			log.info("Available components: db, cache, deps, all");
		}
	}

	/**
	 * Run comprehensive diagnostics
	 */
	async runDiagnostics() {
		log.info("Running comprehensive diagnostics...");

		const diagnostics = {
			system: await this.getSystemInfo(),
			node: await this.getNodeInfo(),
			dependencies: await this.getDependencyInfo(),
			environment: await this.getEnvironmentInfo(),
			database: await this.getDatabaseInfo(),
			performance: await this.getPerformanceInfo(),
		};

		// Save diagnostics to file
		const diagnosticsFile = path.join(
			this.projectRoot,
			".cache",
			"diagnostics.json",
		);
		fs.mkdirSync(path.dirname(diagnosticsFile), { recursive: true });
		fs.writeFileSync(diagnosticsFile, JSON.stringify(diagnostics, null, 2));

		// Display summary
		this.displayDiagnosticsSummary(diagnostics);

		log.success(`Diagnostics saved to ${diagnosticsFile}`);
	}

	/**
	 * Manage dependencies
	 */
	async manageDependencies(action = "check") {
		log.info(`Managing dependencies (${action})...`);

		const actions = {
			check: () => this.checkDependencies(),
			update: () => this.updateDependencies(),
			audit: () => this.auditDependencies(),
			outdated: () => this.checkOutdatedDependencies(),
		};

		if (actions[action]) {
			await actions[action]();
		} else {
			log.error(`Unknown dependency action: ${action}`);
			log.info("Available actions: check, update, audit, outdated");
		}
	}

	/**
	 * Manage database
	 */
	async manageDatabase(action = "status") {
		log.info(`Managing database (${action})...`);

		const actions = {
			status: () => this.checkDatabaseConnection(),
			setup: () => this.setupDatabase(),
			reset: () => this.resetDatabase(),
			seed: () => this.seedDatabase(),
			backup: () => this.backupDatabase(),
			restore: () => this.restoreDatabase(),
		};

		if (actions[action]) {
			await actions[action]();
		} else {
			log.error(`Unknown database action: ${action}`);
			log.info(
				"Available actions: status, setup, reset, seed, backup, restore",
			);
		}
	}

	/**
	 * Check prerequisites
	 */
	async checkPrerequisites() {
		log.info("Checking prerequisites...");

		// Check Node.js version
		const nodeCheck = await this.checkNodeVersion();
		if (!nodeCheck.success) {
			throw new Error(nodeCheck.error);
		}

		// Check Yarn
		const yarnCheck = await this.checkYarnVersion();
		if (!yarnCheck.success) {
			throw new Error(yarnCheck.error);
		}

		log.success("Prerequisites check passed");
	}

	/**
	 * Check Node.js version
	 */
	async checkNodeVersion() {
		try {
			const nodeVersion = process.version;
			const nodeMajor = parseInt(nodeVersion.slice(1).split(".")[0]);

			const engines = this.packageJson.engines;
			const _requiredNode = engines?.node;

			if (nodeMajor < 18) {
				return {
					success: false,
					error: `Node.js ${nodeVersion} is not supported. Please use Node.js 18.x or higher.`,
				};
			}

			if (nodeMajor === 20) {
				return {
					success: true,
					message: `Node.js ${nodeVersion} detected (enhanced compatibility mode)`,
				};
			}

			return {
				success: true,
				message: `Node.js ${nodeVersion} is supported`,
			};
		} catch (error) {
			return {
				success: false,
				error: `Failed to check Node.js version: ${error.message}`,
			};
		}
	}

	/**
	 * Check Yarn version
	 */
	async checkYarnVersion() {
		try {
			const { stdout } = await execAsync("yarn --version");
			const yarnVersion = stdout.trim();

			return {
				success: true,
				message: `Yarn ${yarnVersion} is available`,
			};
		} catch (_error) {
			return {
				success: false,
				error: "Yarn is not installed. Please install Yarn package manager.",
			};
		}
	}

	/**
	 * Check dependencies
	 */
	async checkDependencies() {
		try {
			if (!fs.existsSync(path.join(this.projectRoot, "node_modules"))) {
				return {
					success: false,
					error: 'Dependencies not installed. Run "yarn install"',
				};
			}

			// Check critical dependencies
			const criticalDeps = ["@strapi/strapi", "esbuild-loader", "typescript"];
			for (const dep of criticalDeps) {
				const depPath = path.join(this.projectRoot, "node_modules", dep);
				if (!fs.existsSync(depPath)) {
					return {
						success: false,
						error: `Critical dependency ${dep} is missing`,
					};
				}
			}

			return {
				success: true,
				message: "All dependencies are installed",
			};
		} catch (error) {
			return {
				success: false,
				error: `Failed to check dependencies: ${error.message}`,
			};
		}
	}

	/**
	 * Check environment file
	 */
	async checkEnvironmentFile() {
		try {
			if (!fs.existsSync(this.envFile)) {
				return {
					success: false,
					error: ".env file not found. Run setup to create it.",
				};
			}

			const envContent = fs.readFileSync(this.envFile, "utf8");
			const requiredVars = ["HOST", "PORT", "APP_KEYS", "DATABASE_CLIENT"];

			for (const varName of requiredVars) {
				if (!envContent.includes(`${varName}=`)) {
					return {
						success: false,
						error: `Required environment variable ${varName} is missing`,
					};
				}
			}

			return {
				success: true,
				message: "Environment file is properly configured",
			};
		} catch (error) {
			return {
				success: false,
				error: `Failed to check environment file: ${error.message}`,
			};
		}
	}

	/**
	 * Check database connection
	 */
	async checkDatabaseConnection() {
		try {
			// This is a simplified check - in a real implementation,
			// you would actually test the database connection
			const envContent = fs.readFileSync(this.envFile, "utf8");

			if (envContent.includes("DATABASE_CLIENT=sqlite")) {
				const dbPath = path.join(this.projectRoot, ".tmp", "data.db");
				const dbDir = path.dirname(dbPath);

				if (!fs.existsSync(dbDir)) {
					fs.mkdirSync(dbDir, { recursive: true });
				}

				return {
					success: true,
					message: "SQLite database configuration is valid",
				};
			}

			return {
				success: true,
				message: "Database configuration appears valid",
			};
		} catch (error) {
			return {
				success: false,
				error: `Failed to check database: ${error.message}`,
			};
		}
	}

	/**
	 * Check port availability
	 */
	async checkPortAvailability() {
		try {
			const port = process.env.PORT || 1337;
			const net = require("node:net");

			return new Promise((resolve) => {
				const server = net.createServer();

				server.listen(port, () => {
					server.once("close", () => {
						resolve({
							success: true,
							message: `Port ${port} is available`,
						});
					});
					server.close();
				});

				server.on("error", () => {
					resolve({
						success: false,
						error: `Port ${port} is already in use`,
					});
				});
			});
		} catch (error) {
			return {
				success: false,
				error: `Failed to check port availability: ${error.message}`,
			};
		}
	}

	/**
	 * Check disk space
	 */
	async checkDiskSpace() {
		try {
			const { stdout } = await execAsync("df -h .");
			const lines = stdout.trim().split("\n");
			const diskInfo = lines[1].split(/\s+/);
			const available = diskInfo[3];

			return {
				success: true,
				message: `Disk space available: ${available}`,
			};
		} catch (_error) {
			return {
				success: true,
				message: "Disk space check skipped (platform not supported)",
			};
		}
	}

	/**
	 * Check memory
	 */
	async checkMemory() {
		try {
			const totalMem = Math.round(
				require("node:os").totalmem() / 1024 / 1024 / 1024,
			);
			const freeMem = Math.round(
				require("node:os").freemem() / 1024 / 1024 / 1024,
			);

			if (freeMem < 1) {
				return {
					success: false,
					error: `Low memory: ${freeMem}GB free of ${totalMem}GB total`,
				};
			}

			return {
				success: true,
				message: `Memory: ${freeMem}GB free of ${totalMem}GB total`,
			};
		} catch (error) {
			return {
				success: false,
				error: `Failed to check memory: ${error.message}`,
			};
		}
	}

	/**
	 * Setup environment file
	 */
	async setupEnvironmentFile() {
		if (!fs.existsSync(this.envFile)) {
			const exampleFile = path.join(this.projectRoot, ".env.example");
			if (fs.existsSync(exampleFile)) {
				fs.copyFileSync(exampleFile, this.envFile);
				log.success("Created .env file from .env.example");
				log.warning("Please update .env file with your configuration");
			} else {
				throw new Error(".env.example file not found");
			}
		} else {
			log.info(".env file already exists");
		}
	}

	/**
	 * Install dependencies
	 */
	async installDependencies() {
		log.info("Installing dependencies...");

		return new Promise((resolve, reject) => {
			const child = spawn("yarn", ["install"], {
				stdio: "inherit",
				cwd: this.projectRoot,
			});

			child.on("close", (code) => {
				if (code === 0) {
					log.success("Dependencies installed successfully");
					resolve();
				} else {
					reject(new Error(`Dependency installation failed with code ${code}`));
				}
			});

			child.on("error", reject);
		});
	}

	/**
	 * Setup database
	 */
	async setupDatabase() {
		log.info("Setting up database...");

		// Ensure .tmp directory exists for SQLite
		const tmpDir = path.join(this.projectRoot, ".tmp");
		if (!fs.existsSync(tmpDir)) {
			fs.mkdirSync(tmpDir, { recursive: true });
			log.success("Created .tmp directory");
		}

		log.success("Database setup complete");
	}

	/**
	 * Validate setup
	 */
	async validateSetup() {
		log.info("Validating setup...");

		const isValid = await this.checkEnvironment();
		if (!isValid) {
			throw new Error("Setup validation failed");
		}

		log.success("Setup validation passed");
	}

	/**
	 * Show next steps
	 */
	showNextSteps() {
		log.divider();
		log.header("🎯 Next Steps:");
		console.log("");
		console.log("1. Start development server:");
		console.log("   yarn develop");
		console.log("");
		console.log("2. Start debug mode:");
		console.log("   yarn dev:debug");
		console.log("");
		console.log("3. Access admin panel:");
		console.log("   http://localhost:1337/admin");
		console.log("");
		console.log("4. Useful commands:");
		console.log("   yarn dev:env check    - Check environment health");
		console.log("   yarn dev:env doctor   - Run diagnostics");
		console.log("   yarn dev:env clean    - Clean cache and build files");
		console.log("");
	}

	/**
	 * Show help
	 */
	showHelp() {
		log.header("🛠️  Development Environment Manager");
		console.log("");
		console.log("Usage: yarn dev:env <command> [options]");
		console.log("");
		console.log("Commands:");
		console.log("  setup [mode]     Setup development environment");
		console.log("                   Modes: full, deps, db");
		console.log("  check            Check environment health");
		console.log("  clean [target]   Clean development files");
		console.log("                   Targets: cache, deps, build, logs, all");
		console.log("  reset [component] Reset environment components");
		console.log("                   Components: db, cache, deps, all");
		console.log("  doctor           Run comprehensive diagnostics");
		console.log("  deps [action]    Manage dependencies");
		console.log("                   Actions: check, update, audit, outdated");
		console.log("  db [action]      Manage database");
		console.log("                   Actions: status, setup, reset, seed");
		console.log("  help             Show this help message");
		console.log("");
		console.log("Examples:");
		console.log("  yarn dev:env setup");
		console.log("  yarn dev:env check");
		console.log("  yarn dev:env clean cache");
		console.log("  yarn dev:env doctor");
		console.log("");
	}

	/**
	 * Helper methods for cleaning, diagnostics, etc.
	 */
	async cleanCache() {
		const cacheDirs = [".cache", "dist", "build", ".tmp/build"];
		for (const dir of cacheDirs) {
			const dirPath = path.join(this.projectRoot, dir);
			if (fs.existsSync(dirPath)) {
				await execAsync(`rm -rf "${dirPath}"`);
				log.success(`Cleaned ${dir}`);
			}
		}
	}

	async cleanBuildArtifacts() {
		const buildFiles = ["dist", "build", ".strapi"];
		for (const file of buildFiles) {
			const filePath = path.join(this.projectRoot, file);
			if (fs.existsSync(filePath)) {
				await execAsync(`rm -rf "${filePath}"`);
				log.success(`Cleaned ${file}`);
			}
		}
	}

	async cleanLogs() {
		const logFiles = ["*.log", "logs"];
		for (const pattern of logFiles) {
			try {
				await execAsync(`find . -name "${pattern}" -type f -delete`);
			} catch (_error) {
				// Ignore errors for missing files
			}
		}
		log.success("Cleaned log files");
	}

	async confirmAction(message) {
		const readline = require("node:readline");
		const rl = readline.createInterface({
			input: process.stdin,
			output: process.stdout,
		});

		return new Promise((resolve) => {
			rl.question(`${message} (y/N): `, (answer) => {
				rl.close();
				resolve(answer.toLowerCase() === "y" || answer.toLowerCase() === "yes");
			});
		});
	}

	// Additional helper methods would be implemented here...
	async getSystemInfo() {
		const os = require("node:os");
		return {
			platform: os.platform(),
			arch: os.arch(),
			release: os.release(),
			cpus: os.cpus().length,
			memory: `${Math.round(os.totalmem() / 1024 / 1024 / 1024)}GB`,
		};
	}

	async getNodeInfo() {
		return {
			version: process.version,
			platform: process.platform,
			arch: process.arch,
		};
	}

	async getDependencyInfo() {
		// Implementation for dependency analysis
		return {};
	}

	async getEnvironmentInfo() {
		// Implementation for environment analysis
		return {};
	}

	async getDatabaseInfo() {
		// Implementation for database analysis
		return {};
	}

	async getPerformanceInfo() {
		// Implementation for performance analysis
		return {};
	}

	displayDiagnosticsSummary(diagnostics) {
		log.header("📊 Diagnostics Summary");
		console.log("");
		console.log(
			`System: ${diagnostics.system.platform} ${diagnostics.system.arch}`,
		);
		console.log(`Node.js: ${diagnostics.node.version}`);
		console.log(`Memory: ${diagnostics.system.memory}`);
		console.log(`CPUs: ${diagnostics.system.cpus}`);
		console.log("");
	}
}

// Main execution
if (require.main === module) {
	const manager = new DevEnvironmentManager();
	manager.run().catch((error) => {
		console.error("❌ Fatal error:", error.message);
		process.exit(1);
	});
}

module.exports = DevEnvironmentManager;
