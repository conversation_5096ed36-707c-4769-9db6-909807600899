#!/bin/bash

# Enhanced Strapi Adapty CMS Development Setup Script
# Includes comprehensive error handling, Node.js version checks, and diagnostics

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${CYAN}$1${NC}"
}

log_divider() {
    echo "============================================================"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check Node.js version compatibility
check_node_version() {
    log_info "Checking Node.js version compatibility..."
    
    if ! command_exists node; then
        log_error "Node.js is not installed. Please install Node.js 18.x or higher."
        echo "Visit: https://nodejs.org/"
        exit 1
    fi
    
    NODE_VERSION=$(node --version)
    NODE_MAJOR=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
    
    log_info "Node.js version: $NODE_VERSION"
    
    if [ "$NODE_MAJOR" -lt 18 ]; then
        log_error "Node.js version $NODE_VERSION is not supported."
        log_error "Please install Node.js 18.x or higher."
        echo "Current version: $NODE_VERSION"
        echo "Required: 18.x or higher"
        exit 1
    elif [ "$NODE_MAJOR" -eq 18 ]; then
        log_success "Node.js v18.x detected - stable support"
    elif [ "$NODE_MAJOR" -eq 20 ]; then
        log_success "Node.js v20.x detected - enhanced compatibility mode"
        export NODE_OPTIONS="--max-old-space-size=4096"
    elif [ "$NODE_MAJOR" -ge 22 ]; then
        log_warning "Node.js v$NODE_MAJOR detected - experimental support"
        export NODE_OPTIONS="--max-old-space-size=4096"
    else
        log_success "Node.js v$NODE_MAJOR detected - supported"
    fi
}

# Function to check Yarn version
check_yarn_version() {
    log_info "Checking Yarn package manager..."
    
    if ! command_exists yarn; then
        log_error "Yarn is not installed. Please install Yarn first."
        echo "Install with: npm install -g yarn"
        echo "Or visit: https://yarnpkg.com/getting-started/install"
        exit 1
    fi
    
    YARN_VERSION=$(yarn --version)
    log_success "Yarn version: $YARN_VERSION"
}

# Function to check Docker (optional)
check_docker() {
    log_info "Checking Docker availability..."
    
    if command_exists docker; then
        DOCKER_VERSION=$(docker --version | cut -d' ' -f3 | sed 's/,//')
        log_success "Docker version: $DOCKER_VERSION"
        
        if command_exists docker-compose; then
            COMPOSE_VERSION=$(docker-compose --version | cut -d' ' -f3 | sed 's/,//')
            log_success "Docker Compose version: $COMPOSE_VERSION"
            DOCKER_AVAILABLE=true
        else
            log_warning "Docker Compose is not installed. Database setup will use local configuration."
            DOCKER_AVAILABLE=false
        fi
    else
        log_warning "Docker is not installed. Database setup will use local configuration."
        DOCKER_AVAILABLE=false
    fi
}

# Function to check system resources
check_system_resources() {
    log_info "Checking system resources..."
    
    # Check available memory
    if command_exists free; then
        TOTAL_MEM=$(free -m | awk 'NR==2{printf "%.1f", $2/1024}')
        AVAIL_MEM=$(free -m | awk 'NR==2{printf "%.1f", $7/1024}')
        log_info "Memory: ${AVAIL_MEM}GB available of ${TOTAL_MEM}GB total"
        
        if (( $(echo "$AVAIL_MEM < 1.0" | bc -l) )); then
            log_warning "Low available memory (${AVAIL_MEM}GB). Consider closing other applications."
        fi
    elif command_exists vm_stat; then
        # macOS memory check
        TOTAL_MEM=$(echo "$(sysctl -n hw.memsize) / 1024 / 1024 / 1024" | bc)
        log_info "Memory: ${TOTAL_MEM}GB total (macOS)"
    fi
    
    # Check disk space
    DISK_AVAIL=$(df -h . | awk 'NR==2 {print $4}')
    log_info "Disk space available: $DISK_AVAIL"
}

# Function to setup environment file with validation
setup_environment_file() {
    log_info "Setting up environment configuration..."
    
    if [ ! -f .env ]; then
        if [ -f .env.example ]; then
            cp .env.example .env
            log_success "Created .env file from .env.example"
            
            # Generate secure keys
            log_info "Generating secure keys..."
            
            # Generate APP_KEYS
            APP_KEY1=$(openssl rand -base64 32 2>/dev/null || head -c 32 /dev/urandom | base64)
            APP_KEY2=$(openssl rand -base64 32 2>/dev/null || head -c 32 /dev/urandom | base64)
            APP_KEY3=$(openssl rand -base64 32 2>/dev/null || head -c 32 /dev/urandom | base64)
            APP_KEY4=$(openssl rand -base64 32 2>/dev/null || head -c 32 /dev/urandom | base64)
            
            # Generate JWT secrets
            JWT_SECRET=$(openssl rand -base64 32 2>/dev/null || head -c 32 /dev/urandom | base64)
            ADMIN_JWT_SECRET=$(openssl rand -base64 32 2>/dev/null || head -c 32 /dev/urandom | base64)
            API_TOKEN_SALT=$(openssl rand -base64 32 2>/dev/null || head -c 32 /dev/urandom | base64)
            TRANSFER_TOKEN_SALT=$(openssl rand -base64 32 2>/dev/null || head -c 32 /dev/urandom | base64)
            
            # Update .env file with generated keys
            if command_exists sed; then
                # Use different sed syntax for macOS vs Linux
                if [[ "$OSTYPE" == "darwin"* ]]; then
                    sed -i '' "s/APP_KEYS=.*/APP_KEYS=$APP_KEY1,$APP_KEY2,$APP_KEY3,$APP_KEY4/" .env
                    sed -i '' "s/JWT_SECRET=.*/JWT_SECRET=$JWT_SECRET/" .env
                    sed -i '' "s/ADMIN_JWT_SECRET=.*/ADMIN_JWT_SECRET=$ADMIN_JWT_SECRET/" .env
                    sed -i '' "s/API_TOKEN_SALT=.*/API_TOKEN_SALT=$API_TOKEN_SALT/" .env
                    sed -i '' "s/TRANSFER_TOKEN_SALT=.*/TRANSFER_TOKEN_SALT=$TRANSFER_TOKEN_SALT/" .env
                else
                    sed -i "s/APP_KEYS=.*/APP_KEYS=$APP_KEY1,$APP_KEY2,$APP_KEY3,$APP_KEY4/" .env
                    sed -i "s/JWT_SECRET=.*/JWT_SECRET=$JWT_SECRET/" .env
                    sed -i "s/ADMIN_JWT_SECRET=.*/ADMIN_JWT_SECRET=$ADMIN_JWT_SECRET/" .env
                    sed -i "s/API_TOKEN_SALT=.*/API_TOKEN_SALT=$API_TOKEN_SALT/" .env
                    sed -i "s/TRANSFER_TOKEN_SALT=.*/TRANSFER_TOKEN_SALT=$TRANSFER_TOKEN_SALT/" .env
                fi
                log_success "Generated and configured secure keys"
            else
                log_warning "Could not automatically update keys. Please update them manually."
            fi
            
            log_warning "Please review and update the .env file with your specific configuration:"
            echo "   - ADAPTY_API_KEY (required for Adapty integration)"
            echo "   - ADAPTY_WEBHOOK_SECRET (required for webhooks)"
            echo "   - Database settings (if not using SQLite)"
            echo ""
            
            read -p "Press Enter to continue after reviewing .env file..."
        else
            log_error ".env.example file not found. Cannot create .env file."
            exit 1
        fi
    else
        log_info ".env file already exists"
        
        # Validate existing .env file
        log_info "Validating .env file..."
        REQUIRED_VARS=("HOST" "PORT" "APP_KEYS" "DATABASE_CLIENT")
        
        for var in "${REQUIRED_VARS[@]}"; do
            if grep -q "^$var=" .env; then
                log_success "$var is configured"
            else
                log_warning "$var is not configured in .env"
            fi
        done
    fi
}

# Function to install dependencies with error handling
install_dependencies() {
    log_info "Installing dependencies..."
    
    # Check if node_modules exists and is recent
    if [ -d "node_modules" ]; then
        PACKAGE_JSON_TIME=$(stat -c %Y package.json 2>/dev/null || stat -f %m package.json)
        NODE_MODULES_TIME=$(stat -c %Y node_modules 2>/dev/null || stat -f %m node_modules)
        
        if [ "$PACKAGE_JSON_TIME" -gt "$NODE_MODULES_TIME" ]; then
            log_warning "package.json is newer than node_modules. Reinstalling dependencies..."
            rm -rf node_modules
        fi
    fi
    
    # Install with retry logic
    INSTALL_ATTEMPTS=0
    MAX_ATTEMPTS=3
    
    while [ $INSTALL_ATTEMPTS -lt $MAX_ATTEMPTS ]; do
        if yarn install; then
            log_success "Dependencies installed successfully"
            break
        else
            INSTALL_ATTEMPTS=$((INSTALL_ATTEMPTS + 1))
            if [ $INSTALL_ATTEMPTS -lt $MAX_ATTEMPTS ]; then
                log_warning "Installation attempt $INSTALL_ATTEMPTS failed. Retrying..."
                sleep 2
            else
                log_error "Failed to install dependencies after $MAX_ATTEMPTS attempts"
                exit 1
            fi
        fi
    done
    
    # Verify critical dependencies
    log_info "Verifying critical dependencies..."
    CRITICAL_DEPS=("@strapi/strapi" "esbuild-loader" "typescript")
    
    for dep in "${CRITICAL_DEPS[@]}"; do
        if [ -d "node_modules/$dep" ]; then
            VERSION=$(node -p "require('./node_modules/$dep/package.json').version" 2>/dev/null || echo "unknown")
            log_success "$dep@$VERSION is installed"
        else
            log_error "Critical dependency $dep is missing"
            exit 1
        fi
    done
}

# Function to setup database
setup_database() {
    log_info "Setting up database..."
    
    # Create .tmp directory for SQLite
    mkdir -p .tmp
    log_success "Created .tmp directory for SQLite database"
    
    if [ "$DOCKER_AVAILABLE" = true ]; then
        log_info "Docker is available. Setting up containerized database..."
        
        # Check if docker-compose files exist
        if [ -f "docker-compose.yml" ] && [ -f "docker-compose.dev.yml" ]; then
            log_info "Starting Docker services..."
            
            if docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d postgres redis 2>/dev/null; then
                log_success "Docker services started"
                
                # Wait for PostgreSQL to be ready
                log_info "Waiting for PostgreSQL to be ready..."
                WAIT_ATTEMPTS=0
                MAX_WAIT=30
                
                while [ $WAIT_ATTEMPTS -lt $MAX_WAIT ]; do
                    if docker-compose exec -T postgres pg_isready -U strapi -d strapi_adapty_cms >/dev/null 2>&1; then
                        log_success "PostgreSQL is ready"
                        break
                    else
                        WAIT_ATTEMPTS=$((WAIT_ATTEMPTS + 1))
                        sleep 1
                    fi
                done
                
                if [ $WAIT_ATTEMPTS -eq $MAX_WAIT ]; then
                    log_warning "PostgreSQL readiness check timed out. Continuing with setup..."
                fi
            else
                log_warning "Failed to start Docker services. Using local database configuration."
            fi
        else
            log_warning "Docker Compose files not found. Using local database configuration."
        fi
    else
        log_info "Using local SQLite database configuration"
    fi
    
    log_success "Database setup complete"
}

# Function to validate setup
validate_setup() {
    log_info "Validating development environment setup..."
    
    # Check if all required files exist
    REQUIRED_FILES=(".env" "package.json" "tsconfig.json")
    
    for file in "${REQUIRED_FILES[@]}"; do
        if [ -f "$file" ]; then
            log_success "$file exists"
        else
            log_error "$file is missing"
            exit 1
        fi
    done
    
    # Check if node_modules exists
    if [ -d "node_modules" ]; then
        log_success "node_modules directory exists"
    else
        log_error "node_modules directory is missing"
        exit 1
    fi
    
    # Test basic Node.js functionality
    if node -e "console.log('Node.js is working')" >/dev/null 2>&1; then
        log_success "Node.js is functioning correctly"
    else
        log_error "Node.js is not functioning correctly"
        exit 1
    fi
    
    log_success "Environment validation complete"
}

# Function to show next steps
show_next_steps() {
    log_divider
    log_header "🎯 Development Environment Setup Complete!"
    log_divider
    echo ""
    log_header "Next Steps:"
    echo ""
    echo "1. Start the development server:"
    echo "   ${GREEN}yarn develop${NC}"
    echo ""
    echo "2. Start debug mode:"
    echo "   ${GREEN}yarn dev:debug${NC}"
    echo ""
    echo "3. Access the admin panel:"
    echo "   ${BLUE}http://localhost:1337/admin${NC}"
    echo ""
    echo "4. Create your first admin user when prompted"
    echo ""
    log_header "Useful Commands:"
    echo ""
    echo "   ${CYAN}yarn dev:env check${NC}     - Check environment health"
    echo "   ${CYAN}yarn dev:env doctor${NC}    - Run comprehensive diagnostics"
    echo "   ${CYAN}yarn dev:env clean${NC}     - Clean cache and build files"
    echo "   ${CYAN}yarn seed:example${NC}      - Seed example data"
    echo ""
    
    if [ "$DOCKER_AVAILABLE" = true ]; then
        log_header "Docker Services:"
        echo ""
        echo "   - PostgreSQL: localhost:5432"
        echo "   - Redis: localhost:6379"
        echo "   - View logs: ${CYAN}docker-compose logs${NC}"
        echo ""
    fi
    
    log_header "Documentation:"
    echo ""
    echo "   - Development Guide: .docs/development-setup.md"
    echo "   - API Documentation: docs/mobile-api-v1-documentation.md"
    echo "   - Troubleshooting: Run ${CYAN}yarn dev:env doctor${NC}"
    echo ""
}

# Function to handle errors
handle_error() {
    local exit_code=$?
    log_error "Setup failed with exit code $exit_code"
    echo ""
    log_header "Troubleshooting:"
    echo ""
    echo "1. Check the error messages above"
    echo "2. Ensure all prerequisites are installed"
    echo "3. Run: ${CYAN}yarn dev:env doctor${NC} for diagnostics"
    echo "4. Check the documentation in .docs/"
    echo ""
    exit $exit_code
}

# Main execution
main() {
    # Set up error handling
    trap handle_error ERR
    
    log_header "🚀 Enhanced Strapi Adapty CMS Development Setup"
    log_divider
    echo ""
    
    # Run setup steps
    check_node_version
    check_yarn_version
    check_docker
    check_system_resources
    setup_environment_file
    install_dependencies
    setup_database
    validate_setup
    
    echo ""
    show_next_steps
}

# Execute main function
main "$@"