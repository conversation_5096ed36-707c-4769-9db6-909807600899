#!/bin/bash

# Enhanced Development Debug Script with <PERSON><PERSON><PERSON>ling and Fallback Mechanisms
# This script provides robust debug mode with comprehensive error handling

set -e  # Exit on any error

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check Node.js version compatibility
check_node_version() {
    log_info "Checking Node.js version compatibility..."
    
    NODE_VERSION=$(node --version)
    NODE_MAJOR=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
    
    log_info "Node.js version: $NODE_VERSION"
    
    if [ "$NODE_MAJOR" -lt 18 ]; then
        log_error "Node.js version $NODE_VERSION is not supported. Please use Node.js 18.x or higher."
        exit 1
    elif [ "$NODE_MAJOR" -eq 20 ]; then
        log_success "Node.js v20.x detected - using enhanced compatibility mode"
        export NODE_OPTIONS="--max-old-space-size=4096"
    elif [ "$NODE_MAJOR" -ge 22 ]; then
        log_warning "Node.js v$NODE_MAJOR detected - experimental support"
        export NODE_OPTIONS="--max-old-space-size=4096 --experimental-loader"
    else
        log_success "Node.js v$NODE_MAJOR detected - stable support"
    fi
}

# Function to validate dependencies
check_dependencies() {
    log_info "Validating critical dependencies..."
    
    # Check if esbuild-loader is updated
    ESBUILD_LOADER_VERSION=$(node -p "require('./node_modules/esbuild-loader/package.json').version" 2>/dev/null || echo "not found")
    
    if [[ "$ESBUILD_LOADER_VERSION" == "not found" ]]; then
        log_error "esbuild-loader not found. Please run 'yarn install'"
        exit 1
    elif [[ "$ESBUILD_LOADER_VERSION" < "4.0.0" ]]; then
        log_error "esbuild-loader version $ESBUILD_LOADER_VERSION is outdated. Please update to 4.x"
        log_info "Run: yarn add --dev esbuild-loader@^4.3.0"
        exit 1
    else
        log_success "esbuild-loader version $ESBUILD_LOADER_VERSION is compatible"
    fi
    
    # Check ESBuild version
    ESBUILD_VERSION=$(node -p "require('./node_modules/esbuild/package.json').version" 2>/dev/null || echo "not found")
    if [[ "$ESBUILD_VERSION" != "not found" ]]; then
        log_success "esbuild version $ESBUILD_VERSION detected"
    fi
}

# Function to setup environment variables with fallbacks
setup_environment() {
    log_info "Setting up debug environment..."
    
    # Core environment
    export NODE_ENV=development
    export DEBUG=strapi:*
    
    # Source map configuration with fallbacks
    export GENERATE_SOURCEMAP=true
    export DEV_SERVER_SOURCEMAP=true
    
    # TypeScript configuration
    export TS_NODE_OPTIONS="--transpile-only --files --esm"
    
    # Enhanced debugging options
    export STRAPI_LOG_LEVEL=debug
    export STRAPI_LOG_TIMESTAMP=true
    export STRAPI_LOG_FORCE_COLOR=true
    
    # Build optimization for debug mode
    export WEBPACK_DEV_SERVER=true
    export VITE_DEV_SERVER=true
    export BUILD_ANALYZE=false
    
    # Memory and performance settings
    export UV_THREADPOOL_SIZE=128
    
    # Error handling settings
    export UNHANDLED_REJECTION_POLICY=strict
    export NODE_OPTIONS="$NODE_OPTIONS --trace-warnings --trace-uncaught"
    
    log_success "Environment configured for enhanced debugging"
}

# Function to clean build artifacts with error handling
clean_build_artifacts() {
    log_info "Cleaning build artifacts..."
    
    # Clean directories with error handling
    for dir in dist .cache build .tmp/build; do
        if [ -d "$dir" ]; then
            rm -rf "$dir" && log_success "Cleaned $dir" || log_warning "Failed to clean $dir"
        fi
    done
    
    # Ensure required directories exist
    mkdir -p .tmp
    mkdir -p dist
    
    log_success "Build artifacts cleaned"
}

# Function to validate environment files
check_env_files() {
    log_info "Validating environment configuration..."
    
    if [ ! -f ".env" ]; then
        log_warning ".env file not found"
        if [ -f ".env.example" ]; then
            log_info "Copying .env.example to .env"
            cp .env.example .env
            log_warning "Please configure your .env file with proper values"
        else
            log_error "No .env.example found. Please create .env file"
            exit 1
        fi
    else
        log_success ".env file found"
    fi
    
    # Check for required environment variables
    required_vars=("HOST" "PORT" "APP_KEYS" "DATABASE_CLIENT")
    for var in "${required_vars[@]}"; do
        if grep -q "^$var=" .env; then
            log_success "$var is configured"
        else
            log_warning "$var is not configured in .env"
        fi
    done
}

# Function to start debug mode with error handling and fallbacks
start_debug_mode() {
    log_info "Starting Strapi in debug mode..."
    log_info "Debug features enabled:"
    log_info "  - Source maps: ENABLED"
    log_info "  - TypeScript debugging: ENABLED"
    log_info "  - Hot reload: ENABLED"
    log_info "  - Enhanced error handling: ENABLED"
    log_info "  - Performance monitoring: ENABLED"
    log_info ""
    log_info "Admin panel will be available at: http://localhost:${PORT:-1337}/admin"
    log_info ""
    
    # Create a trap to handle interruptions gracefully
    trap 'log_warning "Debug session interrupted. Cleaning up..."; exit 0' INT TERM
    
    # Start with enhanced error handling
    if yarn develop --debug; then
        log_success "Debug session completed successfully"
    else
        local exit_code=$?
        log_error "Debug session failed with exit code $exit_code"
        
        # Provide troubleshooting suggestions
        log_info ""
        log_info "Troubleshooting suggestions:"
        log_info "1. Check if port ${PORT:-1337} is available"
        log_info "2. Verify database connection settings"
        log_info "3. Check .env file configuration"
        log_info "4. Try running 'yarn install' to refresh dependencies"
        log_info "5. Check logs above for specific error messages"
        
        # Offer fallback mode
        log_info ""
        read -p "Would you like to try fallback mode without debug flags? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            log_info "Starting in fallback mode..."
            export GENERATE_SOURCEMAP=false
            export DEV_SERVER_SOURCEMAP=false
            yarn develop
        fi
        
        exit $exit_code
    fi
}

# Function to display system information
show_system_info() {
    log_info "System Information:"
    log_info "  Node.js: $(node --version)"
    log_info "  Yarn: $(yarn --version)"
    log_info "  Platform: $(uname -s) $(uname -m)"
    log_info "  Working Directory: $(pwd)"
    log_info ""
}

# Main execution
main() {
    echo "🚀 Enhanced Strapi Debug Mode"
    echo "============================="
    echo ""
    
    show_system_info
    check_node_version
    check_dependencies
    check_env_files
    setup_environment
    clean_build_artifacts
    start_debug_mode
}

# Execute main function
main "$@"