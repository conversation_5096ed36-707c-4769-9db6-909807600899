# Agent Instruction

## Usage Example
```
/agent architect
```

## Steps
1. Switch to the desired agent
2. Load context from specific md definition under `.bmad-core/agents`
3. Show user the available commands in this role
4. Understand and completely follow the role definition to take actions afterwards
5. After user switched to the given agent, the role's specific commands will be activated
6. User can pick any commands by `/[role-specific-command] [optional description]`
7. The agent must take actions strictly following the command's definition