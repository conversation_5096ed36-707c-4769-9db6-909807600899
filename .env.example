# =============================================================================
# MANDATORY CONFIGURATION - These fields are REQUIRED and have no defaults
# =============================================================================

# Server Configuration (REQUIRED)
HOST=0.0.0.0
PORT=1337
APP_KEYS="toBeModified1,toBeModified2,toBeModified3,toBeModified4"

# Security Secrets (REQUIRED)
API_TOKEN_SALT=tobemodified
ADMIN_JWT_SECRET=tobemodified
TRANSFER_TOKEN_SALT=tobemodified
JWT_SECRET=tobemodified
ENCRYPTION_KEY=tobemodified

# Database Configuration (REQUIRED)
# Choose one: sqlite, postgres, mysql
DATABASE_CLIENT=sqlite

# For SQLite (REQUIRED if DATABASE_CLIENT=sqlite)
DATABASE_FILENAME=.tmp/data.db

# For PostgreSQL (REQUIRED if DATABASE_CLIENT=postgres)
# DATABASE_HOST=localhost
# DATABASE_PORT=5432
# DATABASE_NAME=strapi_adapty_cms
# DATABASE_USERNAME=strapi
# DATABASE_PASSWORD=strapi
# DATABASE_SSL=false

# For MySQL (REQUIRED if DATABASE_CLIENT=mysql)
# DATABASE_HOST=localhost
# DATABASE_PORT=3306
# DATABASE_NAME=strapi_adapty_cms
# DATABASE_USERNAME=strapi
# DATABASE_PASSWORD=strapi
# DATABASE_SSL=false

# Adapty Integration
ADAPTY_API_KEY=your_adapty_api_key_here
ADAPTY_WEBHOOK_SECRET=your_webhook_secret_here

# Redis Cache (optional)
REDIS_URL=redis://localhost:6379

# Media Upload Configuration
UPLOAD_PROVIDER=local
UPLOAD_MAX_SIZE=52428800

# AWS S3 Configuration (if using aws-s3 provider)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_ACCESS_SECRET=your_aws_secret_key
AWS_REGION=us-east-1
AWS_BUCKET=your-s3-bucket-name
AWS_CDN_URL=https://your-cloudfront-domain.com

# Cloudinary Configuration (if using cloudinary provider)
CLOUDINARY_NAME=your_cloudinary_name
CLOUDINARY_KEY=your_cloudinary_key
CLOUDINARY_SECRET=your_cloudinary_secret

# CDN Configuration (optional)
CDN_URL=https://your-cdn-domain.com
