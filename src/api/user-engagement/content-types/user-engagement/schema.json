{"kind": "collectionType", "collectionName": "user_engagement", "info": {"singularName": "user-engagement", "pluralName": "user-engagement-records", "displayName": "User Engagement", "description": "User interaction and engagement metrics"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"paywallId": {"type": "string", "required": true}, "timestamp": {"type": "datetime", "required": true}, "sessionDuration": {"type": "integer", "required": true, "default": 0}, "pageViews": {"type": "integer", "required": true, "default": 0}, "clickThroughRate": {"type": "decimal", "required": true, "default": 0}, "scrollDepth": {"type": "decimal", "required": true, "default": 0}, "interactionEvents": {"type": "json", "required": true}, "heatmapData": {"type": "json", "required": true}}}