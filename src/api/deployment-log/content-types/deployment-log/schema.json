{"kind": "collectionType", "collectionName": "deployment_logs", "info": {"singularName": "deployment-log", "pluralName": "deployment-logs", "displayName": "Deployment Log", "description": "Track deployment and rollback actions for remote configurations"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"type": {"type": "enumeration", "enum": ["deployment", "rollback", "configuration", "maintenance", "ab_test_sync_failure", "quality_report", "translation_activity"], "required": true, "default": "deployment"}, "action": {"type": "enumeration", "enum": ["deploy", "rollback", "preview"], "required": true}, "version_id": {"type": "string", "required": true}, "previous_version_id": {"type": "string"}, "reason": {"type": "text"}, "timestamp": {"type": "datetime", "required": true}, "performed_by": {"type": "string"}, "success": {"type": "boolean", "default": true}, "error_message": {"type": "text"}, "placement_id": {"type": "string"}, "locale": {"type": "string", "default": "en"}, "test_id": {"type": "integer"}, "sync_type": {"type": "enumeration", "enum": ["status", "metrics", "full"]}, "retry_count": {"type": "integer", "default": 0}, "occurred_at": {"type": "datetime"}, "status": {"type": "string"}, "data": {"type": "json"}}}