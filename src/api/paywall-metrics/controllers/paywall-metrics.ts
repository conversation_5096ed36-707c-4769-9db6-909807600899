/**
 * Paywall Metrics Controller
 */

import { factories } from "@strapi/strapi";

export default factories.createCoreController(
	"api::paywall-metrics.paywall-metrics",
	({ strapi }) => ({
		/**
		 * Record new paywall metrics
		 */
		async create(ctx) {
			try {
				const { data } = ctx.request.body;

				// Validate required fields
				const requiredFields = [
					"paywallId",
					"impressions",
					"conversions",
					"region",
					"locale",
					"deviceType",
					"platform",
				];
				for (const field of requiredFields) {
					if (!data[field]) {
						return ctx.badRequest(`Missing required field: ${field}`);
					}
				}

				// Calculate derived metrics
				data.conversionRate =
					data.impressions > 0 ? data.conversions / data.impressions : 0;
				data.averageRevenuePerUser =
					data.conversions > 0 ? (data.revenue || 0) / data.conversions : 0;
				data.timestamp = new Date();

				const entity = await strapi.entityService.create(
					"api::paywall-metrics.paywall-metrics",
					{
						data,
					},
				);

				return this.sanitizeOutput(entity, ctx);
			} catch (error) {
				strapi.log.error("Error creating paywall metrics:", error);
				return ctx.internalServerError("Failed to create paywall metrics");
			}
		},

		/**
		 * Get metrics with advanced filtering
		 */
		async find(ctx) {
			try {
				const { query } = ctx;

				// Build filters
				const filters: any = {};

				if (query.paywallId) {
					filters.paywallId = query.paywallId;
				}

				if (query.region) {
					filters.region = query.region;
				}

				if (query.dateFrom || query.dateTo) {
					filters.timestamp = {};
					if (query.dateFrom) {
						filters.timestamp.$gte = new Date(query.dateFrom);
					}
					if (query.dateTo) {
						filters.timestamp.$lte = new Date(query.dateTo);
					}
				}

				const entities = await strapi.entityService.findMany(
					"api::paywall-metrics.paywall-metrics",
					{
						filters,
						sort: { timestamp: "desc" },
						pagination: {
							page: query.page || 1,
							pageSize: query.pageSize || 25,
						},
					},
				);

				return this.sanitizeOutput(entities, ctx);
			} catch (error) {
				strapi.log.error("Error fetching paywall metrics:", error);
				return ctx.internalServerError("Failed to fetch paywall metrics");
			}
		},

		/**
		 * Get aggregated metrics
		 */
		async aggregate(ctx) {
			try {
				const { query } = ctx;
				const { groupBy = "paywall", dateFrom, dateTo } = query;

				const filters: any = {};
				if (dateFrom || dateTo) {
					filters.timestamp = {};
					if (dateFrom) filters.timestamp.$gte = new Date(dateFrom);
					if (dateTo) filters.timestamp.$lte = new Date(dateTo);
				}

				const metrics = await strapi.entityService.findMany(
					"api::paywall-metrics.paywall-metrics",
					{
						filters,
						sort: { timestamp: "desc" },
					},
				);

				// Aggregate data based on groupBy parameter
				const aggregated = this.aggregateMetrics(metrics, groupBy);

				return { data: aggregated };
			} catch (error) {
				strapi.log.error("Error aggregating metrics:", error);
				return ctx.internalServerError("Failed to aggregate metrics");
			}
		},

		/**
		 * Helper method to aggregate metrics
		 */
		aggregateMetrics(metrics: any[], groupBy: string) {
			const groups = new Map();

			metrics.forEach((metric) => {
				let key;
				switch (groupBy) {
					case "paywall":
						key = metric.paywallId;
						break;
					case "region":
						key = metric.region;
						break;
					case "date":
						key = metric.timestamp.toISOString().split("T")[0];
						break;
					case "device":
						key = metric.deviceType;
						break;
					default:
						key = "all";
				}

				if (!groups.has(key)) {
					groups.set(key, {
						key,
						impressions: 0,
						conversions: 0,
						revenue: 0,
						count: 0,
					});
				}

				const group = groups.get(key);
				group.impressions += metric.impressions;
				group.conversions += metric.conversions;
				group.revenue += metric.revenue || 0;
				group.count += 1;
			});

			// Calculate derived metrics for each group
			return Array.from(groups.values()).map((group) => ({
				...group,
				conversionRate:
					group.impressions > 0 ? group.conversions / group.impressions : 0,
				averageRevenuePerUser:
					group.conversions > 0 ? group.revenue / group.conversions : 0,
			}));
		},
	}),
);
