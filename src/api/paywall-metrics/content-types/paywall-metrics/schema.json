{"kind": "collectionType", "collectionName": "paywall_metrics", "info": {"singularName": "paywall-metrics", "pluralName": "paywall-metrics-entries", "displayName": "Paywall Metrics", "description": "Analytics data for paywall performance tracking"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"paywallId": {"type": "string", "required": true}, "timestamp": {"type": "datetime", "required": true}, "impressions": {"type": "integer", "required": true, "default": 0}, "conversions": {"type": "integer", "required": true, "default": 0}, "conversionRate": {"type": "decimal", "required": true, "default": 0}, "revenue": {"type": "decimal", "required": true, "default": 0}, "averageRevenuePerUser": {"type": "decimal", "required": true, "default": 0}, "bounceRate": {"type": "decimal", "required": true, "default": 0}, "timeToConversion": {"type": "integer", "required": true, "default": 0}, "userSegment": {"type": "string", "required": true}, "region": {"type": "string", "required": true}, "locale": {"type": "string", "required": true}, "deviceType": {"type": "enumeration", "enum": ["mobile", "tablet", "desktop"], "required": true}, "platform": {"type": "enumeration", "enum": ["ios", "android", "web"], "required": true}}}