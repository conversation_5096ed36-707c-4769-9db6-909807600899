/**
 * Paywall Metrics Routes
 */

export default {
	routes: [
		{
			method: "GET",
			path: "/paywall-metrics",
			handler: "paywall-metrics.find",
			config: {
				policies: [],
				middlewares: [],
			},
		},
		{
			method: "POST",
			path: "/paywall-metrics",
			handler: "paywall-metrics.create",
			config: {
				policies: [],
				middlewares: [],
			},
		},
		{
			method: "GET",
			path: "/paywall-metrics/aggregate",
			handler: "paywall-metrics.aggregate",
			config: {
				policies: [],
				middlewares: [],
			},
		},
	],
};
