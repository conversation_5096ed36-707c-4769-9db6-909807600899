/**
 * API Performance router
 */

export default {
	routes: [
		{
			method: "GET",
			path: "/api-performance",
			handler: "api-performance.find",
		},
		{
			method: "GET",
			path: "/api-performance/metrics",
			handler: "api-performance.getPerformanceMetrics",
		},
		{
			method: "GET",
			path: "/api-performance/endpoints",
			handler: "api-performance.getEndpointSummary",
		},
		{
			method: "GET",
			path: "/api-performance/slowest",
			handler: "api-performance.getSlowestEndpoints",
		},
		{
			method: "GET",
			path: "/api-performance/errors",
			handler: "api-performance.getErrorAnalysis",
		},
		{
			method: "GET",
			path: "/api-performance/realtime",
			handler: "api-performance.getRealTimeMetrics",
		},
		{
			method: "GET",
			path: "/api-performance/:id",
			handler: "api-performance.findOne",
		},
		{
			method: "POST",
			path: "/api-performance",
			handler: "api-performance.create",
		},
		{
			method: "PUT",
			path: "/api-performance/:id",
			handler: "api-performance.update",
		},
		{
			method: "DELETE",
			path: "/api-performance/:id",
			handler: "api-performance.delete",
		},
	],
};
