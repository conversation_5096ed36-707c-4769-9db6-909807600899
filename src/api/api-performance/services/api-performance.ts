/**
 * API Performance service
 */

import { factories } from "@strapi/strapi";

export default factories.createCoreService(
	"api::api-performance.api-performance",
	({ strapi }) => ({
		/**
		 * Record API performance metric
		 */
		async recordMetric(metricData: any) {
			try {
				return await strapi.entityService.create(
					"api::api-performance.api-performance",
					{
						data: {
							...metricData,
							publishedAt: new Date(),
						},
					},
				);
			} catch (error) {
				// Handle readonly database errors gracefully
				if (error.message && error.message.includes('readonly database')) {
					// Log once per session to avoid spam
					if (!global.readonlyDbWarningLogged) {
						strapi.log.warn("Database is in read-only mode. Performance metrics recording disabled.");
						global.readonlyDbWarningLogged = true;
					}
					return null; // Return null instead of throwing
				}
				strapi.log.error("Failed to record API performance metric:", error);
				throw error;
			}
		},

		/**
		 * Get performance metrics for endpoint
		 */
		async getEndpointMetrics(
			endpoint: string,
			method: "GET" | "POST" | "PUT" | "DELETE" | "PATCH" | "OPTIONS" | "HEAD",
			hours: number = 24,
		) {
			try {
				const since = new Date(Date.now() - hours * 60 * 60 * 1000);

				return await strapi.entityService.findMany(
					"api::api-performance.api-performance",
					{
						filters: {
							endpoint,
							method,
							timestamp: {
								$gte: since.toISOString(),
							},
						},
						sort: "timestamp:desc",
					},
				);
			} catch (error) {
				strapi.log.error("Failed to get endpoint metrics:", error);
				throw error;
			}
		},

		/**
		 * Get performance summary for dashboard
		 */
		async getPerformanceSummary(hours: number = 1) {
			try {
				const since = new Date(Date.now() - hours * 60 * 60 * 1000);

				const metrics = await strapi.entityService.findMany(
					"api::api-performance.api-performance",
					{
						filters: {
							timestamp: {
								$gte: since.toISOString(),
							},
						},
					},
				);

				if (metrics.length === 0) {
					return {
						totalRequests: 0,
						averageResponseTime: 0,
						errorRate: 0,
						requestsPerMinute: 0,
					};
				}

				const totalRequests = metrics.length;
				const errorRequests = metrics.filter((m) => m.statusCode >= 400).length;
				const averageResponseTime =
					metrics.reduce((sum, m) => sum + m.responseTime, 0) / totalRequests;
				const errorRate = (errorRequests / totalRequests) * 100;

				const timeSpan = hours * 60; // minutes
				const requestsPerMinute = totalRequests / timeSpan;

				return {
					totalRequests,
					averageResponseTime: Math.round(averageResponseTime),
					errorRate: Math.round(errorRate * 100) / 100,
					requestsPerMinute: Math.round(requestsPerMinute * 100) / 100,
				};
			} catch (error) {
				strapi.log.error("Failed to get performance summary:", error);
				throw error;
			}
		},

		/**
		 * Check if API performance is healthy
		 */
		async isPerformanceHealthy(): Promise<boolean> {
			try {
				const summary = await this.getPerformanceSummary(1); // Last hour

				// Define healthy thresholds
				const thresholds = {
					maxAverageResponseTime: 1000, // 1 second
					maxErrorRate: 5, // 5%
				};

				return (
					summary.averageResponseTime <= thresholds.maxAverageResponseTime &&
					summary.errorRate <= thresholds.maxErrorRate
				);
			} catch (error) {
				strapi.log.error("Failed to check API performance health:", error);
				return false;
			}
		},

		/**
		 * Get top error endpoints
		 */
		async getTopErrorEndpoints(hours: number = 24, limit: number = 10) {
			try {
				const since = new Date(Date.now() - hours * 60 * 60 * 1000);

				const errorMetrics = await strapi.entityService.findMany(
					"api::api-performance.api-performance",
					{
						filters: {
							timestamp: {
								$gte: since.toISOString(),
							},
							statusCode: {
								$gte: 400,
							},
						},
					},
				);

				const errorsByEndpoint = errorMetrics.reduce((acc, metric) => {
					const key = `${metric.method} ${metric.endpoint}`;
					acc[key] = (acc[key] || 0) + 1;
					return acc;
				}, {});

				return Object.entries(errorsByEndpoint)
					.map(([endpoint, count]) => ({ endpoint, errorCount: count }))
					.sort((a: any, b: any) => b.errorCount - a.errorCount)
					.slice(0, limit);
			} catch (error) {
				strapi.log.error("Failed to get top error endpoints:", error);
				throw error;
			}
		},

		/**
		 * Clean up old performance metrics
		 */
		async cleanupOldMetrics(daysToKeep: number = 7) {
			try {
				const cutoffDate = new Date();
				cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

				const deletedCount = await strapi.db
					.query("api::api-performance.api-performance")
					.deleteMany({
						where: {
							timestamp: {
								$lt: cutoffDate,
							},
						},
					});

				strapi.log.info(
					`Cleaned up ${deletedCount} old API performance metrics`,
				);
				return deletedCount;
			} catch (error) {
				strapi.log.error(
					"Failed to cleanup old API performance metrics:",
					error,
				);
				throw error;
			}
		},
	}),
);
