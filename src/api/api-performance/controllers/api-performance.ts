/**
 * API Performance controller
 */

import { factories } from "@strapi/strapi";

export default factories.createCoreController(
	"api::api-performance.api-performance",
	({ strapi }) => ({
		/**
		 * Get API performance metrics
		 */
		async getPerformanceMetrics(ctx) {
			try {
				const { hours = 24, endpoint, method, limit = 1000 } = ctx.query;
				const since = new Date(Date.now() - hours * 60 * 60 * 1000);

				const filters: any = {
					timestamp: {
						$gte: since.toISOString(),
					},
				};

				if (endpoint) {
					filters.endpoint = { $contains: endpoint };
				}

				if (method) {
					filters.method = method;
				}

				const metrics = await strapi.entityService.findMany(
					"api::api-performance.api-performance",
					{
						filters,
						sort: "timestamp:desc",
						limit: parseInt(limit),
					},
				);

				// Calculate performance statistics
				const metricsArray = Array.isArray(metrics) ? metrics : [metrics];
				const stats = this.calculatePerformanceStats(metricsArray);

				ctx.body = {
					data: metrics,
					meta: {
						total: metrics.length,
						timeRange: { since, hours },
						statistics: stats,
					},
				};
			} catch (_error) {
				ctx.throw(500, "Failed to get API performance metrics");
			}
		},

		/**
		 * Get endpoint performance summary
		 */
		async getEndpointSummary(ctx) {
			try {
				const { hours = 24 } = ctx.query;
				const since = new Date(Date.now() - hours * 60 * 60 * 1000);

				const metrics = await strapi.entityService.findMany(
					"api::api-performance.api-performance",
					{
						filters: {
							timestamp: {
								$gte: since.toISOString(),
							},
						},
					},
				);

				// Group by endpoint and method
				const endpointStats = this.groupByEndpoint(
					Array.isArray(metrics) ? metrics : [metrics],
				);

				ctx.body = {
					data: endpointStats,
					meta: {
						totalEndpoints: Object.keys(endpointStats).length,
						timeRange: { since, hours },
					},
				};
			} catch (_error) {
				ctx.throw(500, "Failed to get endpoint summary");
			}
		},

		/**
		 * Get slowest endpoints
		 */
		async getSlowestEndpoints(ctx) {
			try {
				const { hours = 24, limit = 10 } = ctx.query;
				const since = new Date(Date.now() - hours * 60 * 60 * 1000);

				const metrics = await strapi.entityService.findMany(
					"api::api-performance.api-performance",
					{
						filters: {
							timestamp: {
								$gte: since.toISOString(),
							},
						},
					},
				);

				const endpointStats = this.groupByEndpoint(
					Array.isArray(metrics) ? metrics : [metrics],
				);

				// Sort by average response time
				const slowestEndpoints = Object.entries(endpointStats)
					.map(([endpoint, stats]: [string, any]) => ({
						endpoint,
						...stats,
					}))
					.sort((a, b) => b.averageResponseTime - a.averageResponseTime)
					.slice(0, parseInt(limit));

				ctx.body = {
					data: slowestEndpoints,
					meta: {
						total: slowestEndpoints.length,
						timeRange: { since, hours },
					},
				};
			} catch (_error) {
				ctx.throw(500, "Failed to get slowest endpoints");
			}
		},

		/**
		 * Get error rate analysis
		 */
		async getErrorAnalysis(ctx) {
			try {
				const { hours = 24 } = ctx.query;
				const since = new Date(Date.now() - hours * 60 * 60 * 1000);

				const metrics = await strapi.entityService.findMany(
					"api::api-performance.api-performance",
					{
						filters: {
							timestamp: {
								$gte: since.toISOString(),
							},
						},
					},
				);

				const errorAnalysis = this.analyzeErrors(
					Array.isArray(metrics) ? metrics : [metrics],
				);

				ctx.body = {
					data: errorAnalysis,
					meta: {
						timeRange: { since, hours },
					},
				};
			} catch (_error) {
				ctx.throw(500, "Failed to get error analysis");
			}
		},

		/**
		 * Get real-time performance data
		 */
		async getRealTimeMetrics(ctx) {
			try {
				const since = new Date(Date.now() - 5 * 60 * 1000); // Last 5 minutes

				const recentMetrics = await strapi.entityService.findMany(
					"api::api-performance.api-performance",
					{
						filters: {
							timestamp: {
								$gte: since.toISOString(),
							},
						},
						sort: "timestamp:desc",
						limit: 100,
					},
				);

				const realTimeStats = {
					requestsPerMinute: this.calculateRequestsPerMinute(
						Array.isArray(recentMetrics) ? recentMetrics : [recentMetrics],
					),
					averageResponseTime: this.calculateAverageResponseTime(
						Array.isArray(recentMetrics) ? recentMetrics : [recentMetrics],
					),
					errorRate: this.calculateErrorRate(
						Array.isArray(recentMetrics) ? recentMetrics : [recentMetrics],
					),
					activeEndpoints: this.getActiveEndpoints(
						Array.isArray(recentMetrics) ? recentMetrics : [recentMetrics],
					),
					lastUpdated: new Date().toISOString(),
				};

				ctx.body = {
					data: realTimeStats,
				};
			} catch (_error) {
				ctx.throw(500, "Failed to get real-time metrics");
			}
		},

		/**
		 * Calculate performance statistics
		 */
		calculatePerformanceStats(metrics: any[]): any {
			if (metrics.length === 0) {
				return {
					totalRequests: 0,
					averageResponseTime: 0,
					errorRate: 0,
					requestsPerHour: 0,
				};
			}

			const totalRequests = metrics.length;
			const errorRequests = metrics.filter((m) => m.statusCode >= 400).length;
			const averageResponseTime =
				metrics.reduce((sum, m) => sum + m.responseTime, 0) / totalRequests;
			const errorRate = (errorRequests / totalRequests) * 100;

			// Calculate requests per hour
			const timeSpan =
				new Date(metrics[0].timestamp).getTime() -
				new Date(metrics[metrics.length - 1].timestamp).getTime();
			const hours = timeSpan / (1000 * 60 * 60);
			const requestsPerHour = hours > 0 ? totalRequests / hours : 0;

			return {
				totalRequests,
				averageResponseTime: Math.round(averageResponseTime),
				errorRate: Math.round(errorRate * 100) / 100,
				requestsPerHour: Math.round(requestsPerHour),
				p95ResponseTime: this.calculatePercentile(
					metrics.map((m) => m.responseTime),
					95,
				),
				p99ResponseTime: this.calculatePercentile(
					metrics.map((m) => m.responseTime),
					99,
				),
			};
		},

		/**
		 * Group metrics by endpoint
		 */
		groupByEndpoint(metrics: any[]): any {
			const grouped = metrics.reduce((acc, metric) => {
				const key = `${metric.method} ${metric.endpoint}`;

				if (!acc[key]) {
					acc[key] = {
						method: metric.method,
						endpoint: metric.endpoint,
						requestCount: 0,
						totalResponseTime: 0,
						errorCount: 0,
						responseTimes: [],
					};
				}

				acc[key].requestCount++;
				acc[key].totalResponseTime += metric.responseTime;
				acc[key].responseTimes.push(metric.responseTime);

				if (metric.statusCode >= 400) {
					acc[key].errorCount++;
				}

				return acc;
			}, {});

			// Calculate averages and percentiles
			Object.keys(grouped).forEach((key) => {
				const stats = grouped[key];
				stats.averageResponseTime = Math.round(
					stats.totalResponseTime / stats.requestCount,
				);
				stats.errorRate =
					Math.round((stats.errorCount / stats.requestCount) * 100 * 100) / 100;
				stats.p95ResponseTime = this.calculatePercentile(
					stats.responseTimes,
					95,
				);
				stats.p99ResponseTime = this.calculatePercentile(
					stats.responseTimes,
					99,
				);

				// Clean up temporary data
				delete stats.totalResponseTime;
				delete stats.responseTimes;
			});

			return grouped;
		},

		/**
		 * Analyze error patterns
		 */
		analyzeErrors(metrics: any[]): any {
			const errors = metrics.filter((m) => m.statusCode >= 400);

			const errorsByStatus = errors.reduce((acc, error) => {
				acc[error.statusCode] = (acc[error.statusCode] || 0) + 1;
				return acc;
			}, {});

			const errorsByEndpoint = errors.reduce((acc, error) => {
				const key = `${error.method} ${error.endpoint}`;
				acc[key] = (acc[key] || 0) + 1;
				return acc;
			}, {});

			const errorsByHour = errors.reduce((acc, error) => {
				const hour = new Date(error.timestamp).getHours();
				acc[hour] = (acc[hour] || 0) + 1;
				return acc;
			}, {});

			return {
				totalErrors: errors.length,
				errorsByStatusCode: errorsByStatus,
				errorsByEndpoint: Object.entries(errorsByEndpoint)
					.map(([endpoint, count]) => ({ endpoint, count }))
					.sort((a: any, b: any) => b.count - a.count)
					.slice(0, 10),
				errorsByHour,
				commonErrorMessages: this.getCommonErrorMessages(errors),
			};
		},

		/**
		 * Get common error messages
		 */
		getCommonErrorMessages(errors: any[]): any[] {
			const errorMessages = errors
				.filter((e) => e.errorMessage)
				.reduce((acc, error) => {
					acc[error.errorMessage] = (acc[error.errorMessage] || 0) + 1;
					return acc;
				}, {});

			return Object.entries(errorMessages)
				.map(([message, count]) => ({ message, count }))
				.sort((a: any, b: any) => b.count - a.count)
				.slice(0, 5);
		},

		/**
		 * Calculate percentile
		 */
		calculatePercentile(values: number[], percentile: number): number {
			if (values.length === 0) return 0;

			const sorted = values.sort((a, b) => a - b);
			const index = Math.ceil((percentile / 100) * sorted.length) - 1;
			return sorted[index] || 0;
		},

		/**
		 * Calculate requests per minute
		 */
		calculateRequestsPerMinute(metrics: any[]): number {
			if (metrics.length === 0) return 0;

			const timeSpan =
				new Date(metrics[0].timestamp).getTime() -
				new Date(metrics[metrics.length - 1].timestamp).getTime();
			const minutes = timeSpan / (1000 * 60);
			return minutes > 0 ? Math.round(metrics.length / minutes) : 0;
		},

		/**
		 * Calculate average response time
		 */
		calculateAverageResponseTime(metrics: any[]): number {
			if (metrics.length === 0) return 0;
			return Math.round(
				metrics.reduce((sum, m) => sum + m.responseTime, 0) / metrics.length,
			);
		},

		/**
		 * Calculate error rate
		 */
		calculateErrorRate(metrics: any[]): number {
			if (metrics.length === 0) return 0;
			const errors = metrics.filter((m) => m.statusCode >= 400).length;
			return Math.round((errors / metrics.length) * 100 * 100) / 100;
		},

		/**
		 * Get active endpoints
		 */
		getActiveEndpoints(metrics: any[]): string[] {
			const endpoints = new Set(
				metrics.map((m) => `${m.method} ${m.endpoint}`),
			);
			return Array.from(endpoints);
		},
	}),
);
