{"kind": "collectionType", "collectionName": "api_performance", "info": {"singularName": "api-performance", "pluralName": "api-performances", "displayName": "API Performance", "description": "API endpoint performance monitoring"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"endpoint": {"type": "string", "required": true}, "method": {"type": "enumeration", "enum": ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS", "HEAD"], "required": true}, "responseTime": {"type": "integer", "required": true, "default": 0}, "statusCode": {"type": "integer", "required": true}, "timestamp": {"type": "datetime", "required": true}, "userAgent": {"type": "text"}, "ipAddress": {"type": "string"}, "requestSize": {"type": "integer", "default": 0}, "responseSize": {"type": "integer", "default": 0}, "errorMessage": {"type": "text"}}}