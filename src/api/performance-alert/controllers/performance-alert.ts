/**
 * Performance Alert controller
 */

import { factories } from "@strapi/strapi";

export default factories.createCoreController(
	"api::performance-alert.performance-alert",
	({ strapi }) => ({
		/**
		 * Get active alerts
		 */
		async getActiveAlerts(ctx) {
			try {
				const alerts = await strapi.entityService.findMany(
					"api::performance-alert.performance-alert",
					{
						filters: {
							status: "active",
						},
						sort: "timestamp:desc",
						limit: 50,
					},
				);

				ctx.body = {
					data: alerts,
					meta: {
						total: alerts.length,
					},
				};
			} catch (_error) {
				ctx.throw(500, "Failed to fetch active alerts");
			}
		},

		/**
		 * Acknowledge an alert
		 */
		async acknowledgeAlert(ctx) {
			try {
				const { id } = ctx.params;
				const { userId, notes } = ctx.request.body;

				const updatedAlert = await strapi.entityService.update(
					"api::performance-alert.performance-alert",
					id,
					{
						data: {
							status: "acknowledged",
							acknowledgedBy: userId,
							acknowledgedAt: new Date(),
							notes: notes || "",
						} as any,
					},
				);

				ctx.body = {
					data: updatedAlert,
				};
			} catch (_error) {
				ctx.throw(500, "Failed to acknowledge alert");
			}
		},

		/**
		 * Resolve an alert
		 */
		async resolveAlert(ctx) {
			try {
				const { id } = ctx.params;
				const { userId, notes } = ctx.request.body;

				const updatedAlert = await strapi.entityService.update(
					"api::performance-alert.performance-alert",
					id,
					{
						data: {
							status: "resolved",
							resolvedBy: userId,
							resolvedAt: new Date(),
							notes: notes || "",
						} as any,
					},
				);

				ctx.body = {
					data: updatedAlert,
				};
			} catch (_error) {
				ctx.throw(500, "Failed to resolve alert");
			}
		},

		/**
		 * Get alert statistics
		 */
		async getAlertStats(ctx) {
			try {
				const { startDate, endDate } = ctx.query;

				const filters: any = {};
				if (startDate && endDate) {
					filters.timestamp = {
						$gte: startDate,
						$lte: endDate,
					};
				}

				const alerts = await strapi.entityService.findMany(
					"api::performance-alert.performance-alert",
					{
						filters,
					},
				);

				const stats = {
					total: alerts.length,
					byType: {},
					bySeverity: {},
					byStatus: {},
					averageResolutionTime: 0,
				};

				let totalResolutionTime = 0;
				let resolvedCount = 0;

				alerts.forEach((alert) => {
					// Count by type
					stats.byType[alert.type] = (stats.byType[alert.type] || 0) + 1;

					// Count by severity
					stats.bySeverity[alert.severity] =
						(stats.bySeverity[alert.severity] || 0) + 1;

					// Count by status
					stats.byStatus[alert.status] =
						(stats.byStatus[alert.status] || 0) + 1;

					// Calculate resolution time
					if (alert.status === "resolved" && alert.resolvedAt) {
						const resolutionTime =
							new Date(alert.resolvedAt).getTime() -
							new Date(alert.timestamp).getTime();
						totalResolutionTime += resolutionTime;
						resolvedCount++;
					}
				});

				if (resolvedCount > 0) {
					stats.averageResolutionTime =
						totalResolutionTime / resolvedCount / (1000 * 60 * 60); // in hours
				}

				ctx.body = {
					data: stats,
				};
			} catch (_error) {
				ctx.throw(500, "Failed to get alert statistics");
			}
		},
	}),
);
