/**
 * Performance Alert router
 */

export default {
	routes: [
		{
			method: "GET",
			path: "/performance-alerts",
			handler: "performance-alert.find",
		},
		{
			method: "GET",
			path: "/performance-alerts/active",
			handler: "performance-alert.getActiveAlerts",
		},
		{
			method: "GET",
			path: "/performance-alerts/stats",
			handler: "performance-alert.getAlertStats",
		},
		{
			method: "GET",
			path: "/performance-alerts/:id",
			handler: "performance-alert.findOne",
		},
		{
			method: "POST",
			path: "/performance-alerts",
			handler: "performance-alert.create",
		},
		{
			method: "PUT",
			path: "/performance-alerts/:id",
			handler: "performance-alert.update",
		},
		{
			method: "PUT",
			path: "/performance-alerts/:id/acknowledge",
			handler: "performance-alert.acknowledgeAlert",
		},
		{
			method: "PUT",
			path: "/performance-alerts/:id/resolve",
			handler: "performance-alert.resolveAlert",
		},
		{
			method: "DELETE",
			path: "/performance-alerts/:id",
			handler: "performance-alert.delete",
		},
	],
};
