/**
 * Performance Alert service
 */

import { factories } from "@strapi/strapi";

export default factories.createCoreService(
	"api::performance-alert.performance-alert",
	({ strapi }) => ({
		/**
		 * Create a new performance alert
		 */
		async createAlert(alertData: any) {
			try {
				return await strapi.entityService.create(
					"api::performance-alert.performance-alert",
					{
						data: {
							...alertData,
							publishedAt: new Date(),
						},
					},
				);
			} catch (error) {
				strapi.log.error("Failed to create performance alert:", error);
				throw error;
			}
		},

		/**
		 * Get alerts by criteria
		 */
		async getAlertsByCriteria(criteria: any) {
			try {
				return await strapi.entityService.findMany(
					"api::performance-alert.performance-alert",
					{
						filters: criteria,
						sort: "timestamp:desc",
					},
				);
			} catch (error) {
				strapi.log.error("Failed to get alerts by criteria:", error);
				throw error;
			}
		},

		/**
		 * Update alert status
		 */
		async updateAlertStatus(
			alertId: number,
			status: string,
			userId?: string,
			notes?: string,
		) {
			try {
				const updateData: any = { status };

				if (status === "acknowledged") {
					updateData.acknowledgedBy = userId;
					updateData.acknowledgedAt = new Date();
				} else if (status === "resolved") {
					updateData.resolvedBy = userId;
					updateData.resolvedAt = new Date();
				}

				if (notes) {
					updateData.notes = notes;
				}

				return await strapi.entityService.update(
					"api::performance-alert.performance-alert",
					alertId,
					{
						data: updateData,
					},
				);
			} catch (error) {
				strapi.log.error("Failed to update alert status:", error);
				throw error;
			}
		},

		/**
		 * Clean up old resolved alerts
		 */
		async cleanupOldAlerts(daysToKeep: number = 30) {
			try {
				const cutoffDate = new Date();
				cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

				const oldAlerts = await strapi.entityService.findMany(
					"api::performance-alert.performance-alert",
					{
						filters: {
							status: "resolved",
							resolvedAt: {
								$lt: cutoffDate.toISOString(),
							},
						},
					},
				);

				const alertArray = Array.isArray(oldAlerts) ? oldAlerts : [oldAlerts];
				for (const alert of alertArray) {
					await strapi.entityService.delete(
						"api::performance-alert.performance-alert",
						alert.id,
					);
				}

				strapi.log.info(
					`Cleaned up ${oldAlerts.length} old performance alerts`,
				);
				return oldAlerts.length;
			} catch (error) {
				strapi.log.error("Failed to cleanup old alerts:", error);
				throw error;
			}
		},
	}),
);
