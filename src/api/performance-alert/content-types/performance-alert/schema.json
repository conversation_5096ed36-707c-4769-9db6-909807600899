{"kind": "collectionType", "collectionName": "performance_alerts", "info": {"singularName": "performance-alert", "pluralName": "performance-alerts", "displayName": "Performance Alert", "description": "Automated performance alerts from Adapty analytics integration"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"alertId": {"type": "string", "required": true, "unique": true}, "type": {"type": "enumeration", "enum": ["conversion_drop", "revenue_spike", "revenue_drop", "churn_increase", "trial_conversion_drop"], "required": true}, "severity": {"type": "enumeration", "enum": ["low", "medium", "high", "critical"], "required": true}, "message": {"type": "text", "required": true}, "currentValue": {"type": "decimal", "required": true}, "previousValue": {"type": "decimal", "required": true}, "threshold": {"type": "decimal", "required": true}, "timestamp": {"type": "datetime", "required": true}, "affectedMetrics": {"type": "json", "required": true}, "recommendedActions": {"type": "json", "required": true}, "status": {"type": "enumeration", "enum": ["active", "acknowledged", "resolved", "dismissed"], "default": "active", "required": true}, "acknowledgedBy": {"type": "string"}, "acknowledgedAt": {"type": "datetime"}, "resolvedBy": {"type": "string"}, "resolvedAt": {"type": "datetime"}, "notes": {"type": "text"}}}