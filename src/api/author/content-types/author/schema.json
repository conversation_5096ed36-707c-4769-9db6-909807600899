{"kind": "collectionType", "collectionName": "authors", "info": {"singularName": "author", "pluralName": "authors", "displayName": "Author", "description": "Create authors for your content"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"name": {"type": "string"}, "avatar": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos"]}, "email": {"type": "string"}, "articles": {"type": "relation", "relation": "oneToMany", "target": "api::article.article", "mappedBy": "author"}}}