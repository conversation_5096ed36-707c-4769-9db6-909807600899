{"kind": "collectionType", "collectionName": "translation_assignments", "info": {"singularName": "translation-assignment", "pluralName": "translation-assignments", "displayName": "Translation Assignment", "description": "Manages translation assignments and progress tracking"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"content_type": {"type": "string", "required": true, "maxLength": 255}, "entity_id": {"type": "integer", "required": true}, "source_locale": {"type": "string", "required": true, "maxLength": 10}, "target_locale": {"type": "string", "required": true, "maxLength": 10}, "status": {"type": "enumeration", "enum": ["pending", "assigned", "in_progress", "completed", "reviewed", "approved", "rejected"], "default": "pending"}, "priority": {"type": "enumeration", "enum": ["low", "medium", "high", "urgent"], "default": "medium"}, "assigned_translator": {"type": "relation", "relation": "manyToOne", "target": "admin::user"}, "assigned_reviewer": {"type": "relation", "relation": "manyToOne", "target": "admin::user"}, "deadline": {"type": "datetime", "required": false}, "estimated_words": {"type": "integer", "required": false, "min": 0}, "actual_words": {"type": "integer", "required": false, "min": 0}, "progress_percentage": {"type": "integer", "default": 0, "min": 0, "max": 100}, "fields_to_translate": {"type": "json", "required": false}, "completed_fields": {"type": "json", "required": false}, "notes": {"type": "text", "required": false}, "reviewer_notes": {"type": "text", "required": false}, "quality_score": {"type": "integer", "required": false, "min": 1, "max": 10}, "started_at": {"type": "datetime", "required": false}, "completed_at": {"type": "datetime", "required": false}, "reviewed_at": {"type": "datetime", "required": false}, "approved_at": {"type": "datetime", "required": false}, "translation_memory_matches": {"type": "json", "required": false}, "external_service_data": {"type": "json", "required": false}}}