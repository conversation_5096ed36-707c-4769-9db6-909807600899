{"kind": "collectionType", "collectionName": "cache_invalidations", "info": {"singularName": "cache-invalidation", "pluralName": "cache-invalidation-entries", "displayName": "Cache Invalidation", "description": "Tracking cache invalidation events and management operations"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"content_type": {"type": "string", "required": true}, "event": {"type": "enumeration", "enum": ["afterCreate", "afterUpdate", "afterDelete", "afterPublish", "afterUnpublish", "bulk_update", "manual_invalidation"], "required": true}, "tags_invalidated": {"type": "integer", "required": true, "default": 0}, "cache_keys": {"type": "json"}, "reason": {"type": "text"}, "triggered_by": {"type": "string"}, "success": {"type": "boolean", "default": true}, "error_message": {"type": "text"}, "processing_time": {"type": "integer", "default": 0}, "timestamp": {"type": "datetime", "required": true}}}