/**
 * Analytics Controller - Main analytics API endpoints
 */

import { analyticsService } from "../../../services/analytics/analytics-service";

export default {
	/**
	 * Get comprehensive dashboard data
	 */
	async dashboard(ctx) {
		try {
			const {
				dateRange,
				paywallIds,
				regions,
				locales,
				deviceTypes,
				platforms,
				userSegments,
			} = ctx.request.body;

			if (!dateRange || !dateRange.start || !dateRange.end) {
				return ctx.badRequest("Date range is required");
			}

			const filter = {
				dateRange: {
					start: new Date(dateRange.start),
					end: new Date(dateRange.end),
				},
				paywallIds,
				regions,
				locales,
				deviceTypes,
				platforms,
				userSegments,
			};

			const dashboardData = await analyticsService.getDashboardData(filter);

			return { data: dashboardData };
		} catch (error) {
			strapi.log.error("Error getting dashboard data:", error);
			return ctx.internalServerError("Failed to get dashboard data");
		}
	},

	/**
	 * Get real-time metrics
	 */
	async realtime(ctx) {
		try {
			const { paywallIds } = ctx.request.body;

			const realtimeMetrics =
				await analyticsService.getRealtimeMetrics(paywallIds);

			return { data: realtimeMetrics };
		} catch (error) {
			strapi.log.error("Error getting realtime metrics:", error);
			return ctx.internalServerError("Failed to get realtime metrics");
		}
	},

	/**
	 * Export analytics data
	 */
	async export(ctx) {
		try {
			const { format, dateRange, includeCharts, metrics, groupBy } =
				ctx.request.body;

			if (!format || !["csv", "json", "pdf"].includes(format)) {
				return ctx.badRequest("Invalid export format");
			}

			if (!dateRange || !dateRange.start || !dateRange.end) {
				return ctx.badRequest("Date range is required");
			}

			const exportOptions = {
				format,
				includeCharts: includeCharts || false,
				dateRange: {
					start: new Date(dateRange.start),
					end: new Date(dateRange.end),
				},
				metrics: metrics || ["impressions", "conversions", "revenue"],
				groupBy,
			};

			const exportData = await analyticsService.exportData(exportOptions);

			// Set appropriate headers based on format
			switch (format) {
				case "csv":
					ctx.set("Content-Type", "text/csv");
					ctx.set(
						"Content-Disposition",
						`attachment; filename="analytics-${new Date().toISOString().split("T")[0]}.csv"`,
					);
					break;
				case "json":
					ctx.set("Content-Type", "application/json");
					ctx.set(
						"Content-Disposition",
						`attachment; filename="analytics-${new Date().toISOString().split("T")[0]}.json"`,
					);
					break;
				case "pdf":
					ctx.set("Content-Type", "application/pdf");
					ctx.set(
						"Content-Disposition",
						`attachment; filename="analytics-${new Date().toISOString().split("T")[0]}.pdf"`,
					);
					break;
			}

			return exportData;
		} catch (error) {
			strapi.log.error("Error exporting analytics data:", error);
			return ctx.internalServerError("Failed to export analytics data");
		}
	},

	/**
	 * Record paywall metrics (for mobile apps)
	 */
	async recordMetrics(ctx) {
		try {
			const metricsData = ctx.request.body;

			// Validate required fields
			const requiredFields = [
				"paywallId",
				"impressions",
				"conversions",
				"region",
				"locale",
				"deviceType",
				"platform",
			];
			for (const field of requiredFields) {
				if (metricsData[field] === undefined) {
					return ctx.badRequest(`Missing required field: ${field}`);
				}
			}

			await analyticsService.recordPaywallMetrics(metricsData);

			return { success: true, message: "Metrics recorded successfully" };
		} catch (error) {
			strapi.log.error("Error recording metrics:", error);
			return ctx.internalServerError("Failed to record metrics");
		}
	},

	/**
	 * Record user engagement (for mobile apps)
	 */
	async recordEngagement(ctx) {
		try {
			const engagementData = ctx.request.body;

			// Validate required fields
			const requiredFields = [
				"paywallId",
				"sessionDuration",
				"pageViews",
				"clickThroughRate",
				"scrollDepth",
			];
			for (const field of requiredFields) {
				if (engagementData[field] === undefined) {
					return ctx.badRequest(`Missing required field: ${field}`);
				}
			}

			await analyticsService.recordUserEngagement(engagementData);

			return { success: true, message: "Engagement recorded successfully" };
		} catch (error) {
			strapi.log.error("Error recording engagement:", error);
			return ctx.internalServerError("Failed to record engagement");
		}
	},

	/**
	 * Get performance alerts
	 */
	async alerts(ctx) {
		try {
			const { query } = ctx;
			const { severity = "all", limit = 10 } = query;

			// This would implement alert logic based on performance thresholds
			const alerts = [
				{
					id: "1",
					type: "conversion_drop",
					severity: "high",
					message: "Conversion rate dropped by 15% in the last hour",
					paywallId: "paywall_123",
					timestamp: new Date(),
					threshold: 0.15,
					currentValue: 0.08,
					previousValue: 0.12,
				},
				{
					id: "2",
					type: "revenue_spike",
					severity: "medium",
					message: "Revenue increased by 25% compared to yesterday",
					paywallId: "paywall_456",
					timestamp: new Date(),
					threshold: 0.2,
					currentValue: 1250,
					previousValue: 1000,
				},
			];

			const filteredAlerts =
				severity === "all"
					? alerts
					: alerts.filter((alert) => alert.severity === severity);

			return {
				data: filteredAlerts.slice(0, parseInt(limit.toString())),
			};
		} catch (error) {
			strapi.log.error("Error getting alerts:", error);
			return ctx.internalServerError("Failed to get alerts");
		}
	},

	/**
	 * Get comparative analysis
	 */
	async compare(ctx) {
		try {
			const { paywallIds, dateRange1, dateRange2, metrics } = ctx.request.body;

			if (!paywallIds || !Array.isArray(paywallIds) || paywallIds.length < 2) {
				return ctx.badRequest(
					"At least 2 paywall IDs are required for comparison",
				);
			}

			if (!dateRange1 || !dateRange2) {
				return ctx.badRequest("Two date ranges are required for comparison");
			}

			// Get data for both periods
			const [period1Data, period2Data] = await Promise.all([
				analyticsService.getDashboardData({
					dateRange: {
						start: new Date(dateRange1.start),
						end: new Date(dateRange1.end),
					},
					paywallIds,
				}),
				analyticsService.getDashboardData({
					dateRange: {
						start: new Date(dateRange2.start),
						end: new Date(dateRange2.end),
					},
					paywallIds,
				}),
			]);

			// Calculate comparison metrics
			const comparison = {
				period1: period1Data,
				period2: period2Data,
				changes: {
					impressions: this.calculateChange(
						period1Data.overview.totalImpressions,
						period2Data.overview.totalImpressions,
					),
					conversions: this.calculateChange(
						period1Data.overview.totalConversions,
						period2Data.overview.totalConversions,
					),
					conversionRate: this.calculateChange(
						period1Data.overview.overallConversionRate,
						period2Data.overview.overallConversionRate,
					),
					revenue: this.calculateChange(
						period1Data.overview.totalRevenue,
						period2Data.overview.totalRevenue,
					),
					averageRevenuePerUser: this.calculateChange(
						period1Data.overview.averageRevenuePerUser,
						period2Data.overview.averageRevenuePerUser,
					),
				},
			};

			return { data: comparison };
		} catch (error) {
			strapi.log.error("Error performing comparison:", error);
			return ctx.internalServerError("Failed to perform comparison");
		}
	},

	/**
	 * Helper method to calculate percentage change
	 */
	calculateChange(current: number, previous: number) {
		if (previous === 0) return current > 0 ? 100 : 0;
		return ((current - previous) / previous) * 100;
	},
};
