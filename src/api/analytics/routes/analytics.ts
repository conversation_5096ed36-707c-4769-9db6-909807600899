/**
 * Analytics Routes
 */

export default {
	routes: [
		{
			method: "POST",
			path: "/analytics/dashboard",
			handler: "analytics.dashboard",
			config: {
				policies: [],
				middlewares: [],
			},
		},
		{
			method: "POST",
			path: "/analytics/realtime",
			handler: "analytics.realtime",
			config: {
				policies: [],
				middlewares: [],
			},
		},
		{
			method: "POST",
			path: "/analytics/export",
			handler: "analytics.export",
			config: {
				policies: [],
				middlewares: [],
			},
		},
		{
			method: "POST",
			path: "/analytics/record-metrics",
			handler: "analytics.recordMetrics",
			config: {
				policies: [],
				middlewares: [],
			},
		},
		{
			method: "POST",
			path: "/analytics/record-engagement",
			handler: "analytics.recordEngagement",
			config: {
				policies: [],
				middlewares: [],
			},
		},
		{
			method: "GET",
			path: "/analytics/alerts",
			handler: "analytics.alerts",
			config: {
				policies: [],
				middlewares: [],
			},
		},
		{
			method: "POST",
			path: "/analytics/compare",
			handler: "analytics.compare",
			config: {
				policies: [],
				middlewares: [],
			},
		},
	],
};
