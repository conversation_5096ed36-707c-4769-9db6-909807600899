{"kind": "collectionType", "collectionName": "analytics", "info": {"singularName": "analytics", "pluralName": "analytics-entries", "displayName": "Analytics", "description": "General analytics data storage for various metrics and events"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"event_type": {"type": "enumeration", "enum": ["paywall_impression", "user_engagement", "conversion", "interaction", "page_view", "session_start", "session_end"], "required": true}, "paywall_id": {"type": "string"}, "user_id": {"type": "string"}, "session_id": {"type": "string"}, "placement_id": {"type": "string"}, "app_id": {"type": "string"}, "platform": {"type": "enumeration", "enum": ["ios", "android", "web"], "required": true}, "device_type": {"type": "enumeration", "enum": ["mobile", "tablet", "desktop"], "required": true}, "region": {"type": "string", "required": true}, "locale": {"type": "string", "required": true, "default": "en"}, "timestamp": {"type": "datetime", "required": true}, "metadata": {"type": "json"}, "value": {"type": "decimal", "default": 0}, "count": {"type": "integer", "default": 1}}}