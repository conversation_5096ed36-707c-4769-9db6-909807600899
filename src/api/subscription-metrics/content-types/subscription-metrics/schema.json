{"kind": "collectionType", "collectionName": "subscription_metrics", "info": {"singularName": "subscription-metrics", "pluralName": "subscription-metrics-entries", "displayName": "Subscription Metrics", "description": "Subscription lifecycle data from Adapty integration"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"type": {"type": "enumeration", "enum": ["overall", "country", "product", "variation"], "required": true}, "identifier": {"type": "string", "required": true}, "timestamp": {"type": "datetime", "required": true}, "revenue": {"type": "decimal", "required": true, "default": 0}, "subscriptions": {"type": "integer", "required": true, "default": 0}, "trials": {"type": "integer", "required": true, "default": 0}, "conversions": {"type": "integer", "required": true, "default": 0}, "impressions": {"type": "integer", "required": true, "default": 0}, "conversionRate": {"type": "decimal", "required": true, "default": 0}, "trialConversionRate": {"type": "decimal", "required": true, "default": 0}, "averageRevenuePerUser": {"type": "decimal", "required": true, "default": 0}}}