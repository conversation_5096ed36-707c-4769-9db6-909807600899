{"kind": "collectionType", "collectionName": "paywalls", "info": {"singularName": "paywall", "pluralName": "paywalls", "displayName": "Paywall", "description": "Paywall configuration for Adapty integration"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"name": {"type": "string", "required": true, "maxLength": 255, "pluginOptions": {"i18n": {"localized": false}}}, "description": {"type": "text", "required": false, "pluginOptions": {"i18n": {"localized": true}}}, "placement_id": {"type": "string", "required": true, "unique": true, "maxLength": 100, "pluginOptions": {"i18n": {"localized": false}}}, "status": {"type": "enumeration", "enum": ["draft", "review", "approved", "published", "archived"], "default": "draft", "pluginOptions": {"i18n": {"localized": false}}}, "title": {"type": "string", "required": true, "maxLength": 255, "pluginOptions": {"i18n": {"localized": true}}}, "subtitle": {"type": "string", "required": false, "maxLength": 500, "pluginOptions": {"i18n": {"localized": true}}}, "description_text": {"type": "text", "required": false, "pluginOptions": {"i18n": {"localized": true}}}, "cta_text": {"type": "string", "required": true, "maxLength": 100, "pluginOptions": {"i18n": {"localized": true}}}, "cta_secondary_text": {"type": "string", "required": false, "maxLength": 200, "pluginOptions": {"i18n": {"localized": true}}}, "theme": {"type": "component", "repeatable": false, "required": true, "component": "shared.theme"}, "features": {"type": "component", "repeatable": true, "required": false, "component": "shared.feature"}, "testimonials": {"type": "component", "repeatable": true, "required": false, "component": "shared.testimonial"}, "product_labels": {"type": "component", "repeatable": true, "required": false, "component": "shared.product-label"}, "adapty_sync_status": {"type": "enumeration", "enum": ["pending", "synced", "error"], "default": "pending", "pluginOptions": {"i18n": {"localized": false}}}, "adapty_last_sync": {"type": "datetime", "required": false, "pluginOptions": {"i18n": {"localized": false}}}, "adapty_remote_config_id": {"type": "string", "required": false, "maxLength": 100, "pluginOptions": {"i18n": {"localized": false}}}, "remote_config_versions": {"type": "relation", "relation": "oneToMany", "target": "api::remote-config-version.remote-config-version", "mappedBy": "paywall"}}}