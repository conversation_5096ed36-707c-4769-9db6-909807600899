/**
 * paywall controller
 */

import { factories } from "@strapi/strapi";

export default factories.createCoreController(
	"api::paywall.paywall",
	({ strapi }) => ({
		/**
		 * Create a new paywall with validation
		 */
		async create(ctx) {
			const { data } = ctx.request.body;

			// Validate placement_id uniqueness
			if (data.placement_id) {
				const existingPaywall = await strapi.entityService.findMany(
					"api::paywall.paywall",
					{
						filters: { placement_id: data.placement_id },
						limit: 1,
					},
				);

				if (existingPaywall.length > 0) {
					return ctx.badRequest("Placement ID already exists");
				}
			}

			// Set default sync status
			data.adapty_sync_status = "pending";

			const response = await super.create(ctx);

			// Trigger sync with Adapty after creation
			// TODO: Implement Adapty sync service call

			return response;
		},

		/**
		 * Update paywall with sync status management
		 */
		async update(ctx) {
			const { id } = ctx.params;
			const { data } = ctx.request.body;

			// Reset sync status when content changes
			const contentFields = [
				"title",
				"subtitle",
				"description_text",
				"cta_text",
				"cta_secondary_text",
				"theme",
				"features",
				"testimonials",
				"product_labels",
			];
			const hasContentChanges = contentFields.some(
				(field) => data[field] !== undefined,
			);

			if (hasContentChanges) {
				data.adapty_sync_status = "pending";
				data.adapty_last_sync = null;
			}

			const response = await super.update(ctx);

			// Trigger sync with Adapty if content changed
			// TODO: Implement Adapty sync service call

			return response;
		},

		/**
		 * Get paywall with populated relations
		 */
		async findOne(ctx) {
			const { id } = ctx.params;

			const entity = await strapi.entityService.findOne(
				"api::paywall.paywall",
				id,
				{
					populate: {
						theme: {
							populate: ["background_image", "logo"],
						},
						features: {
							populate: ["icon_image"],
						},
						testimonials: {
							populate: ["author_avatar"],
						},
						product_labels: true,
						localizations: {
							populate: ["theme", "features", "testimonials", "product_labels"],
						},
					},
				},
			);

			const sanitizedEntity = await this.sanitizeOutput(entity, ctx);

			return this.transformResponse(sanitizedEntity);
		},

		/**
		 * Find paywalls with populated relations
		 */
		async find(ctx) {
			const { query } = ctx;

			const entities = await strapi.entityService.findMany(
				"api::paywall.paywall",
				{
					...query,
					populate: {
						theme: {
							populate: ["background_image", "logo"],
						},
						features: {
							populate: ["icon_image"],
						},
						testimonials: {
							populate: ["author_avatar"],
						},
						product_labels: true,
						localizations: true,
					},
				},
			);

			const sanitizedEntities = await this.sanitizeOutput(entities, ctx);

			return this.transformResponse(sanitizedEntities);
		},

		/**
		 * Get paywall by placement ID for mobile API
		 */
		async findByPlacement(ctx) {
			const { placementId } = ctx.params;
			const { locale = "en" } = ctx.query;

			const entity = await strapi.entityService.findMany(
				"api::paywall.paywall",
				{
					filters: { placement_id: placementId },
					locale,
					populate: {
						theme: {
							populate: ["background_image", "logo"],
						},
						features: {
							populate: ["icon_image"],
						},
						testimonials: {
							populate: ["author_avatar"],
						},
						product_labels: true,
					},
					limit: 1,
				},
			);

			if (!entity || entity.length === 0) {
				return ctx.notFound("Paywall not found");
			}

			const sanitizedEntity = await this.sanitizeOutput(entity[0], ctx);

			return this.transformResponse(sanitizedEntity);
		},
	}),
);
