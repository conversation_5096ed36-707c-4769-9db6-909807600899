/**
 * Custom paywall routes for mobile API
 */

export default {
	routes: [
		{
			method: "GET",
			path: "/paywalls/placement/:placementId",
			handler: "paywall.findByPlacement",
			config: {
				auth: false,
				policies: [],
				middlewares: ["api::paywall.mobile-cache"],
			},
		},
		{
			method: "GET",
			path: "/paywalls/placement/:placementId/locale/:locale",
			handler: "paywall.findByPlacement",
			config: {
				auth: false,
				policies: [],
				middlewares: ["api::paywall.mobile-cache"],
			},
		},
	],
};
