/**
 * Middleware for mobile API caching
 */

export default (_config, { strapi }) => {
	return async (ctx, next) => {
		// Set cache headers for mobile API responses
		ctx.set("Cache-Control", "public, max-age=300, stale-while-revalidate=60"); // 5 minutes cache
		ctx.set("Vary", "Accept-Language, Accept-Encoding");

		// Handle ETag for conditional requests
		const ifNoneMatch = ctx.get("If-None-Match");

		await next();

		// Add ETag to response if paywall data is present
		if (ctx.body?.data) {
			const paywall = ctx.body.data;
			const etag = strapi.service("api::paywall.paywall").generateETag(paywall);

			ctx.set("ETag", `"${etag}"`);

			// Return 304 if ETag matches
			if (ifNoneMatch === `"${etag}"`) {
				ctx.status = 304;
				ctx.body = null;
				return;
			}
		}
	};
};
