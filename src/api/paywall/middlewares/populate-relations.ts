/**
 * Middleware to populate paywall relations
 */

export default (_config, { strapi }) => {
	return async (ctx, next) => {
		// Add default population for paywall queries
		if (!ctx.query.populate) {
			ctx.query.populate = {
				theme: {
					populate: ["background_image", "logo"],
				},
				features: {
					populate: ["icon_image"],
				},
				testimonials: {
					populate: ["author_avatar"],
				},
				product_labels: true,
				localizations: true,
			};
		}

		await next();
	};
};
