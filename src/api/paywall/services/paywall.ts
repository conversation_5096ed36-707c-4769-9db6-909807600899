/**
 * paywall service
 */

import { factories } from "@strapi/strapi";

export default factories.createCoreService(
	"api::paywall.paywall",
	({ strapi }) => ({
		/**
		 * Find paywall by placement ID
		 */
		async findByPlacementId(placementId: string, locale: string = "en") {
			const entities = await strapi.entityService.findMany(
				"api::paywall.paywall",
				{
					filters: { placement_id: placementId },
					locale,
					populate: {
						theme: {
							populate: ["background_image", "logo"],
						},
						features: {
							populate: ["icon_image"],
						},
						testimonials: {
							populate: ["author_avatar"],
						},
						product_labels: true,
						localizations: true,
					},
					limit: 1,
				},
			);

			return entities.length > 0 ? entities[0] : null;
		},

		/**
		 * Get paywalls that need Adapty sync
		 */
		async findPendingSync() {
			return await strapi.entityService.findMany("api::paywall.paywall", {
				filters: { adapty_sync_status: "pending" },
				populate: {
					theme: true,
					features: true,
					testimonials: true,
					product_labels: true,
				},
			});
		},

		/**
		 * Update sync status
		 */
		async updateSyncStatus(
			id: number,
			status: "pending" | "synced" | "error",
			remoteConfigId?: string,
		) {
			const updateData: any = {
				adapty_sync_status: status,
				adapty_last_sync: new Date(),
			};

			if (remoteConfigId) {
				updateData.adapty_remote_config_id = remoteConfigId;
			}

			return await strapi.entityService.update("api::paywall.paywall", id, {
				data: updateData,
			});
		},

		/**
		 * Transform paywall data for mobile API response
		 */
		transformForMobileAPI(paywall: any) {
			return {
				paywall: {
					id: paywall.id.toString(),
					name: paywall.name,
					placement_id: paywall.placement_id,
					content: {
						title: paywall.title,
						subtitle: paywall.subtitle,
						description: paywall.description_text,
						cta_text: paywall.cta_text,
						cta_secondary_text: paywall.cta_secondary_text,
					},
					theme: this.transformThemeForAPI(paywall.theme),
					features: paywall.features?.map(this.transformFeatureForAPI) || [],
					testimonials:
						paywall.testimonials?.map(this.transformTestimonialForAPI) || [],
					product_labels: this.transformProductLabelsForAPI(
						paywall.product_labels || [],
					),
					layout: {
						show_features: paywall.theme?.show_features || true,
						show_testimonials: paywall.theme?.show_testimonials || false,
						header_style: paywall.theme?.header_style || "hero",
						product_display_style:
							paywall.theme?.product_display_style || "list",
					},
				},
				cache_info: {
					version: 1,
					etag: this.generateETag(paywall),
					expires_at: new Date(Date.now() + 5 * 60 * 1000).toISOString(), // 5 minutes
				},
			};
		},

		/**
		 * Transform theme for API response
		 */
		transformThemeForAPI(theme: any) {
			if (!theme) return null;

			return {
				primary_color: theme.primary_color,
				background_color: theme.background_color,
				text_color: theme.text_color,
				button_style: theme.button_style,
				gradient_colors: theme.gradient_colors,
				background_image: theme.background_image?.url || null,
				logo: theme.logo?.url || null,
				custom_styles: theme.custom_styles,
			};
		},

		/**
		 * Transform feature for API response
		 */
		transformFeatureForAPI(feature: any) {
			return {
				id: feature.id,
				icon: feature.icon,
				title: feature.title,
				description: feature.description,
				is_highlighted: feature.is_highlighted,
				icon_image: feature.icon_image?.url || null,
				order: feature.order,
			};
		},

		/**
		 * Transform testimonial for API response
		 */
		transformTestimonialForAPI(testimonial: any) {
			return {
				id: testimonial.id,
				author_name: testimonial.author_name,
				author_title: testimonial.author_title,
				content: testimonial.content,
				rating: testimonial.rating,
				company: testimonial.company,
				author_avatar: testimonial.author_avatar?.url || null,
				order: testimonial.order,
			};
		},

		/**
		 * Transform product labels for API response
		 */
		transformProductLabelsForAPI(productLabels: any[]) {
			const labels: Record<string, any> = {};

			productLabels.forEach((label) => {
				labels[label.product_id] = {
					badge_text: label.badge_text,
					badge_color: label.badge_color,
					subtitle: label.subtitle,
					highlight: label.highlight,
					savings_percentage: label.savings_percentage,
					badge_position: label.badge_position,
				};
			});

			return labels;
		},

		/**
		 * Generate ETag for caching
		 */
		generateETag(paywall: any): string {
			const content = JSON.stringify({
				id: paywall.id,
				updated_at: paywall.updated_at,
				adapty_last_sync: paywall.adapty_last_sync,
			});

			// Simple hash function for ETag
			let hash = 0;
			for (let i = 0; i < content.length; i++) {
				const char = content.charCodeAt(i);
				hash = (hash << 5) - hash + char;
				hash = hash & hash; // Convert to 32-bit integer
			}

			return Math.abs(hash).toString(16);
		},
	}),
);
