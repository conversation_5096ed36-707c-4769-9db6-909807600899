{"kind": "collectionType", "collectionName": "paywall_variations", "info": {"singularName": "paywall-variation", "pluralName": "paywall-variations", "displayName": "Paywall Variation", "description": "Individual variations for A/B testing with traffic allocation and performance tracking"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"name": {"type": "string", "required": true, "maxLength": 255, "pluginOptions": {"i18n": {"localized": false}}}, "description": {"type": "text", "required": false, "pluginOptions": {"i18n": {"localized": true}}}, "variation_type": {"type": "enumeration", "enum": ["control", "variant", "challenger"], "default": "variant", "required": true, "pluginOptions": {"i18n": {"localized": false}}}, "traffic_percentage": {"type": "decimal", "required": true, "default": 50.0, "min": 0.1, "max": 100.0, "pluginOptions": {"i18n": {"localized": false}}}, "ab_test": {"type": "relation", "relation": "manyToOne", "target": "api::ab-test.ab-test", "inversedBy": "variations"}, "base_paywall": {"type": "relation", "relation": "manyToOne", "target": "api::paywall.paywall"}, "title_override": {"type": "string", "required": false, "maxLength": 255, "pluginOptions": {"i18n": {"localized": true}}}, "subtitle_override": {"type": "string", "required": false, "maxLength": 500, "pluginOptions": {"i18n": {"localized": true}}}, "description_override": {"type": "text", "required": false, "pluginOptions": {"i18n": {"localized": true}}}, "cta_text_override": {"type": "string", "required": false, "maxLength": 100, "pluginOptions": {"i18n": {"localized": true}}}, "cta_secondary_text_override": {"type": "string", "required": false, "maxLength": 200, "pluginOptions": {"i18n": {"localized": true}}}, "theme_override": {"type": "component", "repeatable": false, "required": false, "component": "shared.theme"}, "features_override": {"type": "component", "repeatable": true, "required": false, "component": "shared.feature"}, "testimonials_override": {"type": "component", "repeatable": true, "required": false, "component": "shared.testimonial"}, "product_labels_override": {"type": "component", "repeatable": true, "required": false, "component": "shared.product-label"}, "pricing_override": {"type": "json", "required": false, "pluginOptions": {"i18n": {"localized": false}}}, "custom_properties": {"type": "json", "required": false, "pluginOptions": {"i18n": {"localized": false}}}, "performance_metrics": {"type": "json", "required": false, "pluginOptions": {"i18n": {"localized": false}}}, "conversion_rate": {"type": "decimal", "required": false, "min": 0.0, "max": 100.0, "pluginOptions": {"i18n": {"localized": false}}}, "revenue_per_user": {"type": "decimal", "required": false, "min": 0.0, "pluginOptions": {"i18n": {"localized": false}}}, "total_impressions": {"type": "biginteger", "required": false, "min": 0, "pluginOptions": {"i18n": {"localized": false}}}, "total_conversions": {"type": "biginteger", "required": false, "min": 0, "pluginOptions": {"i18n": {"localized": false}}}, "total_revenue": {"type": "decimal", "required": false, "min": 0.0, "pluginOptions": {"i18n": {"localized": false}}}, "unique_users": {"type": "biginteger", "required": false, "min": 0, "pluginOptions": {"i18n": {"localized": false}}}, "confidence_interval_lower": {"type": "decimal", "required": false, "pluginOptions": {"i18n": {"localized": false}}}, "confidence_interval_upper": {"type": "decimal", "required": false, "pluginOptions": {"i18n": {"localized": false}}}, "statistical_significance": {"type": "decimal", "required": false, "min": 0.0, "max": 100.0, "pluginOptions": {"i18n": {"localized": false}}}, "is_winner": {"type": "boolean", "default": false, "pluginOptions": {"i18n": {"localized": false}}}, "promoted_at": {"type": "datetime", "required": false, "pluginOptions": {"i18n": {"localized": false}}}, "adapty_variation_id": {"type": "string", "required": false, "maxLength": 100, "pluginOptions": {"i18n": {"localized": false}}}, "adapty_remote_config_id": {"type": "string", "required": false, "maxLength": 100, "pluginOptions": {"i18n": {"localized": false}}}, "last_metrics_update": {"type": "datetime", "required": false, "pluginOptions": {"i18n": {"localized": false}}}, "notes": {"type": "text", "required": false, "pluginOptions": {"i18n": {"localized": true}}}}}