{"kind": "collectionType", "collectionName": "cache_metrics", "info": {"singularName": "cache-metrics", "pluralName": "cache-metrics-entries", "displayName": "<PERSON><PERSON>", "description": "Analytics data for cache performance tracking and monitoring"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"endpoint": {"type": "string", "required": true}, "method": {"type": "enumeration", "enum": ["GET", "POST", "PUT", "DELETE", "PATCH"], "required": true}, "cache_type": {"type": "enumeration", "enum": ["redis", "memory", "cdn", "database"], "required": true}, "app_id": {"type": "string"}, "response_time": {"type": "integer", "required": true, "default": 0}, "cache_ttl": {"type": "integer", "required": true, "default": 300}, "cache_hit": {"type": "boolean", "default": false}, "cache_size": {"type": "integer", "default": 0}, "timestamp": {"type": "datetime", "required": true}}}