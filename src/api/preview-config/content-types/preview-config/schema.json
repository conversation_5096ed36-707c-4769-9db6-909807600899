{"kind": "collectionType", "collectionName": "preview_configs", "info": {"singularName": "preview-config", "pluralName": "preview-configs", "displayName": "Preview Config", "description": "Store preview configurations for testing remote configs before deployment"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"preview_id": {"type": "string", "required": true, "unique": true}, "paywall": {"type": "relation", "relation": "manyToOne", "target": "api::paywall.paywall"}, "locale": {"type": "string", "default": "en"}, "config_data": {"type": "json", "required": true}, "expires_at": {"type": "datetime", "required": true}, "access_count": {"type": "integer", "default": 0}, "last_accessed": {"type": "datetime"}, "created_by": {"type": "string"}}}