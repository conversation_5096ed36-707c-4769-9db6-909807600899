/**
 * Product router
 */

export default {
    routes: [
        {
            method: 'POST',
            path: '/products/sync-from-adapty',
            handler: 'product.syncFromAdapty',
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: 'GET',
            path: '/products/with-sync-status',
            handler: 'product.findWithSyncStatus',
            config: {
                policies: [],
                middlewares: [],
            },
        },
    ],
};