{"kind": "collectionType", "collectionName": "products", "info": {"singularName": "product", "pluralName": "products", "displayName": "Product", "description": "Products synced from Adapty for paywall configuration"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"adapty_product_id": {"type": "string", "required": true, "unique": true, "maxLength": 100}, "vendor_product_id": {"type": "string", "required": true, "maxLength": 255}, "name": {"type": "string", "required": true, "maxLength": 255}, "type": {"type": "enumeration", "enum": ["subscription", "consumable", "non_consumable"], "required": true}, "store": {"type": "enumeration", "enum": ["app_store", "play_store", "stripe"], "required": true}, "price_amount": {"type": "decimal", "required": false}, "price_currency": {"type": "string", "required": false, "maxLength": 3}, "price_localized": {"type": "string", "required": false, "maxLength": 50}, "subscription_period_unit": {"type": "enumeration", "enum": ["day", "week", "month", "year"], "required": false}, "subscription_period_count": {"type": "integer", "required": false, "min": 1}, "last_synced": {"type": "datetime", "required": false}, "is_active": {"type": "boolean", "default": true}, "metadata": {"type": "json", "required": false}}}