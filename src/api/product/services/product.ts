/**
 * Product service
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreService('api::product.product', ({ strapi }) => ({
    /**
     * Find product by Adapty product ID
     */
    async findByAdaptyId(adaptyProductId: string) {
        const products = await strapi.entityService.findMany('api::product.product', {
            filters: {
                adapty_product_id: adaptyProductId
            }
        });

        return products.length > 0 ? products[0] : null;
    },

    /**
     * Update product sync status
     */
    async updateSyncStatus(productId: number, status: 'synced' | 'error', errorMessage?: string) {
        return await strapi.entityService.update('api::product.product', productId, {
            data: {
                last_synced: new Date(),
                // Note: sync status is tracked in sync-status content type
            } as any
        });
    },

    /**
     * Get products that need syncing
     */
    async findOutdated(maxAge: number = 24 * 60 * 60 * 1000) { // 24 hours default
        const cutoffDate = new Date(Date.now() - maxAge);

        return await strapi.entityService.findMany('api::product.product', {
            filters: {
                $or: [
                    { last_synced: { $null: true } },
                    { last_synced: { $lt: cutoffDate } }
                ]
            } as any
        });
    },

    /**
     * Bulk update products
     */
    async bulkUpdate(updates: Array<{ id: number; data: any }>) {
        const results = [];

        for (const update of updates) {
            try {
                const result = await strapi.entityService.update('api::product.product', update.id, {
                    data: update.data
                });
                results.push({ id: update.id, success: true, data: result });
            } catch (error) {
                results.push({ id: update.id, success: false, error: error.message });
            }
        }

        return results;
    }
}));