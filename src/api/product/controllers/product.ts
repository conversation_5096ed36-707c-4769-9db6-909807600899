/**
 * Product controller
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreController('api::product.product', ({ strapi }) => ({
    /**
     * Sync products from Adapty
     */
    async syncFromAdapty(ctx) {
        try {
            const { adaptyIntegration } = require('../../../services/adapty/strapi-integration');

            const result = await adaptyIntegration.syncProductsFromAdapty();

            ctx.body = {
                success: result.success,
                synced_count: result.synced_count,
                errors: result.errors
            };
        } catch (error) {
            strapi.log.error('Product sync controller error:', error);
            ctx.throw(500, 'Failed to sync products from Adapty');
        }
    },

    /**
     * Get products with sync status
     */
    async findWithSyncStatus(ctx) {
        try {
            const products = await strapi.entityService.findMany('api::product.product', {
                ...ctx.query,
                populate: {
                    sync_status: true
                }
            });

            ctx.body = products;
        } catch (error) {
            strapi.log.error('Product find with sync status error:', error);
            ctx.throw(500, 'Failed to fetch products with sync status');
        }
    }
}));