/**
 * Chrome MCP Monitoring API Routes
 * API endpoints for Chrome MCP health monitoring dashboard
 * Implements Story 2.1: Chrome MCP Server Health Monitoring Dashboard
 */

export default {
  routes: [
    {
      method: 'GET',
      path: '/chrome-mcp-monitoring/status',
      handler: 'monitoring.getStatus',
      config: {
        policies: [],
        middlewares: [],
        auth: false, // Allow access for monitoring purposes
      },
    },
    {
      method: 'GET',
      path: '/chrome-mcp-monitoring/metrics',
      handler: 'monitoring.getMetrics',
      config: {
        policies: [],
        middlewares: [],
        auth: false,
      },
    },
    {
      method: 'GET',
      path: '/chrome-mcp-monitoring/metrics/current',
      handler: 'monitoring.getCurrentMetrics',
      config: {
        policies: [],
        middlewares: [],
        auth: false,
      },
    },
    {
      method: 'GET',
      path: '/chrome-mcp-monitoring/metrics/historical',
      handler: 'monitoring.getHistoricalMetrics',
      config: {
        policies: [],
        middlewares: [],
        auth: false,
      },
    },
    {
      method: 'GET',
      path: '/chrome-mcp-monitoring/alerts',
      handler: 'monitoring.getAlerts',
      config: {
        policies: [],
        middlewares: [],
        auth: false,
      },
    },
    {
      method: 'POST',
      path: '/chrome-mcp-monitoring/start',
      handler: 'monitoring.startMonitoring',
      config: {
        policies: [],
        middlewares: [],
        auth: false,
      },
    },
    {
      method: 'POST',
      path: '/chrome-mcp-monitoring/stop',
      handler: 'monitoring.stopMonitoring',
      config: {
        policies: [],
        middlewares: [],
        auth: false,
      },
    },
    {
      method: 'POST',
      path: '/chrome-mcp-monitoring/test',
      handler: 'monitoring.runHealthTest',
      config: {
        policies: [],
        middlewares: [],
        auth: false,
      },
    },
  ],
};
