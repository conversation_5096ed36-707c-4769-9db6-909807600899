/**
 * Chrome MCP Monitoring Service
 * Backend service for Chrome MCP health monitoring dashboard
 * Implements Story 2.1: Chrome MCP Server Health Monitoring Dashboard
 */

import type { Core } from '@strapi/strapi';

interface ChromeMCPHealthMetrics {
  id: string;
  timestamp: Date;
  server_status: 'healthy' | 'degraded' | 'unhealthy';
  uptime_seconds: number;
  response_time_ms: number;
  memory_usage_mb: number;
  cpu_usage_percent: number;
  active_connections: number;
  connection_success_rate: number;
  error_count: number;
  test_results?: {
    navigation_test: boolean;
    content_retrieval_test: boolean;
    screenshot_test: boolean;
    health_check_test: boolean;
  };
}

interface MonitoringAlert {
  id: string;
  type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: Date;
  resolved: boolean;
}

class ChromeMCPMonitoringService {
  private strapi: Core.Strapi;
  private metricsHistory: ChromeMCPHealthMetrics[] = [];
  private activeAlerts: MonitoringAlert[] = [];
  private isMonitoring: boolean = false;
  private monitoringInterval: NodeJS.Timeout | null = null;

  constructor(strapi: Core.Strapi) {
    this.strapi = strapi;
  }

  /**
   * Start real-time monitoring
   */
  async startMonitoring(): Promise<void> {
    if (this.isMonitoring) {
      return;
    }

    this.isMonitoring = true;
    console.log('🚀 Starting Chrome MCP health monitoring...');

    // Initial health check
    await this.collectHealthMetrics();

    // Set up periodic monitoring (every 30 seconds as per Story 2.1)
    this.monitoringInterval = setInterval(async () => {
      try {
        await this.collectHealthMetrics();
      } catch (error) {
        console.error('Error collecting Chrome MCP health metrics:', error);
      }
    }, 30000); // 30 seconds

    console.log('✅ Chrome MCP monitoring started');
  }

  /**
   * Stop monitoring
   */
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    this.isMonitoring = false;
    console.log('🛑 Chrome MCP monitoring stopped');
  }

  /**
   * Collect current health metrics
   */
  async collectHealthMetrics(): Promise<ChromeMCPHealthMetrics> {
    try {
      // Simulate Chrome MCP health check operations
      const healthCheckResults = await this.performHealthChecks();
      
      // Calculate metrics based on health check results
      const metrics: ChromeMCPHealthMetrics = {
        id: `metrics_${Date.now()}`,
        timestamp: new Date(),
        server_status: this.calculateServerStatus(healthCheckResults),
        uptime_seconds: Math.floor(Date.now() / 1000), // Simplified uptime
        response_time_ms: healthCheckResults.averageResponseTime,
        memory_usage_mb: this.getMemoryUsage(),
        cpu_usage_percent: this.getCPUUsage(),
        active_connections: healthCheckResults.activeConnections,
        connection_success_rate: healthCheckResults.successRate,
        error_count: healthCheckResults.errorCount,
        test_results: healthCheckResults.testResults
      };

      // Store metrics (keep last 100 entries)
      this.metricsHistory.push(metrics);
      if (this.metricsHistory.length > 100) {
        this.metricsHistory = this.metricsHistory.slice(-100);
      }

      // Check for alerts
      await this.checkAlertConditions(metrics);

      return metrics;

    } catch (error) {
      console.error('Failed to collect Chrome MCP health metrics:', error);
      
      // Return error state metrics
      const errorMetrics: ChromeMCPHealthMetrics = {
        id: `error_${Date.now()}`,
        timestamp: new Date(),
        server_status: 'unhealthy',
        uptime_seconds: 0,
        response_time_ms: 0,
        memory_usage_mb: 0,
        cpu_usage_percent: 0,
        active_connections: 0,
        connection_success_rate: 0,
        error_count: 1
      };

      this.metricsHistory.push(errorMetrics);
      return errorMetrics;
    }
  }

  /**
   * Perform health checks
   */
  private async performHealthChecks(): Promise<{
    averageResponseTime: number;
    activeConnections: number;
    successRate: number;
    errorCount: number;
    testResults: {
      navigation_test: boolean;
      content_retrieval_test: boolean;
      screenshot_test: boolean;
      health_check_test: boolean;
    };
  }> {
    const healthChecks = [];
    let errorCount = 0;
    const testResults = {
      navigation_test: false,
      content_retrieval_test: false,
      screenshot_test: false,
      health_check_test: false
    };

    // Simulate navigation test
    try {
      const navStart = Date.now();
      await this.simulateNavigationTest();
      healthChecks.push(Date.now() - navStart);
      testResults.navigation_test = true;
    } catch (error) {
      errorCount++;
      healthChecks.push(5000); // Timeout value
    }

    // Simulate content retrieval test
    try {
      const contentStart = Date.now();
      await this.simulateContentRetrievalTest();
      healthChecks.push(Date.now() - contentStart);
      testResults.content_retrieval_test = true;
    } catch (error) {
      errorCount++;
      healthChecks.push(3000); // Timeout value
    }

    // Simulate screenshot test
    try {
      const screenshotStart = Date.now();
      await this.simulateScreenshotTest();
      healthChecks.push(Date.now() - screenshotStart);
      testResults.screenshot_test = true;
    } catch (error) {
      errorCount++;
      healthChecks.push(2000); // Timeout value
    }

    // Simulate general health check
    try {
      const healthStart = Date.now();
      await this.simulateHealthEndpointCheck();
      healthChecks.push(Date.now() - healthStart);
      testResults.health_check_test = true;
    } catch (error) {
      errorCount++;
      healthChecks.push(1000); // Timeout value
    }

    const averageResponseTime = healthChecks.reduce((sum, time) => sum + time, 0) / healthChecks.length;
    const successfulTests = Object.values(testResults).filter(result => result).length;
    const successRate = successfulTests / Object.keys(testResults).length;

    return {
      averageResponseTime: Math.floor(averageResponseTime),
      activeConnections: Math.floor(5 + Math.random() * 10), // Simulated
      successRate,
      errorCount,
      testResults
    };
  }

  /**
   * Calculate server status based on health check results
   */
  private calculateServerStatus(healthResults: any): 'healthy' | 'degraded' | 'unhealthy' {
    const { successRate, averageResponseTime, errorCount } = healthResults;

    if (successRate >= 0.95 && averageResponseTime < 1000 && errorCount === 0) {
      return 'healthy';
    } else if (successRate >= 0.80 && averageResponseTime < 3000 && errorCount <= 2) {
      return 'degraded';
    } else {
      return 'unhealthy';
    }
  }

  /**
   * Check alert conditions
   */
  private async checkAlertConditions(metrics: ChromeMCPHealthMetrics): Promise<void> {
    const alerts: MonitoringAlert[] = [];

    // Response time alert
    if (metrics.response_time_ms > 2000) {
      alerts.push({
        id: `alert_response_time_${Date.now()}`,
        type: 'High Response Time',
        severity: metrics.response_time_ms > 5000 ? 'critical' : 'high',
        message: `Chrome MCP response time is ${metrics.response_time_ms}ms (threshold: 2000ms)`,
        timestamp: new Date(),
        resolved: false
      });
    }

    // Connection success rate alert
    if (metrics.connection_success_rate < 0.90) {
      alerts.push({
        id: `alert_connection_rate_${Date.now()}`,
        type: 'Low Connection Success Rate',
        severity: metrics.connection_success_rate < 0.75 ? 'critical' : 'high',
        message: `Connection success rate is ${(metrics.connection_success_rate * 100).toFixed(1)}% (threshold: 90%)`,
        timestamp: new Date(),
        resolved: false
      });
    }

    // Error count alert
    if (metrics.error_count > 0) {
      alerts.push({
        id: `alert_errors_${Date.now()}`,
        type: 'Errors Detected',
        severity: metrics.error_count > 5 ? 'critical' : 'medium',
        message: `${metrics.error_count} error(s) detected in Chrome MCP operations`,
        timestamp: new Date(),
        resolved: false
      });
    }

    // Server status alert
    if (metrics.server_status !== 'healthy') {
      alerts.push({
        id: `alert_server_status_${Date.now()}`,
        type: 'Server Status',
        severity: metrics.server_status === 'unhealthy' ? 'critical' : 'high',
        message: `Chrome MCP server status is ${metrics.server_status}`,
        timestamp: new Date(),
        resolved: false
      });
    }

    // Add new alerts
    this.activeAlerts.push(...alerts);

    // Keep only recent alerts (last 50)
    if (this.activeAlerts.length > 50) {
      this.activeAlerts = this.activeAlerts.slice(-50);
    }
  }

  /**
   * Get current metrics
   */
  getCurrentMetrics(): ChromeMCPHealthMetrics | null {
    return this.metricsHistory.length > 0 ? this.metricsHistory[this.metricsHistory.length - 1] : null;
  }

  /**
   * Get historical metrics
   */
  getHistoricalMetrics(limit: number = 20): ChromeMCPHealthMetrics[] {
    return this.metricsHistory.slice(-limit);
  }

  /**
   * Get active alerts
   */
  getActiveAlerts(): MonitoringAlert[] {
    return this.activeAlerts.filter(alert => !alert.resolved);
  }

  /**
   * Get monitoring status
   */
  getMonitoringStatus(): { isActive: boolean; uptime: number; metricsCount: number } {
    return {
      isActive: this.isMonitoring,
      uptime: this.isMonitoring ? Math.floor(Date.now() / 1000) : 0,
      metricsCount: this.metricsHistory.length
    };
  }

  // Simulation methods (in real implementation, these would call actual Chrome MCP functions)
  private async simulateNavigationTest(): Promise<void> {
    await this.sleep(800 + Math.random() * 400);
    if (Math.random() < 0.05) throw new Error('Navigation test failed');
  }

  private async simulateContentRetrievalTest(): Promise<void> {
    await this.sleep(500 + Math.random() * 300);
    if (Math.random() < 0.03) throw new Error('Content retrieval test failed');
  }

  private async simulateScreenshotTest(): Promise<void> {
    await this.sleep(600 + Math.random() * 200);
    if (Math.random() < 0.02) throw new Error('Screenshot test failed');
  }

  private async simulateHealthEndpointCheck(): Promise<void> {
    await this.sleep(100 + Math.random() * 100);
    if (Math.random() < 0.01) throw new Error('Health endpoint check failed');
  }

  private getMemoryUsage(): number {
    // Simulate memory usage (400-800 MB)
    return Math.floor(400 + Math.random() * 400);
  }

  private getCPUUsage(): number {
    // Simulate CPU usage (10-60%)
    return Math.floor(10 + Math.random() * 50);
  }

  private async sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

export default ChromeMCPMonitoringService;
