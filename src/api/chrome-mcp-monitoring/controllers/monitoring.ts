/**
 * Chrome MCP Monitoring Controller
 * API controller for Chrome MCP health monitoring dashboard
 * Implements Story 2.1: Chrome MCP Server Health Monitoring Dashboard
 */

import type { Core } from '@strapi/strapi';
import ChromeMCPMonitoringService from '../services/monitoring';

// Global monitoring service instance
let monitoringService: ChromeMCPMonitoringService | null = null;

const getMonitoringService = (strapi: Core.Strapi): ChromeMCPMonitoringService => {
  if (!monitoringService) {
    monitoringService = new ChromeMCPMonitoringService(strapi);
  }
  return monitoringService;
};

export default ({ strapi }: { strapi: Core.Strapi }) => ({
  /**
   * Get monitoring status
   * GET /api/chrome-mcp-monitoring/status
   */
  async getStatus(ctx: any) {
    try {
      const service = getMonitoringService(strapi);
      const status = service.getMonitoringStatus();
      const currentMetrics = service.getCurrentMetrics();

      ctx.body = {
        success: true,
        data: {
          monitoring: status,
          server: currentMetrics ? {
            status: currentMetrics.server_status,
            uptime: currentMetrics.uptime_seconds,
            lastCheck: currentMetrics.timestamp,
            responseTime: currentMetrics.response_time_ms,
            errorCount: currentMetrics.error_count
          } : null
        },
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error getting monitoring status:', error);
      ctx.status = 500;
      ctx.body = {
        success: false,
        error: 'Failed to get monitoring status',
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  },

  /**
   * Get all metrics (current + historical)
   * GET /api/chrome-mcp-monitoring/metrics
   */
  async getMetrics(ctx: any) {
    try {
      const service = getMonitoringService(strapi);
      const currentMetrics = service.getCurrentMetrics();
      const historicalMetrics = service.getHistoricalMetrics(20);
      const alerts = service.getActiveAlerts();

      ctx.body = {
        success: true,
        data: {
          current: currentMetrics,
          historical: historicalMetrics,
          alerts: alerts,
          summary: {
            totalMetrics: historicalMetrics.length,
            activeAlerts: alerts.length,
            lastUpdated: currentMetrics?.timestamp || null
          }
        },
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error getting metrics:', error);
      ctx.status = 500;
      ctx.body = {
        success: false,
        error: 'Failed to get metrics',
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  },

  /**
   * Get current metrics only
   * GET /api/chrome-mcp-monitoring/metrics/current
   */
  async getCurrentMetrics(ctx: any) {
    try {
      const service = getMonitoringService(strapi);
      const currentMetrics = service.getCurrentMetrics();

      if (!currentMetrics) {
        ctx.status = 404;
        ctx.body = {
          success: false,
          error: 'No current metrics available',
          message: 'Monitoring may not be started yet'
        };
        return;
      }

      ctx.body = {
        success: true,
        data: currentMetrics,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error getting current metrics:', error);
      ctx.status = 500;
      ctx.body = {
        success: false,
        error: 'Failed to get current metrics',
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  },

  /**
   * Get historical metrics
   * GET /api/chrome-mcp-monitoring/metrics/historical?limit=20
   */
  async getHistoricalMetrics(ctx: any) {
    try {
      const limit = parseInt(ctx.query.limit) || 20;
      const service = getMonitoringService(strapi);
      const historicalMetrics = service.getHistoricalMetrics(limit);

      ctx.body = {
        success: true,
        data: {
          metrics: historicalMetrics,
          count: historicalMetrics.length,
          limit: limit
        },
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error getting historical metrics:', error);
      ctx.status = 500;
      ctx.body = {
        success: false,
        error: 'Failed to get historical metrics',
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  },

  /**
   * Get active alerts
   * GET /api/chrome-mcp-monitoring/alerts
   */
  async getAlerts(ctx: any) {
    try {
      const service = getMonitoringService(strapi);
      const alerts = service.getActiveAlerts();

      ctx.body = {
        success: true,
        data: {
          alerts: alerts,
          count: alerts.length,
          severityCounts: {
            critical: alerts.filter(a => a.severity === 'critical').length,
            high: alerts.filter(a => a.severity === 'high').length,
            medium: alerts.filter(a => a.severity === 'medium').length,
            low: alerts.filter(a => a.severity === 'low').length
          }
        },
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error getting alerts:', error);
      ctx.status = 500;
      ctx.body = {
        success: false,
        error: 'Failed to get alerts',
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  },

  /**
   * Start monitoring
   * POST /api/chrome-mcp-monitoring/start
   */
  async startMonitoring(ctx: any) {
    try {
      const service = getMonitoringService(strapi);
      await service.startMonitoring();

      ctx.body = {
        success: true,
        message: 'Chrome MCP monitoring started successfully',
        data: {
          status: service.getMonitoringStatus()
        },
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error starting monitoring:', error);
      ctx.status = 500;
      ctx.body = {
        success: false,
        error: 'Failed to start monitoring',
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  },

  /**
   * Stop monitoring
   * POST /api/chrome-mcp-monitoring/stop
   */
  async stopMonitoring(ctx: any) {
    try {
      const service = getMonitoringService(strapi);
      service.stopMonitoring();

      ctx.body = {
        success: true,
        message: 'Chrome MCP monitoring stopped successfully',
        data: {
          status: service.getMonitoringStatus()
        },
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error stopping monitoring:', error);
      ctx.status = 500;
      ctx.body = {
        success: false,
        error: 'Failed to stop monitoring',
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  },

  /**
   * Run health test
   * POST /api/chrome-mcp-monitoring/test
   */
  async runHealthTest(ctx: any) {
    try {
      const service = getMonitoringService(strapi);
      const metrics = await service.collectHealthMetrics();

      ctx.body = {
        success: true,
        message: 'Health test completed successfully',
        data: {
          metrics: metrics,
          testResults: metrics.test_results,
          summary: {
            status: metrics.server_status,
            responseTime: metrics.response_time_ms,
            successRate: metrics.connection_success_rate,
            errorCount: metrics.error_count
          }
        },
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error running health test:', error);
      ctx.status = 500;
      ctx.body = {
        success: false,
        error: 'Failed to run health test',
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
});
