/**
 * Sync Operation router
 */

export default {
	routes: [
		{
			method: "GET",
			path: "/sync-operations",
			handler: "sync-operation.find",
		},
		{
			method: "GET",
			path: "/sync-operations/status",
			handler: "sync-operation.getSyncStatus",
		},
		{
			method: "GET",
			path: "/sync-operations/running",
			handler: "sync-operation.getRunningSyncs",
		},
		{
			method: "GET",
			path: "/sync-operations/history",
			handler: "sync-operation.getSyncHistory",
		},
		{
			method: "GET",
			path: "/sync-operations/analytics",
			handler: "sync-operation.getSyncAnalytics",
		},
		{
			method: "GET",
			path: "/sync-operations/:id",
			handler: "sync-operation.findOne",
		},
		{
			method: "POST",
			path: "/sync-operations",
			handler: "sync-operation.create",
		},
		{
			method: "PUT",
			path: "/sync-operations/:id",
			handler: "sync-operation.update",
		},
		{
			method: "PUT",
			path: "/sync-operations/:id/stop",
			handler: "sync-operation.stopSyncOperation",
		},
		{
			method: "DELETE",
			path: "/sync-operations/:id",
			handler: "sync-operation.delete",
		},
	],
};
