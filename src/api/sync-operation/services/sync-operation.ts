/**
 * Sync Operation service
 */

import { factories } from "@strapi/strapi";

export default factories.createCoreService(
	"api::sync-operation.sync-operation",
	({ strapi }) => ({
		/**
		 * Start a new sync operation
		 */
		async startSyncOperation(operationType: "adapty_sync" | "remote_config_deploy" | "ab_test_sync" | "analytics_sync", operationId: string) {
			try {
				return await strapi.entityService.create(
					"api::sync-operation.sync-operation",
					{
						data: {
							operationType,
							operationId,
							startTime: new Date(),
							status: "running",
							recordsProcessed: 0,
							errorCount: 0,
							successRate: 0,
						},
					},
				);
			} catch (error) {
				strapi.log.error("Failed to start sync operation:", error);
				throw error;
			}
		},

		/**
		 * Complete a sync operation
		 */
		async completeSyncOperation(
			operationId: string,
			recordsProcessed: number,
			errorCount: number = 0,
		) {
			try {
				const operation = await strapi.entityService.findMany(
					"api::sync-operation.sync-operation",
					{
						filters: { operationId },
					},
				);

				if (operation.length === 0) {
					throw new Error(`Sync operation not found: ${operationId}`);
				}

				const startTime = new Date(operation[0].startTime);
				const endTime = new Date();
				const duration = endTime.getTime() - startTime.getTime();
				const successRate =
					recordsProcessed > 0
						? ((recordsProcessed - errorCount) / recordsProcessed) * 100
						: 100;

				return await strapi.entityService.update(
					"api::sync-operation.sync-operation",
					operation[0].id,
					{
						data: {
							endTime,
							duration,
							status: "completed",
							recordsProcessed,
							errorCount,
							successRate,
						} as any,
					},
				);
			} catch (error) {
				strapi.log.error("Failed to complete sync operation:", error);
				throw error;
			}
		},

		/**
		 * Fail a sync operation
		 */
		async failSyncOperation(operationId: string, errorDetails: string[]) {
			try {
				const operation = await strapi.entityService.findMany(
					"api::sync-operation.sync-operation",
					{
						filters: { operationId },
					},
				);

				if (operation.length === 0) {
					throw new Error(`Sync operation not found: ${operationId}`);
				}

				const startTime = new Date(operation[0].startTime);
				const endTime = new Date();
				const duration = endTime.getTime() - startTime.getTime();

				return await strapi.entityService.update(
					"api::sync-operation.sync-operation",
					operation[0].id,
					{
						data: {
							endTime,
							duration,
							status: "failed",
							errorDetails,
							successRate: 0,
						} as any,
					},
				);
			} catch (error) {
				strapi.log.error("Failed to fail sync operation:", error);
				throw error;
			}
		},

		/**
		 * Update sync operation progress
		 */
		async updateSyncProgress(
			operationId: string,
			recordsProcessed: number,
			errorCount: number = 0,
		) {
			try {
				const operation = await strapi.entityService.findMany(
					"api::sync-operation.sync-operation",
					{
						filters: { operationId },
					},
				);

				if (operation.length === 0) {
					throw new Error(`Sync operation not found: ${operationId}`);
				}

				const successRate =
					recordsProcessed > 0
						? ((recordsProcessed - errorCount) / recordsProcessed) * 100
						: 100;

				return await strapi.entityService.update(
					"api::sync-operation.sync-operation",
					operation[0].id,
					{
						data: {
							recordsProcessed,
							errorCount,
							successRate,
						} as any,
					},
				);
			} catch (error) {
				strapi.log.error("Failed to update sync progress:", error);
				throw error;
			}
		},

		/**
		 * Get sync operation status
		 */
		async getSyncOperationStatus(operationId: string) {
			try {
				const operation = await strapi.entityService.findMany(
					"api::sync-operation.sync-operation",
					{
						filters: { operationId },
					},
				);

				if (operation.length === 0) {
					return null;
				}

				return operation[0];
			} catch (error) {
				strapi.log.error("Failed to get sync operation status:", error);
				throw error;
			}
		},

		/**
		 * Get running sync operations
		 */
		async getRunningSyncOperations() {
			try {
				return await strapi.entityService.findMany(
					"api::sync-operation.sync-operation",
					{
						filters: {
							status: "running",
						},
						sort: "startTime:desc",
					},
				);
			} catch (error) {
				strapi.log.error("Failed to get running sync operations:", error);
				throw error;
			}
		},

		/**
		 * Check for stuck sync operations
		 */
		async checkForStuckOperations(timeoutMinutes: number = 60) {
			try {
				const cutoffTime = new Date(Date.now() - timeoutMinutes * 60 * 1000);

				const stuckOperations = await strapi.entityService.findMany(
					"api::sync-operation.sync-operation",
					{
						filters: {
							status: "running",
							startTime: {
								$lt: cutoffTime.toISOString(),
							},
						},
					},
				);

				// Mark stuck operations as failed
				const stuckArray = Array.isArray(stuckOperations)
					? stuckOperations
					: [stuckOperations];
				for (const operation of stuckArray) {
					await this.failSyncOperation(operation.operationId, [
						`Operation timed out after ${timeoutMinutes} minutes`,
					]);
				}

				if (stuckOperations.length > 0) {
					strapi.log.warn(
						`Found and marked ${stuckOperations.length} stuck sync operations as failed`,
					);
				}

				return stuckOperations;
			} catch (error) {
				strapi.log.error("Failed to check for stuck operations:", error);
				throw error;
			}
		},

		/**
		 * Get sync operation statistics
		 */
		async getSyncStatistics(hours: number = 24) {
			try {
				const since = new Date(Date.now() - hours * 60 * 60 * 1000);

				const operations = await strapi.entityService.findMany(
					"api::sync-operation.sync-operation",
					{
						filters: {
							startTime: {
								$gte: since.toISOString(),
							},
						},
					},
				);

				const completed = operations.filter((op) => op.status === "completed");
				const failed = operations.filter((op) => op.status === "failed");
				const running = operations.filter((op) => op.status === "running");

				const totalRecords = completed.reduce(
					(sum, op) => sum + (op.recordsProcessed || 0),
					0,
				);
				const totalErrors = operations.reduce(
					(sum, op) => sum + (op.errorCount || 0),
					0,
				);
				const averageDuration =
					completed.length > 0
						? completed.reduce((sum, op) => sum + (op.duration || 0), 0) /
						completed.length
						: 0;

				const successRate =
					completed.length + failed.length > 0
						? (completed.length / (completed.length + failed.length)) * 100
						: 0;

				return {
					totalOperations: operations.length,
					completedOperations: completed.length,
					failedOperations: failed.length,
					runningOperations: running.length,
					totalRecordsProcessed: totalRecords,
					totalErrors,
					successRate: Math.round(successRate * 100) / 100,
					averageDuration: Math.round(averageDuration),
					timeRange: { since, hours },
				};
			} catch (error) {
				strapi.log.error("Failed to get sync statistics:", error);
				throw error;
			}
		},

		/**
		 * Clean up old sync operations
		 */
		async cleanupOldOperations(daysToKeep: number = 30) {
			try {
				const cutoffDate = new Date();
				cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

				const deletedCount = await strapi.db
					.query("api::sync-operation.sync-operation")
					.deleteMany({
						where: {
							endTime: {
								$lt: cutoffDate,
							},
							status: {
								$in: ["completed", "failed"],
							},
						},
					});

				strapi.log.info(`Cleaned up ${deletedCount} old sync operations`);
				return deletedCount;
			} catch (error) {
				strapi.log.error("Failed to cleanup old sync operations:", error);
				throw error;
			}
		},
	}),
);
