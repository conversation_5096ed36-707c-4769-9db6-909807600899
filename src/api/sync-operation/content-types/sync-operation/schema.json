{"kind": "collectionType", "collectionName": "sync_operations", "info": {"singularName": "sync-operation", "pluralName": "sync-operations", "displayName": "Sync Operation", "description": "Synchronization operation monitoring"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"operationType": {"type": "enumeration", "enum": ["adapty_sync", "remote_config_deploy", "ab_test_sync", "analytics_sync"], "required": true}, "operationId": {"type": "string", "required": true, "unique": true}, "startTime": {"type": "datetime", "required": true}, "endTime": {"type": "datetime"}, "duration": {"type": "integer"}, "status": {"type": "enumeration", "enum": ["running", "completed", "failed", "timeout"], "required": true, "default": "running"}, "recordsProcessed": {"type": "integer", "required": true, "default": 0}, "errorCount": {"type": "integer", "required": true, "default": 0}, "successRate": {"type": "decimal", "required": true, "default": 0}, "errorDetails": {"type": "json"}}}