/**
 * Sync Operation controller
 */

import { factories } from "@strapi/strapi";

export default factories.createCoreController(
	"api::sync-operation.sync-operation",
	({ strapi }) => ({
		/**
		 * Get sync operation status
		 */
		async getSyncStatus(ctx) {
			try {
				const { operationType, hours = 24 } = ctx.query;
				const since = new Date(Date.now() - hours * 60 * 60 * 1000);

				const filters: any = {
					startTime: {
						$gte: since.toISOString(),
					},
				};

				if (operationType) {
					filters.operationType = operationType;
				}

				const operations = await strapi.entityService.findMany(
					"api::sync-operation.sync-operation",
					{
						filters,
						sort: "startTime:desc",
					},
				);

				const operationsArray = Array.isArray(operations)
					? operations
					: [operations];
				const summary = this.calculateSyncSummary(operationsArray);

				ctx.body = {
					data: operations,
					meta: {
						total: operations.length,
						timeRange: { since, hours },
						summary,
					},
				};
			} catch (_error) {
				ctx.throw(500, "Failed to get sync status");
			}
		},

		/**
		 * Get running sync operations
		 */
		async getRunningSyncs(ctx) {
			try {
				const runningOps = await strapi.entityService.findMany(
					"api::sync-operation.sync-operation",
					{
						filters: {
							status: "running",
						},
						sort: "startTime:desc",
					},
				);

				// Check for stuck operations (running for more than 1 hour)
				const now = new Date();
				const stuckOps = runningOps.filter((op) => {
					const startTime = new Date(op.startTime);
					const duration = now.getTime() - startTime.getTime();
					return duration > 60 * 60 * 1000; // 1 hour
				});

				ctx.body = {
					data: runningOps,
					meta: {
						total: runningOps.length,
						stuckOperations: stuckOps.length,
						stuckOps: stuckOps.map((op) => ({
							id: op.id,
							operationType: op.operationType,
							operationId: op.operationId,
							duration: now.getTime() - new Date(op.startTime).getTime(),
						})),
					},
				};
			} catch (_error) {
				ctx.throw(500, "Failed to get running syncs");
			}
		},

		/**
		 * Get sync operation history
		 */
		async getSyncHistory(ctx) {
			try {
				const { operationType, status, hours = 168, limit = 100 } = ctx.query; // Default 7 days
				const since = new Date(Date.now() - hours * 60 * 60 * 1000);

				const filters: any = {
					startTime: {
						$gte: since.toISOString(),
					},
				};

				if (operationType) {
					filters.operationType = operationType;
				}

				if (status) {
					filters.status = status;
				}

				const operations = await strapi.entityService.findMany(
					"api::sync-operation.sync-operation",
					{
						filters,
						sort: "startTime:desc",
						limit: parseInt(limit),
					},
				);

				// Calculate trends
				const operationsArray2 = Array.isArray(operations)
					? operations
					: [operations];
				const trends = this.calculateSyncTrends(operationsArray2);

				ctx.body = {
					data: operations,
					meta: {
						total: operations.length,
						timeRange: { since, hours },
						trends,
					},
				};
			} catch (_error) {
				ctx.throw(500, "Failed to get sync history");
			}
		},

		/**
		 * Get sync operation analytics
		 */
		async getSyncAnalytics(ctx) {
			try {
				const { hours = 168 } = ctx.query; // Default 7 days
				const since = new Date(Date.now() - hours * 60 * 60 * 1000);

				const operations = await strapi.entityService.findMany(
					"api::sync-operation.sync-operation",
					{
						filters: {
							startTime: {
								$gte: since.toISOString(),
							},
						},
					},
				);

				const operationsArray3 = Array.isArray(operations)
					? operations
					: [operations];
				const analytics = this.generateSyncAnalytics(operationsArray3);

				ctx.body = {
					data: analytics,
					meta: {
						timeRange: { since, hours },
					},
				};
			} catch (_error) {
				ctx.throw(500, "Failed to get sync analytics");
			}
		},

		/**
		 * Force stop a running sync operation
		 */
		async stopSyncOperation(ctx) {
			try {
				const { id } = ctx.params;
				const { reason } = ctx.request.body;

				const operation = await strapi.entityService.findOne(
					"api::sync-operation.sync-operation",
					id,
				);

				if (!operation) {
					ctx.throw(404, "Sync operation not found");
				}

				if (operation.status !== "running") {
					ctx.throw(400, "Operation is not running");
				}

				const updatedOperation = await strapi.entityService.update(
					"api::sync-operation.sync-operation",
					id,
					{
						data: {
							status: "failed",
							endTime: new Date(),
							duration: Date.now() - new Date(operation.startTime).getTime(),
							errorDetails: [
								`Manually stopped: ${reason || "No reason provided"}`,
							],
						} as any,
					},
				);

				ctx.body = {
					data: updatedOperation,
				};
			} catch (_error) {
				ctx.throw(500, "Failed to stop sync operation");
			}
		},

		/**
		 * Calculate sync summary
		 */
		calculateSyncSummary(operations: any[]): any {
			if (operations.length === 0) {
				return {
					totalOperations: 0,
					successRate: 0,
					averageDuration: 0,
					operationTypes: {},
				};
			}

			const completed = operations.filter((op) => op.status === "completed");
			const failed = operations.filter((op) => op.status === "failed");
			const running = operations.filter((op) => op.status === "running");

			const successRate =
				operations.length > 0
					? (completed.length / (completed.length + failed.length)) * 100
					: 0;

			const averageDuration =
				completed.length > 0
					? completed.reduce((sum, op) => sum + (op.duration || 0), 0) /
						completed.length
					: 0;

			const operationTypes = operations.reduce((acc, op) => {
				acc[op.operationType] = (acc[op.operationType] || 0) + 1;
				return acc;
			}, {});

			return {
				totalOperations: operations.length,
				completedOperations: completed.length,
				failedOperations: failed.length,
				runningOperations: running.length,
				successRate: Math.round(successRate * 100) / 100,
				averageDuration: Math.round(averageDuration),
				operationTypes,
			};
		},

		/**
		 * Calculate sync trends
		 */
		calculateSyncTrends(operations: any[]): any {
			if (operations.length < 10) {
				return {
					successRate: "insufficient_data",
					duration: "insufficient_data",
					frequency: "insufficient_data",
				};
			}

			// Split into recent and older operations
			const midpoint = Math.floor(operations.length / 2);
			const recent = operations.slice(0, midpoint);
			const older = operations.slice(midpoint);

			const recentSuccessRate = this.calculateSuccessRate(recent);
			const olderSuccessRate = this.calculateSuccessRate(older);

			const recentAvgDuration = this.calculateAverageDuration(recent);
			const olderAvgDuration = this.calculateAverageDuration(older);

			const getTrend = (
				recent: number,
				older: number,
				threshold: number = 10,
			) => {
				if (older === 0) return "stable";
				const change = ((recent - older) / older) * 100;
				if (Math.abs(change) < threshold) return "stable";
				return change > 0 ? "increasing" : "decreasing";
			};

			return {
				successRate: getTrend(recentSuccessRate, olderSuccessRate),
				duration: getTrend(recentAvgDuration, olderAvgDuration),
				frequency: this.calculateFrequencyTrend(recent, older),
			};
		},

		/**
		 * Generate sync analytics
		 */
		generateSyncAnalytics(operations: any[]): any {
			const analytics = {
				overview: this.calculateSyncSummary(operations),
				byOperationType: {},
				byHour: {},
				byDay: {},
				failureAnalysis: this.analyzeFailures(operations),
				performanceMetrics: this.calculatePerformanceMetrics(operations),
			};

			// Group by operation type
			const byType = operations.reduce((acc, op) => {
				if (!acc[op.operationType]) {
					acc[op.operationType] = [];
				}
				acc[op.operationType].push(op);
				return acc;
			}, {});

			Object.keys(byType).forEach((type) => {
				analytics.byOperationType[type] = this.calculateSyncSummary(
					byType[type],
				);
			});

			// Group by hour of day
			operations.forEach((op) => {
				const hour = new Date(op.startTime).getHours();
				analytics.byHour[hour] = (analytics.byHour[hour] || 0) + 1;
			});

			// Group by day of week
			operations.forEach((op) => {
				const day = new Date(op.startTime).getDay();
				analytics.byDay[day] = (analytics.byDay[day] || 0) + 1;
			});

			return analytics;
		},

		/**
		 * Analyze sync failures
		 */
		analyzeFailures(operations: any[]): any {
			const failures = operations.filter((op) => op.status === "failed");

			if (failures.length === 0) {
				return {
					totalFailures: 0,
					failureRate: 0,
					commonErrors: [],
					failuresByType: {},
				};
			}

			const failuresByType = failures.reduce((acc, failure) => {
				acc[failure.operationType] = (acc[failure.operationType] || 0) + 1;
				return acc;
			}, {});

			const commonErrors = failures
				.filter((f) => f.errorDetails && f.errorDetails.length > 0)
				.reduce((acc, failure) => {
					failure.errorDetails.forEach((error) => {
						acc[error] = (acc[error] || 0) + 1;
					});
					return acc;
				}, {});

			const sortedErrors = Object.entries(commonErrors)
				.map(([error, count]) => ({ error, count }))
				.sort((a: any, b: any) => b.count - a.count)
				.slice(0, 5);

			return {
				totalFailures: failures.length,
				failureRate: (failures.length / operations.length) * 100,
				commonErrors: sortedErrors,
				failuresByType,
			};
		},

		/**
		 * Calculate performance metrics
		 */
		calculatePerformanceMetrics(operations: any[]): any {
			const completed = operations.filter(
				(op) => op.status === "completed" && op.duration,
			);

			if (completed.length === 0) {
				return {
					averageDuration: 0,
					medianDuration: 0,
					p95Duration: 0,
					averageRecordsPerSecond: 0,
				};
			}

			const durations = completed
				.map((op) => op.duration)
				.sort((a, b) => a - b);
			const averageDuration =
				durations.reduce((sum, d) => sum + d, 0) / durations.length;
			const medianDuration = durations[Math.floor(durations.length / 2)];
			const p95Duration = durations[Math.floor(durations.length * 0.95)];

			const totalRecords = completed.reduce(
				(sum, op) => sum + (op.recordsProcessed || 0),
				0,
			);
			const totalDurationSeconds =
				completed.reduce((sum, op) => sum + (op.duration || 0), 0) / 1000;
			const averageRecordsPerSecond =
				totalDurationSeconds > 0 ? totalRecords / totalDurationSeconds : 0;

			return {
				averageDuration: Math.round(averageDuration),
				medianDuration: Math.round(medianDuration),
				p95Duration: Math.round(p95Duration),
				averageRecordsPerSecond:
					Math.round(averageRecordsPerSecond * 100) / 100,
			};
		},

		/**
		 * Helper methods
		 */
		calculateSuccessRate(operations: any[]): number {
			if (operations.length === 0) return 0;
			const completed = operations.filter(
				(op) => op.status === "completed",
			).length;
			const total = operations.filter((op) => op.status !== "running").length;
			return total > 0 ? (completed / total) * 100 : 0;
		},

		calculateAverageDuration(operations: any[]): number {
			const completed = operations.filter(
				(op) => op.status === "completed" && op.duration,
			);
			if (completed.length === 0) return 0;
			return (
				completed.reduce((sum, op) => sum + op.duration, 0) / completed.length
			);
		},

		calculateFrequencyTrend(recent: any[], older: any[]): string {
			// This is a simplified frequency calculation
			// In a real implementation, you'd want to consider time windows
			if (recent.length > older.length * 1.2) return "increasing";
			if (recent.length < older.length * 0.8) return "decreasing";
			return "stable";
		},
	}),
);
