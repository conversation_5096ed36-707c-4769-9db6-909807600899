{"kind": "collectionType", "collectionName": "ab_tests", "info": {"singularName": "ab-test", "pluralName": "ab-tests", "displayName": "A/B Test", "description": "A/B test configuration with metrics and audience targeting"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"name": {"type": "string", "required": true, "maxLength": 255, "pluginOptions": {"i18n": {"localized": false}}}, "description": {"type": "text", "required": false, "pluginOptions": {"i18n": {"localized": true}}}, "hypothesis": {"type": "text", "required": true, "pluginOptions": {"i18n": {"localized": true}}}, "status": {"type": "enumeration", "enum": ["draft", "scheduled", "running", "paused", "completed", "cancelled"], "default": "draft", "required": true, "pluginOptions": {"i18n": {"localized": false}}}, "test_type": {"type": "enumeration", "enum": ["paywall_content", "pricing", "theme", "features", "full_paywall"], "default": "paywall_content", "required": true, "pluginOptions": {"i18n": {"localized": false}}}, "primary_metric": {"type": "enumeration", "enum": ["conversion_rate", "revenue_per_user", "trial_conversion", "retention_rate", "ltv"], "default": "conversion_rate", "required": true, "pluginOptions": {"i18n": {"localized": false}}}, "secondary_metrics": {"type": "json", "required": false, "pluginOptions": {"i18n": {"localized": false}}}, "start_date": {"type": "datetime", "required": true, "pluginOptions": {"i18n": {"localized": false}}}, "end_date": {"type": "datetime", "required": false, "pluginOptions": {"i18n": {"localized": false}}}, "duration_days": {"type": "integer", "required": false, "min": 1, "max": 365, "pluginOptions": {"i18n": {"localized": false}}}, "traffic_allocation": {"type": "decimal", "required": true, "default": 100.0, "min": 0.1, "max": 100.0, "pluginOptions": {"i18n": {"localized": false}}}, "minimum_sample_size": {"type": "integer", "required": true, "default": 1000, "min": 100, "pluginOptions": {"i18n": {"localized": false}}}, "confidence_level": {"type": "decimal", "required": true, "default": 95.0, "min": 80.0, "max": 99.9, "pluginOptions": {"i18n": {"localized": false}}}, "statistical_power": {"type": "decimal", "required": true, "default": 80.0, "min": 50.0, "max": 99.0, "pluginOptions": {"i18n": {"localized": false}}}, "minimum_detectable_effect": {"type": "decimal", "required": true, "default": 5.0, "min": 0.1, "max": 100.0, "pluginOptions": {"i18n": {"localized": false}}}, "audience_targeting": {"type": "json", "required": false, "pluginOptions": {"i18n": {"localized": false}}}, "geographic_targeting": {"type": "json", "required": false, "pluginOptions": {"i18n": {"localized": false}}}, "device_targeting": {"type": "json", "required": false, "pluginOptions": {"i18n": {"localized": false}}}, "user_segment_filters": {"type": "json", "required": false, "pluginOptions": {"i18n": {"localized": false}}}, "variations": {"type": "relation", "relation": "oneToMany", "target": "api::paywall-variation.paywall-variation", "mappedBy": "ab_test"}, "winner_variation": {"type": "relation", "relation": "oneToOne", "target": "api::paywall-variation.paywall-variation"}, "winner_selected_at": {"type": "datetime", "required": false, "pluginOptions": {"i18n": {"localized": false}}}, "winner_selection_method": {"type": "enumeration", "enum": ["manual", "automatic", "scheduled"], "default": "manual", "pluginOptions": {"i18n": {"localized": false}}}, "auto_promote_winner": {"type": "boolean", "default": false, "pluginOptions": {"i18n": {"localized": false}}}, "current_results": {"type": "json", "required": false, "pluginOptions": {"i18n": {"localized": false}}}, "statistical_significance": {"type": "decimal", "required": false, "min": 0.0, "max": 100.0, "pluginOptions": {"i18n": {"localized": false}}}, "p_value": {"type": "decimal", "required": false, "min": 0.0, "max": 1.0, "pluginOptions": {"i18n": {"localized": false}}}, "effect_size": {"type": "decimal", "required": false, "pluginOptions": {"i18n": {"localized": false}}}, "confidence_interval": {"type": "json", "required": false, "pluginOptions": {"i18n": {"localized": false}}}, "adapty_test_id": {"type": "string", "required": false, "maxLength": 100, "pluginOptions": {"i18n": {"localized": false}}}, "adapty_sync_status": {"type": "enumeration", "enum": ["pending", "synced", "error"], "default": "pending", "pluginOptions": {"i18n": {"localized": false}}}, "adapty_last_sync": {"type": "datetime", "required": false, "pluginOptions": {"i18n": {"localized": false}}}, "created_by_user": {"type": "string", "required": false, "pluginOptions": {"i18n": {"localized": false}}}, "notes": {"type": "text", "required": false, "pluginOptions": {"i18n": {"localized": true}}}}}