{"kind": "collectionType", "collectionName": "mobile_api_usages", "info": {"singularName": "mobile-api-usage", "pluralName": "mobile-api-usages", "displayName": "Mobile API Usage", "description": "Tracks mobile API usage and performance metrics"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"app_id": {"type": "string", "required": true, "maxLength": 50}, "user_id": {"type": "string", "maxLength": 100}, "platform": {"type": "enumeration", "enum": ["ios", "android", "react_native", "unknown"], "required": true}, "app_version": {"type": "string", "maxLength": 20}, "endpoint": {"type": "string", "required": true, "maxLength": 200}, "method": {"type": "enumeration", "enum": ["GET", "POST", "PUT", "DELETE", "PATCH"], "required": true}, "status_code": {"type": "integer", "required": true}, "response_time": {"type": "integer", "required": true}, "request_size": {"type": "integer", "default": 0}, "response_size": {"type": "integer", "default": 0}, "ip_address": {"type": "string", "maxLength": 45}, "user_agent": {"type": "string", "maxLength": 500}, "error_message": {"type": "text"}, "cache_hit": {"type": "boolean", "default": false}, "timestamp": {"type": "datetime", "required": true}, "request_params": {"type": "json"}, "session_id": {"type": "string", "maxLength": 100}}}