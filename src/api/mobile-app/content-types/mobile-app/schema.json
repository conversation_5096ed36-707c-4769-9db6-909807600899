{"kind": "collectionType", "collectionName": "mobile_apps", "info": {"singularName": "mobile-app", "pluralName": "mobile-apps", "displayName": "Mobile App", "description": "Mobile application registrations for API access"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"app_id": {"type": "string", "required": true, "unique": true, "maxLength": 50}, "name": {"type": "string", "required": true, "maxLength": 100}, "platform": {"type": "enumeration", "enum": ["ios", "android", "react_native"], "required": true}, "bundle_id": {"type": "string", "required": true, "maxLength": 200}, "api_key": {"type": "string", "required": true, "unique": true, "maxLength": 100}, "secret_key": {"type": "string", "required": true, "maxLength": 200}, "min_version": {"type": "string", "required": true, "maxLength": 20}, "max_version": {"type": "string", "maxLength": 20}, "permissions": {"type": "json", "required": true, "default": ["paywall:read"]}, "rate_limits": {"type": "json", "required": true, "default": {"requests_per_minute": 60, "burst_limit": 100}}, "is_active": {"type": "boolean", "required": true, "default": true}, "description": {"type": "text"}, "webhook_url": {"type": "string", "maxLength": 500}, "webhook_secret": {"type": "string", "maxLength": 100}, "last_used": {"type": "datetime"}, "usage_stats": {"type": "json", "default": {"total_requests": 0, "last_24h_requests": 0, "error_rate": 0}}}}