/**
 * mobile-app controller
 */

import { factories } from "@strapi/strapi";

export default factories.createCoreController(
	"api::mobile-app.mobile-app",
	({ strapi }) => ({
		/**
		 * Create a new mobile app registration
		 */
		async create(ctx) {
			const { data } = ctx.request.body;

			// Generate API credentials
			const authService = strapi.service("api::mobile.v1.auth");
			const apiKey = authService.generateApiKey();
			const secretKey = authService.generateSecretKey();

			// Set generated credentials
			data.api_key = apiKey;
			data.secret_key = secretKey;

			// Set default permissions if not provided
			if (!data.permissions) {
				data.permissions = ["paywall:read"];
			}

			// Set default rate limits if not provided
			if (!data.rate_limits) {
				data.rate_limits = {
					requests_per_minute: 60,
					burst_limit: 100,
				};
			}

			const response = await super.create(ctx);

			// Don't expose secret key in response
			if (response.data?.attributes) {
				delete response.data.attributes.secret_key;
			}

			return response;
		},

		/**
		 * Update mobile app configuration
		 */
		async update(ctx) {
			const { data } = ctx.request.body;

			// Don't allow updating API credentials directly
			delete data.api_key;
			delete data.secret_key;

			const response = await super.update(ctx);

			// Don't expose secret key in response
			if (response.data?.attributes) {
				delete response.data.attributes.secret_key;
			}

			return response;
		},

		/**
		 * Find mobile apps (hide secret keys)
		 */
		async find(ctx) {
			const response = await super.find(ctx);

			// Hide secret keys from response
			if (Array.isArray(response.data)) {
				response.data.forEach((item) => {
					if (item.attributes) {
						delete item.attributes.secret_key;
					}
				});
			}

			return response;
		},

		/**
		 * Find one mobile app (hide secret key)
		 */
		async findOne(ctx) {
			const response = await super.findOne(ctx);

			// Hide secret key from response
			if (response.data?.attributes) {
				delete response.data.attributes.secret_key;
			}

			return response;
		},

		/**
		 * Regenerate API credentials
		 */
		async regenerateCredentials(ctx) {
			const { id } = ctx.params;

			try {
				const authService = strapi.service("api::mobile.v1.auth");
				const newApiKey = authService.generateApiKey();
				const newSecretKey = authService.generateSecretKey();

				const updatedApp = await strapi.entityService.update(
					"api::mobile-app.mobile-app",
					id,
					{
						data: {
							api_key: newApiKey,
							secret_key: newSecretKey,
						} as any,
					},
				);

				// Invalidate existing tokens for this app
				await authService.invalidateAppTokens(updatedApp.app_id);

				ctx.body = {
					data: {
						id: updatedApp.id,
						apiKey: newApiKey,
						message:
							"API credentials regenerated successfully. Please update your app configuration.",
					},
				};
			} catch (error) {
				strapi.log.error("Failed to regenerate credentials:", error);
				ctx.throw(500, "Failed to regenerate credentials");
			}
		},

		/**
		 * Get app usage statistics
		 */
		async getUsageStats(ctx) {
			const { id } = ctx.params;
			const { days = 7 } = ctx.query;

			try {
				const app = await strapi.entityService.findOne(
					"api::mobile-app.mobile-app",
					id,
				);
				if (!app) {
					return ctx.notFound("Mobile app not found");
				}

				const stats = await strapi
					.service("api::mobile.v1.auth")
					.getAppUsageStats(app.app_id, parseInt(days));

				ctx.body = {
					data: {
						app_id: app.app_id,
						name: app.name,
						platform: app.platform,
						stats,
					},
				};
			} catch (error) {
				strapi.log.error("Failed to get usage stats:", error);
				ctx.throw(500, "Failed to retrieve usage statistics");
			}
		},
	}),
);
