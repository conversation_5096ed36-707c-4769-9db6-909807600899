{"kind": "collectionType", "collectionName": "translation_memories", "info": {"singularName": "translation-memory", "pluralName": "translation-memories", "displayName": "Translation Memory", "description": "Stores translation pairs for consistency and reuse"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"source_text": {"type": "text", "required": true}, "target_text": {"type": "text", "required": true}, "source_locale": {"type": "string", "required": true, "maxLength": 10}, "target_locale": {"type": "string", "required": true, "maxLength": 10}, "content_type": {"type": "string", "required": false, "maxLength": 255}, "field_name": {"type": "string", "required": false, "maxLength": 255}, "context": {"type": "string", "required": false, "maxLength": 500}, "quality_score": {"type": "integer", "required": false, "min": 1, "max": 10, "default": 5}, "usage_count": {"type": "integer", "default": 0, "min": 0}, "last_used": {"type": "datetime", "required": false}, "created_by_translator": {"type": "relation", "relation": "manyToOne", "target": "admin::user"}, "approved_by": {"type": "relation", "relation": "manyToOne", "target": "admin::user"}, "tags": {"type": "json", "required": false}, "is_approved": {"type": "boolean", "default": false}, "is_fuzzy_match": {"type": "boolean", "default": false}, "similarity_score": {"type": "decimal", "required": false, "min": 0, "max": 1}}}