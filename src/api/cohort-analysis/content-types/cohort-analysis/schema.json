{"kind": "collectionType", "collectionName": "cohort_analysis", "info": {"singularName": "cohort-analysis", "pluralName": "cohort-analyses", "displayName": "Cohort Analysis", "description": "User cohort retention and revenue analysis"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"cohortName": {"type": "string", "required": true}, "cohortStartDate": {"type": "date", "required": true}, "totalUsers": {"type": "integer", "required": true, "default": 0}, "retentionRates": {"type": "json", "required": true}, "revenueMetrics": {"type": "json", "required": true}, "churnAnalysis": {"type": "json", "required": true}, "conversionMetrics": {"type": "json", "required": true}, "timestamp": {"type": "datetime", "required": true}}}