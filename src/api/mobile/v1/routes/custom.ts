/**
 * Mobile API v1 - Custom Routes
 * Additional endpoints for mobile API functionality
 */

export default {
	routes: [
		// API Documentation endpoint
		{
			method: "GET",
			path: "/mobile/v1/docs",
			handler: "mobile.v1.paywall.getAPIDocumentation",
			config: {
				auth: false,
				middlewares: ["api::mobile.v1.rate-limit"],
			},
		},

		// API Version info
		{
			method: "GET",
			path: "/mobile/v1/version",
			handler: "mobile.v1.paywall.getVersionInfo",
			config: {
				auth: false,
				middlewares: ["api::mobile.v1.rate-limit"],
			},
		},

		// Bulk paywall operations
		{
			method: "POST",
			path: "/mobile/v1/paywalls/bulk",
			handler: "mobile.v1.paywall.bulkOperations",
			config: {
				middlewares: [
					"api::mobile.v1.auth",
					"api::mobile.v1.rate-limit",
					"api::mobile.v1.analytics",
				],
			},
		},

		// Get paywalls by multiple placement IDs
		{
			method: "GET",
			path: "/mobile/v1/paywalls/placements/:placementIds",
			handler: "mobile.v1.paywall.getByMultiplePlacements",
			config: {
				middlewares: [
					"api::mobile.v1.auth",
					"api::mobile.v1.rate-limit",
					"api::mobile.v1.cache",
					"api::mobile.v1.analytics",
				],
			},
		},

		// Get paywall configuration
		{
			method: "GET",
			path: "/mobile/v1/paywalls/:placementId/config",
			handler: "mobile.v1.paywall.getConfiguration",
			config: {
				middlewares: [
					"api::mobile.v1.auth",
					"api::mobile.v1.rate-limit",
					"api::mobile.v1.cache",
					"api::mobile.v1.analytics",
				],
			},
		},

		// Validate paywall configuration
		{
			method: "POST",
			path: "/mobile/v1/paywalls/validate",
			handler: "mobile.v1.paywall.validateConfiguration",
			config: {
				middlewares: ["api::mobile.v1.auth", "api::mobile.v1.rate-limit"],
			},
		},

		// Get app-specific paywalls
		{
			method: "GET",
			path: "/mobile/v1/app/:appId/paywalls",
			handler: "mobile.v1.paywall.getAppPaywalls",
			config: {
				middlewares: [
					"api::mobile.v1.auth",
					"api::mobile.v1.rate-limit",
					"api::mobile.v1.cache",
					"api::mobile.v1.analytics",
				],
			},
		},

		// Record multiple interactions
		{
			method: "POST",
			path: "/mobile/v1/interactions/batch",
			handler: "mobile.v1.paywall.recordBatchInteractions",
			config: {
				middlewares: ["api::mobile.v1.auth", "api::mobile.v1.rate-limit"],
			},
		},

		// Get paywall analytics
		{
			method: "GET",
			path: "/mobile/v1/paywalls/:placementId/analytics",
			handler: "mobile.v1.paywall.getPaywallAnalytics",
			config: {
				middlewares: ["api::mobile.v1.auth", "api::mobile.v1.rate-limit"],
			},
		},

		// Prefetch paywalls for offline use
		{
			method: "POST",
			path: "/mobile/v1/paywalls/prefetch",
			handler: "mobile.v1.paywall.prefetchPaywalls",
			config: {
				middlewares: [
					"api::mobile.v1.auth",
					"api::mobile.v1.rate-limit",
					"api::mobile.v1.analytics",
				],
			},
		},
	],
};
