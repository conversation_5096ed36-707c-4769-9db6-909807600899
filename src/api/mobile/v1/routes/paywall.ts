/**
 * Mobile API v1 - Paywall Routes
 * RESTful endpoints optimized for mobile app consumption
 */

export default {
	routes: [
		// Health check endpoint
		{
			method: "GET",
			path: "/mobile/v1/health",
			handler: "mobile.v1.paywall.healthCheck",
			config: {
				auth: false,
				middlewares: ["api::mobile.v1.rate-limit"],
			},
		},

		// Get paywall by placement ID
		{
			method: "GET",
			path: "/mobile/v1/paywalls/:placementId",
			handler: "mobile.v1.paywall.getByPlacement",
			config: {
				middlewares: [
					"api::mobile.v1.auth",
					"api::mobile.v1.rate-limit",
					"api::mobile.v1.cache",
					"api::mobile.v1.analytics",
				],
			},
		},

		// Batch get paywalls
		{
			method: "POST",
			path: "/mobile/v1/paywalls/batch",
			handler: "mobile.v1.paywall.getBatch",
			config: {
				middlewares: [
					"api::mobile.v1.auth",
					"api::mobile.v1.rate-limit",
					"api::mobile.v1.analytics",
				],
			},
		},

		// Get paywall with A/B testing
		{
			method: "GET",
			path: "/mobile/v1/paywalls/:placementId/ab-test",
			handler: "mobile.v1.paywall.getWithABTest",
			config: {
				middlewares: [
					"api::mobile.v1.auth",
					"api::mobile.v1.rate-limit",
					"api::mobile.v1.cache",
					"api::mobile.v1.analytics",
				],
			},
		},

		// Record paywall interaction
		{
			method: "POST",
			path: "/mobile/v1/paywalls/:placementId/interactions",
			handler: "mobile.v1.paywall.recordInteraction",
			config: {
				middlewares: ["api::mobile.v1.auth", "api::mobile.v1.rate-limit"],
			},
		},

		// Get paywall preview (for testing)
		{
			method: "GET",
			path: "/mobile/v1/paywalls/:placementId/preview",
			handler: "mobile.v1.paywall.getPreview",
			config: {
				middlewares: ["api::mobile.v1.auth", "api::mobile.v1.rate-limit"],
			},
		},
	],
};
