/**
 * Mobile API v1 - Paywall Controller
 * Optimized endpoints for mobile app paywall content delivery
 */

import { factories } from "@strapi/strapi";

export default factories.createCoreController(
	"api::paywall.paywall",
	({ strapi }) => ({
		/**
		 * Get paywall by placement ID - Mobile optimized
		 */
		async getByPlacement(ctx) {
			try {
				const { placementId } = ctx.params;
				const {
					locale = "en",
					version = "latest",
					device_type = "mobile",
					app_version,
					user_id,
				} = ctx.query;

				// Validate placement ID
				if (!placementId) {
					return ctx.badRequest("Placement ID is required");
				}

				// Get paywall with mobile-optimized population
				const paywall = await strapi
					.service("api::mobile.v1.paywall")
					.getPaywallForMobile({
						placementId,
						locale,
						version,
						deviceType: device_type,
						appVersion: app_version,
						userId: user_id,
					});

				if (!paywall) {
					return ctx.notFound("Paywall not found");
				}

				// Record analytics
				await strapi
					.service("api::analytics.analytics")
					.recordPaywallImpression({
						paywallId: paywall.id,
						placementId,
						userId: user_id,
						deviceType: device_type,
						locale,
						appVersion: app_version,
						timestamp: new Date(),
					});

				ctx.body = {
					data: paywall,
					meta: {
						version: version,
						locale: locale,
						device_type: device_type,
						cached_at: new Date().toISOString(),
						expires_at: new Date(Date.now() + 5 * 60 * 1000).toISOString(), // 5 minutes
					},
				};
			} catch (error) {
				strapi.log.error("Mobile API - Get paywall error:", error);
				ctx.throw(500, "Failed to retrieve paywall");
			}
		},

		/**
		 * Get multiple paywalls by placement IDs - Batch request
		 */
		async getBatch(ctx) {
			try {
				const {
					placement_ids,
					locale = "en",
					device_type = "mobile",
					user_id,
				} = ctx.request.body;

				if (!placement_ids || !Array.isArray(placement_ids)) {
					return ctx.badRequest("placement_ids array is required");
				}

				if (placement_ids.length > 10) {
					return ctx.badRequest(
						"Maximum 10 placement IDs allowed per batch request",
					);
				}

				const paywalls = await strapi
					.service("api::mobile.v1.paywall")
					.getBatchPaywalls({
						placementIds: placement_ids,
						locale,
						deviceType: device_type,
						userId: user_id,
					});

				// Record batch analytics
				for (const paywall of paywalls) {
					await strapi
						.service("api::analytics.analytics")
						.recordPaywallImpression({
							paywallId: paywall.id,
							placementId: paywall.placement_id,
							userId: user_id,
							deviceType: device_type,
							locale,
							timestamp: new Date(),
						});
				}

				ctx.body = {
					data: paywalls,
					meta: {
						total: paywalls.length,
						locale: locale,
						device_type: device_type,
						cached_at: new Date().toISOString(),
					},
				};
			} catch (error) {
				strapi.log.error("Mobile API - Batch paywalls error:", error);
				ctx.throw(500, "Failed to retrieve paywalls");
			}
		},

		/**
		 * Get paywall configuration for A/B testing
		 */
		async getWithABTest(ctx) {
			try {
				const { placementId } = ctx.params;
				const {
					locale = "en",
					device_type = "mobile",
					user_id,
					ab_test_id,
				} = ctx.query;

				if (!user_id) {
					return ctx.badRequest("user_id is required for A/B testing");
				}

				const result = await strapi
					.service("api::mobile.v1.paywall")
					.getPaywallWithABTest({
						placementId,
						locale,
						deviceType: device_type,
						userId: user_id,
						abTestId: ab_test_id,
					});

				if (!result.paywall) {
					return ctx.notFound("Paywall not found");
				}

				// Record A/B test impression
				if (result.abTestVariation) {
					await strapi
						.service("api::ab-testing.ab-test-manager")
						.recordVariationImpression({
							testId: result.abTestVariation.testId,
							variationId: result.abTestVariation.id,
							userId: user_id,
							placementId,
							deviceType: device_type,
						});
				}

				ctx.body = {
					data: result.paywall,
					meta: {
						ab_test: result.abTestVariation
							? {
									test_id: result.abTestVariation.testId,
									variation_id: result.abTestVariation.id,
									variation_name: result.abTestVariation.name,
									is_control: result.abTestVariation.isControl,
								}
							: null,
						locale: locale,
						device_type: device_type,
					},
				};
			} catch (error) {
				strapi.log.error("Mobile API - A/B test paywall error:", error);
				ctx.throw(500, "Failed to retrieve A/B test paywall");
			}
		},

		/**
		 * Record paywall interaction (view, click, conversion)
		 */
		async recordInteraction(ctx) {
			try {
				const { placementId } = ctx.params;
				const {
					interaction_type,
					user_id,
					device_type = "mobile",
					ab_test_id,
					variation_id,
					additional_data,
				} = ctx.request.body;

				if (!interaction_type || !user_id) {
					return ctx.badRequest("interaction_type and user_id are required");
				}

				const validInteractions = [
					"view",
					"click",
					"close",
					"purchase_start",
					"purchase_complete",
				];
				if (!validInteractions.includes(interaction_type)) {
					return ctx.badRequest("Invalid interaction_type");
				}

				// Record interaction
				await strapi.service("api::analytics.analytics").recordUserEngagement({
					paywallId: placementId,
					userId: user_id,
					interactionType: interaction_type,
					deviceType: device_type,
					abTestId: ab_test_id,
					variationId: variation_id,
					additionalData: additional_data,
					timestamp: new Date(),
				});

				// If it's a conversion, record it for A/B testing
				if (
					interaction_type === "purchase_complete" &&
					ab_test_id &&
					variation_id
				) {
					await strapi
						.service("api::ab-testing.ab-test-manager")
						.recordVariationConversion({
							testId: ab_test_id,
							variationId: variation_id,
							userId: user_id,
							placementId,
							deviceType: device_type,
							revenue: additional_data?.revenue || 0,
						});
				}

				ctx.body = {
					success: true,
					message: "Interaction recorded successfully",
				};
			} catch (error) {
				strapi.log.error("Mobile API - Record interaction error:", error);
				ctx.throw(500, "Failed to record interaction");
			}
		},

		/**
		 * Get paywall preview configuration
		 */
		async getPreview(ctx) {
			try {
				const { placementId } = ctx.params;
				const { locale = "en", device_type = "mobile" } = ctx.query;

				const paywall = await strapi
					.service("api::mobile.v1.paywall")
					.getPaywallPreview({
						placementId,
						locale,
						deviceType: device_type,
					});

				if (!paywall) {
					return ctx.notFound("Paywall preview not found");
				}

				ctx.body = {
					data: paywall,
					meta: {
						is_preview: true,
						locale: locale,
						device_type: device_type,
						warning:
							"This is preview data and should not be used in production",
					},
				};
			} catch (error) {
				strapi.log.error("Mobile API - Get preview error:", error);
				ctx.throw(500, "Failed to retrieve paywall preview");
			}
		},

		/**
		 * Health check for mobile API
		 */
		async healthCheck(ctx) {
			try {
				const health = await strapi
					.service("api::mobile.v1.paywall")
					.checkAPIHealth();

				ctx.body = {
					status: "healthy",
					version: "v1",
					timestamp: new Date().toISOString(),
					services: health,
				};
			} catch (error) {
				strapi.log.error("Mobile API - Health check error:", error);
				ctx.status = 503;
				ctx.body = {
					status: "unhealthy",
					version: "v1",
					timestamp: new Date().toISOString(),
					error: error.message,
				};
			}
		},

		/**
		 * Get API documentation
		 */
		async getAPIDocumentation(ctx) {
			try {
				const documentation = {
					version: "v1",
					title: "Strapi-Adapty Mobile API",
					description: "RESTful API for mobile paywall content delivery",
					base_url: `${strapi.config.get("server.url")}/api/mobile/v1`,
					authentication: {
						type: "Bearer Token",
						description: "Include JWT token in Authorization header",
						example: "Authorization: Bearer <your-jwt-token>",
					},
					endpoints: {
						health: {
							method: "GET",
							path: "/health",
							description: "Check API health status",
							auth_required: false,
						},
						get_paywall: {
							method: "GET",
							path: "/paywalls/{placementId}",
							description: "Get paywall by placement ID",
							auth_required: true,
							parameters: {
								placementId: "string (required) - Adapty placement ID",
								locale: "string (optional) - Language code (default: en)",
								device_type: "string (optional) - Device type (mobile, tablet)",
								app_version: "string (optional) - App version",
								user_id: "string (optional) - User identifier",
							},
						},
						batch_paywalls: {
							method: "POST",
							path: "/paywalls/batch",
							description: "Get multiple paywalls in one request",
							auth_required: true,
							body: {
								placement_ids: "array (required) - Array of placement IDs",
								locale: "string (optional) - Language code",
								device_type: "string (optional) - Device type",
								user_id: "string (optional) - User identifier",
							},
						},
						ab_test_paywall: {
							method: "GET",
							path: "/paywalls/{placementId}/ab-test",
							description: "Get paywall with A/B test variation",
							auth_required: true,
						},
						record_interaction: {
							method: "POST",
							path: "/paywalls/{placementId}/interactions",
							description: "Record paywall interaction",
							auth_required: true,
							body: {
								interaction_type: "string (required) - Type of interaction",
								user_id: "string (optional) - User identifier",
								metadata: "object (optional) - Additional data",
							},
						},
					},
					rate_limits: {
						default: "1000 requests per 15 minutes",
						paywall: "60 requests per minute",
						analytics: "120 requests per minute",
						batch: "10 requests per minute",
					},
					error_codes: {
						400: "Bad Request - Invalid parameters",
						401: "Unauthorized - Invalid or missing token",
						403: "Forbidden - Insufficient permissions",
						404: "Not Found - Resource not found",
						429: "Too Many Requests - Rate limit exceeded",
						500: "Internal Server Error - Server error",
					},
				};

				ctx.body = { data: documentation };
			} catch (error) {
				strapi.log.error("Mobile API - Documentation error:", error);
				ctx.throw(500, "Failed to retrieve API documentation");
			}
		},

		/**
		 * Get API version information
		 */
		async getVersionInfo(ctx) {
			try {
				const versionInfo = {
					version: "v1",
					build: process.env.BUILD_NUMBER || "development",
					release_date: "2024-01-01",
					supported_features: [
						"paywall_delivery",
						"ab_testing",
						"analytics_tracking",
						"batch_operations",
						"caching",
						"rate_limiting",
						"authentication",
					],
					deprecation_notices: [],
					changelog_url: `${strapi.config.get("server.url")}/docs/mobile-api/changelog`,
					support_contact: "<EMAIL>",
				};

				ctx.body = { data: versionInfo };
			} catch (error) {
				strapi.log.error("Mobile API - Version info error:", error);
				ctx.throw(500, "Failed to retrieve version information");
			}
		},

		/**
		 * Get paywalls by multiple placement IDs
		 */
		async getByMultiplePlacements(ctx) {
			try {
				const { placementIds } = ctx.params;
				const { locale = "en", device_type = "mobile", user_id } = ctx.query;

				// Parse placement IDs from comma-separated string
				const placementIdArray = placementIds
					.split(",")
					.map((id) => id.trim())
					.filter(Boolean);

				if (placementIdArray.length === 0) {
					return ctx.badRequest("At least one placement ID is required");
				}

				if (placementIdArray.length > 10) {
					return ctx.badRequest("Maximum 10 placement IDs allowed");
				}

				const paywalls = await strapi
					.service("api::mobile.v1.paywall")
					.getBatchPaywalls({
						placementIds: placementIdArray,
						locale,
						deviceType: device_type,
						userId: user_id,
					});

				// Record analytics for each paywall
				for (const paywall of paywalls) {
					await strapi
						.service("api::analytics.analytics")
						.recordPaywallImpression({
							placement_id: paywall.placement_id,
							user_id,
							app_id: ctx.state.auth?.app_id,
							platform: ctx.state.auth?.platform,
							device_type,
							timestamp: new Date(),
						});
				}

				ctx.body = {
					data: paywalls,
					meta: {
						total: paywalls.length,
						requested: placementIdArray.length,
						locale,
						device_type,
					},
				};
			} catch (error) {
				strapi.log.error("Mobile API - Multiple placements error:", error);
				ctx.throw(500, "Failed to retrieve paywalls");
			}
		},

		/**
		 * Bulk operations for paywalls
		 */
		async bulkOperations(ctx) {
			try {
				const { operations } = ctx.request.body;

				if (!Array.isArray(operations) || operations.length === 0) {
					return ctx.badRequest("Operations array is required");
				}

				if (operations.length > 50) {
					return ctx.badRequest("Maximum 50 operations allowed per request");
				}

				const results = [];

				for (const operation of operations) {
					try {
						let result;

						switch (operation.type) {
							case "get_paywall":
								result = await strapi
									.service("api::mobile.v1.paywall")
									.getPaywallForMobile({
										placementId: operation.placement_id,
										locale: operation.locale || "en",
										deviceType: operation.device_type || "mobile",
										userId: operation.user_id,
									});
								break;

							case "record_interaction":
								await strapi
									.service("api::analytics.analytics")
									.recordUserEngagement({
										placement_id: operation.placement_id,
										interaction_type: operation.interaction_type,
										user_id: operation.user_id,
										app_id: ctx.state.auth?.app_id,
										metadata: operation.metadata,
									});
								result = { success: true };
								break;

							default:
								result = { error: "Unknown operation type" };
						}

						results.push({
							operation_id: operation.id || results.length,
							type: operation.type,
							success: !result.error,
							data: result.error ? null : result,
							error: result.error || null,
						});
					} catch (operationError) {
						results.push({
							operation_id: operation.id || results.length,
							type: operation.type,
							success: false,
							data: null,
							error: operationError.message,
						});
					}
				}

				ctx.body = {
					data: results,
					meta: {
						total_operations: operations.length,
						successful: results.filter((r) => r.success).length,
						failed: results.filter((r) => !r.success).length,
					},
				};
			} catch (error) {
				strapi.log.error("Mobile API - Bulk operations error:", error);
				ctx.throw(500, "Failed to process bulk operations");
			}
		},

		/**
		 * Record batch interactions
		 */
		async recordBatchInteractions(ctx) {
			try {
				const { interactions } = ctx.request.body;

				if (!Array.isArray(interactions) || interactions.length === 0) {
					return ctx.badRequest("Interactions array is required");
				}

				if (interactions.length > 100) {
					return ctx.badRequest("Maximum 100 interactions allowed per batch");
				}

				const results = [];

				for (const interaction of interactions) {
					try {
						await strapi
							.service("api::analytics.analytics")
							.recordUserEngagement({
								placement_id: interaction.placement_id,
								interaction_type: interaction.interaction_type,
								user_id: interaction.user_id || ctx.state.auth?.user_id,
								app_id: ctx.state.auth?.app_id,
								platform: ctx.state.auth?.platform,
								metadata: interaction.metadata,
								timestamp: new Date(interaction.timestamp || Date.now()),
							});

						results.push({
							interaction_id: interaction.id || results.length,
							success: true,
							error: null,
						});
					} catch (interactionError) {
						results.push({
							interaction_id: interaction.id || results.length,
							success: false,
							error: interactionError.message,
						});
					}
				}

				ctx.body = {
					data: {
						processed: results.length,
						successful: results.filter((r) => r.success).length,
						failed: results.filter((r) => !r.success).length,
						results,
					},
				};
			} catch (error) {
				strapi.log.error("Mobile API - Batch interactions error:", error);
				ctx.throw(500, "Failed to record batch interactions");
			}
		},

		/**
		 * Prefetch paywalls for offline use
		 */
		async prefetchPaywalls(ctx) {
			try {
				const {
					placement_ids,
					locale = "en",
					device_type = "mobile",
				} = ctx.request.body;

				if (!Array.isArray(placement_ids) || placement_ids.length === 0) {
					return ctx.badRequest("Placement IDs array is required");
				}

				if (placement_ids.length > 20) {
					return ctx.badRequest(
						"Maximum 20 placement IDs allowed for prefetch",
					);
				}

				const paywalls = await strapi
					.service("api::mobile.v1.paywall")
					.getBatchPaywalls({
						placementIds: placement_ids,
						locale,
						deviceType: device_type,
						userId: ctx.state.auth?.user_id,
					});

				// Add prefetch metadata
				const prefetchData = paywalls.map((paywall) => ({
					...paywall,
					prefetch_info: {
						cached_at: new Date().toISOString(),
						expires_at: new Date(
							Date.now() + 24 * 60 * 60 * 1000,
						).toISOString(), // 24 hours
						cache_key: `prefetch_${paywall.placement_id}_${locale}_${device_type}`,
					},
				}));

				ctx.body = {
					data: prefetchData,
					meta: {
						total: prefetchData.length,
						locale,
						device_type,
						prefetch_expires: new Date(
							Date.now() + 24 * 60 * 60 * 1000,
						).toISOString(),
					},
				};
			} catch (error) {
				strapi.log.error("Mobile API - Prefetch error:", error);
				ctx.throw(500, "Failed to prefetch paywalls");
			}
		},
	}),
);
