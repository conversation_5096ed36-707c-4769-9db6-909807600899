/**
 * Mobile API v1 - Authentication Middleware
 * Token-based authentication for mobile apps
 */

import jwt from "jsonwebtoken";
import type { Context, Next } from "koa";

interface MobileAuthToken {
	app_id: string;
	app_version: string;
	platform: "ios" | "android" | "react_native";
	user_id?: string;
	permissions: string[];
	iat: number;
	exp: number;
}

export default () => {
	return async (ctx: Context, next: Next) => {
		try {
			// Extract token from Authorization header
			const authHeader = ctx.request.headers.authorization;
			if (!authHeader || !authHeader.startsWith("Bearer ")) {
				return ctx.unauthorized("Missing or invalid authorization header");
			}

			const token = authHeader.substring(7); // Remove 'Bearer ' prefix

			// Verify JWT token
			const jwtSecret =
				process.env.MOBILE_API_JWT_SECRET ||
				strapi.config.get("server.app.keys")[0];
			let decoded: MobileAuthToken;

			try {
				decoded = jwt.verify(token, jwtSecret) as MobileAuthToken;
			} catch (jwtError) {
				if (jwtError.name === "TokenExpiredError") {
					return ctx.unauthorized("Token expired");
				} else if (jwtError.name === "JsonWebTokenError") {
					return ctx.unauthorized("Invalid token");
				}
				throw jwtError;
			}

			// Validate token structure
			if (!decoded.app_id || !decoded.platform || !decoded.permissions) {
				return ctx.unauthorized("Invalid token structure");
			}

			// Check if app is authorized
			const authorizedApps = await strapi
				.service("api::mobile.v1.auth")
				.getAuthorizedApps();
			const app = authorizedApps.find((a) => a.app_id === decoded.app_id);

			if (!app) {
				return ctx.unauthorized("App not authorized");
			}

			// Check app version compatibility
			if (app.min_version && decoded.app_version) {
				const isVersionSupported = await strapi
					.service("api::mobile.v1.auth")
					.isVersionSupported(decoded.app_version, app.min_version);

				if (!isVersionSupported) {
					return ctx.badRequest("App version not supported", {
						min_version: app.min_version,
						current_version: decoded.app_version,
					});
				}
			}

			// Check permissions for the current endpoint
			const requiredPermission = getRequiredPermission(
				ctx.request.path,
				ctx.request.method,
			);
			if (
				requiredPermission &&
				!decoded.permissions.includes(requiredPermission)
			) {
				return ctx.forbidden("Insufficient permissions");
			}

			// Add auth context to request
			ctx.state.auth = {
				app_id: decoded.app_id,
				app_version: decoded.app_version,
				platform: decoded.platform,
				user_id: decoded.user_id,
				permissions: decoded.permissions,
				app_config: app,
			};

			// Record API usage
			await strapi.service("api::mobile.v1.analytics").recordAPIUsage({
				app_id: decoded.app_id,
				endpoint: ctx.request.path,
				method: ctx.request.method,
				user_id: decoded.user_id,
				platform: decoded.platform,
				app_version: decoded.app_version,
				timestamp: new Date(),
			});

			await next();
		} catch (error) {
			strapi.log.error("Mobile API auth error:", error);
			ctx.throw(500, "Authentication failed");
		}
	};
};

/**
 * Get required permission for endpoint
 */
function getRequiredPermission(path: string, method: string): string | null {
	const permissionMap: Record<string, string> = {
		"GET:/mobile/v1/paywalls": "paywall:read",
		"POST:/mobile/v1/paywalls/batch": "paywall:read",
		"GET:/mobile/v1/paywalls/*/ab-test": "paywall:read",
		"POST:/mobile/v1/paywalls/*/interactions": "analytics:write",
		"GET:/mobile/v1/paywalls/*/preview": "paywall:preview",
	};

	// Normalize path for wildcard matching
	const normalizedPath = path.replace(/\/[^/]+\/([^/]+)$/, "/*/$1");
	const key = `${method}:${normalizedPath}`;

	return permissionMap[key] || permissionMap[`${method}:${path}`] || null;
}
