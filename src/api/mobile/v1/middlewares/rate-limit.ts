/**
 * Mobile API v1 - Rate Limiting Middleware
 * Prevents API abuse and ensures fair usage
 */

import type { Context, Next } from "koa";

interface RateLimitConfig {
	windowMs: number; // Time window in milliseconds
	maxRequests: number; // Maximum requests per window
	skipSuccessfulRequests?: boolean;
	skipFailedRequests?: boolean;
	keyGenerator?: (ctx: Context) => string;
}

interface RateLimitStore {
	[key: string]: {
		count: number;
		resetTime: number;
	};
}

class RateLimiter {
	private store: RateLimitStore = {};
	private config: RateLimitConfig;

	constructor(config: RateLimitConfig) {
		this.config = config;

		// Clean up expired entries every minute
		setInterval(() => this.cleanup(), 60000);
	}

	async isAllowed(
		key: string,
	): Promise<{ allowed: boolean; remaining: number; resetTime: number }> {
		const now = Date.now();
		const entry = this.store[key];

		if (!entry || now > entry.resetTime) {
			// Create new entry or reset expired entry
			this.store[key] = {
				count: 1,
				resetTime: now + this.config.windowMs,
			};

			return {
				allowed: true,
				remaining: this.config.maxRequests - 1,
				resetTime: this.store[key].resetTime,
			};
		}

		if (entry.count >= this.config.maxRequests) {
			return {
				allowed: false,
				remaining: 0,
				resetTime: entry.resetTime,
			};
		}

		entry.count++;
		return {
			allowed: true,
			remaining: this.config.maxRequests - entry.count,
			resetTime: entry.resetTime,
		};
	}

	private cleanup(): void {
		const now = Date.now();
		Object.keys(this.store).forEach((key) => {
			if (now > this.store[key].resetTime) {
				delete this.store[key];
			}
		});
	}
}

// Create rate limiters for different endpoint types
const rateLimiters = {
	default: new RateLimiter({
		windowMs: 15 * 60 * 1000, // 15 minutes
		maxRequests: 1000, // 1000 requests per 15 minutes
	}),

	paywall: new RateLimiter({
		windowMs: 60 * 1000, // 1 minute
		maxRequests: 60, // 60 requests per minute
	}),

	analytics: new RateLimiter({
		windowMs: 60 * 1000, // 1 minute
		maxRequests: 120, // 120 analytics events per minute
	}),

	batch: new RateLimiter({
		windowMs: 60 * 1000, // 1 minute
		maxRequests: 10, // 10 batch requests per minute
	}),

	health: new RateLimiter({
		windowMs: 60 * 1000, // 1 minute
		maxRequests: 30, // 30 health checks per minute
	}),
};

export default () => {
	return async (ctx: Context, next: Next) => {
		try {
			// Determine rate limiter based on endpoint
			const limiter = getRateLimiterForEndpoint(
				ctx.request.path,
				ctx.request.method,
			);

			// Generate rate limit key
			const key = generateRateLimitKey(ctx);

			// Check rate limit
			const result = await limiter.isAllowed(key);

			// Set rate limit headers
			ctx.set(
				"X-RateLimit-Limit",
				(limiter as any).config.maxRequests.toString(),
			);
			ctx.set("X-RateLimit-Remaining", result.remaining.toString());
			ctx.set("X-RateLimit-Reset", new Date(result.resetTime).toISOString());

			if (!result.allowed) {
				// Log rate limit violation
				await strapi
					.service("api::mobile.v1.analytics")
					.recordRateLimitViolation({
						key,
						endpoint: ctx.request.path,
						method: ctx.request.method,
						app_id: ctx.state.auth?.app_id,
						user_id: ctx.state.auth?.user_id,
						ip_address: ctx.request.ip,
						timestamp: new Date(),
					});

				ctx.status = 429;
				ctx.body = {
					error: "Rate limit exceeded",
					message: "Too many requests. Please try again later.",
					retry_after: Math.ceil((result.resetTime - Date.now()) / 1000),
				};
				return;
			}

			await next();
		} catch (error) {
			strapi.log.error("Rate limiting error:", error);
			// Continue without rate limiting on error to avoid breaking the API
			await next();
		}
	};
};

/**
 * Get appropriate rate limiter for endpoint
 */
function getRateLimiterForEndpoint(path: string, _method: string) {
	if (path.includes("/health")) {
		return rateLimiters.health;
	}

	if (path.includes("/batch")) {
		return rateLimiters.batch;
	}

	if (path.includes("/interactions")) {
		return rateLimiters.analytics;
	}

	if (path.includes("/paywalls")) {
		return rateLimiters.paywall;
	}

	return rateLimiters.default;
}

/**
 * Generate rate limit key based on app and user
 */
function generateRateLimitKey(ctx: Context): string {
	const auth = ctx.state.auth;

	if (auth?.app_id) {
		// Use app_id and user_id if available for more granular limiting
		const userPart = auth.user_id ? `:${auth.user_id}` : "";
		return `mobile:${auth.app_id}${userPart}`;
	}

	// Fallback to IP address for unauthenticated requests
	return `ip:${ctx.request.ip}`;
}
