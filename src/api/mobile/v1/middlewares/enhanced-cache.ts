/**
 * Enhanced Mobile API v1 - Caching Middleware
 * Advanced caching with Redis, ETag support, and CDN integration
 */

import crypto from "node:crypto";
import type { Context, Next } from "koa";

interface CacheConfig {
	ttl: number; // Time to live in seconds
	varyBy?: string[]; // Headers to vary cache by
	skipCache?: (ctx: Context) => boolean;
	tags?: string[]; // Cache tags for invalidation
	compress?: boolean; // Enable compression
	cdnTtl?: number; // CDN cache TTL
}

const cacheConfigs: Record<string, CacheConfig> = {
	"GET:/mobile/v1/paywalls": {
		ttl: 1800, // 30 minutes
		varyBy: ["locale", "device_type", "app_version"],
		skipCache: (ctx) => ctx.query.preview === "true",
		tags: ["paywalls"],
		compress: true,
		cdnTtl: 3600, // 1 hour on CDN
	},

	"GET:/mobile/v1/paywalls/*/ab-test": {
		ttl: 300, // 5 minutes (shorter for A/B tests)
		varyBy: ["locale", "device_type", "user_id"],
		skipCache: (ctx) => !ctx.state.auth?.user_id,
		tags: ["paywalls", "ab-tests"],
		compress: true,
		cdnTtl: 600, // 10 minutes on CDN
	},

	"GET:/mobile/v1/paywalls/*/preview": {
		ttl: 0, // No caching for preview
		skipCache: () => true,
	},

	"POST:/mobile/v1/paywalls/batch": {
		ttl: 900, // 15 minutes
		varyBy: ["locale", "device_type"],
		tags: ["paywalls", "batch"],
		compress: true,
		cdnTtl: 1800, // 30 minutes on CDN
	},

	"GET:/mobile/v1/paywalls/placements/*": {
		ttl: 1800, // 30 minutes
		varyBy: ["locale", "device_type"],
		tags: ["paywalls"],
		compress: true,
		cdnTtl: 3600, // 1 hour on CDN
	},

	"GET:/mobile/v1/health": {
		ttl: 60, // 1 minute
		tags: ["health"],
		cdnTtl: 300, // 5 minutes on CDN
	},

	"GET:/mobile/v1/docs": {
		ttl: 3600, // 1 hour
		tags: ["docs"],
		cdnTtl: 86400, // 24 hours on CDN
	},

	"GET:/mobile/v1/version": {
		ttl: 3600, // 1 hour
		tags: ["version"],
		cdnTtl: 86400, // 24 hours on CDN
	},
};

export default () => {
	return async (ctx: Context, next: Next) => {
		try {
			// Only cache GET requests and specific POST requests
			if (!["GET", "POST"].includes(ctx.request.method)) {
				return await next();
			}

			// Get cache config for this endpoint
			const config = getCacheConfig(ctx.request.path, ctx.request.method);
			if (!config || config.skipCache?.(ctx)) {
				return await next();
			}

			const cacheService = strapi.service("api::caching.redis-cache-service");
			if (!cacheService) {
				return await next();
			}

			// Generate cache key
			const cacheKey = generateCacheKey(ctx, config);

			// Check for conditional requests (ETag)
			const ifNoneMatch = ctx.request.headers["if-none-match"];
			if (ifNoneMatch) {
				const cachedData = await cacheService.get(cacheKey);
				if (cachedData && cachedData.etag === ifNoneMatch) {
					ctx.status = 304; // Not Modified
					return;
				}
			}

			// Try to get from cache
			const cachedResponse = await cacheService.get(cacheKey);
			if (cachedResponse) {
				// Set cache headers
				setCacheHeaders(ctx, cachedResponse, config);

				// Set response
				ctx.status = cachedResponse.status || 200;
				ctx.body = cachedResponse.body;

				// Record cache hit
				await recordCacheMetrics(ctx, "hit", config);
				return;
			}

			// Execute the request
			await next();

			// Cache successful responses
			if (ctx.status >= 200 && ctx.status < 300 && ctx.body) {
				await cacheResponse(ctx, cacheKey, config, cacheService);
			}

			// Record cache miss
			await recordCacheMetrics(ctx, "miss", config);
		} catch (error) {
			strapi.log.error("Enhanced cache middleware error:", error);
			// Continue without caching on error
			if (!ctx.headerSent) {
				await next();
			}
		}
	};
};

/**
 * Get cache configuration for endpoint
 */
function getCacheConfig(path: string, method: string): CacheConfig | null {
	// Normalize path for wildcard matching
	const normalizedPath = path.replace(/\/[^/]+(?=\/[^/]*$)/, "/*");
	const key = `${method}:${normalizedPath}`;

	return cacheConfigs[key] || cacheConfigs[`${method}:${path}`] || null;
}

/**
 * Generate cache key
 */
function generateCacheKey(ctx: Context, config: CacheConfig): string {
	const parts = ["mobile:v1:enhanced", ctx.request.method, ctx.request.path];

	// Add query parameters
	const queryString = new URLSearchParams(ctx.query as any).toString();
	if (queryString) {
		parts.push(queryString);
	}

	// Add vary-by headers
	if (config.varyBy) {
		const varyParts = config.varyBy.map((header) => {
			const value = ctx.query[header] || ctx.request.headers[header] || "";
			return `${header}:${value}`;
		});
		parts.push(...varyParts);
	}

	// Add auth context if available
	if (ctx.state.auth) {
		parts.push(`app:${ctx.state.auth.app_id}`);
		if (ctx.state.auth.user_id) {
			parts.push(`user:${ctx.state.auth.user_id}`);
		}
	}

	// Add POST body hash for POST requests
	if (ctx.request.method === "POST" && ctx.request.body) {
		const bodyHash = crypto
			.createHash("md5")
			.update(JSON.stringify(ctx.request.body))
			.digest("hex");
		parts.push(`body:${bodyHash}`);
	}

	const keyString = parts.join(":");
	return crypto.createHash("md5").update(keyString).digest("hex");
}

/**
 * Cache response
 */
async function cacheResponse(
	ctx: Context,
	cacheKey: string,
	config: CacheConfig,
	cacheService: any,
): Promise<void> {
	try {
		// Generate ETag
		const etag = generateETag(ctx.body);

		const cacheData = {
			status: ctx.status,
			body: ctx.body,
			headers: {
				"content-type": ctx.response.headers["content-type"],
				"content-encoding": ctx.response.headers["content-encoding"],
			},
			etag,
			cachedAt: new Date().toISOString(),
			expiresAt: new Date(Date.now() + config.ttl * 1000).toISOString(),
		};

		// Set cache headers before caching
		setCacheHeaders(ctx, cacheData, config);

		// Cache the response
		await cacheService.set(cacheKey, cacheData, {
			ttl: config.ttl,
			tags: config.tags || [],
			compress: config.compress || false,
		});

		// Warm CDN cache if configured
		if (config.cdnTtl && process.env.CDN_ENABLED === "true") {
			await warmCDNCache(ctx, config);
		}
	} catch (error) {
		strapi.log.error("Cache response error:", error);
	}
}

/**
 * Set cache headers
 */
function setCacheHeaders(
	ctx: Context,
	cacheData: any,
	config: CacheConfig,
): void {
	// Set ETag
	if (cacheData.etag) {
		ctx.set("ETag", cacheData.etag);
	}

	// Set cache control headers
	if (config.ttl > 0) {
		ctx.set("Cache-Control", `public, max-age=${config.ttl}`);

		if (cacheData.expiresAt) {
			ctx.set("Expires", new Date(cacheData.expiresAt).toUTCString());
		}
	} else {
		ctx.set("Cache-Control", "no-cache, no-store, must-revalidate");
	}

	// Set last modified
	if (cacheData.cachedAt) {
		ctx.set("Last-Modified", new Date(cacheData.cachedAt).toUTCString());
	}

	// Set CDN cache headers
	if (config.cdnTtl) {
		ctx.set("CDN-Cache-Control", `public, max-age=${config.cdnTtl}`);
	}

	// Set custom headers
	ctx.set("X-Cache-Status", cacheData.cachedAt ? "HIT" : "MISS");
	ctx.set("X-Cache-Key", generateCacheKeyHash(ctx));
}

/**
 * Generate ETag
 */
function generateETag(body: any): string {
	const content = typeof body === "string" ? body : JSON.stringify(body);
	return `"${crypto.createHash("md5").update(content).digest("hex")}"`;
}

/**
 * Generate cache key hash for headers
 */
function generateCacheKeyHash(ctx: Context): string {
	const keyData = `${ctx.request.method}:${ctx.request.path}:${JSON.stringify(ctx.query)}`;
	return crypto.createHash("md5").update(keyData).digest("hex").substring(0, 8);
}

/**
 * Warm CDN cache
 */
async function warmCDNCache(ctx: Context, _config: CacheConfig): Promise<void> {
	try {
		const cdnService = strapi.service("api::caching.cdn-service");
		if (cdnService) {
			const fullUrl = `${strapi.config.get("server.url")}${ctx.request.path}`;
			await cdnService.preloadContent([fullUrl]);
		}
	} catch (error) {
		strapi.log.warn("CDN cache warming failed:", error);
	}
}

/**
 * Record cache metrics
 */
async function recordCacheMetrics(
	ctx: Context,
	type: "hit" | "miss",
	config: CacheConfig,
): Promise<void> {
	try {
		// Record metrics asynchronously
		setImmediate(async () => {
			try {
				// TODO: Create cache-metrics content type or use existing metrics content type
				// await strapi.entityService.create("api::cache-metrics.cache-metrics", {
				// 	data: {
				// 		endpoint: ctx.request.path,
				// 		method: ctx.request.method,
				// 		cache_type: type,
				// 		app_id: ctx.state.auth?.app_id,
				// 		response_time: ctx.state.responseTime || 0,
				// 		cache_ttl: config.ttl,
				// 		timestamp: new Date(),
				// 	},
				// });
			} catch (error) {
				// Don't throw error for metrics recording failure
				strapi.log.warn("Failed to record cache metrics:", error);
			}
		});
	} catch (_error) {
		// Ignore metrics errors
	}
}
