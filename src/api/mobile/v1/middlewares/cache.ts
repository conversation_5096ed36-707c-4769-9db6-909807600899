/**
 * Mobile API v1 - Caching Middleware
 * Optimizes response times for mobile apps
 */

import crypto from "node:crypto";
import type { Context, Next } from "koa";

interface CacheConfig {
	ttl: number; // Time to live in seconds
	varyBy?: string[]; // Headers to vary cache by
	skipCache?: (ctx: Context) => boolean;
}

const cacheConfigs: Record<string, CacheConfig> = {
	"GET:/mobile/v1/paywalls": {
		ttl: 300, // 5 minutes
		varyBy: ["locale", "device_type", "app_version"],
		skipCache: (ctx) => ctx.query.preview === "true",
	},

	"GET:/mobile/v1/paywalls/*/ab-test": {
		ttl: 60, // 1 minute (shorter for A/B tests)
		varyBy: ["locale", "device_type", "user_id"],
		skipCache: (ctx) => !ctx.state.auth?.user_id,
	},

	"GET:/mobile/v1/paywalls/*/preview": {
		ttl: 0, // No caching for preview
		skipCache: () => true,
	},
};

export default () => {
	return async (ctx: Context, next: Next) => {
		try {
			// Only cache GET requests
			if (ctx.request.method !== "GET") {
				return await next();
			}

			// Get cache config for this endpoint
			const cacheKey = `${ctx.request.method}:${ctx.request.path}`;
			const normalizedKey = normalizePathForCache(cacheKey);
			const config = cacheConfigs[normalizedKey];

			if (!config || config.ttl === 0 || config.skipCache?.(ctx)) {
				return await next();
			}

			// Generate cache key
			const fullCacheKey = generateCacheKey(ctx, config);

			// Try to get from cache
			const cached = await getCachedResponse(fullCacheKey);
			if (cached) {
				// Set cache headers
				ctx.set("X-Cache", "HIT");
				ctx.set("X-Cache-Key", fullCacheKey);
				ctx.set("Cache-Control", `public, max-age=${config.ttl}`);
				ctx.set("ETag", cached.etag);

				// Check if client has current version
				if (ctx.request.headers["if-none-match"] === cached.etag) {
					ctx.status = 304;
					return;
				}

				ctx.status = cached.status;
				ctx.body = cached.body;

				// Set any custom headers from cached response
				if (cached.headers) {
					Object.entries(cached.headers).forEach(([key, value]) => {
						ctx.set(key, value as string);
					});
				}

				return;
			}

			// Cache miss - execute request
			await next();

			// Cache successful responses
			if (ctx.status >= 200 && ctx.status < 300 && ctx.body) {
				await setCachedResponse(
					fullCacheKey,
					{
						status: ctx.status,
						body: ctx.body,
						headers: extractCacheableHeaders(ctx),
						etag: generateETag(ctx.body),
						cachedAt: new Date().toISOString(),
					},
					config.ttl,
				);

				ctx.set("X-Cache", "MISS");
				ctx.set("X-Cache-Key", fullCacheKey);
				ctx.set("Cache-Control", `public, max-age=${config.ttl}`);
				ctx.set("ETag", generateETag(ctx.body));
			}
		} catch (error) {
			strapi.log.error("Cache middleware error:", error);
			// Continue without caching on error
			await next();
		}
	};
};

/**
 * Normalize path for cache key matching
 */
function normalizePathForCache(path: string): string {
	// Replace dynamic segments with wildcards
	return path.replace(/\/[^/]+\/([^/]+)$/, "/*/$1");
}

/**
 * Generate cache key based on request context
 */
function generateCacheKey(ctx: Context, config: CacheConfig): string {
	const parts = [
		ctx.request.method,
		ctx.request.path,
		ctx.state.auth?.app_id || "anonymous",
	];

	// Add vary-by parameters
	if (config.varyBy) {
		config.varyBy.forEach((header) => {
			const value = ctx.query[header] || ctx.request.headers[header] || "";
			parts.push(`${header}:${value}`);
		});
	}

	// Create hash of the key parts
	const keyString = parts.join("|");
	return `mobile:v1:${crypto.createHash("md5").update(keyString).digest("hex")}`;
}

/**
 * Generate ETag for response body
 */
function generateETag(body: any): string {
	const content = typeof body === "string" ? body : JSON.stringify(body);
	return `"${crypto.createHash("md5").update(content).digest("hex")}"`;
}

/**
 * Extract headers that should be cached
 */
function extractCacheableHeaders(ctx: Context): Record<string, string> {
	const cacheableHeaders = ["content-type", "content-encoding"];
	const headers: Record<string, string> = {};

	cacheableHeaders.forEach((header) => {
		const value = ctx.response.headers[header];
		if (value) {
			headers[header] = value as string;
		}
	});

	return headers;
}

/**
 * Get cached response
 */
async function getCachedResponse(key: string): Promise<any> {
	try {
		// Try Redis cache first if available
		if ((strapi as any).cache) {
			return await (strapi as any).cache.get(key);
		}

		// Fallback to in-memory cache (for development)
		return inMemoryCache.get(key);
	} catch (error) {
		strapi.log.error("Cache get error:", error);
		return null;
	}
}

/**
 * Set cached response
 */
async function setCachedResponse(
	key: string,
	data: any,
	ttl: number,
): Promise<void> {
	try {
		// Try Redis cache first if available
		if ((strapi as any).cache) {
			await (strapi as any).cache.set(key, data, ttl * 1000); // Convert to milliseconds
			return;
		}

		// Fallback to in-memory cache (for development)
		inMemoryCache.set(key, data, ttl);
	} catch (error) {
		strapi.log.error("Cache set error:", error);
	}
}

// Simple in-memory cache for development
class InMemoryCache {
	private cache = new Map<string, { data: any; expires: number }>();

	get(key: string): any {
		const entry = this.cache.get(key);
		if (!entry || Date.now() > entry.expires) {
			this.cache.delete(key);
			return null;
		}
		return entry.data;
	}

	set(key: string, data: any, ttl: number): void {
		this.cache.set(key, {
			data,
			expires: Date.now() + ttl * 1000,
		});
	}

	delete(key: string): void {
		this.cache.delete(key);
	}

	clear(): void {
		this.cache.clear();
	}
}

const inMemoryCache = new InMemoryCache();

// Clean up expired entries every 5 minutes
setInterval(
	() => {
		// This will happen automatically when accessing expired entries
	},
	5 * 60 * 1000,
);
