/**
 * Mobile API v1 - Analytics Middleware
 * Tracks API usage and performance metrics
 */

import type { Context, Next } from "koa";

export default () => {
	return async (ctx: Context, next: Next) => {
		const startTime = Date.now();
		let error: Error | null = null;

		try {
			await next();
		} catch (err) {
			error = err as Error;
			throw err;
		} finally {
			// Record analytics asynchronously to avoid blocking the response
			setImmediate(async () => {
				try {
					await recordAPIUsage(ctx, startTime, error);
				} catch (analyticsError) {
					strapi.log.error("Analytics recording failed:", analyticsError);
				}
			});
		}
	};
};

/**
 * Record API usage metrics
 */
async function recordAPIUsage(
	ctx: Context,
	startTime: number,
	error: Error | null,
): Promise<void> {
	try {
		const endTime = Date.now();
		const responseTime = endTime - startTime;
		const auth = ctx.state.auth;

		const usageData = {
			app_id: auth?.app_id || "anonymous",
			user_id: auth?.user_id,
			platform: auth?.platform,
			app_version: auth?.app_version,
			endpoint: ctx.request.path,
			method: ctx.request.method,
			status_code: ctx.status,
			response_time: responseTime,
			request_size: getRequestSize(ctx),
			response_size: getResponseSize(ctx),
			ip_address: ctx.request.ip,
			user_agent: ctx.request.headers["user-agent"],
			referer: ctx.request.headers.referer,
			timestamp: new Date(startTime),
			error_message: error?.message,
			query_params: sanitizeQueryParams(ctx.query),
			cache_status: ctx.response.headers["x-cache"] || "NONE",
		};

		// Store in mobile API usage collection
		await strapi.entityService.create(
			"api::mobile-api-usage.mobile-api-usage",
			{
				data: {
					...usageData,
					publishedAt: new Date(),
				},
			},
		);

		// Also record in general API performance metrics
		await strapi.service("api::api-performance.api-performance").recordMetric({
			endpoint: ctx.request.path,
			method: ctx.request.method,
			responseTime: responseTime,
			statusCode: ctx.status,
			timestamp: new Date(startTime),
			userAgent: ctx.request.headers["user-agent"],
			ipAddress: ctx.request.ip,
			requestSize: getRequestSize(ctx),
			responseSize: getResponseSize(ctx),
			errorMessage: error?.message,
		});

		// Record performance alerts if needed
		await checkPerformanceThresholds(ctx, responseTime, error);
	} catch (recordingError) {
		strapi.log.error("Failed to record API usage:", recordingError);
	}
}

/**
 * Get request size in bytes
 */
function getRequestSize(ctx: Context): number {
	const contentLength = ctx.request.headers["content-length"];
	if (contentLength) {
		return parseInt(contentLength, 10);
	}

	// Estimate size if content-length is not available
	if (ctx.request.body) {
		const bodyString =
			typeof ctx.request.body === "string"
				? ctx.request.body
				: JSON.stringify(ctx.request.body);
		return Buffer.byteLength(bodyString, "utf8");
	}

	return 0;
}

/**
 * Get response size in bytes
 */
function getResponseSize(ctx: Context): number {
	const contentLength = ctx.response.headers["content-length"];
	if (contentLength) {
		return parseInt(contentLength as string, 10);
	}

	// Estimate size if content-length is not available
	if (ctx.body) {
		const bodyString =
			typeof ctx.body === "string" ? ctx.body : JSON.stringify(ctx.body);
		return Buffer.byteLength(bodyString, "utf8");
	}

	return 0;
}

/**
 * Sanitize query parameters for logging
 */
function sanitizeQueryParams(query: any): any {
	const sanitized = { ...query };

	// Remove sensitive parameters
	const sensitiveParams = ["api_key", "token", "password", "secret"];
	sensitiveParams.forEach((param) => {
		if (sanitized[param]) {
			sanitized[param] = "[REDACTED]";
		}
	});

	return sanitized;
}

/**
 * Check performance thresholds and create alerts
 */
async function checkPerformanceThresholds(
	ctx: Context,
	responseTime: number,
	error: Error | null,
): Promise<void> {
	try {
		const thresholds = {
			responseTimeWarning: 2000, // 2 seconds for mobile
			responseTimeCritical: 5000, // 5 seconds
			errorRateThreshold: 5, // 5% error rate
		};

		// Check slow response times
		if (responseTime > thresholds.responseTimeCritical) {
			await createMobileAPIAlert({
				type: "slow_mobile_response",
				severity: "critical",
				message: `Critical slow mobile API response: ${ctx.request.method} ${ctx.request.path} took ${responseTime}ms`,
				endpoint: ctx.request.path,
				method: ctx.request.method,
				responseTime,
				threshold: thresholds.responseTimeCritical,
				app_id: ctx.state.auth?.app_id,
			});
		} else if (responseTime > thresholds.responseTimeWarning) {
			await createMobileAPIAlert({
				type: "slow_mobile_response",
				severity: "warning",
				message: `Slow mobile API response: ${ctx.request.method} ${ctx.request.path} took ${responseTime}ms`,
				endpoint: ctx.request.path,
				method: ctx.request.method,
				responseTime,
				threshold: thresholds.responseTimeWarning,
				app_id: ctx.state.auth?.app_id,
			});
		}

		// Check for errors
		if (error || ctx.status >= 500) {
			await createMobileAPIAlert({
				type: "mobile_api_error",
				severity: "high",
				message: `Mobile API error: ${ctx.request.method} ${ctx.request.path} - ${error?.message || "Server error"}`,
				endpoint: ctx.request.path,
				method: ctx.request.method,
				statusCode: ctx.status,
				errorMessage: error?.message,
				app_id: ctx.state.auth?.app_id,
			});
		}

		// Check error rate for the app
		if (ctx.state.auth?.app_id) {
			await checkAppErrorRate(
				ctx.state.auth.app_id,
				thresholds.errorRateThreshold,
			);
		}
	} catch (alertError) {
		strapi.log.error("Failed to check performance thresholds:", alertError);
	}
}

/**
 * Create mobile API performance alert
 */
async function createMobileAPIAlert(alertData: any): Promise<void> {
	try {
		await strapi.entityService.create(
			"api::performance-alert.performance-alert",
			{
				data: {
					alertId: `mobile_api_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
					type: alertData.type,
					severity: alertData.severity,
					message: alertData.message,
					currentValue: alertData.responseTime || alertData.statusCode || 0,
					previousValue: 0,
					threshold: alertData.threshold || 0,
					timestamp: new Date(),
					affectedMetrics: ["mobile_api_performance"],
					recommendedActions: getMobileAPIRecommendedActions(alertData.type),
					status: "active",
				},
			},
		);
	} catch (error) {
		strapi.log.error("Failed to create mobile API alert:", error);
	}
}

/**
 * Check app-specific error rate
 */
async function checkAppErrorRate(
	appId: string,
	threshold: number,
): Promise<void> {
	try {
		const since = new Date(Date.now() - 5 * 60 * 1000); // Last 5 minutes

		const recentUsage = await strapi.entityService.findMany(
			"api::mobile-api-usage.mobile-api-usage",
			{
				filters: {
					app_id: appId,
					timestamp: {
						$gte: since.toISOString(),
					},
				},
			},
		);

		if (recentUsage.length < 10) {
			return; // Not enough data to determine error rate
		}

		const errorCount = recentUsage.filter(
			(usage) => usage.status_code >= 400,
		).length;
		const errorRate = (errorCount / recentUsage.length) * 100;

		if (errorRate > threshold) {
			await createMobileAPIAlert({
				type: "high_mobile_error_rate",
				severity: errorRate > 15 ? "critical" : "high",
				message: `High error rate for mobile app ${appId}: ${errorRate.toFixed(1)}%`,
				app_id: appId,
				errorRate,
				threshold,
			});
		}
	} catch (error) {
		strapi.log.error("Failed to check app error rate:", error);
	}
}

/**
 * Get recommended actions for mobile API alerts
 */
function getMobileAPIRecommendedActions(alertType: string): string[] {
	const actionMap: Record<string, string[]> = {
		slow_mobile_response: [
			"Optimize database queries",
			"Check mobile-specific caching",
			"Review data serialization",
			"Consider response compression",
			"Monitor CDN performance",
		],
		mobile_api_error: [
			"Check mobile app compatibility",
			"Review API version support",
			"Verify authentication flow",
			"Check network connectivity",
			"Review error handling",
		],
		high_mobile_error_rate: [
			"Review mobile app integration",
			"Check API documentation",
			"Verify authentication setup",
			"Monitor app version distribution",
			"Review rate limiting configuration",
		],
	};

	return (
		actionMap[alertType] || [
			"Investigate mobile API issue",
			"Check mobile app logs",
			"Review API documentation",
			"Contact mobile development team",
		]
	);
}
