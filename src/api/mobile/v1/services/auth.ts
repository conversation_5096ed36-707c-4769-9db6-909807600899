/**
 * Mobile API v1 - Authentication Service
 * Manages mobile app authentication and authorization
 */

import { factories } from "@strapi/strapi";
import jwt from "jsonwebtoken";

interface MobileApp {
	id: string;
	app_id: string;
	name: string;
	platform: "ios" | "android" | "react_native";
	bundle_id: string;
	api_key: string;
	secret_key: string;
	min_version: string;
	max_version?: string;
	permissions: string[];
	rate_limits: {
		requests_per_minute: number;
		burst_limit: number;
	};
	is_active: boolean;
	created_at: Date;
	updated_at: Date;
}

export default factories.createCoreService(
	"api::mobile-app.mobile-app",
	({ strapi }) => ({
		/**
		 * Generate JWT token for mobile app
		 */
		async generateToken(
			appId: string,
			userId?: string,
			customPermissions?: string[],
		): Promise<string> {
			try {
				const app = await this.getAppById(appId);
				if (!app || !app.is_active) {
					throw new Error("App not found or inactive");
				}

				const permissions = customPermissions || app.permissions;
				const jwtSecret =
					process.env.MOBILE_API_JWT_SECRET ||
					strapi.config.get("server.app.keys")[0];

				const payload = {
					app_id: app.app_id,
					app_version: app.min_version,
					platform: app.platform,
					user_id: userId,
					permissions: permissions,
					iat: Math.floor(Date.now() / 1000),
					exp: Math.floor(Date.now() / 1000) + 24 * 60 * 60, // 24 hours
				};

				return jwt.sign(payload, jwtSecret);
			} catch (error) {
				strapi.log.error("Failed to generate token:", error);
				throw error;
			}
		},

		/**
		 * Validate API key and secret
		 */
		async validateApiCredentials(
			apiKey: string,
			secretKey: string,
		): Promise<MobileApp | null> {
			try {
				const apps = (await strapi.entityService.findMany(
					"api::mobile-app.mobile-app",
					{
						filters: {
							api_key: apiKey,
							is_active: true,
						},
					},
				)) as any[];

				if (!apps || apps.length === 0) {
					return null;
				}

				const app = apps[0] as MobileApp;

				// Verify secret key (assuming it's hashed)
				const isValidSecret = await this.verifySecretKey(
					secretKey,
					app.secret_key,
				);
				if (!isValidSecret) {
					return null;
				}

				return app;
			} catch (error) {
				strapi.log.error("Failed to validate API credentials:", error);
				return null;
			}
		},

		/**
		 * Get authorized apps
		 */
		async getAuthorizedApps(): Promise<MobileApp[]> {
			try {
				const apps = (await strapi.entityService.findMany(
					"api::mobile-app.mobile-app",
					{
						filters: {
							is_active: true,
						},
					},
				)) as any[];

				return Array.isArray(apps)
					? (apps as MobileApp[])
					: [apps as MobileApp];
			} catch (error) {
				strapi.log.error("Failed to get authorized apps:", error);
				return [];
			}
		},

		/**
		 * Get app by ID
		 */
		async getAppById(appId: string): Promise<MobileApp | null> {
			try {
				const apps = (await strapi.entityService.findMany(
					"api::mobile-app.mobile-app",
					{
						filters: {
							app_id: appId,
						},
						limit: 1,
					},
				)) as any[];

				if (!apps || apps.length === 0) {
					return null;
				}

				return apps[0] as MobileApp;
			} catch (error) {
				strapi.log.error("Failed to get app by ID:", error);
				return null;
			}
		},

		/**
		 * Get app by internal ID
		 */
		async getAppByInternalId(id: string): Promise<MobileApp | null> {
			try {
				const app = (await strapi.entityService.findOne(
					"api::mobile-app.mobile-app",
					id,
				)) as MobileApp;
				return app?.is_active ? app : null;
			} catch (error) {
				strapi.log.error("Failed to get app by internal ID:", error);
				return null;
			}
		},

		/**
		 * Check if app version is supported
		 */
		async isVersionSupported(appId: string, version: string): Promise<boolean> {
			try {
				const app = await this.getAppById(appId);
				if (!app) {
					return false;
				}

				const current = this.parseVersion(version);
				const min = this.parseVersion(app.min_version);
				const maxVersion = app.max_version;

				if (this.compareVersions(current, min) < 0) {
					return false; // Current version is below minimum
				}

				if (maxVersion) {
					const max = this.parseVersion(maxVersion);
					if (this.compareVersions(current, max) > 0) {
						return false; // Current version is above maximum
					}
				}

				return true;
			} catch (error) {
				strapi.log.error("Failed to check version support:", error);
				return false;
			}
		},

		/**
		 * Create new mobile app registration
		 */
		async createMobileApp(appData: {
			name: string;
			platform: "ios" | "android" | "react_native";
			bundle_id: string;
			min_version: string;
			permissions: string[];
		}): Promise<MobileApp> {
			try {
				const appId = this.generateAppId();
				const apiKey = this.generateApiKey();
				const secretKey = this.generateSecretKey();
				const hashedSecret = await this.hashSecretKey(secretKey);

				const app = (await strapi.entityService.create(
					"api::mobile-app.mobile-app",
					{
						data: {
							app_id: appId,
							name: appData.name,
							platform: appData.platform,
							bundle_id: appData.bundle_id,
							api_key: apiKey,
							secret_key: hashedSecret,
							min_version: appData.min_version,
							permissions: appData.permissions,
							rate_limits: {
								requests_per_minute: 100,
								burst_limit: 200,
							},
							is_active: true,
							publishedAt: new Date(),
						},
					},
				)) as MobileApp;

				// Return app with plain secret key for initial setup
				return { ...app, secret_key: secretKey };
			} catch (error) {
				strapi.log.error("Failed to create mobile app:", error);
				throw error;
			}
		},

		/**
		 * Update mobile app
		 */
		async updateMobileApp(
			appId: string,
			updates: Partial<MobileApp>,
		): Promise<MobileApp> {
			try {
				const app = await this.getAppById(appId);
				if (!app) {
					throw new Error("App not found");
				}

				// Hash secret key if provided
				if (updates.secret_key) {
					updates.secret_key = await this.hashSecretKey(updates.secret_key);
				}

				const updatedApp = (await strapi.entityService.update(
					"api::mobile-app.mobile-app",
					app.id,
					{
						data: {
							...updates,
							updated_at: new Date(),
						},
					},
				)) as MobileApp;

				return updatedApp;
			} catch (error) {
				strapi.log.error("Failed to update mobile app:", error);
				throw error;
			}
		},

		/**
		 * Revoke app access
		 */
		async revokeAppAccess(appId: string): Promise<void> {
			try {
				const app = await this.getAppById(appId);
				if (!app) {
					throw new Error("App not found");
				}

				await strapi.entityService.update(
					"api::mobile-app.mobile-app",
					app.id,
					{
						data: {
							is_active: false,
							updated_at: new Date(),
						} as any,
					},
				);

				// Invalidate all tokens for this app (if using token blacklist)
				await this.invalidateAppTokens(appId);
			} catch (error) {
				strapi.log.error("Failed to revoke app access:", error);
				throw error;
			}
		},

		/**
		 * Get app usage statistics
		 */
		async getAppUsageStats(appId: string, days: number = 30): Promise<any> {
			try {
				const since = new Date(Date.now() - days * 24 * 60 * 60 * 1000);

				// Get API usage stats
				const apiUsage = (await strapi.entityService.findMany(
					"api::mobile-api-usage.mobile-api-usage",
					{
						filters: {
							app_id: appId,
							timestamp: {
								$gte: since.toISOString(),
							},
						},
					},
				)) as any[];

				// Calculate statistics
				const usageArray = Array.isArray(apiUsage) ? apiUsage : [apiUsage];
				const totalRequests = usageArray.length;
				const uniqueUsers = new Set(
					usageArray.map((u: any) => u.user_id).filter(Boolean),
				).size;
				const endpointUsage = usageArray.reduce((acc: any, usage: any) => {
					const key = `${usage.method} ${usage.endpoint}`;
					acc[key] = (acc[key] || 0) + 1;
					return acc;
				}, {});

				const errorRate =
					usageArray.filter((u: any) => u.status_code >= 400).length /
					totalRequests;

				return {
					total_requests: totalRequests,
					unique_users: uniqueUsers,
					error_rate: errorRate,
					endpoint_usage: endpointUsage,
					daily_usage: this.groupUsageByDay(usageArray),
					platform_breakdown: this.groupUsageByPlatform(usageArray),
				};
			} catch (error) {
				strapi.log.error("Failed to get app usage stats:", error);
				throw error;
			}
		},

		/**
		 * Verify secret key (placeholder implementation)
		 */
		async verifySecretKey(
			plainSecret: string,
			hashedSecret: string,
		): Promise<boolean> {
			// Simple comparison for now - in production, use proper hashing
			return plainSecret === hashedSecret;
		},

		/**
		 * Parse version string
		 */
		parseVersion(version: string): number[] {
			return version.split(".").map((v) => parseInt(v, 10));
		},

		/**
		 * Compare version arrays
		 */
		compareVersions(v1: number[], v2: number[]): number {
			for (let i = 0; i < Math.max(v1.length, v2.length); i++) {
				const a = v1[i] || 0;
				const b = v2[i] || 0;
				if (a > b) return 1;
				if (a < b) return -1;
			}
			return 0;
		},

		/**
		 * Generate app ID
		 */
		generateAppId(): string {
			return `app_${Math.random().toString(36).substr(2, 9)}`;
		},

		/**
		 * Generate API key
		 */
		generateApiKey(): string {
			return `ak_${Math.random().toString(36).substr(2, 16)}`;
		},

		/**
		 * Generate secret key
		 */
		generateSecretKey(): string {
			return `sk_${Math.random().toString(36).substr(2, 32)}`;
		},

		/**
		 * Hash secret key
		 */
		async hashSecretKey(secretKey: string): Promise<string> {
			// Simple implementation - use proper hashing in production
			return secretKey;
		},

		/**
		 * Invalidate app tokens (placeholder)
		 */
		async invalidateAppTokens(appId: string): Promise<void> {
			// Implementation for token invalidation
			console.log(`Invalidating tokens for app: ${appId}`);
		},

		/**
		 * Group usage by day
		 */
		groupUsageByDay(usage: any[]): any {
			return usage.reduce((acc, item) => {
				const day = new Date(item.timestamp).toISOString().split("T")[0];
				acc[day] = (acc[day] || 0) + 1;
				return acc;
			}, {});
		},

		/**
		 * Group usage by platform
		 */
		groupUsageByPlatform(usage: any[]): any {
			return usage.reduce((acc, item) => {
				const platform = item.platform || "unknown";
				acc[platform] = (acc[platform] || 0) + 1;
				return acc;
			}, {});
		},
	}),
);
