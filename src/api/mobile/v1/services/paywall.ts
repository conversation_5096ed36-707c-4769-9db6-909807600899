/**
 * Mobile API v1 - Paywall Service
 * Business logic for mobile-optimized paywall delivery
 */

import { factories } from "@strapi/strapi";

export default factories.createCoreService(
	"api::paywall.paywall",
	({ strapi }) => ({
		/**
		 * Get paywall optimized for mobile consumption
		 */
		async getPaywallForMobile({
			placementId,
			locale = "en",
			version = "latest",
			deviceType = "mobile",
			appVersion = "1.0.0",
			userId,
		}: {
			placementId: string;
			locale?: string;
			version?: string;
			deviceType?: string;
			appVersion?: string;
			userId?: string;
		}) {
			try {
				// Find paywall by placement ID
				const paywalls = await strapi.entityService.findMany(
					"api::paywall.paywall",
					{
						filters: {
							placement_id: placementId,
							publishedAt: { $notNull: true },
						},
						locale,
						populate: this.getMobilePopulateConfig(deviceType),
						limit: 1,
					},
				);

				if (!paywalls || paywalls.length === 0) {
					return null;
				}

				const paywall = paywalls[0];

				// Transform for mobile consumption
				return this.transformForMobile(paywall, {
					deviceType,
					appVersion,
					userId,
					locale,
				});
			} catch (error) {
				strapi.log.error("Failed to get paywall for mobile:", error);
				throw error;
			}
		},

		/**
		 * Get multiple paywalls in batch
		 */
		async getBatchPaywalls({
			placementIds,
			locale = "en",
			deviceType = "mobile",
			userId,
		}) {
			try {
				const paywalls = await strapi.entityService.findMany(
					"api::paywall.paywall",
					{
						filters: {
							placement_id: { $in: placementIds },
							publishedAt: { $notNull: true },
						},
						locale,
						populate: this.getMobilePopulateConfig(deviceType),
					},
				);

				return paywalls.map((paywall) =>
					this.transformForMobile(paywall, { deviceType, userId, locale }),
				);
			} catch (error) {
				strapi.log.error("Failed to get batch paywalls:", error);
				throw error;
			}
		},

		/**
		 * Get paywall with A/B test variation
		 */
		async getPaywallWithABTest({
			placementId,
			locale = "en",
			deviceType = "mobile",
			userId,
			appVersion = "1.0.0",
			abTestId,
		}: {
			placementId: string;
			locale?: string;
			deviceType?: string;
			userId?: string;
			appVersion?: string;
			abTestId?: string;
		}) {
			try {
				// Get base paywall
				const paywall = await this.getPaywallForMobile({
					placementId,
					locale,
					deviceType,
					userId,
					appVersion,
				});

				if (!paywall) {
					return { paywall: null, abTestVariation: null };
				}

				// Check for active A/B tests
				let abTestVariation = null;
				if (abTestId) {
					abTestVariation = await strapi
						.service("api::ab-testing.ab-test-manager")
						.getUserVariation(abTestId, userId);
				} else {
					// Find active tests for this placement
					const activeTests = await strapi
						.service("api::ab-testing.ab-test-manager")
						.getActiveTestsForPlacement(placementId);

					if (activeTests.length > 0) {
						abTestVariation = await strapi
							.service("api::ab-testing.ab-test-manager")
							.getUserVariation(activeTests[0].id, userId);
					}
				}

				// Apply A/B test variation if exists
				if (abTestVariation?.paywallConfig) {
					const modifiedPaywall = this.applyABTestVariation(
						paywall,
						abTestVariation.paywallConfig,
					);
					return { paywall: modifiedPaywall, abTestVariation };
				}

				return { paywall, abTestVariation: null };
			} catch (error) {
				strapi.log.error("Failed to get paywall with A/B test:", error);
				throw error;
			}
		},

		/**
		 * Get paywall preview (draft/unpublished content)
		 */
		async getPaywallPreview({
			placementId,
			locale = "en",
			deviceType = "mobile",
		}) {
			try {
				const paywalls = await strapi.entityService.findMany(
					"api::paywall.paywall",
					{
						filters: { placement_id: placementId },
						locale,
						populate: this.getMobilePopulateConfig(deviceType),
						publicationState: "preview", // Include draft content
						limit: 1,
					},
				);

				if (!paywalls || paywalls.length === 0) {
					return null;
				}

				return this.transformForMobile(paywalls[0], {
					deviceType,
					locale,
					isPreview: true,
				});
			} catch (error) {
				strapi.log.error("Failed to get paywall preview:", error);
				throw error;
			}
		},

		/**
		 * Get mobile-optimized populate configuration
		 */
		getMobilePopulateConfig(deviceType = "mobile") {
			const baseConfig = {
				theme: {
					populate: {
						background_image: {
							populate: ["paywall_optimizations"],
						},
						logo: {
							populate: ["paywall_optimizations"],
						},
					},
				},
				features: {
					populate: {
						icon_image: {
							populate: ["paywall_optimizations"],
						},
					},
				},
				testimonials: {
					populate: {
						author_avatar: {
							populate: ["paywall_optimizations"],
						},
					},
				},
				product_labels: true,
			};

			// Device-specific optimizations
			if (deviceType === "tablet") {
				// Tablet might need different image sizes
				baseConfig.theme.populate.background_image.populate.push(
					"tablet_optimizations",
				);
			}

			return baseConfig;
		},

		/**
		 * Transform paywall data for mobile consumption
		 */
		transformForMobile(
			paywall: any,
			options: {
				deviceType?: string;
				appVersion?: string;
				userId?: string;
				locale?: string;
				isPreview?: boolean;
			} = {},
		) {
			const {
				deviceType = "mobile",
				appVersion,
				userId,
				locale,
				isPreview = false,
			} = options;

			// Base transformation
			const transformed = {
				id: paywall.id,
				placement_id: paywall.placement_id,
				title: paywall.title,
				subtitle: paywall.subtitle,
				description: paywall.description_text,
				cta_text: paywall.cta_text,
				cta_secondary_text: paywall.cta_secondary_text,

				// Transform theme with optimized images
				theme: this.transformThemeForMobile(paywall.theme, deviceType),

				// Transform features
				features: this.transformFeaturesForMobile(paywall.features, deviceType),

				// Transform testimonials
				testimonials: this.transformTestimonialsForMobile(
					paywall.testimonials,
					deviceType,
				),

				// Transform product labels
				product_labels: this.transformProductLabelsForMobile(
					paywall.product_labels,
				),

				// Metadata
				locale: locale,
				last_updated: paywall.updatedAt,
				version: paywall.adapty_version || "1.0.0",
			};

			// Add preview warning if applicable
			if (isPreview) {
				(transformed as any).preview_warning =
					"This is preview content and may not reflect the published version";
			}

			// Device-specific optimizations
			if (deviceType === "mobile") {
				// Limit features for mobile to prevent overcrowding
				if (transformed.features && transformed.features.length > 6) {
					transformed.features = transformed.features.slice(0, 6);
					(transformed as any).features_truncated = true;
				}

				// Limit testimonials for mobile
				if (transformed.testimonials && transformed.testimonials.length > 3) {
					transformed.testimonials = transformed.testimonials.slice(0, 3);
					(transformed as any).testimonials_truncated = true;
				}
			}

			return transformed;
		},

		/**
		 * Transform theme for mobile
		 */
		transformThemeForMobile(theme, deviceType) {
			if (!theme) return null;

			return {
				primary_color: theme.primary_color,
				secondary_color: theme.secondary_color,
				background_color: theme.background_color,
				text_color: theme.text_color,
				button_style: theme.button_style,
				layout_style: theme.layout_style,

				// Optimized images
				background_image: this.getOptimizedImageUrl(
					theme.background_image,
					deviceType,
				),
				logo: this.getOptimizedImageUrl(theme.logo, deviceType),

				// Mobile-specific adjustments
				mobile_adjustments: {
					font_scale: deviceType === "mobile" ? 0.9 : 1.0,
					padding_scale: deviceType === "mobile" ? 0.8 : 1.0,
				},
			};
		},

		/**
		 * Transform features for mobile
		 */
		transformFeaturesForMobile(features, deviceType) {
			if (!features || !Array.isArray(features)) return [];

			return features
				.map((feature) => ({
					id: feature.id,
					title: feature.title,
					description: feature.description,
					icon_url: this.getOptimizedImageUrl(feature.icon_image, deviceType),
					order: feature.order || 0,
				}))
				.sort((a, b) => a.order - b.order);
		},

		/**
		 * Transform testimonials for mobile
		 */
		transformTestimonialsForMobile(testimonials, deviceType) {
			if (!testimonials || !Array.isArray(testimonials)) return [];

			return testimonials.map((testimonial) => ({
				id: testimonial.id,
				content: testimonial.content,
				author_name: testimonial.author_name,
				author_title: testimonial.author_title,
				rating: testimonial.rating,
				avatar_url: this.getOptimizedImageUrl(
					testimonial.author_avatar,
					deviceType,
				),
			}));
		},

		/**
		 * Transform product labels for mobile
		 */
		transformProductLabelsForMobile(productLabels) {
			if (!productLabels || !Array.isArray(productLabels)) return [];

			return productLabels.map((label) => ({
				id: label.id,
				text: label.text,
				style: label.style,
				color: label.color,
				background_color: label.background_color,
				position: label.position,
			}));
		},

		/**
		 * Get optimized image URL for device type
		 */
		getOptimizedImageUrl(image, deviceType) {
			if (!image) return null;

			// Check for device-specific optimizations
			const optimizations = image.paywall_optimizations;
			if (optimizations?.[deviceType]) {
				return optimizations[deviceType].url;
			}

			// Fallback to original image
			return image.url;
		},

		/**
		 * Apply A/B test variation to paywall
		 */
		applyABTestVariation(paywall, variationConfig) {
			const modified = { ...paywall };

			// Apply variation overrides
			if (variationConfig.title) modified.title = variationConfig.title;
			if (variationConfig.subtitle)
				modified.subtitle = variationConfig.subtitle;
			if (variationConfig.description)
				modified.description = variationConfig.description;
			if (variationConfig.cta_text)
				modified.cta_text = variationConfig.cta_text;
			if (variationConfig.theme)
				modified.theme = { ...modified.theme, ...variationConfig.theme };
			if (variationConfig.features)
				modified.features = variationConfig.features;

			return modified;
		},

		/**
		 * Check API health
		 */
		async checkAPIHealth() {
			try {
				const health = {
					database: false,
					cache: false,
					adapty: false,
				};

				// Check database
				try {
					await strapi.db.connection.raw("SELECT 1");
					health.database = true;
				} catch (error) {
					strapi.log.error("Database health check failed:", error);
				}

				// Check cache (if Redis is configured)
				try {
					if ((strapi as any).cache) {
						await (strapi as any).cache.get("health_check");
						health.cache = true;
					} else {
						health.cache = "not_configured" as any;
					}
				} catch (error) {
					strapi.log.error("Cache health check failed:", error);
				}

				// Check Adapty connection
				try {
					const adaptyClient = strapi.service("api::adapty.client");
					if (adaptyClient) {
						await adaptyClient.getHealth();
						health.adapty = true;
					}
				} catch (error) {
					strapi.log.error("Adapty health check failed:", error);
				}

				return health;
			} catch (error) {
				strapi.log.error("API health check failed:", error);
				throw error;
			}
		},
	}),
);
