{"kind": "collectionType", "collectionName": "remote_config_versions", "info": {"singularName": "remote-config-version", "pluralName": "remote-config-versions", "displayName": "Remote Config Version", "description": "Track versions of remote configurations for Adapty deployment"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"version_id": {"type": "string", "required": true, "unique": true}, "version_number": {"type": "string", "required": true}, "config_data": {"type": "json", "required": true}, "status": {"type": "enumeration", "enum": ["draft", "deployed", "rolled_back"], "default": "draft", "required": true}, "deployment_notes": {"type": "text"}, "created_by": {"type": "string", "required": true}, "deployed_at": {"type": "datetime"}, "paywall": {"type": "relation", "relation": "manyToOne", "target": "api::paywall.paywall", "inversedBy": "remote_config_versions"}, "placement_id": {"type": "string", "required": true}, "locale": {"type": "string", "default": "en"}}}