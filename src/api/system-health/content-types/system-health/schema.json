{"kind": "collectionType", "collectionName": "system_health", "info": {"singularName": "system-health", "pluralName": "system-health-records", "displayName": "System Health", "description": "System health monitoring metrics"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"timestamp": {"type": "datetime", "required": true}, "uptime": {"type": "biginteger", "required": true, "default": 0}, "memoryUsed": {"type": "biginteger", "required": true, "default": 0}, "memoryTotal": {"type": "biginteger", "required": true, "default": 0}, "memoryPercentage": {"type": "decimal", "required": true, "default": 0}, "cpuUsage": {"type": "decimal", "required": true, "default": 0}, "responseTime": {"type": "integer", "required": true, "default": 0}, "activeConnections": {"type": "integer", "required": true, "default": 0}, "errorRate": {"type": "decimal", "required": true, "default": 0}, "status": {"type": "enumeration", "enum": ["healthy", "warning", "critical", "down"], "required": true, "default": "healthy"}}}