/**
 * System Health service
 */

import { factories } from "@strapi/strapi";

export default factories.createCoreService(
	"api::system-health.system-health",
	({ strapi }) => ({
		/**
		 * Get system health status for dashboard
		 */
		async getHealthForDashboard() {
			try {
				const latestHealth = await strapi.entityService.findMany(
					"api::system-health.system-health",
					{
						sort: "timestamp:desc",
						limit: 1,
					},
				);

				if (latestHealth.length === 0) {
					return {
						status: "unknown",
						message: "No health data available",
					};
				}

				return latestHealth[0];
			} catch (error) {
				strapi.log.error("Failed to get health for dashboard:", error);
				throw error;
			}
		},

		/**
		 * Check if system is healthy
		 */
		async isSystemHealthy(): Promise<boolean> {
			try {
				const latestHealth = await this.getHealthForDashboard();
				return (
					latestHealth.status === "healthy" || latestHealth.status === "warning"
				);
			} catch (error) {
				strapi.log.error("Failed to check system health:", error);
				return false;
			}
		},

		/**
		 * Get health metrics for time period
		 */
		async getHealthMetrics(startDate: Date, endDate: Date) {
			try {
				return await strapi.entityService.findMany(
					"api::system-health.system-health",
					{
						filters: {
							timestamp: {
								$gte: startDate.toISOString(),
								$lte: endDate.toISOString(),
							},
						},
						sort: "timestamp:asc",
					},
				);
			} catch (error) {
				strapi.log.error("Failed to get health metrics:", error);
				throw error;
			}
		},

		/**
		 * Calculate uptime percentage
		 */
		async calculateUptime(hours: number = 24): Promise<number> {
			try {
				const since = new Date(Date.now() - hours * 60 * 60 * 1000);

				const healthRecords = await strapi.entityService.findMany(
					"api::system-health.system-health",
					{
						filters: {
							timestamp: {
								$gte: since.toISOString(),
							},
						},
					},
				);

				if (healthRecords.length === 0) return 0;

				const healthyRecords = healthRecords.filter(
					(record) =>
						record.status === "healthy" || record.status === "warning",
				);

				return (healthyRecords.length / healthRecords.length) * 100;
			} catch (error) {
				strapi.log.error("Failed to calculate uptime:", error);
				return 0;
			}
		},
	}),
);
