/**
 * Health Check router
 * Provides system health endpoints for monitoring and load balancers
 */

export default {
	routes: [
		{
			method: "GET",
			path: "/_health",
			handler: "system-health.healthCheck",
			config: {
				auth: false, // Allow unauthenticated access for load balancers
			},
		},
		{
			method: "GET",
			path: "/_health/detailed",
			handler: "system-health.detailedHealthCheck",
		},
		{
			method: "GET",
			path: "/_health/ready",
			handler: "system-health.readinessCheck",
			config: {
				auth: false,
			},
		},
		{
			method: "GET",
			path: "/_health/live",
			handler: "system-health.livenessCheck",
			config: {
				auth: false,
			},
		},
	],
};
