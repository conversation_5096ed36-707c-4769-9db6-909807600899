/**
 * System Health router
 */

export default {
	routes: [
		{
			method: "GET",
			path: "/system-health",
			handler: "system-health.find",
		},
		{
			method: "GET",
			path: "/system-health/current",
			handler: "system-health.getCurrentStatus",
		},
		{
			method: "GET",
			path: "/system-health/history",
			handler: "system-health.getHealthHistory",
		},
		{
			method: "GET",
			path: "/system-health/summary",
			handler: "system-health.getHealthSummary",
		},
		{
			method: "GET",
			path: "/system-health/alerts",
			handler: "system-health.getHealthAlerts",
		},
		{
			method: "GET",
			path: "/system-health/:id",
			handler: "system-health.findOne",
		},
		{
			method: "POST",
			path: "/system-health",
			handler: "system-health.create",
		},
		{
			method: "PUT",
			path: "/system-health/:id",
			handler: "system-health.update",
		},
		{
			method: "DELETE",
			path: "/system-health/:id",
			handler: "system-health.delete",
		},
	],
};
