/**
 * System Health controller
 */

import { factories } from "@strapi/strapi";
import { performanceMonitor } from "../../../services/monitoring/performance-monitor";

export default factories.createCoreController(
	"api::system-health.system-health",
	({ strapi }) => ({
		/**
		 * Get current system health status
		 */
		async getCurrentStatus(ctx) {
			try {
				const latestHealth = await strapi.entityService.findMany(
					"api::system-health.system-health",
					{
						sort: "timestamp:desc",
						limit: 1,
					},
				);

				if (latestHealth.length === 0) {
					ctx.body = {
						data: {
							status: "unknown",
							message: "No health data available",
						},
					};
					return;
				}

				const health = latestHealth[0];
				ctx.body = {
					data: {
						status: health.status,
						timestamp: health.timestamp,
						uptime: health.uptime,
						memoryUsage: {
							used: health.memoryUsed,
							total: health.memoryTotal,
							percentage: health.memoryPercentage,
						},
						cpuUsage: health.cpuUsage,
						responseTime: health.responseTime,
						activeConnections: health.activeConnections,
						errorRate: health.errorRate,
					},
				};
			} catch (_error) {
				ctx.throw(500, "Failed to get system health status");
			}
		},

		/**
		 * Get system health history
		 */
		async getHealthHistory(ctx) {
			try {
				const { hours = 24, limit = 100 } = ctx.query;
				const since = new Date(Date.now() - hours * 60 * 60 * 1000);

				const healthHistory = await strapi.entityService.findMany(
					"api::system-health.system-health",
					{
						filters: {
							timestamp: {
								$gte: since.toISOString(),
							},
						},
						sort: "timestamp:desc",
						limit: parseInt(limit),
					},
				);

				// Calculate trends
				const healthArray = Array.isArray(healthHistory)
					? healthHistory
					: [healthHistory];
				const trends = this.calculateHealthTrends(healthArray);

				ctx.body = {
					data: healthHistory,
					meta: {
						total: healthHistory.length,
						timeRange: { since, hours },
						trends,
					},
				};
			} catch (_error) {
				ctx.throw(500, "Failed to get health history");
			}
		},

		/**
		 * Get system health summary
		 */
		async getHealthSummary(ctx) {
			try {
				const { hours = 24 } = ctx.query;
				const summary = await performanceMonitor.getPerformanceSummary(
					parseInt(hours),
				);

				ctx.body = {
					data: summary,
				};
			} catch (_error) {
				ctx.throw(500, "Failed to get health summary");
			}
		},

		/**
		 * Get system health alerts
		 */
		async getHealthAlerts(ctx) {
			try {
				const { status = "active", limit = 50 } = ctx.query;

				const alerts = await strapi.entityService.findMany(
					"api::performance-alert.performance-alert",
					{
						filters: {
							status,
							type: {
								$in: [
									"conversion_drop",
									"revenue_drop",
									"churn_increase",
									"trial_conversion_drop",
								],
							},
						},
						sort: "timestamp:desc",
						limit: parseInt(limit),
					},
				);

				ctx.body = {
					data: alerts,
					meta: {
						total: alerts.length,
						status,
					},
				};
			} catch (_error) {
				ctx.throw(500, "Failed to get health alerts");
			}
		},

		/**
		 * Calculate health trends
		 */
		/**
		 * Basic health check endpoint
		 */
		async healthCheck(ctx) {
			try {
				const health = await strapi
					.service("api::system-health.system-health")
					.getHealthForDashboard();

				if (health.status === "healthy" || health.status === "warning") {
					ctx.status = 200;
					ctx.body = {
						status: "ok",
						timestamp: new Date().toISOString(),
						uptime: process.uptime(),
						version: strapi.config.info?.version || "1.0.0",
					};
				} else {
					ctx.status = 503;
					ctx.body = {
						status: "error",
						message: "System is not healthy",
						timestamp: new Date().toISOString(),
					};
				}
			} catch (_error) {
				ctx.status = 503;
				ctx.body = {
					status: "error",
					message: "Health check failed",
					timestamp: new Date().toISOString(),
				};
			}
		},

		/**
		 * Detailed health check with metrics
		 */
		async detailedHealthCheck(ctx) {
			try {
				const health = await strapi
					.service("api::system-health.system-health")
					.getHealthForDashboard();
				const apiHealth = await strapi
					.service("api::api-performance.api-performance")
					.isPerformanceHealthy();
				const uptime = await strapi
					.service("api::system-health.system-health")
					.calculateUptime(24);

				ctx.body = {
					status: health.status,
					timestamp: new Date().toISOString(),
					uptime: {
						process: process.uptime(),
						system: health.uptime,
						percentage: uptime,
					},
					memory: {
						used: health.memoryUsed,
						total: health.memoryTotal,
						percentage: health.memoryPercentage,
					},
					performance: {
						responseTime: health.responseTime,
						errorRate: health.errorRate,
						apiHealthy: apiHealth,
					},
					version: strapi.config.info?.version || "1.0.0",
				};
			} catch (_error) {
				ctx.status = 503;
				ctx.body = {
					status: "error",
					message: "Detailed health check failed",
					timestamp: new Date().toISOString(),
				};
			}
		},

		/**
		 * Readiness check - indicates if the service is ready to handle requests
		 */
		async readinessCheck(ctx) {
			try {
				// Check database connectivity
				await strapi.db.connection.raw("SELECT 1");

				// Check if essential services are running
				const health = await strapi
					.service("api::system-health.system-health")
					.isSystemHealthy();

				if (health) {
					ctx.status = 200;
					ctx.body = { status: "ready" };
				} else {
					ctx.status = 503;
					ctx.body = { status: "not ready", reason: "system unhealthy" };
				}
			} catch (error) {
				ctx.status = 503;
				ctx.body = {
					status: "not ready",
					reason: "database connection failed",
					error: error.message,
				};
			}
		},

		/**
		 * Liveness check - indicates if the service is alive
		 */
		async livenessCheck(ctx) {
			try {
				// Simple check that the process is running
				ctx.status = 200;
				ctx.body = {
					status: "alive",
					timestamp: new Date().toISOString(),
					pid: process.pid,
					uptime: process.uptime(),
				};
			} catch (_error) {
				ctx.status = 503;
				ctx.body = { status: "dead" };
			}
		},

		calculateHealthTrends(healthData: any[]): any {
			if (healthData.length < 2) {
				return {
					responseTime: "stable",
					memoryUsage: "stable",
					cpuUsage: "stable",
					errorRate: "stable",
				};
			}

			const recent = healthData.slice(0, Math.floor(healthData.length / 2));
			const older = healthData.slice(Math.floor(healthData.length / 2));

			const recentAvg = {
				responseTime:
					recent.reduce((sum, h) => sum + h.responseTime, 0) / recent.length,
				memoryUsage:
					recent.reduce((sum, h) => sum + h.memoryPercentage, 0) /
					recent.length,
				cpuUsage:
					recent.reduce((sum, h) => sum + h.cpuUsage, 0) / recent.length,
				errorRate:
					recent.reduce((sum, h) => sum + h.errorRate, 0) / recent.length,
			};

			const olderAvg = {
				responseTime:
					older.reduce((sum, h) => sum + h.responseTime, 0) / older.length,
				memoryUsage:
					older.reduce((sum, h) => sum + h.memoryPercentage, 0) / older.length,
				cpuUsage: older.reduce((sum, h) => sum + h.cpuUsage, 0) / older.length,
				errorRate:
					older.reduce((sum, h) => sum + h.errorRate, 0) / older.length,
			};

			const getTrend = (
				recent: number,
				older: number,
				threshold: number = 10,
			) => {
				const change = ((recent - older) / older) * 100;
				if (Math.abs(change) < threshold) return "stable";
				return change > 0 ? "increasing" : "decreasing";
			};

			return {
				responseTime: getTrend(recentAvg.responseTime, olderAvg.responseTime),
				memoryUsage: getTrend(recentAvg.memoryUsage, olderAvg.memoryUsage),
				cpuUsage: getTrend(recentAvg.cpuUsage, olderAvg.cpuUsage),
				errorRate: getTrend(recentAvg.errorRate, olderAvg.errorRate),
			};
		},
	}),
);
