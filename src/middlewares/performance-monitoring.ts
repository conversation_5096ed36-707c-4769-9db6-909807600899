/**
 * Performance Monitoring Middleware
 * Tracks API performance and integrates with the monitoring system
 */

import type { Context, Next } from "koa";
import { performanceMonitor } from "../services/monitoring/performance-monitor";

export default () => {
	return async (ctx: Context, next: Next) => {
		const startTime = Date.now();
		const requestSize = ctx.request.length || 0;

		// Skip monitoring for health check and monitoring endpoints
		const skipPaths = [
			"/_health",
			"/api/system-health",
			"/api/api-performance",
			"/api/sync-operations",
			"/api/performance-alert",
		];

		const shouldSkip = skipPaths.some((path) =>
			ctx.request.url.startsWith(path),
		);

		if (shouldSkip) {
			return await next();
		}

		let error: Error | null = null;

		try {
			await next();
		} catch (err) {
			error = err as Error;
			throw err;
		} finally {
			const endTime = Date.now();
			const responseTime = endTime - startTime;
			const responseSize = ctx.response.length || 0;
			const statusCode = error ? 500 : ctx.response.status;

			// Record performance metrics asynchronously
			setImmediate(async () => {
				try {
					// Check if the api-performance service exists and database is writable
					const service = strapi.service("api::api-performance.api-performance");
					if (service && typeof service.recordMetric === 'function') {
						await service.recordMetric({
							endpoint: ctx.request.url,
							method: ctx.request.method,
							responseTime,
							statusCode,
							timestamp: new Date(),
							userAgent: ctx.request.headers["user-agent"],
							ipAddress: ctx.request.ip,
							requestSize,
							responseSize,
							errorMessage: error?.message,
						});

						// Check for performance issues and create alerts
						await checkPerformanceThresholds(ctx, responseTime, statusCode);
					}
				} catch (monitoringError) {
					// Silently handle database write errors to prevent them from affecting the application
					if (monitoringError.message && monitoringError.message.includes('readonly database')) {
						// Log once per session to avoid spam
						if (!global.readonlyDbWarningLogged) {
							strapi.log.warn("Database is in read-only mode. Performance monitoring disabled.");
							global.readonlyDbWarningLogged = true;
						}
					} else {
						strapi.log.error("Performance monitoring error:", monitoringError);
					}
				}
			});
		}
	};
};

/**
 * Check performance thresholds and create alerts if needed
 */
async function checkPerformanceThresholds(
	ctx: Context,
	responseTime: number,
	statusCode: number,
): Promise<void> {
	const thresholds = {
		responseTimeWarning: 1000, // 1 second
		responseTimeCritical: 5000, // 5 seconds
	};

	// Check for slow response times
	if (responseTime > thresholds.responseTimeCritical) {
		await createPerformanceAlert({
			type: "slow_api_response",
			severity: "critical",
			message: `Critical slow API response: ${ctx.request.method} ${ctx.request.url} took ${responseTime}ms`,
			endpoint: ctx.request.url,
			method: ctx.request.method,
			responseTime,
			threshold: thresholds.responseTimeCritical,
		});
	} else if (responseTime > thresholds.responseTimeWarning) {
		await createPerformanceAlert({
			type: "slow_api_response",
			severity: "warning",
			message: `Slow API response: ${ctx.request.method} ${ctx.request.url} took ${responseTime}ms`,
			endpoint: ctx.request.url,
			method: ctx.request.method,
			responseTime,
			threshold: thresholds.responseTimeWarning,
		});
	}

	// Check for API errors
	if (statusCode >= 500) {
		await createPerformanceAlert({
			type: "api_error",
			severity: "high",
			message: `API server error: ${ctx.request.method} ${ctx.request.url} returned ${statusCode}`,
			endpoint: ctx.request.url,
			method: ctx.request.method,
			statusCode,
			threshold: 500,
		});
	}
}

/**
 * Create performance alert
 */
async function createPerformanceAlert(alertData: any): Promise<void> {
	try {
		await strapi.entityService.create(
			"api::performance-alert.performance-alert",
			{
				data: {
					alertId: `api_perf_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
					type: alertData.type,
					severity: alertData.severity,
					message: alertData.message,
					currentValue: alertData.responseTime || alertData.statusCode,
					previousValue: 0,
					threshold: alertData.threshold,
					timestamp: new Date(),
					affectedMetrics: [alertData.type, "api_performance"],
					recommendedActions: getRecommendedActions(alertData.type),
					status: "active",
				},
			},
		);

		// Emit alert event for real-time notifications
		performanceMonitor.emit("performanceAlert", alertData);
	} catch (error) {
		// Silently handle database write errors for alerts
		if (error.message && error.message.includes('readonly database')) {
			// Don't log repeatedly for readonly database errors
			return;
		}
		strapi.log.error("Failed to create performance alert:", error);
	}
}

/**
 * Get recommended actions for alert type
 */
function getRecommendedActions(alertType: string): string[] {
	const actionMap: Record<string, string[]> = {
		slow_api_response: [
			"Check database query performance",
			"Review endpoint implementation",
			"Monitor server resources",
			"Check for N+1 queries",
			"Consider adding caching",
		],
		api_error: [
			"Check application logs",
			"Verify database connectivity",
			"Review recent code changes",
			"Check external service dependencies",
			"Monitor error patterns",
		],
	};

	return (
		actionMap[alertType] || [
			"Investigate the issue",
			"Check system logs",
			"Monitor system resources",
			"Contact support if needed",
		]
	);
}
