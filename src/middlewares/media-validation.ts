/**
 * Middleware for validating media uploads for paywall assets
 */

import type { Context, Next } from "koa";

export default () => {
	return async (ctx: Context, next: Next) => {
		// Only apply to upload endpoints
		if (!ctx.request.url.includes("/upload") || ctx.request.method !== "POST") {
			return await next();
		}

		try {
			// Get file information from request
			const files = ctx.request.files;
			if (!files) {
				return await next();
			}

			// Validate each uploaded file
			const fileArray = Array.isArray(files.files)
				? files.files
				: [files.files];
			const validationErrors: string[] = [];

			for (const file of fileArray) {
				if (!file) continue;

				// Determine field context from request
				const _field = ctx.request.body?.field || ctx.request.body?.info?.field;
				const entityType = ctx.request.body?.ref || ctx.request.body?.info?.ref;

				// Apply paywall-specific validation
				if (entityType === "api::paywall.paywall") {
					// const uploadService = strapi.service("plugin::upload.upload"); // strapi not available in middleware context
					// const validation = await uploadService.validatePaywallMedia(file, field); // uploadService not available
					// Skip paywall-specific validation for now
					const validation = { valid: true, errors: [] };

					if (!validation.valid) {
						validationErrors.push(...validation.errors);
					}
				}

				// General file validation
				const generalValidation = file
					? await _validateGeneralFile(file)
					: { valid: true, errors: [] };
				if (!generalValidation.valid) {
					validationErrors.push(...generalValidation.errors);
				}
			}

			// Return validation errors if any
			if (validationErrors.length > 0) {
				ctx.status = 400;
				ctx.body = {
					error: {
						status: 400,
						name: "ValidationError",
						message: "File validation failed",
						details: {
							errors: validationErrors,
						},
					},
				};
				return;
			}

			await next();
		} catch (error) {
			console.error("Media validation middleware error:", error);
			await next();
		}
	};
};

/**
 * General file validation rules
 */
async function _validateGeneralFile(
	file: any,
): Promise<{ valid: boolean; errors: string[] }> {
	const errors: string[] = [];

	// Check file size (50MB max)
	const maxSize = 50 * 1024 * 1024;
	if (file.size > maxSize) {
		errors.push(`File size exceeds maximum allowed size of 50MB`);
	}

	// Check for malicious file types
	const dangerousExtensions = [".exe", ".bat", ".cmd", ".scr", ".pif", ".com"];
	const fileExtension = file.name?.toLowerCase().split(".").pop();
	if (fileExtension && dangerousExtensions.includes(`.${fileExtension}`)) {
		errors.push("File type not allowed for security reasons");
	}

	// Validate MIME type
	const allowedMimeTypes = [
		"image/jpeg",
		"image/png",
		"image/gif",
		"image/webp",
		"image/svg+xml",
		"video/mp4",
		"video/webm",
		"application/pdf",
	];

	if (!allowedMimeTypes.includes(file.type)) {
		errors.push(`File type ${file.type} is not allowed`);
	}

	return {
		valid: errors.length === 0,
		errors,
	};
}
