/**
 * Custom media library folders for paywall assets
 */

import { Box, Flex, Typography } from "@strapi/design-system";
import { Folder } from "@strapi/icons";
import { useEffect } from "react";
import { useIntl } from "react-intl";
import styled from "styled-components";

const FolderBox = styled(Box)`
  cursor: pointer;
  border-radius: 4px;
  &:hover {
    background-color: ${({ theme }) => theme.colors.primary100};
  }
`;

const MediaLibraryFolders = ({ onFolderChange }) => {
	const { formatMessage } = useIntl();

	// Define folder structure for paywall media
	const folders = [
		{
			id: "paywalls",
			name: formatMessage({
				id: "upload.folders.paywalls",
				defaultMessage: "Paywalls",
			}),
			description: formatMessage({
				id: "upload.folders.paywalls.description",
				defaultMessage: "Media assets for paywall configurations",
			}),
			subfolders: [
				{
					id: "paywalls/backgrounds",
					name: formatMessage({
						id: "upload.folders.backgrounds",
						defaultMessage: "Backgrounds",
					}),
				},
				{
					id: "paywalls/logos",
					name: formatMessage({
						id: "upload.folders.logos",
						defaultMessage: "Logos",
					}),
				},
				{
					id: "paywalls/icons",
					name: formatMessage({
						id: "upload.folders.icons",
						defaultMessage: "Icons",
					}),
				},
				{
					id: "paywalls/testimonials",
					name: formatMessage({
						id: "upload.folders.testimonials",
						defaultMessage: "Testimonials",
					}),
				},
			],
		},
		{
			id: "themes",
			name: formatMessage({
				id: "upload.folders.themes",
				defaultMessage: "Themes",
			}),
			description: formatMessage({
				id: "upload.folders.themes.description",
				defaultMessage: "Theme-related media assets",
			}),
		},
		{
			id: "general",
			name: formatMessage({
				id: "upload.folders.general",
				defaultMessage: "General",
			}),
			description: formatMessage({
				id: "upload.folders.general.description",
				defaultMessage: "General purpose media files",
			}),
		},
	];

	// Create folders on component mount
	useEffect(() => {
		const createFolders = async () => {
			try {
				// Check if folders exist and create them if not
				const existingFolders =
					await strapi.api.upload.services.folder.findMany();

				for (const folder of folders) {
					if (!existingFolders.find((f) => f.name === folder.name)) {
						await strapi.api.upload.services.folder.create({
							name: folder.name,
							description: folder.description,
						});
					}

					// Create subfolders if any
					if (folder.subfolders) {
						const parentFolder =
							await strapi.api.upload.services.folder.findOne({
								name: folder.name,
							});

						for (const subfolder of folder.subfolders) {
							if (
								!existingFolders.find(
									(f) =>
										f.name === subfolder.name &&
										f.parent?.id === parentFolder.id,
								)
							) {
								await strapi.api.upload.services.folder.create({
									name: subfolder.name,
									parent: parentFolder.id,
								});
							}
						}
					}
				}
			} catch (error) {
				console.error("Error creating media folders:", error);
			}
		};

		createFolders();
	}, [folders]);

	return (
		<Box padding={4}>
			<Typography variant="sigma" textColor="neutral600">
				{formatMessage({
					id: "upload.folders.title",
					defaultMessage: "Media Folders",
				})}
			</Typography>

			<Box paddingTop={2}>
				{folders.map((folder) => (
					<Box key={folder.id} marginBottom={2}>
						<FolderBox
							padding={3}
							background="neutral100"
							onClick={() => onFolderChange(folder.id)}
						>
							<Flex>
								<Box marginRight={2}>
									<Folder />
								</Box>
								<Typography variant="pi" fontWeight="bold">
									{folder.name}
								</Typography>
							</Flex>
							{folder.description && (
								<Box paddingTop={1}>
									<Typography variant="pi" textColor="neutral600">
										{folder.description}
									</Typography>
								</Box>
							)}
						</FolderBox>

						{folder.subfolders && (
							<Box paddingLeft={4} paddingTop={2}>
								{folder.subfolders.map((subfolder) => (
									<FolderBox
										key={subfolder.id}
										padding={2}
										marginBottom={1}
										onClick={() => onFolderChange(subfolder.id)}
									>
										<Flex>
											<Box marginRight={2}>
												<Folder width="12px" height="12px" />
											</Box>
											<Typography variant="pi">{subfolder.name}</Typography>
										</Flex>
									</FolderBox>
								))}
							</Box>
						)}
					</Box>
				))}
			</Box>
		</Box>
	);
};

export default MediaLibraryFolders;
