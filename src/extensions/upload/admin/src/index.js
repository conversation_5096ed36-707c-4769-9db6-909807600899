/**
 * Custom media library extensions for paywall assets
 */

import MediaLibraryFolders from "./components/MediaLibraryFolders";

export default {
	register(app) {
		// Override media library components
		app.overridePlugin("upload", {
			apis: {
				// Add custom API methods
				upload: {
					// Add method to get optimized versions
					getOptimizedVersions: async (fileId) => {
						try {
							const file = await app.api.get(`/upload/files/${fileId}`);
							return file.data.paywall_optimizations || {};
						} catch (error) {
							console.error("Error fetching optimized versions:", error);
							return {};
						}
					},

					// Add method to get appropriate folder for field
					getFieldFolder: (field) => {
						const folderMap = {
							background_image: "paywalls/backgrounds",
							logo: "paywalls/logos",
							icon_image: "paywalls/icons",
							author_avatar: "paywalls/testimonials",
						};

						return folderMap[field] || "paywalls";
					},
				},
			},

			// Inject custom components
			injectionZones: {
				homePage: {
					left: [
						{
							name: "MediaLibraryFolders",
							Component: MediaLibraryFolders,
						},
					],
				},
			},
		});
	},

	bootstrap(app) {
		// Add custom translations
		app.addTranslations({
			en: {
				"upload.folders.title": "Media Folders",
				"upload.folders.paywalls": "Paywalls",
				"upload.folders.paywalls.description":
					"Media assets for paywall configurations",
				"upload.folders.backgrounds": "Backgrounds",
				"upload.folders.logos": "Logos",
				"upload.folders.icons": "Icons",
				"upload.folders.testimonials": "Testimonials",
				"upload.folders.themes": "Themes",
				"upload.folders.themes.description": "Theme-related media assets",
				"upload.folders.general": "General",
				"upload.folders.general.description": "General purpose media files",
			},
		});
	},
};
