/**
 * Custom upload service for paywall media management
 */

import path from "node:path";
import { factories } from "@strapi/strapi";
import fs from "fs-extra";
import sharp from "sharp";

export default factories.createCoreService(
	"plugin::upload.file",
	({ strapi }) => ({
		/**
		 * Enhanced file upload with paywall-specific optimizations
		 */
		async upload(
			file: any,
			{ entity, field }: { entity?: any; field?: string } = {},
		) {
			// Call parent upload method
			const uploadedFile = await super.upload(file);

			// Apply paywall-specific optimizations
			if (entity && entity.__type === "api::paywall.paywall") {
				await this.optimizePaywallAsset(uploadedFile, field);
			}

			return uploadedFile;
		},

		/**
		 * Optimize media assets for paywall usage
		 */
		async optimizePaywallAsset(file: any, field?: string) {
			if (!file.mime?.startsWith("image/")) {
				return file;
			}

			try {
				const filePath = path.join(strapi.dirs.static.public, file.url);

				// Create optimized versions for different use cases
				const optimizations = await this.createPaywallOptimizations(
					filePath,
					field,
				);

				// Store optimization metadata
				file.paywall_optimizations = optimizations;

				// Update file record with optimization info
				await strapi.entityService.update("plugin::upload.file", file.id, {
					data: {
						alternativeText:
							file.alternativeText || this.generateAltText(field),
						caption: file.caption || this.generateCaption(field),
						paywall_optimizations: optimizations,
					} as any,
				});

				return file;
			} catch (error) {
				strapi.log.error("Failed to optimize paywall asset:", error);
				return file;
			}
		},

		/**
		 * Create multiple optimized versions for different paywall contexts
		 */
		async createPaywallOptimizations(filePath: string, field?: string) {
			const optimizations: Record<string, any> = {};

			try {
				const image = sharp(filePath);
				const metadata = await image.metadata();

				// Define optimization profiles based on field type
				const profiles = this.getOptimizationProfiles(field);

				for (const [profileName, profile] of Object.entries(profiles)) {
					const outputPath = this.getOptimizedPath(filePath, profileName);

					let pipeline = image.clone();

					// Apply transformations
					const profileConfig = profile as any;
					if (profileConfig.resize) {
						pipeline = pipeline.resize(
							(profile as any).resize.width,
							(profile as any).resize.height,
							{
								fit: (profile as any).resize.fit || "cover",
								position: (profile as any).resize.position || "center",
							},
						);
					}

					if (profileConfig.format) {
						pipeline = pipeline.toFormat(profileConfig.format, {
							quality: profileConfig.quality || 85,
							progressive: true,
						});
					}

					// Save optimized version
					await pipeline.toFile(outputPath);

					// Store optimization info
					optimizations[profileName] = {
						url: this.getOptimizedUrl(filePath, profileName),
						width: profileConfig.resize?.width || metadata.width,
						height: profileConfig.resize?.height || metadata.height,
						format: profileConfig.format || metadata.format,
						size: (await fs.stat(outputPath)).size,
					};
				}

				return optimizations;
			} catch (error) {
				strapi.log.error("Failed to create paywall optimizations:", error);
				return {};
			}
		},

		/**
		 * Get optimization profiles based on field type
		 */
		getOptimizationProfiles(field?: string) {
			const baseProfiles = {
				thumbnail: {
					resize: { width: 150, height: 150, fit: "cover" },
					format: "webp",
					quality: 80,
				},
				mobile: {
					resize: { width: 400, height: 400, fit: "inside" },
					format: "webp",
					quality: 85,
				},
				desktop: {
					resize: { width: 800, height: 800, fit: "inside" },
					format: "webp",
					quality: 90,
				},
			};

			// Field-specific profiles
			const fieldProfiles: Record<string, any> = {
				background_image: {
					...baseProfiles,
					hero: {
						resize: { width: 1920, height: 1080, fit: "cover" },
						format: "webp",
						quality: 85,
					},
				},
				logo: {
					...baseProfiles,
					vector: {
						resize: { width: 200, height: 200, fit: "inside" },
						format: "png",
						quality: 95,
					},
				},
				icon_image: {
					thumbnail: {
						resize: { width: 64, height: 64, fit: "cover" },
						format: "webp",
						quality: 90,
					},
					small: {
						resize: { width: 128, height: 128, fit: "cover" },
						format: "webp",
						quality: 90,
					},
				},
				author_avatar: {
					thumbnail: {
						resize: { width: 80, height: 80, fit: "cover" },
						format: "webp",
						quality: 85,
					},
					medium: {
						resize: { width: 160, height: 160, fit: "cover" },
						format: "webp",
						quality: 90,
					},
				},
			};

			return fieldProfiles[field] || baseProfiles;
		},

		/**
		 * Generate optimized file path
		 */
		getOptimizedPath(originalPath: string, profile: string): string {
			const ext = path.extname(originalPath);
			const name = path.basename(originalPath, ext);
			const dir = path.dirname(originalPath);

			return path.join(dir, "optimized", `${name}_${profile}.webp`);
		},

		/**
		 * Generate optimized file URL
		 */
		getOptimizedUrl(originalPath: string, profile: string): string {
			const relativePath = path.relative(
				strapi.dirs.static.public,
				originalPath,
			);
			const ext = path.extname(relativePath);
			const name = path.basename(relativePath, ext);
			const dir = path.dirname(relativePath);

			return `/${dir}/optimized/${name}_${profile}.webp`;
		},

		/**
		 * Generate appropriate alt text based on field
		 */
		generateAltText(field?: string): string {
			const altTexts: Record<string, string> = {
				background_image: "Paywall background image",
				logo: "Company logo",
				icon_image: "Feature icon",
				author_avatar: "Author profile picture",
			};

			return altTexts[field] || "Paywall asset";
		},

		/**
		 * Generate appropriate caption based on field
		 */
		generateCaption(field?: string): string {
			const captions: Record<string, string> = {
				background_image: "Background image for paywall display",
				logo: "Brand logo displayed in paywall",
				icon_image: "Icon representing paywall feature",
				author_avatar: "Profile picture of testimonial author",
			};

			return captions[field] || "Media asset for paywall";
		},

		/**
		 * Validate paywall media files
		 */
		async validatePaywallMedia(
			file: any,
			field?: string,
		): Promise<{ valid: boolean; errors: string[] }> {
			const errors: string[] = [];

			// File size validation
			const maxSizes: Record<string, number> = {
				background_image: 10 * 1024 * 1024, // 10MB
				logo: 2 * 1024 * 1024, // 2MB
				icon_image: 1 * 1024 * 1024, // 1MB
				author_avatar: 1 * 1024 * 1024, // 1MB
			};

			const maxSize = maxSizes[field] || 5 * 1024 * 1024; // 5MB default
			if (file.size > maxSize) {
				errors.push(
					`File size exceeds maximum allowed size of ${maxSize / (1024 * 1024)}MB`,
				);
			}

			// Dimension validation for images
			if (file.mime?.startsWith("image/")) {
				try {
					const metadata = await sharp(file.buffer || file.path).metadata();

					const minDimensions: Record<
						string,
						{ width: number; height: number }
					> = {
						background_image: { width: 800, height: 600 },
						logo: { width: 100, height: 100 },
						icon_image: { width: 32, height: 32 },
						author_avatar: { width: 64, height: 64 },
					};

					const minDim = minDimensions[field];
					if (minDim && metadata.width && metadata.height) {
						if (
							metadata.width < minDim.width ||
							metadata.height < minDim.height
						) {
							errors.push(
								`Image dimensions must be at least ${minDim.width}x${minDim.height}px`,
							);
						}
					}
				} catch (_error) {
					errors.push("Invalid image file");
				}
			}

			// Format validation
			const allowedFormats: Record<string, string[]> = {
				background_image: ["image/jpeg", "image/png", "image/webp"],
				logo: ["image/png", "image/svg+xml", "image/webp"],
				icon_image: ["image/png", "image/svg+xml", "image/webp"],
				author_avatar: ["image/jpeg", "image/png", "image/webp"],
			};

			const allowed = allowedFormats[field] || [
				"image/jpeg",
				"image/png",
				"image/webp",
				"image/svg+xml",
			];
			if (!allowed.includes(file.mime)) {
				errors.push(
					`File format not allowed. Supported formats: ${allowed.join(", ")}`,
				);
			}

			return {
				valid: errors.length === 0,
				errors,
			};
		},

		/**
		 * Get media organization folders
		 */
		getMediaFolders() {
			return {
				paywalls: {
					name: "Paywalls",
					description: "Media assets for paywall configurations",
					subfolders: {
						backgrounds: "Background images",
						logos: "Brand logos",
						icons: "Feature icons",
						avatars: "Author profile pictures",
					},
				},
				themes: {
					name: "Themes",
					description: "Theme-related media assets",
				},
				general: {
					name: "General",
					description: "General purpose media files",
				},
			};
		},
	}),
);
