/**
 * Custom upload controller for paywall media management
 */

import { factories } from "@strapi/strapi";
import type { Context } from "koa";

export default factories.createCoreController(
	"plugin::upload.file",
	({ strapi }) => ({
		/**
		 * Enhanced upload handler for paywall assets
		 */
		async upload(ctx: Context) {
			try {
				// Extract entity information from request
				const { refId, ref, field } = ctx.request.body || {};
				const isPaywallAsset = ref === "api::paywall.paywall";

				// For paywall assets, apply special handling
				if (isPaywallAsset) {
					// Get paywall information if available
					let paywall;
					if (refId) {
						paywall = await strapi.entityService.findOne(
							"api::paywall.paywall",
							refId,
						);
					}

					// Set appropriate folder based on field type
					let folderPath = "paywalls";

					if (field) {
						switch (field) {
							case "background_image":
								folderPath = "paywalls/backgrounds";
								break;
							case "logo":
								folderPath = "paywalls/logos";
								break;
							case "icon_image":
								folderPath = "paywalls/icons";
								break;
							case "author_avatar":
								folderPath = "paywalls/testimonials";
								break;
							default:
								folderPath = "paywalls";
						}

						// Add paywall ID to path if available
						if (paywall?.placement_id) {
							folderPath = `${folderPath}/${paywall.placement_id}`;
						}
					}

					// Set folder path in request
					ctx.request.body.folder = folderPath;
				}

				// Call parent upload method
				const response = await super.upload(ctx);

				// For paywall assets, apply post-upload processing
				if (isPaywallAsset && response.data) {
					const fileIds = Array.isArray(response.data)
						? response.data.map((file) => file.id)
						: [response.data.id];

					// Process each uploaded file
					for (const fileId of fileIds) {
						await this.processPaywallAsset(fileId, field);
					}
				}

				return response;
			} catch (error) {
				strapi.log.error("Error in custom upload controller:", error);
				ctx.throw(500, error);
			}
		},

		/**
		 * Process paywall asset after upload
		 */
		async processPaywallAsset(fileId: number, field?: string) {
			try {
				// Get file information
				const file = await strapi.entityService.findOne(
					"plugin::upload.file",
					fileId,
				);
				if (!file) return;

				// Apply field-specific processing
				if (field) {
					// Update file metadata based on field type
					const metadata: Record<string, any> = {
						alternativeText:
							file.alternativeText || this.generateAltText(field),
						caption: file.caption || this.generateCaption(field),
					};

					// Add field-specific tags
					const fileTags = (file as any).tags;
					if (!fileTags) {
						metadata.tags = [field.replace("_", "-")];
					} else if (
						Array.isArray(fileTags) &&
						!fileTags.includes(field.replace("_", "-"))
					) {
						metadata.tags = [...fileTags, field.replace("_", "-")];
					}

					// Update file with metadata
					await strapi.entityService.update("plugin::upload.file", fileId, {
						data: metadata,
					});

					// Trigger optimization service
					await strapi
						.service("plugin::upload.upload")
						.optimizePaywallAsset(file, field);
				}
			} catch (error) {
				strapi.log.error("Error processing paywall asset:", error);
			}
		},

		/**
		 * Generate appropriate alt text based on field
		 */
		generateAltText(field?: string): string {
			const altTexts: Record<string, string> = {
				background_image: "Paywall background image",
				logo: "Company logo",
				icon_image: "Feature icon",
				author_avatar: "Author profile picture",
			};

			return altTexts[field] || "Paywall asset";
		},

		/**
		 * Generate appropriate caption based on field
		 */
		generateCaption(field?: string): string {
			const captions: Record<string, string> = {
				background_image: "Background image for paywall display",
				logo: "Brand logo displayed in paywall",
				icon_image: "Icon representing paywall feature",
				author_avatar: "Profile picture of testimonial author",
			};

			return captions[field] || "Media asset for paywall";
		},
	}),
);
