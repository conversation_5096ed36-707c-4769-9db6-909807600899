import { mergeConfig, type UserConfig } from "vite";

export default (config: UserConfig) => {
	// Important: always return the modified config
	return mergeConfig(config, {
		// Enable source maps for debugging TypeScript files
		build: {
			sourcemap: true,
			// Keep original file names for easier debugging
			rollupOptions: {
				output: {
					// Preserve original file structure in dev tools
					preserveModules: false,
					// Use readable names for chunks
					chunkFileNames: "assets/[name]-[hash].js",
					entryFileNames: "assets/[name]-[hash].js",
					assetFileNames: "assets/[name]-[hash].[ext]",
				},
			},
		},

		// Development server configuration
		server: {
			// Enable source map support in dev server
			sourcemapIgnoreList: false,
		},

		// Resolve configuration for better module resolution
		resolve: {
			alias: {
				"@": "/src",
				"@admin": "/src/admin",
				"@components": "/src/admin/extensions/components",
				"@services": "/src/services",
				"@api": "/src/api",
			},
		},

		// Enable CSS source maps
		css: {
			devSourcemap: true,
		},

		// Optimize dependencies for faster development
		optimizeDeps: {
			include: [
				"react",
				"react-dom",
				"react-router-dom",
				"styled-components",
				"react-beautiful-dnd",
				"react-color",
			],
		},

		// Define environment variables for development
		define: {
			__DEV__: JSON.stringify(process.env.NODE_ENV !== "production"),
		},
	});
};
