/**
 * Custom admin panel configuration for Adapty CMS
 */

import ABTestDashboard from "./extensions/components/ABTestDashboard";
import ABTestInterface from "./extensions/components/ABTestInterface";
import ABTestManager from "./extensions/components/ABTestManager";
import BulkPaywallOperations from "./extensions/components/BulkPaywallOperations";
import ChromeMCPDashboard from "./extensions/components/ChromeMCPDashboard";
import FeatureManager from "./extensions/components/FeatureManager";
import HealthStatusCard from "./extensions/components/HealthStatusCard";
import Logo from "./extensions/components/Logo";
import MetricsChart from "./extensions/components/MetricsChart";
import PaywallFormValidation from "./extensions/components/PaywallFormValidation";
import PaywallPreview from "./extensions/components/PaywallPreview";
import ProductLabelManager from "./extensions/components/ProductLabelManager";
import TestConclusionWorkflow from "./extensions/components/TestConclusionWorkflow";
import TestimonialManager from "./extensions/components/TestimonialManager";
import TestResultsVisualization from "./extensions/components/TestResultsVisualization";
import ThemeColorPicker from "./extensions/components/ThemeColorPicker";
import VariationComparison from "./extensions/components/VariationComparison";
import favicon from "./extensions/favicon.ico";

export default {
	config: {
		// Replace Strapi's logo by your own
		head: {
			favicon: favicon,
		},
		// Replace the Strapi logo in the main navigation
		menu: {
			logo: Logo,
		},
		// Override or extend the theme
		theme: {
			colors: {
				primary100: "#f6ecfc",
				primary200: "#e0c1f4",
				primary500: "#ac73e6",
				primary600: "#9736e8",
				primary700: "#8312d1",
				danger700: "#b72b1a",
			},
		},
		// Extend the translations
		translations: {
			en: {
				"app.components.LeftMenu.navbrand.title": "Adapty CMS",
				"app.components.LeftMenu.navbrand.workplace": "Paywall Management",
				"app.components.PaywallPreview.title": "Paywall Preview",
				"app.components.PaywallPreview.description":
					"Preview how your paywall will appear on mobile devices",
				"app.components.ThemeColorPicker.title": "Theme Colors",
				"app.components.ThemeColorPicker.primary": "Primary Color",
				"app.components.ThemeColorPicker.background": "Background Color",
				"app.components.ThemeColorPicker.text": "Text Color",
				"app.components.FeatureManager.title": "Feature Management",
				"app.components.FeatureManager.addFeature": "Add Feature",
				"app.components.FeatureManager.reorder": "Drag to Reorder",
				"app.components.TestimonialManager.title": "Testimonial Management",
				"app.components.TestimonialManager.addTestimonial": "Add Testimonial",
				"app.components.ProductLabelManager.title": "Product Label Management",
				"app.components.ProductLabelManager.addLabel": "Add Product Label",
				"app.components.BulkPaywallOperations.title": "Bulk Operations",
				"app.components.BulkPaywallOperations.execute": "Execute Operation",
				"app.components.PaywallFormValidation.title": "Form Validation",
				"app.components.PaywallFormValidation.valid": "Configuration Valid",
				"app.components.ABTestManager.title": "A/B Test Manager",
				"app.components.ABTestManager.create": "Create New Test",
				"app.components.ABTestManager.manage": "Manage Tests",
				"app.components.ABTestDashboard.title": "A/B Test Dashboard",
				"app.components.ABTestDashboard.performance": "Performance Metrics",
				"app.components.ABTestDashboard.conversions": "Conversion Rates",
				"app.components.VariationComparison.title": "Variation Comparison",
				"app.components.VariationComparison.sideBySide": "Side-by-Side View",
				"app.components.ChromeMCPDashboard.title": "Chrome MCP Health Monitoring",
				"app.components.ChromeMCPDashboard.description": "Real-time monitoring dashboard for Chrome MCP server infrastructure",
				"app.components.ChromeMCPDashboard.serverStatus": "Server Status",
				"app.components.ChromeMCPDashboard.responseTime": "Response Time",
				"app.components.ChromeMCPDashboard.memoryUsage": "Memory Usage",
				"app.components.ChromeMCPDashboard.cpuUsage": "CPU Usage",
				"app.components.ChromeMCPDashboard.connectionReliability": "Connection Reliability",
				"app.components.ChromeMCPDashboard.performanceTrends": "Performance Trends",
				"app.components.ChromeMCPDashboard.activeAlerts": "Active Alerts",
				"app.components.ChromeMCPDashboard.liveMode": "Live Mode",
				"app.components.ChromeMCPDashboard.enableLive": "Enable Live",
				"app.components.ChromeMCPDashboard.refresh": "Refresh",
			},
		},
		// Disable video tutorials
		tutorials: false,
		// Disable notifications about new Strapi releases
		notifications: { releases: false },
	},

	bootstrap(app) {
		console.log('Bootstrapping Adapty CMS admin extensions...');

		// In Strapi 4, custom components are typically registered as plugins or used directly in content types
		// For now, we'll store them in the app registry for later use
		if (!app.customComponents) {
			app.customComponents = {};
		}

		// Store custom components for use in content types
		app.customComponents['paywall-preview'] = PaywallPreview;
		app.customComponents['theme-color-picker'] = ThemeColorPicker;
		app.customComponents['feature-manager'] = FeatureManager;
		app.customComponents['testimonial-manager'] = TestimonialManager;
		app.customComponents['product-label-manager'] = ProductLabelManager;
		app.customComponents['bulk-paywall-operations'] = BulkPaywallOperations;
		app.customComponents['paywall-form-validation'] = PaywallFormValidation;
		app.customComponents['ab-test-manager'] = ABTestManager;
		app.customComponents['ab-test-dashboard'] = ABTestDashboard;
		app.customComponents['variation-comparison'] = VariationComparison;
		app.customComponents['ab-test-interface'] = ABTestInterface;
		app.customComponents['test-results-visualization'] = TestResultsVisualization;
		app.customComponents['test-conclusion-workflow'] = TestConclusionWorkflow;
		app.customComponents['chrome-mcp-dashboard'] = ChromeMCPDashboard;
		app.customComponents['health-status-card'] = HealthStatusCard;
		app.customComponents['metrics-chart'] = MetricsChart;

		console.log('Custom components registered:', Object.keys(app.customComponents));
	},
};
