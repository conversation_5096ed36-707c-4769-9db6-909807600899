{
	"compilerOptions": {
		"target": "ESNext",
		"module": "ESNext",
		"moduleResolution": "Bundler",
		"useDefineForClassFields": true,
		"lib": ["DOM", "DOM.Iterable", "ESNext"],
		"allowJs": false,
		"skipLibCheck": true,
		"esModuleInterop": true,
		"allowSyntheticDefaultImports": true,
		"strict": true,
		"forceConsistentCasingInFileNames": true,
		"resolveJsonModule": true,
		"noEmit": true,
		"jsx": "react-jsx",

		// Enable source map generation for debugging
		"sourceMap": true,
		"inlineSourceMap": false,
		"inlineSources": false,

		// Path mapping for better imports and debugging
		"baseUrl": ".",
		"paths": {
			"@/*": ["../src/*"],
			"@admin/*": ["./*"],
			"@components/*": ["./extensions/components/*"],
			"@services/*": ["../services/*"],
			"@api/*": ["../api/*"]
		}
	},
	"include": ["../plugins/**/admin/src/**/*", "./"],
	"exclude": ["node_modules/", "build/", "dist/", "**/*.test.ts"]
}
