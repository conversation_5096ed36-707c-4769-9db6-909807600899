/**
 * Product label management component
 */

import {
	Box,
	Button,
	Card,
	CardAction,
	CardBadge,
	CardBody,
	CardContent,
	CardTitle,
	Checkbox,
	Flex,
	IconButton,
	ModalBody,
	ModalFooter,
	ModalHeader,
	ModalLayout,
	NumberInput,
	Option,
	Select,
	Stack,
	TextInput,
	Typography,
} from "@strapi/design-system";
import { Information, Pencil, Plus, Trash } from "@strapi/icons";
import { useEffect, useState } from "react";
import { useIntl } from "react-intl";
import styled from "styled-components";

// Styled components for product label management
const ProductCard = styled(Card)`
  margin-bottom: 16px;
  position: relative;
  overflow: visible;
`;

const ColorPreview = styled.div`
  width: 24px;
  height: 24px;
  border-radius: 4px;
  border: 1px solid ${({ theme }) => theme.colors.neutral200};
  background-color: ${({ color }) => color || "transparent"};
  margin-right: 8px;
`;

const BadgePreview = styled.div`
  background-color: ${({ color }) => color || "#FF6B35"};
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  display: inline-block;
  margin-top: 8px;
`;

const _PopoverContainer = styled.div`
  position: absolute;
  z-index: 2;
  margin-top: 8px;
`;

const _PopoverCover = styled.div`
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
`;

const ProductLabelManager = ({ name, attribute, value, onChange }) => {
	const { formatMessage } = useIntl();
	const [productLabels, setProductLabels] = useState([]);
	const [isModalVisible, setIsModalVisible] = useState(false);
	const [editingIndex, setEditingIndex] = useState(-1);
	const [newProductLabel, setNewProductLabel] = useState({
		product_id: "",
		badge_text: "",
		badge_color: "#FF6B35",
		subtitle: "",
		highlight: false,
		savings_percentage: 0,
		badge_position: "top-right",
	});

	// Sample product IDs for dropdown
	const sampleProductIds = [
		{ value: "monthly", label: "Monthly Subscription" },
		{ value: "yearly", label: "Yearly Subscription" },
		{ value: "lifetime", label: "Lifetime Access" },
		{ value: "trial", label: "Free Trial" },
	];

	// Badge position options
	const badgePositions = [
		{ value: "top-right", label: "Top Right" },
		{ value: "top-left", label: "Top Left" },
		{ value: "bottom-right", label: "Bottom Right" },
		{ value: "bottom-left", label: "Bottom Left" },
	];

	// Initialize product labels from value
	useEffect(() => {
		if (value && Array.isArray(value)) {
			setProductLabels([...value]);
		} else {
			setProductLabels([]);
		}
	}, [value]);

	// Handle add product label
	const handleAddProductLabel = () => {
		if (!newProductLabel.product_id) return;

		const updatedProductLabels = [...productLabels, { ...newProductLabel }];

		setProductLabels(updatedProductLabels);
		onChange({ target: { name, value: updatedProductLabels } });
		resetForm();
	};

	// Handle edit product label
	const handleEditProductLabel = (index) => {
		setEditingIndex(index);
		setNewProductLabel({ ...productLabels[index] });
		setIsModalVisible(true);
	};

	// Handle update product label
	const handleUpdateProductLabel = () => {
		if (!newProductLabel.product_id) return;

		const updatedProductLabels = [...productLabels];
		updatedProductLabels[editingIndex] = newProductLabel;

		setProductLabels(updatedProductLabels);
		onChange({ target: { name, value: updatedProductLabels } });
		resetForm();
	};

	// Handle delete product label
	const handleDeleteProductLabel = (index) => {
		const updatedProductLabels = productLabels.filter((_, i) => i !== index);

		setProductLabels(updatedProductLabels);
		onChange({ target: { name, value: updatedProductLabels } });

		// If deleting the product label being edited, close the modal
		if (editingIndex === index) {
			resetForm();
		}
	};

	// Handle form input change
	const handleInputChange = (field, value) => {
		setNewProductLabel({
			...newProductLabel,
			[field]: value,
		});
	};

	// Reset form
	const resetForm = () => {
		setIsModalVisible(false);
		setEditingIndex(-1);
		setNewProductLabel({
			product_id: "",
			badge_text: "",
			badge_color: "#FF6B35",
			subtitle: "",
			highlight: false,
			savings_percentage: 0,
			badge_position: "top-right",
		});
	};

	return (
		<Box>
			<Flex justifyContent="space-between" alignItems="center" marginBottom={4}>
				<Typography variant="beta">
					{formatMessage({
						id: "app.components.ProductLabelManager.title",
						defaultMessage: "Product Label Management",
					})}
				</Typography>
				<Button startIcon={<Plus />} onClick={() => setIsModalVisible(true)}>
					{formatMessage({
						id: "app.components.ProductLabelManager.addLabel",
						defaultMessage: "Add Product Label",
					})}
				</Button>
			</Flex>

			{productLabels.length === 0 && (
				<Box
					padding={6}
					background="neutral100"
					borderRadius="4px"
					textAlign="center"
				>
					<Typography textColor="neutral600">
						No product labels added yet. Click "Add Product Label" to create
						your first label.
					</Typography>
				</Box>
			)}

			{productLabels.length > 0 && (
				<Box marginTop={4}>
					{productLabels.map((label, index) => (
						<ProductCard key={index}>
							<CardBody>
								<Flex justifyContent="space-between">
									<CardContent>
										<Flex alignItems="center">
											<CardTitle>{getProductName(label.product_id)}</CardTitle>
											{label.highlight && (
												<CardBadge marginLeft={2}>Highlighted</CardBadge>
											)}
										</Flex>

										{label.subtitle && (
											<Typography variant="pi" textColor="neutral600">
												{label.subtitle}
											</Typography>
										)}

										{label.badge_text && (
											<BadgePreview color={label.badge_color}>
												{label.badge_text}
											</BadgePreview>
										)}

										{label.savings_percentage > 0 && (
											<Typography
												variant="pi"
												textColor="success600"
												marginTop={2}
											>
												Save {label.savings_percentage}%
											</Typography>
										)}

										<Flex marginTop={2} alignItems="center">
											<Information width="12px" height="12px" />
											<Typography
												variant="pi"
												textColor="neutral600"
												marginLeft={1}
											>
												Badge position:{" "}
												{getBadgePositionLabel(label.badge_position)}
											</Typography>
										</Flex>
									</CardContent>

									<CardAction position="end">
										<Stack horizontal spacing={2}>
											<IconButton
												onClick={() => handleEditProductLabel(index)}
												label="Edit"
												icon={<Pencil />}
											/>
											<IconButton
												onClick={() => handleDeleteProductLabel(index)}
												label="Delete"
												icon={<Trash />}
											/>
										</Stack>
									</CardAction>
								</Flex>
							</CardBody>
						</ProductCard>
					))}
				</Box>
			)}

			{isModalVisible && (
				<ModalLayout onClose={resetForm} labelledBy="product-label-modal-title">
					<ModalHeader>
						<Typography id="product-label-modal-title" fontWeight="bold">
							{editingIndex >= 0
								? "Edit Product Label"
								: "Add New Product Label"}
						</Typography>
					</ModalHeader>
					<ModalBody>
						<Box marginBottom={4}>
							<Select
								label="Product ID"
								name="product_id"
								placeholder="Select a product"
								value={newProductLabel.product_id}
								onChange={(value) => handleInputChange("product_id", value)}
								required
							>
								{sampleProductIds.map((option) => (
									<Option key={option.value} value={option.value}>
										{option.label}
									</Option>
								))}
							</Select>
						</Box>

						<Box marginBottom={4}>
							<TextInput
								label="Badge Text"
								name="badge_text"
								placeholder="Most Popular"
								value={newProductLabel.badge_text}
								onChange={(e) =>
									handleInputChange("badge_text", e.target.value)
								}
							/>
						</Box>

						<Box marginBottom={4}>
							<Flex alignItems="center">
								<Typography variant="pi" fontWeight="bold" marginRight={2}>
									Badge Color
								</Typography>
								<ColorPreview color={newProductLabel.badge_color} />
								<TextInput
									aria-label="Badge color hex"
									name="badge_color"
									value={newProductLabel.badge_color}
									onChange={(e) =>
										handleInputChange("badge_color", e.target.value)
									}
									placeholder="#FF6B35"
								/>
							</Flex>
						</Box>

						<Box marginBottom={4}>
							<TextInput
								label="Subtitle"
								name="subtitle"
								placeholder="Best value"
								value={newProductLabel.subtitle}
								onChange={(e) => handleInputChange("subtitle", e.target.value)}
							/>
						</Box>

						<Box marginBottom={4}>
							<NumberInput
								label="Savings Percentage"
								name="savings_percentage"
								value={newProductLabel.savings_percentage}
								onValueChange={(value) =>
									handleInputChange("savings_percentage", value)
								}
								placeholder="0"
								min={0}
								max={100}
							/>
						</Box>

						<Box marginBottom={4}>
							<Select
								label="Badge Position"
								name="badge_position"
								value={newProductLabel.badge_position}
								onChange={(value) => handleInputChange("badge_position", value)}
							>
								{badgePositions.map((option) => (
									<Option key={option.value} value={option.value}>
										{option.label}
									</Option>
								))}
							</Select>
						</Box>

						<Box marginBottom={4}>
							<Checkbox
								name="highlight"
								value={newProductLabel.highlight}
								onValueChange={(value) => handleInputChange("highlight", value)}
							>
								Highlight this product
							</Checkbox>
						</Box>
					</ModalBody>
					<ModalFooter
						startActions={
							<Button variant="tertiary" onClick={resetForm}>
								Cancel
							</Button>
						}
						endActions={
							<Button
								onClick={
									editingIndex >= 0
										? handleUpdateProductLabel
										: handleAddProductLabel
								}
								disabled={!newProductLabel.product_id}
							>
								{editingIndex >= 0
									? "Update Product Label"
									: "Add Product Label"}
							</Button>
						}
					/>
				</ModalLayout>
			)}
		</Box>
	);

	// Helper function to get product name from ID
	function getProductName(productId) {
		const product = sampleProductIds.find((p) => p.value === productId);
		return product ? product.label : productId;
	}

	// Helper function to get badge position label
	function getBadgePositionLabel(position) {
		const positionOption = badgePositions.find((p) => p.value === position);
		return positionOption ? positionOption.label : position;
	}
};

export default ProductLabelManager;
