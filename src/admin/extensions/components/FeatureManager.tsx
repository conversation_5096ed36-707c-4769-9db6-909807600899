/**
 * Drag-and-drop feature management component
 */

import {
	Box,
	Button,
	Card,
	CardAction,
	CardBadge,
	CardBody,
	CardContent,
	CardSubtitle,
	CardTitle,
	Checkbox,
	Flex,
	IconButton,
	Stack,
	Textarea,
	TextInput,
	Typography,
} from "@strapi/design-system";
import { Drag, Pencil, Plus, Trash } from "@strapi/icons";
import { useEffect, useState } from "react";
import { DragDropContext, Draggable, Droppable } from "react-beautiful-dnd";
import { useIntl } from "react-intl";
import styled from "styled-components";

// Styled components for feature management
const FeatureCard = styled(Card)`
  margin-bottom: 16px;
  cursor: ${({ isDragging }) => (isDragging ? "grabbing" : "grab")};
  opacity: ${({ isDragging }) => (isDragging ? 0.8 : 1)};
`;

const DragHandle = styled(Box)`
  cursor: grab;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  color: ${({ theme }) => theme.colors.neutral500};
  
  &:hover {
    color: ${({ theme }) => theme.colors.neutral800};
  }
`;

const FeatureForm = styled(Box)`
  margin-top: 24px;
  padding: 16px;
  background-color: ${({ theme }) => theme.colors.neutral100};
  border-radius: 4px;
`;

const FeatureManager = ({ name, attribute, value, onChange }) => {
	const { formatMessage } = useIntl();
	const [features, setFeatures] = useState([]);
	const [showForm, setShowForm] = useState(false);
	const [editingIndex, setEditingIndex] = useState(-1);
	const [newFeature, setNewFeature] = useState({
		icon: "",
		title: "",
		description: "",
		is_highlighted: false,
		order: 0,
	});

	// Initialize features from value
	useEffect(() => {
		if (value && Array.isArray(value)) {
			// Ensure all features have an order property
			const featuresWithOrder = value.map((feature, index) => ({
				...feature,
				order: feature.order !== undefined ? feature.order : index,
			}));

			// Sort by order
			setFeatures(featuresWithOrder.sort((a, b) => a.order - b.order));
		} else {
			setFeatures([]);
		}
	}, [value]);

	// Handle drag end
	const handleDragEnd = (result) => {
		if (!result.destination) return;

		const items = Array.from(features);
		const [reorderedItem] = items.splice(result.source.index, 1);
		items.splice(result.destination.index, 0, reorderedItem);

		// Update order values
		const updatedItems = items.map((item, index) => ({
			...item,
			order: index,
		}));

		setFeatures(updatedItems);
		onChange({ target: { name, value: updatedItems } });
	};

	// Handle add feature
	const handleAddFeature = () => {
		if (!newFeature.title || !newFeature.icon) return;

		const updatedFeatures = [
			...features,
			{
				...newFeature,
				order: features.length,
			},
		];

		setFeatures(updatedFeatures);
		onChange({ target: { name, value: updatedFeatures } });
		setNewFeature({
			icon: "",
			title: "",
			description: "",
			is_highlighted: false,
			order: 0,
		});
		setShowForm(false);
	};

	// Handle edit feature
	const handleEditFeature = (index) => {
		setEditingIndex(index);
		setNewFeature({ ...features[index] });
		setShowForm(true);
	};

	// Handle update feature
	const handleUpdateFeature = () => {
		if (!newFeature.title || !newFeature.icon) return;

		const updatedFeatures = [...features];
		updatedFeatures[editingIndex] = newFeature;

		setFeatures(updatedFeatures);
		onChange({ target: { name, value: updatedFeatures } });
		setNewFeature({
			icon: "",
			title: "",
			description: "",
			is_highlighted: false,
			order: 0,
		});
		setShowForm(false);
		setEditingIndex(-1);
	};

	// Handle delete feature
	const handleDeleteFeature = (index) => {
		const updatedFeatures = features
			.filter((_, i) => i !== index)
			.map((feature, i) => ({ ...feature, order: i }));

		setFeatures(updatedFeatures);
		onChange({ target: { name, value: updatedFeatures } });

		// If deleting the feature being edited, close the form
		if (editingIndex === index) {
			setShowForm(false);
			setEditingIndex(-1);
		}
	};

	// Handle form input change
	const handleInputChange = (field, value) => {
		setNewFeature({
			...newFeature,
			[field]: value,
		});
	};

	// Handle cancel form
	const handleCancelForm = () => {
		setShowForm(false);
		setEditingIndex(-1);
		setNewFeature({
			icon: "",
			title: "",
			description: "",
			is_highlighted: false,
			order: 0,
		});
	};

	return (
		<Box>
			<Flex justifyContent="space-between" alignItems="center" marginBottom={4}>
				<Typography variant="beta">
					{formatMessage({
						id: "app.components.FeatureManager.title",
						defaultMessage: "Feature Management",
					})}
				</Typography>
				<Button
					startIcon={<Plus />}
					onClick={() => setShowForm(true)}
					disabled={showForm}
				>
					{formatMessage({
						id: "app.components.FeatureManager.addFeature",
						defaultMessage: "Add Feature",
					})}
				</Button>
			</Flex>

			{features.length === 0 && !showForm && (
				<Box
					padding={6}
					background="neutral100"
					borderRadius="4px"
					textAlign="center"
				>
					<Typography textColor="neutral600">
						No features added yet. Click "Add Feature" to create your first
						feature.
					</Typography>
				</Box>
			)}

			{showForm && (
				<FeatureForm>
					<Typography variant="delta" marginBottom={4}>
						{editingIndex >= 0 ? "Edit Feature" : "Add New Feature"}
					</Typography>

					<Box marginBottom={4}>
						<TextInput
							label="Icon"
							name="icon"
							placeholder="Enter icon name or emoji (e.g., ✓, 🚀, star)"
							value={newFeature.icon}
							onChange={(e) => handleInputChange("icon", e.target.value)}
							required
						/>
					</Box>

					<Box marginBottom={4}>
						<TextInput
							label="Title"
							name="title"
							placeholder="Feature title"
							value={newFeature.title}
							onChange={(e) => handleInputChange("title", e.target.value)}
							required
						/>
					</Box>

					<Box marginBottom={4}>
						<Textarea
							label="Description"
							name="description"
							placeholder="Feature description"
							value={newFeature.description}
							onChange={(e) => handleInputChange("description", e.target.value)}
						/>
					</Box>

					<Box marginBottom={4}>
						<Checkbox
							name="is_highlighted"
							value={newFeature.is_highlighted}
							onValueChange={(value) =>
								handleInputChange("is_highlighted", value)
							}
						>
							Highlight this feature
						</Checkbox>
					</Box>

					<Flex justifyContent="flex-end" gap={2}>
						<Button variant="tertiary" onClick={handleCancelForm}>
							Cancel
						</Button>
						<Button
							onClick={
								editingIndex >= 0 ? handleUpdateFeature : handleAddFeature
							}
							disabled={!newFeature.title || !newFeature.icon}
						>
							{editingIndex >= 0 ? "Update Feature" : "Add Feature"}
						</Button>
					</Flex>
				</FeatureForm>
			)}

			{features.length > 0 && (
				<Box marginTop={4}>
					<Typography variant="pi" fontWeight="bold" marginBottom={2}>
						{formatMessage({
							id: "app.components.FeatureManager.reorder",
							defaultMessage: "Drag to Reorder",
						})}
					</Typography>

					<DragDropContext onDragEnd={handleDragEnd}>
						<Droppable droppableId="features">
							{(provided) => (
								<div {...provided.droppableProps} ref={provided.innerRef}>
									{features.map((feature, index) => (
										<Draggable
											key={index}
											draggableId={`feature-${index}`}
											index={index}
										>
											{(provided, snapshot) => (
												<FeatureCard
													ref={provided.innerRef}
													{...provided.draggableProps}
													isDragging={snapshot.isDragging}
												>
													<CardBody>
														<Flex>
															<DragHandle {...provided.dragHandleProps}>
																<Drag width="12px" height="12px" />
															</DragHandle>

															<CardContent>
																<Flex alignItems="center" marginBottom={2}>
																	<Box marginRight={2} fontSize="18px">
																		{feature.icon}
																	</Box>
																	<CardTitle>{feature.title}</CardTitle>
																	{feature.is_highlighted && (
																		<CardBadge marginLeft={2}>
																			Highlighted
																		</CardBadge>
																	)}
																</Flex>
																<CardSubtitle>
																	{feature.description}
																</CardSubtitle>
															</CardContent>

															<CardAction position="end">
																<Stack horizontal spacing={2}>
																	<IconButton
																		onClick={() => handleEditFeature(index)}
																		label="Edit"
																		icon={<Pencil />}
																	/>
																	<IconButton
																		onClick={() => handleDeleteFeature(index)}
																		label="Delete"
																		icon={<Trash />}
																	/>
																</Stack>
															</CardAction>
														</Flex>
													</CardBody>
												</FeatureCard>
											)}
										</Draggable>
									))}
									{provided.placeholder}
								</div>
							)}
						</Droppable>
					</DragDropContext>
				</Box>
			)}
		</Box>
	);
};

export default FeatureManager;
