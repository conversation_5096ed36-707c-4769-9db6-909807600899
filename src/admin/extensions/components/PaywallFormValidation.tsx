/**
 * Form validation component with user-friendly error messages for paywall forms
 */

import {
	<PERSON><PERSON>,
	<PERSON>,
	<PERSON>lex,
	Icon,
	Stack,
	Typography,
} from "@strapi/design-system";
import { CheckCircle, ExclamationMarkCircle, Information } from "@strapi/icons";
import type React from "react";
import { useEffect, useState } from "react";
import { useIntl } from "react-intl";
import styled from "styled-components";

interface ValidationRule {
	field: string;
	rule: string;
	message: string;
	severity: "error" | "warning" | "info";
}

interface ValidationError {
	field: string;
	message: string;
	severity: "error" | "warning" | "info";
	code?: string;
}

interface PaywallFormValidationProps {
	data: any;
	onChange?: (isValid: boolean, errors: ValidationError[]) => void;
	showInlineErrors?: boolean;
	showSummary?: boolean;
}

const _ValidationMessage = styled(Box)<{
	severity: "error" | "warning" | "info";
}>`
  border-left: 3px solid ${({ theme, severity }) => {
		switch (severity) {
			case "error":
				return theme.colors.danger600;
			case "warning":
				return theme.colors.warning600;
			case "info":
				return theme.colors.primary600;
			default:
				return theme.colors.neutral300;
		}
	}};
  padding-left: 12px;
  margin-top: 4px;
`;

const PaywallFormValidation: React.FC<PaywallFormValidationProps> = ({
	data,
	onChange,
	showInlineErrors = true,
	showSummary = true,
}) => {
	const { formatMessage } = useIntl();
	const [errors, setErrors] = useState<ValidationError[]>([]);
	const [isValid, setIsValid] = useState(true);

	// Validation rules for paywall fields
	const validationRules: ValidationRule[] = [
		{
			field: "name",
			rule: "required",
			message: formatMessage({
				id: "validation.paywall.name.required",
				defaultMessage:
					"Paywall name is required for identification and organization",
			}),
			severity: "error",
		},
		{
			field: "name",
			rule: "maxLength",
			message: formatMessage({
				id: "validation.paywall.name.maxLength",
				defaultMessage: "Paywall name must be 255 characters or less",
			}),
			severity: "error",
		},
		{
			field: "placement_id",
			rule: "required",
			message: formatMessage({
				id: "validation.paywall.placement_id.required",
				defaultMessage: "Placement ID is required for Adapty integration",
			}),
			severity: "error",
		},
		{
			field: "placement_id",
			rule: "format",
			message: formatMessage({
				id: "validation.paywall.placement_id.format",
				defaultMessage:
					"Placement ID should contain only letters, numbers, and underscores",
			}),
			severity: "error",
		},
		{
			field: "title",
			rule: "required",
			message: formatMessage({
				id: "validation.paywall.title.required",
				defaultMessage: "Title is required - this is what users will see first",
			}),
			severity: "error",
		},
		{
			field: "title",
			rule: "maxLength",
			message: formatMessage({
				id: "validation.paywall.title.maxLength",
				defaultMessage:
					"Title should be concise (255 characters max) for better mobile display",
			}),
			severity: "warning",
		},
		{
			field: "cta_text",
			rule: "required",
			message: formatMessage({
				id: "validation.paywall.cta_text.required",
				defaultMessage: "Call-to-action text is required for user conversion",
			}),
			severity: "error",
		},
		{
			field: "cta_text",
			rule: "maxLength",
			message: formatMessage({
				id: "validation.paywall.cta_text.maxLength",
				defaultMessage:
					"CTA text should be short and actionable (100 characters max)",
			}),
			severity: "warning",
		},
		{
			field: "subtitle",
			rule: "maxLength",
			message: formatMessage({
				id: "validation.paywall.subtitle.maxLength",
				defaultMessage:
					"Subtitle should be brief (500 characters max) to maintain readability",
			}),
			severity: "warning",
		},
		{
			field: "theme.primary_color",
			rule: "format",
			message: formatMessage({
				id: "validation.paywall.theme.primary_color.format",
				defaultMessage:
					"Primary color must be a valid hex color (e.g., #FF5733)",
			}),
			severity: "error",
		},
		{
			field: "theme.background_color",
			rule: "format",
			message: formatMessage({
				id: "validation.paywall.theme.background_color.format",
				defaultMessage:
					"Background color must be a valid hex color (e.g., #FFFFFF)",
			}),
			severity: "error",
		},
		{
			field: "theme.text_color",
			rule: "format",
			message: formatMessage({
				id: "validation.paywall.theme.text_color.format",
				defaultMessage: "Text color must be a valid hex color (e.g., #000000)",
			}),
			severity: "error",
		},
		{
			field: "features",
			rule: "minItems",
			message: formatMessage({
				id: "validation.paywall.features.minItems",
				defaultMessage:
					"Consider adding at least 3 features to highlight your product value",
			}),
			severity: "info",
		},
		{
			field: "features",
			rule: "maxItems",
			message: formatMessage({
				id: "validation.paywall.features.maxItems",
				defaultMessage:
					"Too many features (>8) may overwhelm users on mobile devices",
			}),
			severity: "warning",
		},
	];

	// Validation functions
	const validateField = (field: string, value: any, rule: string): boolean => {
		switch (rule) {
			case "required":
				return value !== null && value !== undefined && value !== "";

			case "maxLength": {
				const maxLengths = {
					name: 255,
					title: 255,
					subtitle: 500,
					cta_text: 100,
					cta_secondary_text: 200,
				};
				return !value || value.length <= (maxLengths[field] || 255);
			}

			case "format":
				if (field.includes("color")) {
					return !value || /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(value);
				}
				if (field === "placement_id") {
					return !value || /^[a-zA-Z0-9_]+$/.test(value);
				}
				return true;

			case "minItems":
				if (field === "features") {
					return !value || value.length >= 3;
				}
				return true;

			case "maxItems":
				if (field === "features") {
					return !value || value.length <= 8;
				}
				return true;

			default:
				return true;
		}
	};

	// Get nested field value
	const getFieldValue = (data: any, fieldPath: string): any => {
		return fieldPath.split(".").reduce((obj, key) => obj?.[key], data);
	};

	// Validate all fields
	const validateForm = () => {
		const newErrors: ValidationError[] = [];

		validationRules.forEach(({ field, rule, message, severity }) => {
			const value = getFieldValue(data, field);
			const isValid = validateField(field, value, rule);

			if (!isValid) {
				newErrors.push({
					field,
					message,
					severity,
					code: `${field}.${rule}`,
				});
			}
		});

		// Additional custom validations

		// Check color contrast
		if (data?.theme?.primary_color && data?.theme?.background_color) {
			const contrast = calculateColorContrast(
				data.theme.primary_color,
				data.theme.background_color,
			);
			if (contrast < 3) {
				newErrors.push({
					field: "theme.colors",
					message: formatMessage({
						id: "validation.paywall.theme.contrast",
						defaultMessage:
							"Low color contrast may affect readability. Consider using more contrasting colors.",
					}),
					severity: "warning",
					code: "theme.contrast",
				});
			}
		}

		// Check for duplicate placement IDs (would need API call in real implementation)
		if (data?.placement_id && data.placement_id.length < 3) {
			newErrors.push({
				field: "placement_id",
				message: formatMessage({
					id: "validation.paywall.placement_id.minLength",
					defaultMessage:
						"Placement ID should be at least 3 characters for better organization",
				}),
				severity: "warning",
				code: "placement_id.minLength",
			});
		}

		// Check if features have proper ordering
		if (data?.features && data.features.length > 1) {
			const hasOrdering = data.features.every(
				(feature: any) => feature.order !== undefined && feature.order !== null,
			);
			if (!hasOrdering) {
				newErrors.push({
					field: "features",
					message: formatMessage({
						id: "validation.paywall.features.ordering",
						defaultMessage:
							"Features should have proper ordering for consistent display",
					}),
					severity: "info",
					code: "features.ordering",
				});
			}
		}

		setErrors(newErrors);
		const formIsValid =
			newErrors.filter((e) => e.severity === "error").length === 0;
		setIsValid(formIsValid);

		if (onChange) {
			onChange(formIsValid, newErrors);
		}
	};

	// Calculate color contrast ratio (simplified)
	const calculateColorContrast = (color1: string, color2: string): number => {
		// Simplified contrast calculation
		// In a real implementation, you'd use a proper color contrast library
		const getLuminance = (hex: string) => {
			const rgb = parseInt(hex.slice(1), 16);
			const r = (rgb >> 16) & 0xff;
			const g = (rgb >> 8) & 0xff;
			const b = (rgb >> 0) & 0xff;
			return (0.299 * r + 0.587 * g + 0.114 * b) / 255;
		};

		const lum1 = getLuminance(color1);
		const lum2 = getLuminance(color2);
		const brightest = Math.max(lum1, lum2);
		const darkest = Math.min(lum1, lum2);

		return (brightest + 0.05) / (darkest + 0.05);
	};

	// Run validation when data changes
	useEffect(() => {
		validateForm();
	}, [validateForm]);

	// Get icon for severity
	const _getSeverityIcon = (severity: "error" | "warning" | "info") => {
		switch (severity) {
			case "error":
				return <ExclamationMarkCircle />;
			case "warning":
				return <ExclamationMarkCircle />;
			case "info":
				return <Information />;
			default:
				return <Information />;
		}
	};

	// Get alert variant for severity
	const _getAlertVariant = (severity: "error" | "warning" | "info") => {
		switch (severity) {
			case "error":
				return "danger";
			case "warning":
				return "default";
			case "info":
				return "success";
			default:
				return "default";
		}
	};

	// Group errors by severity
	const errorsBySeverity = {
		error: errors.filter((e) => e.severity === "error"),
		warning: errors.filter((e) => e.severity === "warning"),
		info: errors.filter((e) => e.severity === "info"),
	};

	if (!showSummary && !showInlineErrors) {
		return null;
	}

	return (
		<Box>
			{showSummary && errors.length > 0 && (
				<Box marginBottom={4}>
					<Stack spacing={2}>
						{/* Error Summary */}
						{errorsBySeverity.error.length > 0 && (
							<Alert
								variant="danger"
								title={formatMessage({
									id: "validation.summary.errors",
									defaultMessage: "Please fix the following errors:",
								})}
							>
								<Stack spacing={1}>
									{errorsBySeverity.error.map((error, index) => (
										<Typography key={index} variant="pi">
											• {error.message}
										</Typography>
									))}
								</Stack>
							</Alert>
						)}

						{/* Warning Summary */}
						{errorsBySeverity.warning.length > 0 && (
							<Alert
								variant="default"
								title={formatMessage({
									id: "validation.summary.warnings",
									defaultMessage: "Recommendations for improvement:",
								})}
							>
								<Stack spacing={1}>
									{errorsBySeverity.warning.map((error, index) => (
										<Typography key={index} variant="pi">
											• {error.message}
										</Typography>
									))}
								</Stack>
							</Alert>
						)}

						{/* Info Summary */}
						{errorsBySeverity.info.length > 0 && (
							<Alert
								variant="success"
								title={formatMessage({
									id: "validation.summary.tips",
									defaultMessage: "Tips for better paywall performance:",
								})}
							>
								<Stack spacing={1}>
									{errorsBySeverity.info.map((error, index) => (
										<Typography key={index} variant="pi">
											• {error.message}
										</Typography>
									))}
								</Stack>
							</Alert>
						)}
					</Stack>
				</Box>
			)}

			{/* Validation Status */}
			{showSummary && (
				<Box marginBottom={4}>
					<Flex alignItems="center" gap={2}>
						{isValid ? (
							<>
								<Icon as={CheckCircle} color="success600" />
								<Typography variant="pi" textColor="success600">
									{formatMessage({
										id: "validation.status.valid",
										defaultMessage:
											"Paywall configuration is valid and ready for publishing",
									})}
								</Typography>
							</>
						) : (
							<>
								<Icon as={ExclamationMarkCircle} color="danger600" />
								<Typography variant="pi" textColor="danger600">
									{formatMessage({
										id: "validation.status.invalid",
										defaultMessage:
											"Please fix validation errors before publishing",
									})}
								</Typography>
							</>
						)}
					</Flex>
				</Box>
			)}
		</Box>
	);
};

// Hook for using validation in forms
export const usePaywallValidation = (data: any) => {
	const [isValid, setIsValid] = useState(true);
	const [errors, setErrors] = useState<ValidationError[]>([]);

	const handleValidationChange = (
		valid: boolean,
		validationErrors: ValidationError[],
	) => {
		setIsValid(valid);
		setErrors(validationErrors);
	};

	return {
		isValid,
		errors,
		ValidationComponent: (
			props: Omit<PaywallFormValidationProps, "data" | "onChange">,
		) => (
			<PaywallFormValidation
				{...props}
				data={data}
				onChange={handleValidationChange}
			/>
		),
	};
};

export default PaywallFormValidation;
