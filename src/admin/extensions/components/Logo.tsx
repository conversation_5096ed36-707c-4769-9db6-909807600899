/**
 * Custom logo component for admin panel
 */

import styled from "styled-components";

const LogoContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
`;

const LogoText = styled.span`
  font-weight: 600;
  font-size: 1.2rem;
  color: ${({ theme }) => theme.colors.primary600};
`;

const Logo = () => {
	return (
		<LogoContainer>
			<LogoText>Adapty CMS</LogoText>
		</LogoContainer>
	);
};

export default Logo;
