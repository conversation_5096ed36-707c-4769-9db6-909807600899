/**
 * Test Results Visualization - Charts and statistical analysis for A/B test results
 */

import {
	<PERSON>ert,
	Badge,
	Box,
	Button,
	Card,
	CardBody,
	CardHeader,
	Flex,
	Grid,
	GridItem,
	Option,
	ProgressBar,
	Select,
	Stack,
	Typography,
} from "@strapi/design-system";
import { useNotification } from "@strapi/helper-plugin";
import {
	CheckCircle,
	Crown,
	Download,
	Information,
	ChartPie as LineChart,
} from "@strapi/icons";
import type React from "react";
import { useEffect, useState } from "react";
import { useIntl } from "react-intl";
import styled from "styled-components";

const ChartContainer = styled(Box)`
  height: 300px;
  width: 100%;
  background: ${({ theme }) => theme.colors.neutral100};
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
`;

const StatCard = styled(Card)`
  height: 100%;
  transition: all 0.2s ease;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
`;

const WinnerBadge = styled(Badge)`
  position: absolute;
  top: 8px;
  right: 8px;
`;

interface TestResultsVisualizationProps {
	testId: string;
	variations: any[];
	metrics: any;
	onExport?: () => void;
}

const TestResultsVisualization: React.FC<TestResultsVisualizationProps> = ({
	testId,
	variations,
	metrics,
	onExport,
}) => {
	const { formatMessage } = useIntl();
	const _toggleNotification = useNotification();
	const [selectedMetric, setSelectedMetric] = useState("conversion_rate");
	const [timeRange, setTimeRange] = useState("7d");
	const [_chartData, _setChartData] = useState(null);
	const [_loading, _setLoading] = useState(false);
	const [statisticalSignificance, setStatisticalSignificance] = useState(null);

	// Calculate statistical significance
	const calculateSignificance = () => {
		if (!variations || variations.length < 2) return null;

		const control = variations.find((v) => v.isControl);
		const variants = variations.filter((v) => !v.isControl);

		if (!control || variants.length === 0) return null;

		return variants.map((variant) => {
			const controlRate = control.conversionRate || 0;
			const variantRate = variant.conversionRate || 0;
			const controlSample = control.sampleSize || 0;
			const variantSample = variant.sampleSize || 0;

			// Simple z-test calculation for conversion rates
			const p1 = controlRate / 100;
			const p2 = variantRate / 100;
			const n1 = controlSample;
			const n2 = variantSample;

			if (n1 === 0 || n2 === 0)
				return { variant: variant.name, significant: false, pValue: 1 };

			const pooledP = (p1 * n1 + p2 * n2) / (n1 + n2);
			const se = Math.sqrt(pooledP * (1 - pooledP) * (1 / n1 + 1 / n2));
			const z = Math.abs(p2 - p1) / se;
			const pValue = 2 * (1 - normalCDF(Math.abs(z)));

			return {
				variant: variant.name,
				significant: pValue < 0.05,
				pValue,
				improvement: (
					((variantRate - controlRate) / controlRate) *
					100
				).toFixed(1),
			};
		});
	};

	// Normal CDF approximation
	const normalCDF = (x: number) => {
		return 0.5 * (1 + erf(x / Math.sqrt(2)));
	};

	const erf = (x: number) => {
		// Approximation of error function
		const a1 = 0.254829592;
		const a2 = -0.284496736;
		const a3 = 1.421413741;
		const a4 = -1.453152027;
		const a5 = 1.061405429;
		const p = 0.3275911;

		const sign = x >= 0 ? 1 : -1;
		x = Math.abs(x);

		const t = 1.0 / (1.0 + p * x);
		const y =
			1.0 -
			((((a5 * t + a4) * t + a3) * t + a2) * t + a1) * t * Math.exp(-x * x);

		return sign * y;
	};

	useEffect(() => {
		const significance = calculateSignificance();
		setStatisticalSignificance(significance);
	}, [calculateSignificance]);

	const handleExport = () => {
		if (onExport) {
			onExport();
		} else {
			// Default export functionality
			const data = {
				testId,
				variations,
				metrics,
				statisticalSignificance,
				exportedAt: new Date().toISOString(),
			};

			const blob = new Blob([JSON.stringify(data, null, 2)], {
				type: "application/json",
			});
			const url = URL.createObjectURL(blob);
			const a = document.createElement("a");
			a.href = url;
			a.download = `ab-test-results-${testId}-${Date.now()}.json`;
			a.click();
			URL.revokeObjectURL(url);
		}
	};

	const getWinningVariation = () => {
		if (!variations || variations.length === 0) return null;
		return variations.reduce((winner, current) =>
			(current.conversionRate || 0) > (winner.conversionRate || 0)
				? current
				: winner,
		);
	};

	const winner = getWinningVariation();

	return (
		<Box>
			{/* Controls */}
			<Flex justifyContent="space-between" alignItems="center" marginBottom={4}>
				<Flex gap={3}>
					<Select
						label="Metric"
						value={selectedMetric}
						onChange={setSelectedMetric}
					>
						<Option value="conversion_rate">Conversion Rate</Option>
						<Option value="revenue">Revenue</Option>
						<Option value="engagement">Engagement</Option>
						<Option value="retention">Retention</Option>
					</Select>
					<Select label="Time Range" value={timeRange} onChange={setTimeRange}>
						<Option value="1d">Last 24 hours</Option>
						<Option value="7d">Last 7 days</Option>
						<Option value="30d">Last 30 days</Option>
						<Option value="all">All time</Option>
					</Select>
				</Flex>
				<Button
					variant="secondary"
					startIcon={<Download />}
					onClick={handleExport}
				>
					Export Results
				</Button>
			</Flex>

			{/* Statistical Significance Alert */}
			{statisticalSignificance && (
				<Alert
					variant={
						statisticalSignificance.some((s) => s.significant)
							? "success"
							: "default"
					}
					title="Statistical Analysis"
					marginBottom={4}
				>
					{statisticalSignificance.some((s) => s.significant)
						? "Statistically significant results detected. You can confidently make decisions based on these results."
						: "Results are not yet statistically significant. Consider running the test longer for more reliable data."}
				</Alert>
			)}

			{/* Performance Chart */}
			<Card marginBottom={4}>
				<CardHeader>
					<Typography variant="delta" fontWeight="bold">
						Performance Over Time
					</Typography>
				</CardHeader>
				<CardBody>
					<ChartContainer>
						<Stack spacing={2} alignItems="center">
							<LineChart width="48px" height="48px" />
							<Typography variant="omega" textColor="neutral600">
								Chart visualization would be implemented here
							</Typography>
							<Typography variant="pi" textColor="neutral500">
								Integration with charting library (Chart.js, D3, etc.)
							</Typography>
						</Stack>
					</ChartContainer>
				</CardBody>
			</Card>

			{/* Variation Performance Grid */}
			<Grid gap={4} gridCols={variations.length || 1}>
				{variations.map((variation, _index) => {
					const isWinner = winner && variation.id === winner.id;
					const significance = statisticalSignificance?.find(
						(s) => s.variant === variation.name,
					);

					return (
						<GridItem key={variation.id}>
							<StatCard>
								<CardHeader>
									<Flex justifyContent="space-between" alignItems="center">
										<Typography variant="delta" fontWeight="bold">
											{variation.name}
										</Typography>
										{isWinner && (
											<WinnerBadge
												backgroundColor="success100"
												textColor="success700"
											>
												<Crown width="12px" height="12px" />
												Winner
											</WinnerBadge>
										)}
									</Flex>
								</CardHeader>
								<CardBody>
									<Stack spacing={3}>
										{/* Conversion Rate */}
										<Box>
											<Flex justifyContent="space-between" alignItems="center">
												<Typography variant="sigma" textColor="neutral600">
													Conversion Rate
												</Typography>
												<Typography variant="omega" fontWeight="bold">
													{(variation.conversionRate || 0).toFixed(2)}%
												</Typography>
											</Flex>
											<ProgressBar
												value={variation.conversionRate || 0}
												maxValue={100}
											/>
										</Box>

										{/* Sample Size */}
										<Flex justifyContent="space-between">
											<Typography variant="sigma" textColor="neutral600">
												Sample Size
											</Typography>
											<Typography variant="omega">
												{(variation.sampleSize || 0).toLocaleString()}
											</Typography>
										</Flex>

										{/* Revenue */}
										<Flex justifyContent="space-between">
											<Typography variant="sigma" textColor="neutral600">
												Revenue
											</Typography>
											<Typography variant="omega">
												${(variation.revenue || 0).toLocaleString()}
											</Typography>
										</Flex>

										{/* Statistical Significance */}
										{significance && !variation.isControl && (
											<Box>
												<Flex alignItems="center" gap={2}>
													{significance.significant ? (
														<CheckCircle
															width="16px"
															height="16px"
															fill="success600"
														/>
													) : (
														<Information
															width="16px"
															height="16px"
															fill="neutral400"
														/>
													)}
													<Typography
														variant="pi"
														textColor={
															significance.significant
																? "success600"
																: "neutral600"
														}
													>
														{significance.significant
															? "Significant"
															: "Not significant"}
													</Typography>
												</Flex>
												{significance.improvement && (
													<Typography variant="pi" textColor="neutral600">
														{significance.improvement > 0 ? "+" : ""}
														{significance.improvement}% vs control
													</Typography>
												)}
											</Box>
										)}

										{/* Control Badge */}
										{variation.isControl && (
											<Badge
												backgroundColor="neutral100"
												textColor="neutral700"
											>
												Control
											</Badge>
										)}
									</Stack>
								</CardBody>
							</StatCard>
						</GridItem>
					);
				})}
			</Grid>
		</Box>
	);
};

export default TestResultsVisualization;
