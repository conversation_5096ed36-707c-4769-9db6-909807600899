/**
 * Variation Comparison - Side-by-side comparison view for test variations
 */

import {
	<PERSON>ert,
	Badge,
	Box,
	Button,
	Card,
	CardBody,
	CardHeader,
	Flex,
	Grid,
	GridItem,
	IconButton,
	ProgressBar,
	Stack,
	Typography,
} from "@strapi/design-system";
import { useNotification } from "@strapi/helper-plugin";
import {
	Crown,
	Eye,
	Information,
	Refresh,
	ArrowDown as TrendDown,
	ArrowUp as TrendUp,
} from "@strapi/icons";
import type React from "react";
import { useEffect, useState } from "react";
import { useIntl } from "react-intl";
import styled from "styled-components";

const ComparisonCard = styled(Card)`
  height: 100%;
  position: relative;
  
  &.winner {
    border: 2px solid #5cb176;
    box-shadow: 0 4px 12px rgba(92, 177, 118, 0.2);
  }
  
  &.loser {
    opacity: 0.8;
  }
`;

const WinnerBadge = styled(Badge)`
  position: absolute;
  top: -8px;
  right: 16px;
  z-index: 1;
`;

const MetricBox = styled(Box)`
  padding: 12px;
  border: 1px solid ${({ theme }) => theme.colors.neutral200};
  border-radius: 4px;
  text-align: center;
  background: ${({ isWinning, theme }) =>
		isWinning ? theme.colors.success100 : "transparent"};
`;

const PreviewFrame = styled(Box)`
  width: 100%;
  height: 300px;
  border: 1px solid ${({ theme }) => theme.colors.neutral200};
  border-radius: 4px;
  overflow: hidden;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
`;

interface Variation {
	id: string;
	name: string;
	description: string;
	trafficAllocation: number;
	paywall: any;
	metrics: {
		impressions: number;
		conversions: number;
		conversionRate: number;
		revenue: number;
		confidence: number;
	};
	isControl: boolean;
	isWinner?: boolean;
}

interface VariationComparisonProps {
	testId: string;
	variations: Variation[];
	onPromoteWinner?: (variationId: string) => void;
	onRefreshData?: () => void;
}

const VariationComparison: React.FC<VariationComparisonProps> = ({
	testId,
	variations,
	onPromoteWinner,
	onRefreshData,
}) => {
	const { formatMessage } = useIntl();
	const toggleNotification = useNotification();
	const [_selectedVariations, setSelectedVariations] = useState<string[]>([]);
	const [showStatisticalSignificance, setShowStatisticalSignificance] =
		useState(false);

	useEffect(() => {
		if (variations.length > 0) {
			// Auto-select first two variations for comparison
			setSelectedVariations(variations.slice(0, 2).map((v) => v.id));
		}
	}, [variations]);

	const calculateLift = (variation: Variation, control: Variation) => {
		if (!control || control.metrics.conversionRate === 0) return 0;
		return (
			((variation.metrics.conversionRate - control.metrics.conversionRate) /
				control.metrics.conversionRate) *
			100
		);
	};

	const getStatisticalSignificance = (variation: Variation) => {
		const confidence = variation.metrics.confidence || 0;
		if (confidence >= 95)
			return {
				level: "high",
				color: "success",
				text: "Statistically Significant",
			};
		if (confidence >= 90)
			return {
				level: "medium",
				color: "warning",
				text: "Approaching Significance",
			};
		return { level: "low", color: "danger", text: "Not Significant" };
	};

	const controlVariation = variations.find((v) => v.isControl);
	const testVariations = variations.filter((v) => !v.isControl);

	const promoteWinner = async (variationId: string) => {
		try {
			const response = await fetch(`/api/ab-tests/${testId}/promote-winner`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({ winnerId: variationId }),
			});

			if (response.ok) {
				toggleNotification({
					type: "success",
					message: "Winner promoted successfully",
				});
				onPromoteWinner?.(variationId);
			}
		} catch (error) {
			console.error("Error promoting winner:", error);
			toggleNotification({
				type: "warning",
				message: "Failed to promote winner",
			});
		}
	};

	const renderVariationCard = (variation: Variation) => {
		const lift = controlVariation
			? calculateLift(variation, controlVariation)
			: 0;
		const significance = getStatisticalSignificance(variation);
		const isWinning =
			variation.isWinner || (lift > 0 && significance.level === "high");

		return (
			<ComparisonCard
				key={variation.id}
				className={isWinning ? "winner" : lift < 0 ? "loser" : ""}
			>
				{isWinning && (
					<WinnerBadge backgroundColor="success500" textColor="neutral0">
						<Crown width={12} height={12} />
						Winner
					</WinnerBadge>
				)}

				<CardHeader>
					<Flex justifyContent="space-between" alignItems="center">
						<Stack spacing={1}>
							<Typography variant="delta">{variation.name}</Typography>
							{variation.isControl && (
								<Badge backgroundColor="neutral200">Control</Badge>
							)}
						</Stack>
						<IconButton
							label="View Details"
							icon={<Eye />}
							onClick={() => {
								/* Open detailed view */
							}}
						/>
					</Flex>
				</CardHeader>

				<CardBody>
					<Stack spacing={4}>
						{/* Preview */}
						<Box>
							<Typography variant="epsilon" marginBottom={2}>
								Paywall Preview
							</Typography>
							<PreviewFrame>
								<Typography variant="omega" textColor="neutral500">
									Paywall Preview
									<br />
									{variation.paywall?.title || "No title set"}
								</Typography>
							</PreviewFrame>
						</Box>

						{/* Key Metrics */}
						<Box>
							<Typography variant="epsilon" marginBottom={2}>
								Performance Metrics
							</Typography>
							<Grid gap={2}>
								<GridItem col={6}>
									<MetricBox isWinning={isWinning}>
										<Typography variant="pi" textColor="neutral600">
											Conversion Rate
										</Typography>
										<Typography variant="delta" marginTop={1}>
											{variation.metrics.conversionRate.toFixed(2)}%
										</Typography>
										{!variation.isControl && (
											<Flex
												justifyContent="center"
												alignItems="center"
												marginTop={1}
											>
												{lift > 0 ? (
													<TrendUp color="success500" />
												) : (
													<TrendDown color="danger500" />
												)}
												<Typography
													variant="pi"
													textColor={lift > 0 ? "success600" : "danger600"}
													marginLeft={1}
												>
													{lift > 0 ? "+" : ""}
													{lift.toFixed(1)}%
												</Typography>
											</Flex>
										)}
									</MetricBox>
								</GridItem>
								<GridItem col={6}>
									<MetricBox>
										<Typography variant="pi" textColor="neutral600">
											Revenue
										</Typography>
										<Typography variant="delta" marginTop={1}>
											${variation.metrics.revenue.toLocaleString()}
										</Typography>
									</MetricBox>
								</GridItem>
								<GridItem col={6}>
									<MetricBox>
										<Typography variant="pi" textColor="neutral600">
											Impressions
										</Typography>
										<Typography variant="delta" marginTop={1}>
											{variation.metrics.impressions.toLocaleString()}
										</Typography>
									</MetricBox>
								</GridItem>
								<GridItem col={6}>
									<MetricBox>
										<Typography variant="pi" textColor="neutral600">
											Conversions
										</Typography>
										<Typography variant="delta" marginTop={1}>
											{variation.metrics.conversions.toLocaleString()}
										</Typography>
									</MetricBox>
								</GridItem>
							</Grid>
						</Box>

						{/* Statistical Significance */}
						{!variation.isControl && (
							<Box>
								<Typography variant="epsilon" marginBottom={2}>
									Statistical Analysis
								</Typography>
								<Box
									padding={3}
									backgroundColor={`${significance.color}100`}
									borderRadius="4px"
								>
									<Flex justifyContent="space-between" alignItems="center">
										<Typography
											variant="pi"
											textColor={`${significance.color}600`}
										>
											{significance.text}
										</Typography>
										<Typography
											variant="pi"
											textColor={`${significance.color}600`}
										>
											{variation.metrics.confidence.toFixed(1)}% confidence
										</Typography>
									</Flex>
									<ProgressBar
										value={variation.metrics.confidence}
										maxValue={100}
										size="S"
										marginTop={2}
									/>
								</Box>
							</Box>
						)}

						{/* Traffic Allocation */}
						<Box>
							<Typography variant="epsilon" marginBottom={2}>
								Traffic Allocation
							</Typography>
							<Flex alignItems="center" gap={2}>
								<ProgressBar
									value={variation.trafficAllocation}
									maxValue={100}
									size="M"
								/>
								<Typography variant="pi">
									{variation.trafficAllocation}%
								</Typography>
							</Flex>
						</Box>

						{/* Actions */}
						{isWinning && !variation.isWinner && (
							<Button
								fullWidth
								startIcon={<Crown />}
								onClick={() => promoteWinner(variation.id)}
							>
								Promote as Winner
							</Button>
						)}
					</Stack>
				</CardBody>
			</ComparisonCard>
		);
	};

	return (
		<Box>
			{/* Header */}
			<Flex justifyContent="space-between" alignItems="center" marginBottom={4}>
				<Typography variant="alpha">Variation Comparison</Typography>
				<Flex gap={2}>
					<Button
						variant="tertiary"
						startIcon={<Refresh />}
						onClick={onRefreshData}
					>
						Refresh Data
					</Button>
					<Button
						variant="secondary"
						startIcon={<Information />}
						onClick={() =>
							setShowStatisticalSignificance(!showStatisticalSignificance)
						}
					>
						Statistical Info
					</Button>
				</Flex>
			</Flex>

			{/* Statistical Significance Info */}
			{showStatisticalSignificance && (
				<Alert
					closeLabel="Close"
					title="Statistical Significance"
					variant="default"
					onClose={() => setShowStatisticalSignificance(false)}
					marginBottom={4}
				>
					<Typography>
						Statistical significance indicates how confident we can be that the
						observed differences are real and not due to random chance. A 95%
						confidence level is generally considered statistically significant
						for making business decisions.
					</Typography>
				</Alert>
			)}

			{/* Variations Grid */}
			<Grid gap={4}>
				{variations.map((variation) => (
					<GridItem col={6} key={variation.id}>
						{renderVariationCard(variation)}
					</GridItem>
				))}
			</Grid>

			{/* Summary Comparison */}
			{controlVariation && testVariations.length > 0 && (
				<Box marginTop={6}>
					<Typography variant="beta" marginBottom={4}>
						Performance Summary
					</Typography>
					<Card>
						<CardBody>
							<Grid gap={4}>
								<GridItem col={12}>
									<Typography variant="epsilon" marginBottom={2}>
										Conversion Rate Comparison vs Control
									</Typography>
									{testVariations.map((variation) => {
										const lift = calculateLift(variation, controlVariation);
										const significance = getStatisticalSignificance(variation);

										return (
											<Flex
												key={variation.id}
												justifyContent="space-between"
												alignItems="center"
												padding={2}
												backgroundColor={
													lift > 0
														? "success100"
														: lift < 0
															? "danger100"
															: "neutral100"
												}
												borderRadius="4px"
												marginBottom={2}
											>
												<Typography variant="pi">{variation.name}</Typography>
												<Flex alignItems="center" gap={2}>
													<Typography
														variant="pi"
														textColor={
															lift > 0
																? "success600"
																: lift < 0
																	? "danger600"
																	: "neutral600"
														}
													>
														{lift > 0 ? "+" : ""}
														{lift.toFixed(1)}% lift
													</Typography>
													<Badge backgroundColor={significance.color}>
														{significance.level}
													</Badge>
												</Flex>
											</Flex>
										);
									})}
								</GridItem>
							</Grid>
						</CardBody>
					</Card>
				</Box>
			)}
		</Box>
	);
};

export default VariationComparison;
