/**
 * A/B Test Manager - Main interface for creating and managing A/B tests
 */

import {
	Badge,
	Box,
	Button,
	Card,
	CardBody,
	CardHeader,
	Flex,
	Grid,
	GridItem,
	Loader,
	ModalBody,
	Modal<PERSON>ooter,
	ModalHeader,
	ModalLayout,
	NumberInput,
	Option,
	Select,
	Stack,
	Textarea,
	TextInput,
	Typography,
} from "@strapi/design-system";
import { useNotification } from "@strapi/helper-plugin";
import {
	ChartCircle as Bar<PERSON><PERSON>,
	Clock as Pause,
	Play,
	Plus,
	Cog as Settings,
	Cross as Stop,
} from "@strapi/icons";
import type React from "react";
import { useEffect, useState } from "react";
import { useIntl } from "react-intl";
import styled from "styled-components";

const TestCard = styled(Card)`
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
`;

const StatusBadge = styled(Badge)`
  text-transform: uppercase;
  font-weight: 600;
`;

const MetricCard = styled(Box)`
  padding: 16px;
  border: 1px solid ${({ theme }) => theme.colors.neutral200};
  border-radius: 4px;
  text-align: center;
`;

interface ABTest {
	id: string;
	name: string;
	description: string;
	status: "draft" | "running" | "paused" | "completed";
	startDate: string;
	endDate: string;
	trafficAllocation: number;
	variations: any[];
	metrics: {
		impressions: number;
		conversions: number;
		conversionRate: number;
		revenue: number;
	};
	winner?: string;
	confidenceLevel: number;
}

interface ABTestManagerProps {
	onTestSelect?: (test: ABTest) => void;
	selectedTest?: ABTest;
}

const ABTestManager: React.FC<ABTestManagerProps> = ({
	onTestSelect,
	selectedTest,
}) => {
	const { formatMessage } = useIntl();
	const toggleNotification = useNotification();
	const [tests, setTests] = useState<ABTest[]>([]);
	const [loading, setLoading] = useState(true);
	const [showCreateModal, setShowCreateModal] = useState(false);
	const [newTest, setNewTest] = useState({
		name: "",
		description: "",
		trafficAllocation: 50,
		startDate: "",
		endDate: "",
		targetMetric: "conversion_rate",
	});

	useEffect(() => {
		fetchABTests();
	}, [fetchABTests]);

	const fetchABTests = async () => {
		try {
			setLoading(true);
			const response = await fetch("/api/ab-tests?populate=*");
			const data = await response.json();
			setTests(data.data || []);
		} catch (error) {
			console.error("Error fetching A/B tests:", error);
			toggleNotification({
				type: "warning",
				message: "Failed to load A/B tests",
			});
		} finally {
			setLoading(false);
		}
	};

	const createTest = async () => {
		try {
			const response = await fetch("/api/ab-tests", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({ data: newTest }),
			});

			if (response.ok) {
				toggleNotification({
					type: "success",
					message: "A/B test created successfully",
				});
				setShowCreateModal(false);
				setNewTest({
					name: "",
					description: "",
					trafficAllocation: 50,
					startDate: "",
					endDate: "",
					targetMetric: "conversion_rate",
				});
				fetchABTests();
			}
		} catch (error) {
			console.error("Error creating A/B test:", error);
			toggleNotification({
				type: "warning",
				message: "Failed to create A/B test",
			});
		}
	};

	const updateTestStatus = async (testId: string, status: string) => {
		try {
			const response = await fetch(`/api/ab-tests/${testId}`, {
				method: "PUT",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({ data: { status } }),
			});

			if (response.ok) {
				toggleNotification({
					type: "success",
					message: `Test ${status} successfully`,
				});
				fetchABTests();
			}
		} catch (error) {
			console.error("Error updating test status:", error);
			toggleNotification({
				type: "warning",
				message: "Failed to update test status",
			});
		}
	};

	const getStatusColor = (status: string) => {
		switch (status) {
			case "running":
				return "success";
			case "paused":
				return "warning";
			case "completed":
				return "secondary";
			default:
				return "neutral";
		}
	};

	const getStatusIcon = (status: string) => {
		switch (status) {
			case "running":
				return <Play />;
			case "paused":
				return <Pause />;
			case "completed":
				return <Stop />;
			default:
				return <Settings />;
		}
	};

	if (loading) {
		return (
			<Box padding={8}>
				<Flex justifyContent="center">
					<Loader>Loading A/B tests...</Loader>
				</Flex>
			</Box>
		);
	}

	return (
		<Box>
			{/* Header */}
			<Flex justifyContent="space-between" alignItems="center" marginBottom={6}>
				<Typography variant="alpha">A/B Test Manager</Typography>
				<Button startIcon={<Plus />} onClick={() => setShowCreateModal(true)}>
					Create New Test
				</Button>
			</Flex>

			{/* Summary Metrics */}
			<Grid gap={4} marginBottom={6}>
				<GridItem col={3}>
					<MetricCard>
						<Typography variant="omega" textColor="neutral600">
							Active Tests
						</Typography>
						<Typography variant="alpha" marginTop={1}>
							{tests.filter((t) => t.status === "running").length}
						</Typography>
					</MetricCard>
				</GridItem>
				<GridItem col={3}>
					<MetricCard>
						<Typography variant="omega" textColor="neutral600">
							Total Tests
						</Typography>
						<Typography variant="alpha" marginTop={1}>
							{tests.length}
						</Typography>
					</MetricCard>
				</GridItem>
				<GridItem col={3}>
					<MetricCard>
						<Typography variant="omega" textColor="neutral600">
							Avg. Conversion Rate
						</Typography>
						<Typography variant="alpha" marginTop={1}>
							{tests.length > 0
								? (
										tests.reduce(
											(acc, t) => acc + (t.metrics?.conversionRate || 0),
											0,
										) / tests.length
									).toFixed(2)
								: "0.00"}
							%
						</Typography>
					</MetricCard>
				</GridItem>
				<GridItem col={3}>
					<MetricCard>
						<Typography variant="omega" textColor="neutral600">
							Total Revenue
						</Typography>
						<Typography variant="alpha" marginTop={1}>
							$
							{tests
								.reduce((acc, t) => acc + (t.metrics?.revenue || 0), 0)
								.toLocaleString()}
						</Typography>
					</MetricCard>
				</GridItem>
			</Grid>

			{/* Tests List */}
			<Grid gap={4}>
				{tests.map((test) => (
					<GridItem col={6} key={test.id}>
						<TestCard
							onClick={() => onTestSelect?.(test)}
							style={{
								border:
									selectedTest?.id === test.id
										? "2px solid #4945ff"
										: undefined,
							}}
						>
							<CardHeader>
								<Flex justifyContent="space-between" alignItems="center">
									<Typography variant="delta">{test.name}</Typography>
									<StatusBadge backgroundColor={getStatusColor(test.status)}>
										{test.status}
									</StatusBadge>
								</Flex>
							</CardHeader>
							<CardBody>
								<Stack spacing={3}>
									<Typography variant="omega" textColor="neutral600">
										{test.description}
									</Typography>

									<Flex gap={4}>
										<Box>
											<Typography variant="pi" textColor="neutral600">
												Variations
											</Typography>
											<Typography variant="epsilon">
												{test.variations?.length || 0}
											</Typography>
										</Box>
										<Box>
											<Typography variant="pi" textColor="neutral600">
												Conversion Rate
											</Typography>
											<Typography variant="epsilon">
												{test.metrics?.conversionRate?.toFixed(2) || "0.00"}%
											</Typography>
										</Box>
										<Box>
											<Typography variant="pi" textColor="neutral600">
												Confidence
											</Typography>
											<Typography variant="epsilon">
												{test.confidenceLevel?.toFixed(1) || "0.0"}%
											</Typography>
										</Box>
									</Flex>

									<Flex justifyContent="space-between" alignItems="center">
										<Flex gap={2}>
											<Button
												size="S"
												variant="secondary"
												startIcon={getStatusIcon(test.status)}
												onClick={(e) => {
													e.stopPropagation();
													const newStatus =
														test.status === "running" ? "paused" : "running";
													updateTestStatus(test.id, newStatus);
												}}
											>
												{test.status === "running" ? "Pause" : "Start"}
											</Button>
											<Button
												size="S"
												variant="tertiary"
												startIcon={<BarChart />}
												onClick={(e) => {
													e.stopPropagation();
													onTestSelect?.(test);
												}}
											>
												View Results
											</Button>
										</Flex>
										<Typography variant="pi" textColor="neutral500">
											{test.startDate &&
												new Date(test.startDate).toLocaleDateString()}
										</Typography>
									</Flex>
								</Stack>
							</CardBody>
						</TestCard>
					</GridItem>
				))}
			</Grid>

			{/* Create Test Modal */}
			{showCreateModal && (
				<ModalLayout
					onClose={() => setShowCreateModal(false)}
					labelledBy="create-test-modal"
				>
					<ModalHeader>
						<Typography
							fontWeight="bold"
							textColor="neutral800"
							as="h2"
							id="create-test-modal"
						>
							Create New A/B Test
						</Typography>
					</ModalHeader>
					<ModalBody>
						<Stack spacing={4}>
							<TextInput
								label="Test Name"
								name="name"
								value={newTest.name}
								onChange={(e) =>
									setNewTest({ ...newTest, name: e.target.value })
								}
								required
							/>
							<Textarea
								label="Description"
								name="description"
								value={newTest.description}
								onChange={(e) =>
									setNewTest({ ...newTest, description: e.target.value })
								}
							/>
							<NumberInput
								label="Traffic Allocation (%)"
								name="trafficAllocation"
								value={newTest.trafficAllocation}
								onValueChange={(value) =>
									setNewTest({ ...newTest, trafficAllocation: value })
								}
								min={1}
								max={100}
							/>
							<Select
								label="Target Metric"
								value={newTest.targetMetric}
								onChange={(value) =>
									setNewTest({ ...newTest, targetMetric: value })
								}
							>
								<Option value="conversion_rate">Conversion Rate</Option>
								<Option value="revenue">Revenue</Option>
								<Option value="retention">Retention</Option>
								<Option value="engagement">Engagement</Option>
							</Select>
							<Grid gap={4}>
								<GridItem col={6}>
									<TextInput
										label="Start Date"
										name="startDate"
										type="datetime-local"
										value={newTest.startDate}
										onChange={(e) =>
											setNewTest({ ...newTest, startDate: e.target.value })
										}
									/>
								</GridItem>
								<GridItem col={6}>
									<TextInput
										label="End Date"
										name="endDate"
										type="datetime-local"
										value={newTest.endDate}
										onChange={(e) =>
											setNewTest({ ...newTest, endDate: e.target.value })
										}
									/>
								</GridItem>
							</Grid>
						</Stack>
					</ModalBody>
					<ModalFooter
						startActions={
							<Button
								variant="tertiary"
								onClick={() => setShowCreateModal(false)}
							>
								Cancel
							</Button>
						}
						endActions={
							<Button onClick={createTest} disabled={!newTest.name}>
								Create Test
							</Button>
						}
					/>
				</ModalLayout>
			)}
		</Box>
	);
};

export default ABTestManager;
