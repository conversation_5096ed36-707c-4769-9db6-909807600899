/**
 * Health Status Card Component
 * Displays real-time server status indicator for Chrome MCP monitoring
 * Part of Story 2.1: Chrome MCP Server Health Monitoring Dashboard
 */

import {
  Badge,
  Box,
  Card,
  CardBody,
  CardHeader,
  Flex,
  Stack,
  Typography,
} from "@strapi/design-system";
import {
  CheckCircle as HealthyIcon,
  Warning as WarningIcon,
  Cross as ErrorIcon,
  Monitor as ServerIcon,
} from "@strapi/icons";
import type React from "react";
import styled from "styled-components";

// Styled Components
const StatusIndicator = styled(Box)<{ status: string }>`
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: ${({ status, theme }) => {
    switch (status) {
      case 'healthy':
        return theme.colors.success600;
      case 'degraded':
        return theme.colors.warning600;
      case 'unhealthy':
        return theme.colors.danger600;
      default:
        return theme.colors.neutral400;
    }
  }};
  animation: ${({ status }) => status === 'healthy' ? 'pulse 2s infinite' : 'none'};
  
  @keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
  }
`;

const StatusCard = styled(Card)`
  height: 100%;
  transition: all 0.2s ease;
  border-left: 4px solid ${({ theme, status }: { theme: any; status: string }) => {
    switch (status) {
      case 'healthy':
        return theme.colors.success600;
      case 'degraded':
        return theme.colors.warning600;
      case 'unhealthy':
        return theme.colors.danger600;
      default:
        return theme.colors.neutral400;
    }
  }};
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
`;

// Types
interface HealthStatusCardProps {
  status: 'healthy' | 'degraded' | 'unhealthy';
  uptime: number;
  lastUpdated: Date;
  responseTime?: number;
  errorCount?: number;
  onRefresh?: () => void;
}

const HealthStatusCard: React.FC<HealthStatusCardProps> = ({
  status,
  uptime,
  lastUpdated,
  responseTime,
  errorCount = 0,
  onRefresh
}) => {
  // Format uptime
  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (days > 0) {
      return `${days}d ${hours}h ${minutes}m`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'success';
      case 'degraded':
        return 'warning';
      case 'unhealthy':
        return 'danger';
      default:
        return 'neutral';
    }
  };

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <HealthyIcon />;
      case 'degraded':
        return <WarningIcon />;
      case 'unhealthy':
        return <ErrorIcon />;
      default:
        return <ServerIcon />;
    }
  };

  // Get status message
  const getStatusMessage = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'All systems operational';
      case 'degraded':
        return 'Performance degraded';
      case 'unhealthy':
        return 'System issues detected';
      default:
        return 'Status unknown';
    }
  };

  return (
    <StatusCard status={status}>
      <CardHeader>
        <Flex alignItems="center" gap={2}>
          {getStatusIcon(status)}
          <Typography variant="delta">Chrome MCP Server</Typography>
          <StatusIndicator status={status} />
        </Flex>
      </CardHeader>
      <CardBody>
        <Stack spacing={3}>
          {/* Status Badge */}
          <Flex alignItems="center" gap={2}>
            <Badge variant={getStatusColor(status)} size="L">
              {status.toUpperCase()}
            </Badge>
          </Flex>

          {/* Status Message */}
          <Typography variant="omega" textColor="neutral600">
            {getStatusMessage(status)}
          </Typography>

          {/* Uptime Information */}
          <Box>
            <Typography variant="pi" fontWeight="semiBold" textColor="neutral800">
              Uptime
            </Typography>
            <Typography variant="omega" textColor="neutral600">
              {formatUptime(uptime)}
            </Typography>
          </Box>

          {/* Response Time (if provided) */}
          {responseTime && (
            <Box>
              <Typography variant="pi" fontWeight="semiBold" textColor="neutral800">
                Response Time
              </Typography>
              <Typography variant="omega" textColor="neutral600">
                {responseTime}ms
              </Typography>
            </Box>
          )}

          {/* Error Count */}
          <Box>
            <Typography variant="pi" fontWeight="semiBold" textColor="neutral800">
              Error Count
            </Typography>
            <Typography 
              variant="omega" 
              textColor={errorCount > 0 ? 'danger600' : 'success600'}
            >
              {errorCount}
            </Typography>
          </Box>

          {/* Last Updated */}
          <Box>
            <Typography variant="pi" fontWeight="semiBold" textColor="neutral800">
              Last Updated
            </Typography>
            <Typography variant="omega" textColor="neutral600">
              {lastUpdated.toLocaleTimeString()}
            </Typography>
          </Box>

          {/* Health Score Indicator */}
          <Box>
            <Typography variant="pi" fontWeight="semiBold" textColor="neutral800">
              Health Score
            </Typography>
            <Flex alignItems="center" gap={2}>
              <Typography 
                variant="omega" 
                textColor={status === 'healthy' ? 'success600' : status === 'degraded' ? 'warning600' : 'danger600'}
              >
                {status === 'healthy' ? '100%' : status === 'degraded' ? '75%' : '25%'}
              </Typography>
              <StatusIndicator status={status} />
            </Flex>
          </Box>
        </Stack>
      </CardBody>
    </StatusCard>
  );
};

export default HealthStatusCard;
