/**
 * Analytics Dashboard - Comprehensive analytics dashboard with conversion rates and revenue metrics
 */

import {
	Alert,
	Box,
	Button,
	Card,
	CardBody,
	CardHeader,
	DatePicker,
	Flex,
	Grid,
	GridItem,
	Loader,
	Option,
	Select,
	Stack,
	Typography,
} from "@strapi/design-system";
import { useNotification } from "@strapi/helper-plugin";
import { Eye, Refresh, TrendDown, TrendUp } from "@strapi/icons";
import type React from "react";
import { useEffect, useState } from "react";
import { useIntl } from "react-intl";
import styled from "styled-components";

const DashboardContainer = styled(Box)`
  padding: 24px;
  background: ${({ theme }) => theme.colors.neutral0};
  min-height: 100vh;
`;

const MetricCard = styled(Card)`
  height: 100%;
  transition: all 0.2s ease;
  cursor: pointer;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
`;

const TrendIndicator = styled(Flex)<{ trend: "up" | "down" | "stable" }>`
  align-items: center;
  gap: 4px;
  color: ${({ theme, trend }) =>
		trend === "up"
			? theme.colors.success600
			: trend === "down"
				? theme.colors.danger600
				: theme.colors.neutral600};
`;

const ChartContainer = styled(Box)`
  height: 300px;
  width: 100%;
  position: relative;
`;

const RealtimeIndicator = styled(Box)<{ isLive: boolean }>`
  display: flex;
  align-items: center;
  gap: 8px;
  
  &::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: ${({ theme, isLive }) =>
			isLive ? theme.colors.success500 : theme.colors.neutral400};
    animation: ${({ isLive }) => (isLive ? "pulse 2s infinite" : "none")};
  }
  
  @keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
  }
`;

interface AnalyticsDashboardProps {
	paywallId?: string;
}

interface DashboardData {
	overview: {
		totalImpressions: number;
		totalConversions: number;
		overallConversionRate: number;
		totalRevenue: number;
		averageRevenuePerUser: number;
		activePaywalls: number;
		topPerformingPaywall: {
			id: string;
			name: string;
			conversionRate: number;
		};
	};
	trends: {
		impressionsTrend: { date: string; value: number }[];
		conversionsTrend: { date: string; value: number }[];
		revenueTrend: { date: string; value: number }[];
		conversionRateTrend: { date: string; value: number }[];
	};
	paywallPerformance: {
		paywallId: string;
		name: string;
		impressions: number;
		conversions: number;
		conversionRate: number;
		revenue: number;
		averageRevenuePerUser: number;
		trend: "up" | "down" | "stable";
		changePercentage: number;
	}[];
	regionalPerformance: {
		region: string;
		impressions: number;
		conversions: number;
		conversionRate: number;
		revenue: number;
		topPaywall: string;
	}[];
	userEngagement: {
		averageSessionDuration: number;
		averagePageViews: number;
		averageClickThroughRate: number;
		bounceRate: number;
		topInteractionElements: {
			element: string;
			interactions: number;
			conversionImpact: number;
		}[];
	};
	realtime: {
		activeUsers: number;
		currentImpressions: number;
		currentConversions: number;
		realtimeConversionRate: number;
	};
}

const AnalyticsDashboard: React.FC<AnalyticsDashboardProps> = ({
	paywallId,
}) => {
	const { formatMessage } = useIntl();
	const toggleNotification = useNotification();

	const [dashboardData, setDashboardData] = useState<DashboardData | null>(
		null,
	);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [dateRange, setDateRange] = useState({
		start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
		end: new Date(),
	});
	const [selectedRegions, setSelectedRegions] = useState<string[]>([]);
	const [selectedPaywalls, setSelectedPaywalls] = useState<string[]>(
		paywallId ? [paywallId] : [],
	);
	const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(
		null,
	);
	const [isRealtime, setIsRealtime] = useState(false);

	useEffect(() => {
		loadDashboardData();
	}, [loadDashboardData]);

	useEffect(() => {
		if (isRealtime) {
			const interval = setInterval(() => {
				loadRealtimeData();
			}, 30000); // Update every 30 seconds
			setRefreshInterval(interval);
		} else {
			if (refreshInterval) {
				clearInterval(refreshInterval);
				setRefreshInterval(null);
			}
		}

		return () => {
			if (refreshInterval) {
				clearInterval(refreshInterval);
			}
		};
	}, [isRealtime, loadRealtimeData, refreshInterval]);

	const loadDashboardData = async () => {
		try {
			setLoading(true);
			setError(null);

			const filter = {
				dateRange,
				paywallIds: selectedPaywalls.length > 0 ? selectedPaywalls : undefined,
				regions: selectedRegions.length > 0 ? selectedRegions : undefined,
			};

			const response = await fetch("/api/analytics/dashboard", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify(filter),
			});

			if (!response.ok) {
				throw new Error("Failed to load dashboard data");
			}

			const data = await response.json();

			// Load realtime data as well
			const realtimeResponse = await fetch("/api/analytics/realtime", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({ paywallIds: selectedPaywalls }),
			});

			const realtimeData = realtimeResponse.ok
				? await realtimeResponse.json()
				: {
						activeUsers: 0,
						currentImpressions: 0,
						currentConversions: 0,
						realtimeConversionRate: 0,
					};

			setDashboardData({
				...data,
				realtime: realtimeData,
			});
		} catch (err) {
			setError(err instanceof Error ? err.message : "An error occurred");
			toggleNotification({
				type: "warning",
				message: "Failed to load analytics data",
			});
		} finally {
			setLoading(false);
		}
	};

	const loadRealtimeData = async () => {
		try {
			const response = await fetch("/api/analytics/realtime", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({ paywallIds: selectedPaywalls }),
			});

			if (response.ok) {
				const realtimeData = await response.json();
				setDashboardData((prev) =>
					prev
						? {
								...prev,
								realtime: realtimeData,
							}
						: null,
				);
			}
		} catch (err) {
			console.error("Failed to load realtime data:", err);
		}
	};

	const handleExport = async (format: "csv" | "json" | "pdf") => {
		try {
			const response = await fetch("/api/analytics/export", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					format,
					dateRange,
					includeCharts: format === "pdf",
					metrics: ["impressions", "conversions", "revenue"],
					groupBy: "paywall",
				}),
			});

			if (!response.ok) {
				throw new Error("Export failed");
			}

			const blob = await response.blob();
			const url = window.URL.createObjectURL(blob);
			const a = document.createElement("a");
			a.href = url;
			a.download = `analytics-${format}-${new Date().toISOString().split("T")[0]}.${format}`;
			document.body.appendChild(a);
			a.click();
			window.URL.revokeObjectURL(url);
			document.body.removeChild(a);

			toggleNotification({
				type: "success",
				message: `Analytics exported as ${format.toUpperCase()}`,
			});
		} catch (_err) {
			toggleNotification({
				type: "warning",
				message: "Export failed",
			});
		}
	};

	const formatNumber = (num: number): string => {
		if (num >= 1000000) {
			return `${(num / 1000000).toFixed(1)}M`;
		} else if (num >= 1000) {
			return `${(num / 1000).toFixed(1)}K`;
		}
		return num.toString();
	};

	const formatCurrency = (amount: number): string => {
		return new Intl.NumberFormat("en-US", {
			style: "currency",
			currency: "USD",
		}).format(amount);
	};

	const formatPercentage = (rate: number): string => {
		return `${(rate * 100).toFixed(2)}%`;
	};

	if (loading) {
		return (
			<DashboardContainer>
				<Flex justifyContent="center" alignItems="center" height="400px">
					<Loader />
					<Typography variant="omega" marginLeft={2}>
						Loading analytics data...
					</Typography>
				</Flex>
			</DashboardContainer>
		);
	}

	if (error || !dashboardData) {
		return (
			<DashboardContainer>
				<Alert variant="danger" title="Error">
					{error || "Failed to load analytics data"}
				</Alert>
			</DashboardContainer>
		);
	}

	return (
		<DashboardContainer>
			{/* Header */}
			<Flex justifyContent="space-between" alignItems="center" marginBottom={6}>
				<Box>
					<Typography variant="alpha">Analytics Dashboard</Typography>
					<Typography variant="omega" textColor="neutral600">
						Monitor paywall performance and user engagement
					</Typography>
				</Box>

				<Flex gap={2}>
					<RealtimeIndicator isLive={isRealtime}>
						<Typography variant="pi">
							{isRealtime ? "Live" : "Static"}
						</Typography>
					</RealtimeIndicator>

					<Button
						variant={isRealtime ? "success" : "secondary"}
						onClick={() => setIsRealtime(!isRealtime)}
						startIcon={<Eye />}
					>
						{isRealtime ? "Live Mode" : "Enable Live"}
					</Button>

					<Button
						variant="secondary"
						onClick={loadDashboardData}
						startIcon={<Refresh />}
					>
						Refresh
					</Button>

					<Select
						placeholder="Export"
						onClear={() => {}}
						value=""
						onChange={(value: string) => handleExport(value as any)}
					>
						<Option value="csv">Export CSV</Option>
						<Option value="json">Export JSON</Option>
						<Option value="pdf">Export PDF</Option>
					</Select>
				</Flex>
			</Flex>

			{/* Filters */}
			<Card marginBottom={6}>
				<CardBody>
					<Grid gap={4}>
						<GridItem col={3}>
							<DatePicker
								label="Start Date"
								selectedDate={dateRange.start}
								onChange={(date: Date) =>
									setDateRange((prev) => ({ ...prev, start: date }))
								}
							/>
						</GridItem>
						<GridItem col={3}>
							<DatePicker
								label="End Date"
								selectedDate={dateRange.end}
								onChange={(date: Date) =>
									setDateRange((prev) => ({ ...prev, end: date }))
								}
							/>
						</GridItem>
						<GridItem col={3}>
							<Select
								label="Regions"
								placeholder="All regions"
								multi
								value={selectedRegions}
								onChange={setSelectedRegions}
							>
								<Option value="US">United States</Option>
								<Option value="EU">Europe</Option>
								<Option value="JP">Japan</Option>
								<Option value="CA">Canada</Option>
							</Select>
						</GridItem>
						<GridItem col={3}>
							<Select
								label="Paywalls"
								placeholder="All paywalls"
								multi
								value={selectedPaywalls}
								onChange={setSelectedPaywalls}
							>
								{dashboardData.paywallPerformance.map((paywall) => (
									<Option key={paywall.paywallId} value={paywall.paywallId}>
										{paywall.name}
									</Option>
								))}
							</Select>
						</GridItem>
					</Grid>
				</CardBody>
			</Card>

			{/* Overview Metrics */}
			<Grid gap={4} marginBottom={6}>
				<GridItem col={2}>
					<MetricCard>
						<CardBody>
							<Stack spacing={2}>
								<Typography variant="sigma" textColor="neutral600">
									Total Impressions
								</Typography>
								<Typography variant="alpha">
									{formatNumber(dashboardData.overview.totalImpressions)}
								</Typography>
								{isRealtime && (
									<Typography variant="pi" textColor="success600">
										+{dashboardData.realtime.currentImpressions} live
									</Typography>
								)}
							</Stack>
						</CardBody>
					</MetricCard>
				</GridItem>

				<GridItem col={2}>
					<MetricCard>
						<CardBody>
							<Stack spacing={2}>
								<Typography variant="sigma" textColor="neutral600">
									Total Conversions
								</Typography>
								<Typography variant="alpha">
									{formatNumber(dashboardData.overview.totalConversions)}
								</Typography>
								{isRealtime && (
									<Typography variant="pi" textColor="success600">
										+{dashboardData.realtime.currentConversions} live
									</Typography>
								)}
							</Stack>
						</CardBody>
					</MetricCard>
				</GridItem>

				<GridItem col={2}>
					<MetricCard>
						<CardBody>
							<Stack spacing={2}>
								<Typography variant="sigma" textColor="neutral600">
									Conversion Rate
								</Typography>
								<Typography variant="alpha">
									{formatPercentage(
										dashboardData.overview.overallConversionRate,
									)}
								</Typography>
								{isRealtime && (
									<Typography variant="pi" textColor="neutral600">
										{formatPercentage(
											dashboardData.realtime.realtimeConversionRate,
										)}{" "}
										live
									</Typography>
								)}
							</Stack>
						</CardBody>
					</MetricCard>
				</GridItem>

				<GridItem col={2}>
					<MetricCard>
						<CardBody>
							<Stack spacing={2}>
								<Typography variant="sigma" textColor="neutral600">
									Total Revenue
								</Typography>
								<Typography variant="alpha">
									{formatCurrency(dashboardData.overview.totalRevenue)}
								</Typography>
							</Stack>
						</CardBody>
					</MetricCard>
				</GridItem>

				<GridItem col={2}>
					<MetricCard>
						<CardBody>
							<Stack spacing={2}>
								<Typography variant="sigma" textColor="neutral600">
									Avg Revenue/User
								</Typography>
								<Typography variant="alpha">
									{formatCurrency(dashboardData.overview.averageRevenuePerUser)}
								</Typography>
							</Stack>
						</CardBody>
					</MetricCard>
				</GridItem>

				<GridItem col={2}>
					<MetricCard>
						<CardBody>
							<Stack spacing={2}>
								<Typography variant="sigma" textColor="neutral600">
									Active Paywalls
								</Typography>
								<Typography variant="alpha">
									{dashboardData.overview.activePaywalls}
								</Typography>
								{isRealtime && (
									<Typography variant="pi" textColor="neutral600">
										{dashboardData.realtime.activeUsers} active users
									</Typography>
								)}
							</Stack>
						</CardBody>
					</MetricCard>
				</GridItem>
			</Grid>

			{/* Charts Section */}
			<Grid gap={4} marginBottom={6}>
				<GridItem col={6}>
					<Card>
						<CardHeader>
							<Typography variant="delta">Conversion Trends</Typography>
						</CardHeader>
						<CardBody>
							<ChartContainer>
								{/* Chart implementation would go here */}
								<Typography variant="omega" textColor="neutral600">
									Chart showing conversion rate trends over time
								</Typography>
							</ChartContainer>
						</CardBody>
					</Card>
				</GridItem>

				<GridItem col={6}>
					<Card>
						<CardHeader>
							<Typography variant="delta">Revenue Trends</Typography>
						</CardHeader>
						<CardBody>
							<ChartContainer>
								{/* Chart implementation would go here */}
								<Typography variant="omega" textColor="neutral600">
									Chart showing revenue trends over time
								</Typography>
							</ChartContainer>
						</CardBody>
					</Card>
				</GridItem>
			</Grid>

			{/* Paywall Performance Table */}
			<Card marginBottom={6}>
				<CardHeader>
					<Typography variant="delta">Paywall Performance</Typography>
				</CardHeader>
				<CardBody>
					<Box overflowX="auto">
						<table style={{ width: "100%", borderCollapse: "collapse" }}>
							<thead>
								<tr style={{ borderBottom: "1px solid #eaeaea" }}>
									<th style={{ padding: "12px", textAlign: "left" }}>
										Paywall
									</th>
									<th style={{ padding: "12px", textAlign: "right" }}>
										Impressions
									</th>
									<th style={{ padding: "12px", textAlign: "right" }}>
										Conversions
									</th>
									<th style={{ padding: "12px", textAlign: "right" }}>
										Conv. Rate
									</th>
									<th style={{ padding: "12px", textAlign: "right" }}>
										Revenue
									</th>
									<th style={{ padding: "12px", textAlign: "right" }}>ARPU</th>
									<th style={{ padding: "12px", textAlign: "center" }}>
										Trend
									</th>
								</tr>
							</thead>
							<tbody>
								{dashboardData.paywallPerformance.map((paywall) => (
									<tr
										key={paywall.paywallId}
										style={{ borderBottom: "1px solid #f6f6f6" }}
									>
										<td style={{ padding: "12px" }}>
											<Typography variant="omega" fontWeight="semiBold">
												{paywall.name}
											</Typography>
										</td>
										<td style={{ padding: "12px", textAlign: "right" }}>
											{formatNumber(paywall.impressions)}
										</td>
										<td style={{ padding: "12px", textAlign: "right" }}>
											{formatNumber(paywall.conversions)}
										</td>
										<td style={{ padding: "12px", textAlign: "right" }}>
											{formatPercentage(paywall.conversionRate)}
										</td>
										<td style={{ padding: "12px", textAlign: "right" }}>
											{formatCurrency(paywall.revenue)}
										</td>
										<td style={{ padding: "12px", textAlign: "right" }}>
											{formatCurrency(paywall.averageRevenuePerUser)}
										</td>
										<td style={{ padding: "12px", textAlign: "center" }}>
											<TrendIndicator trend={paywall.trend}>
												{paywall.trend === "up" && <TrendUp />}
												{paywall.trend === "down" && <TrendDown />}
												{paywall.trend === "stable" && (
													<Typography>—</Typography>
												)}
												<Typography variant="pi">
													{paywall.changePercentage !== 0 &&
														`${paywall.changePercentage > 0 ? "+" : ""}${paywall.changePercentage.toFixed(1)}%`}
												</Typography>
											</TrendIndicator>
										</td>
									</tr>
								))}
							</tbody>
						</table>
					</Box>
				</CardBody>
			</Card>

			{/* Regional Performance */}
			<Grid gap={4}>
				<GridItem col={6}>
					<Card>
						<CardHeader>
							<Typography variant="delta">Regional Performance</Typography>
						</CardHeader>
						<CardBody>
							<Stack spacing={3}>
								{dashboardData.regionalPerformance.map((region) => (
									<Flex
										key={region.region}
										justifyContent="space-between"
										alignItems="center"
									>
										<Box>
											<Typography variant="omega" fontWeight="semiBold">
												{region.region}
											</Typography>
											<Typography variant="pi" textColor="neutral600">
												Top: {region.topPaywall}
											</Typography>
										</Box>
										<Box textAlign="right">
											<Typography variant="omega">
												{formatPercentage(region.conversionRate)}
											</Typography>
											<Typography variant="pi" textColor="neutral600">
												{formatCurrency(region.revenue)}
											</Typography>
										</Box>
									</Flex>
								))}
							</Stack>
						</CardBody>
					</Card>
				</GridItem>

				<GridItem col={6}>
					<Card>
						<CardHeader>
							<Typography variant="delta">User Engagement</Typography>
						</CardHeader>
						<CardBody>
							<Stack spacing={3}>
								<Flex justifyContent="space-between">
									<Typography variant="omega">Avg Session Duration</Typography>
									<Typography variant="omega">
										{Math.round(
											dashboardData.userEngagement.averageSessionDuration / 60,
										)}
										m
									</Typography>
								</Flex>
								<Flex justifyContent="space-between">
									<Typography variant="omega">Avg Page Views</Typography>
									<Typography variant="omega">
										{dashboardData.userEngagement.averagePageViews.toFixed(1)}
									</Typography>
								</Flex>
								<Flex justifyContent="space-between">
									<Typography variant="omega">Click-through Rate</Typography>
									<Typography variant="omega">
										{formatPercentage(
											dashboardData.userEngagement.averageClickThroughRate,
										)}
									</Typography>
								</Flex>
								<Flex justifyContent="space-between">
									<Typography variant="omega">Bounce Rate</Typography>
									<Typography variant="omega">
										{formatPercentage(dashboardData.userEngagement.bounceRate)}
									</Typography>
								</Flex>
							</Stack>
						</CardBody>
					</Card>
				</GridItem>
			</Grid>
		</DashboardContainer>
	);
};

export default AnalyticsDashboard;
