/**
 * Paywall preview component for real-time visualization
 */

import { Box, Button, Flex, Loader, Typography } from "@strapi/design-system";
import { useCMEditViewDataManager } from "@strapi/helper-plugin";
import { Eye } from "@strapi/icons";
import { useEffect, useState } from "react";
import { useIntl } from "react-intl";
import styled from "styled-components";

// Styled components for the preview
const PreviewContainer = styled(Box)`
  border: 1px solid ${({ theme }) => theme.colors.neutral200};
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 24px;
`;

const PhoneFrame = styled(Box)`
  width: 375px;
  height: 667px;
  margin: 0 auto;
  border: 10px solid #111;
  border-radius: 36px;
  overflow: hidden;
  position: relative;
  background-color: ${({ bgColor }) => bgColor || "#fff"};
`;

const StatusBar = styled(Box)`
  height: 24px;
  background-color: #000;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
`;

const PaywallHeader = styled(Box)`
  padding: 16px;
  text-align: center;
`;

const PaywallTitle = styled(Typography)`
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 8px;
  color: ${({ textColor }) => textColor || "#000"};
`;

const PaywallSubtitle = styled(Typography)`
  font-size: 16px;
  margin-bottom: 16px;
  color: ${({ textColor }) => textColor || "#000"};
`;

const FeatureList = styled(Box)`
  padding: 0 16px;
`;

const Feature = styled(Flex)`
  margin-bottom: 12px;
  align-items: center;
`;

const FeatureIcon = styled(Box)`
  width: 24px;
  height: 24px;
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${({ primaryColor }) => primaryColor || "#000"};
`;

const FeatureText = styled(Typography)`
  font-size: 14px;
  color: ${({ textColor }) => textColor || "#000"};
`;

const ProductList = styled(Box)`
  padding: 16px;
  margin-top: 24px;
`;

const Product = styled(Box)`
  border: 2px solid ${({ isHighlighted, primaryColor }) =>
		isHighlighted ? primaryColor || "#000" : "transparent"};
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  background-color: ${({ theme }) => theme.colors.neutral100};
  position: relative;
`;

const ProductBadge = styled(Box)`
  position: absolute;
  top: -10px;
  right: 10px;
  background-color: ${({ badgeColor }) => badgeColor || "#ff6b35"};
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
`;

const CTAButton = styled(Button)`
  width: 100%;
  margin-top: 24px;
  background-color: ${({ primaryColor }) => primaryColor || "#000"};
  &:hover {
    background-color: ${({ primaryColor }) => primaryColor || "#000"};
    opacity: 0.9;
  }
`;

const TestimonialSection = styled(Box)`
  padding: 16px;
  margin-top: 16px;
`;

const Testimonial = styled(Box)`
  padding: 16px;
  background-color: ${({ theme }) => theme.colors.neutral100};
  border-radius: 8px;
  margin-bottom: 12px;
`;

const TestimonialAuthor = styled(Flex)`
  align-items: center;
  margin-top: 8px;
`;

const AuthorAvatar = styled(Box)`
  width: 32px;
  height: 32px;
  border-radius: 16px;
  overflow: hidden;
  margin-right: 8px;
  background-color: ${({ theme }) => theme.colors.neutral200};
`;

const PaywallPreview = ({ name, attribute, value, onChange }) => {
	const { formatMessage } = useIntl();
	const { modifiedData, layout } = useCMEditViewDataManager();
	const [loading, setLoading] = useState(false);
	const [previewData, setPreviewData] = useState(null);

	// Generate preview data from current form values
	useEffect(() => {
		if (modifiedData) {
			setPreviewData({
				title: modifiedData.title || "Paywall Title",
				subtitle: modifiedData.subtitle || "Paywall Subtitle",
				description: modifiedData.description_text || "",
				cta_text: modifiedData.cta_text || "Subscribe Now",
				cta_secondary_text: modifiedData.cta_secondary_text || "",
				theme: modifiedData.theme || {
					primary_color: "#007AFF",
					background_color: "#FFFFFF",
					text_color: "#000000",
					button_style: "rounded",
					show_features: true,
					show_testimonials: false,
				},
				features: modifiedData.features || [],
				testimonials: modifiedData.testimonials || [],
				product_labels: modifiedData.product_labels || [],
			});
		}
	}, [modifiedData]);

	// Handle refresh preview
	const handleRefreshPreview = async () => {
		setLoading(true);

		// Simulate API call to get latest data
		setTimeout(() => {
			setLoading(false);
		}, 500);
	};

	if (!previewData) {
		return (
			<Box padding={4}>
				<Loader>Loading preview...</Loader>
			</Box>
		);
	}

	const {
		title,
		subtitle,
		description,
		cta_text,
		theme,
		features,
		testimonials,
		product_labels,
	} = previewData;

	// Sample products for preview
	const sampleProducts = [
		{ id: "monthly", name: "Monthly", price: "$9.99/month" },
		{ id: "yearly", name: "Yearly", price: "$99.99/year", savings: "17%" },
		{ id: "lifetime", name: "Lifetime", price: "$299.99" },
	];

	return (
		<PreviewContainer>
			<Box padding={4}>
				<Flex
					justifyContent="space-between"
					alignItems="center"
					marginBottom={4}
				>
					<Typography variant="beta">
						{formatMessage({
							id: "app.components.PaywallPreview.title",
							defaultMessage: "Paywall Preview",
						})}
					</Typography>
					<Button
						startIcon={<Eye />}
						onClick={handleRefreshPreview}
						loading={loading}
					>
						Refresh Preview
					</Button>
				</Flex>

				<Typography variant="epsilon" textColor="neutral600" marginBottom={4}>
					{formatMessage({
						id: "app.components.PaywallPreview.description",
						defaultMessage:
							"Preview how your paywall will appear on mobile devices",
					})}
				</Typography>

				<PhoneFrame bgColor={theme?.background_color}>
					<StatusBar>
						<Typography textColor="white" variant="pi">
							9:41
						</Typography>
						<Typography textColor="white" variant="pi">
							100%
						</Typography>
					</StatusBar>

					<PaywallHeader>
						<PaywallTitle textColor={theme?.text_color}>{title}</PaywallTitle>
						<PaywallSubtitle textColor={theme?.text_color}>
							{subtitle}
						</PaywallSubtitle>
					</PaywallHeader>

					{theme?.show_features && features && features.length > 0 && (
						<FeatureList>
							{features
								.sort((a, b) => a.order - b.order)
								.map((feature, index) => (
									<Feature key={index}>
										<FeatureIcon primaryColor={theme?.primary_color}>
											{feature.icon}
										</FeatureIcon>
										<FeatureText textColor={theme?.text_color}>
											{feature.title}
										</FeatureText>
									</Feature>
								))}
						</FeatureList>
					)}

					<ProductList>
						{sampleProducts.map((product) => {
							const productLabel = product_labels?.find(
								(label) => label.product_id === product.id,
							);

							return (
								<Product
									key={product.id}
									isHighlighted={productLabel?.highlight}
									primaryColor={theme?.primary_color}
								>
									{productLabel?.badge_text && (
										<ProductBadge
											badgeColor={
												productLabel.badge_color || theme?.primary_color
											}
										>
											{productLabel.badge_text}
										</ProductBadge>
									)}
									<Typography fontWeight="bold">{product.name}</Typography>
									<Typography>{product.price}</Typography>
									{product.savings && (
										<Typography variant="pi" textColor="success600">
											Save {product.savings}
										</Typography>
									)}
								</Product>
							);
						})}
					</ProductList>

					<Box padding={4}>
						<CTAButton primaryColor={theme?.primary_color}>
							{cta_text}
						</CTAButton>
					</Box>

					{theme?.show_testimonials &&
						testimonials &&
						testimonials.length > 0 && (
							<TestimonialSection>
								{testimonials
									.sort((a, b) => a.order - b.order)
									.map((testimonial, index) => (
										<Testimonial key={index}>
											<Typography>{testimonial.content}</Typography>
											<TestimonialAuthor>
												<AuthorAvatar />
												<Box>
													<Typography fontWeight="bold">
														{testimonial.author_name}
													</Typography>
													<Typography variant="pi">
														{testimonial.author_title}
													</Typography>
												</Box>
											</TestimonialAuthor>
										</Testimonial>
									))}
							</TestimonialSection>
						)}
				</PhoneFrame>
			</Box>
		</PreviewContainer>
	);
};

export default PaywallPreview;
