/**
 * Performance Monitoring Dashboard
 * Real-time system health and performance monitoring interface
 */

import {
	Api as Api<PERSON>con,
	<PERSON>rro<PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
	CheckCircle as HealthyIcon,
	Refresh as RefreshIcon,
	Sync as SyncIcon,
	Warning as WarningIcon,
} from "@mui/icons-material";
import {
	<PERSON>ert,
	Box,
	Card,
	CardContent,
	Chip,
	FormControlLabel,
	Grid,
	IconButton,
	LinearProgress,
	Switch,
	Table,
	TableBody,
	TableCell,
	TableContainer,
	TableHead,
	TableRow,
	Typography,
} from "@mui/material";
import type React from "react";
import { useEffect, useState } from "react";

interface SystemHealth {
	status: "healthy" | "warning" | "critical" | "down";
	timestamp: string;
	uptime: number;
	memoryUsage: {
		used: number;
		total: number;
		percentage: number;
	};
	cpuUsage: number;
	responseTime: number;
	activeConnections: number;
	errorRate: number;
}

interface PerformanceAlert {
	id: string;
	type: string;
	severity: "low" | "medium" | "high" | "critical";
	message: string;
	timestamp: string;
	status: string;
}

interface SyncOperation {
	id: string;
	operationType: string;
	operationId: string;
	status: "running" | "completed" | "failed";
	startTime: string;
	duration?: number;
	recordsProcessed: number;
	errorCount: number;
	successRate: number;
}

const PerformanceMonitoringDashboard: React.FC = () => {
	const [systemHealth, setSystemHealth] = useState<SystemHealth | null>(null);
	const [alerts, setAlerts] = useState<PerformanceAlert[]>([]);
	const [syncOperations, setSyncOperations] = useState<SyncOperation[]>([]);
	const [apiMetrics, setApiMetrics] = useState<any>(null);
	const [autoRefresh, setAutoRefresh] = useState(true);
	const [loading, setLoading] = useState(true);

	useEffect(() => {
		fetchData();

		if (autoRefresh) {
			const interval = setInterval(fetchData, 30000); // Refresh every 30 seconds
			return () => clearInterval(interval);
		}
	}, [autoRefresh, fetchData]);

	const fetchData = async () => {
		try {
			setLoading(true);

			// Fetch system health
			const healthResponse = await fetch("/api/system-health/current");
			const healthData = await healthResponse.json();
			setSystemHealth(healthData.data);

			// Fetch active alerts
			const alertsResponse = await fetch(
				"/api/performance-alerts/active?limit=10",
			);
			const alertsData = await alertsResponse.json();
			setAlerts(alertsData.data);

			// Fetch running sync operations
			const syncResponse = await fetch("/api/sync-operations/running");
			const syncData = await syncResponse.json();
			setSyncOperations(syncData.data);

			// Fetch API performance metrics
			const apiResponse = await fetch("/api/api-performance/realtime");
			const apiData = await apiResponse.json();
			setApiMetrics(apiData.data);
		} catch (error) {
			console.error("Failed to fetch monitoring data:", error);
		} finally {
			setLoading(false);
		}
	};

	const getStatusColor = (status: string) => {
		switch (status) {
			case "healthy":
				return "success";
			case "warning":
				return "warning";
			case "critical":
				return "error";
			case "down":
				return "error";
			default:
				return "default";
		}
	};

	const getStatusIcon = (status: string) => {
		switch (status) {
			case "healthy":
				return <HealthyIcon color="success" />;
			case "warning":
				return <WarningIcon color="warning" />;
			case "critical":
				return <ErrorIcon color="error" />;
			case "down":
				return <ErrorIcon color="error" />;
			default:
				return <HealthyIcon />;
		}
	};

	const formatUptime = (seconds: number) => {
		const days = Math.floor(seconds / 86400);
		const hours = Math.floor((seconds % 86400) / 3600);
		const minutes = Math.floor((seconds % 3600) / 60);
		return `${days}d ${hours}h ${minutes}m`;
	};

	const formatBytes = (bytes: number) => {
		const sizes = ["Bytes", "KB", "MB", "GB"];
		if (bytes === 0) return "0 Bytes";
		const i = Math.floor(Math.log(bytes) / Math.log(1024));
		return `${Math.round((bytes / 1024 ** i) * 100) / 100} ${sizes[i]}`;
	};

	const _formatDuration = (ms: number) => {
		if (ms < 1000) return `${ms}ms`;
		if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
		return `${(ms / 60000).toFixed(1)}m`;
	};

	return (
		<Box sx={{ p: 3 }}>
			{/* Header */}
			<Box
				sx={{
					display: "flex",
					justifyContent: "space-between",
					alignItems: "center",
					mb: 3,
				}}
			>
				<Typography variant="h4" component="h1">
					Performance Monitoring
				</Typography>
				<Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
					<FormControlLabel
						control={
							<Switch
								checked={autoRefresh}
								onChange={(e) => setAutoRefresh(e.target.checked)}
							/>
						}
						label="Auto Refresh"
					/>
					<IconButton onClick={fetchData} disabled={loading}>
						<RefreshIcon />
					</IconButton>
				</Box>
			</Box>

			{loading && <LinearProgress sx={{ mb: 2 }} />}

			<Grid container spacing={3}>
				{/* System Health Overview */}
				<Grid item xs={12} md={6} lg={3}>
					<Card>
						<CardContent>
							<Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
								{systemHealth && getStatusIcon(systemHealth.status)}
								<Typography variant="h6" sx={{ ml: 1 }}>
									System Health
								</Typography>
							</Box>
							{systemHealth && (
								<>
									<Chip
										label={systemHealth.status.toUpperCase()}
										color={getStatusColor(systemHealth.status) as any}
										sx={{ mb: 2 }}
									/>
									<Typography variant="body2" color="text.secondary">
										Uptime: {formatUptime(systemHealth.uptime)}
									</Typography>
									<Typography variant="body2" color="text.secondary">
										Response Time: {systemHealth.responseTime}ms
									</Typography>
									<Typography variant="body2" color="text.secondary">
										Error Rate: {systemHealth.errorRate.toFixed(2)}%
									</Typography>
								</>
							)}
						</CardContent>
					</Card>
				</Grid>

				{/* Memory Usage */}
				<Grid item xs={12} md={6} lg={3}>
					<Card>
						<CardContent>
							<Typography variant="h6" sx={{ mb: 2 }}>
								Memory Usage
							</Typography>
							{systemHealth && (
								<>
									<LinearProgress
										variant="determinate"
										value={systemHealth.memoryUsage.percentage}
										color={
											systemHealth.memoryUsage.percentage > 80
												? "error"
												: "primary"
										}
										sx={{ mb: 1 }}
									/>
									<Typography variant="body2" color="text.secondary">
										{systemHealth.memoryUsage.percentage.toFixed(1)}% (
										{formatBytes(systemHealth.memoryUsage.used)} /{" "}
										{formatBytes(systemHealth.memoryUsage.total)})
									</Typography>
								</>
							)}
						</CardContent>
					</Card>
				</Grid>

				{/* CPU Usage */}
				<Grid item xs={12} md={6} lg={3}>
					<Card>
						<CardContent>
							<Typography variant="h6" sx={{ mb: 2 }}>
								CPU Usage
							</Typography>
							{systemHealth && (
								<>
									<LinearProgress
										variant="determinate"
										value={systemHealth.cpuUsage}
										color={systemHealth.cpuUsage > 80 ? "error" : "primary"}
										sx={{ mb: 1 }}
									/>
									<Typography variant="body2" color="text.secondary">
										{systemHealth.cpuUsage.toFixed(1)}%
									</Typography>
								</>
							)}
						</CardContent>
					</Card>
				</Grid>

				{/* API Performance */}
				<Grid item xs={12} md={6} lg={3}>
					<Card>
						<CardContent>
							<Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
								<ApiIcon />
								<Typography variant="h6" sx={{ ml: 1 }}>
									API Performance
								</Typography>
							</Box>
							{apiMetrics && (
								<>
									<Typography variant="body2" color="text.secondary">
										Requests/min: {apiMetrics.requestsPerMinute}
									</Typography>
									<Typography variant="body2" color="text.secondary">
										Avg Response: {apiMetrics.averageResponseTime}ms
									</Typography>
									<Typography variant="body2" color="text.secondary">
										Error Rate: {apiMetrics.errorRate}%
									</Typography>
								</>
							)}
						</CardContent>
					</Card>
				</Grid>

				{/* Active Alerts */}
				<Grid item xs={12} md={6}>
					<Card>
						<CardContent>
							<Typography variant="h6" sx={{ mb: 2 }}>
								Active Alerts
							</Typography>
							{alerts.length === 0 ? (
								<Typography variant="body2" color="text.secondary">
									No active alerts
								</Typography>
							) : (
								alerts.slice(0, 5).map((alert) => (
									<Alert
										key={alert.id}
										severity={
											alert.severity === "critical"
												? "error"
												: (alert.severity as any)
										}
										sx={{ mb: 1 }}
									>
										<Typography variant="body2">{alert.message}</Typography>
										<Typography variant="caption" color="text.secondary">
											{new Date(alert.timestamp).toLocaleString()}
										</Typography>
									</Alert>
								))
							)}
						</CardContent>
					</Card>
				</Grid>

				{/* Running Sync Operations */}
				<Grid item xs={12} md={6}>
					<Card>
						<CardContent>
							<Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
								<SyncIcon />
								<Typography variant="h6" sx={{ ml: 1 }}>
									Running Sync Operations
								</Typography>
							</Box>
							{syncOperations.length === 0 ? (
								<Typography variant="body2" color="text.secondary">
									No running sync operations
								</Typography>
							) : (
								<TableContainer>
									<Table size="small">
										<TableHead>
											<TableRow>
												<TableCell>Type</TableCell>
												<TableCell>Started</TableCell>
												<TableCell>Records</TableCell>
												<TableCell>Errors</TableCell>
											</TableRow>
										</TableHead>
										<TableBody>
											{syncOperations.slice(0, 5).map((operation) => (
												<TableRow key={operation.id}>
													<TableCell>
														<Chip
															label={operation.operationType}
															size="small"
															color="primary"
														/>
													</TableCell>
													<TableCell>
														{new Date(operation.startTime).toLocaleTimeString()}
													</TableCell>
													<TableCell>{operation.recordsProcessed}</TableCell>
													<TableCell>
														{operation.errorCount > 0 ? (
															<Chip
																label={operation.errorCount}
																size="small"
																color="error"
															/>
														) : (
															"0"
														)}
													</TableCell>
												</TableRow>
											))}
										</TableBody>
									</Table>
								</TableContainer>
							)}
						</CardContent>
					</Card>
				</Grid>
			</Grid>
		</Box>
	);
};

export default PerformanceMonitoringDashboard;
