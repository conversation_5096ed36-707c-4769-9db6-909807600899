/**
 * Metrics Chart Component
 * Real-time performance metrics visualization for Chrome MCP monitoring
 * Part of Story 2.1: Chrome MCP Server Health Monitoring Dashboard
 */

import {
  <PERSON>,
  Card,
  CardBody,
  CardHeader,
  Flex,
  Stack,
  Typography,
} from "@strapi/design-system";
import {
  ChartCircle as MetricsIcon,
  Refresh as RefreshIcon,
} from "@strapi/icons";
import type React from "react";
import { useEffect, useRef } from "react";
import styled from "styled-components";

// Styled Components
const ChartContainer = styled(Box)`
  height: 300px;
  border: 1px solid ${({ theme }) => theme.colors.neutral200};
  border-radius: 4px;
  padding: 16px;
  background: white;
  position: relative;
  overflow: hidden;
`;

const ChartCanvas = styled.canvas`
  width: 100%;
  height: 100%;
`;

const MetricValue = styled(Box)`
  position: absolute;
  top: 16px;
  right: 16px;
  background: rgba(255, 255, 255, 0.9);
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid ${({ theme }) => theme.colors.neutral200};
`;

// Types
interface MetricDataPoint {
  timestamp: Date;
  value: number;
}

interface MetricsChartProps {
  title: string;
  data: MetricDataPoint[];
  unit: string;
  color?: string;
  threshold?: number;
  isRealTime?: boolean;
  onRefresh?: () => void;
}

const MetricsChart: React.FC<MetricsChartProps> = ({
  title,
  data,
  unit,
  color = '#4945ff',
  threshold,
  isRealTime = false,
  onRefresh
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Draw chart
  const drawChart = () => {
    const canvas = canvasRef.current;
    if (!canvas || data.length === 0) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    const rect = canvas.getBoundingClientRect();
    canvas.width = rect.width * window.devicePixelRatio;
    canvas.height = rect.height * window.devicePixelRatio;
    ctx.scale(window.devicePixelRatio, window.devicePixelRatio);

    const width = rect.width;
    const height = rect.height;
    const padding = 40;

    // Clear canvas
    ctx.clearRect(0, 0, width, height);

    // Calculate data bounds
    const values = data.map(d => d.value);
    const minValue = Math.min(...values);
    const maxValue = Math.max(...values);
    const valueRange = maxValue - minValue || 1;

    // Draw grid lines
    ctx.strokeStyle = '#f0f0f0';
    ctx.lineWidth = 1;
    
    // Horizontal grid lines
    for (let i = 0; i <= 5; i++) {
      const y = padding + (height - 2 * padding) * (i / 5);
      ctx.beginPath();
      ctx.moveTo(padding, y);
      ctx.lineTo(width - padding, y);
      ctx.stroke();
    }

    // Vertical grid lines
    for (let i = 0; i <= 6; i++) {
      const x = padding + (width - 2 * padding) * (i / 6);
      ctx.beginPath();
      ctx.moveTo(x, padding);
      ctx.lineTo(x, height - padding);
      ctx.stroke();
    }

    // Draw threshold line if provided
    if (threshold !== undefined) {
      const thresholdY = height - padding - ((threshold - minValue) / valueRange) * (height - 2 * padding);
      ctx.strokeStyle = '#ff4757';
      ctx.lineWidth = 2;
      ctx.setLineDash([5, 5]);
      ctx.beginPath();
      ctx.moveTo(padding, thresholdY);
      ctx.lineTo(width - padding, thresholdY);
      ctx.stroke();
      ctx.setLineDash([]);
    }

    // Draw data line
    if (data.length > 1) {
      ctx.strokeStyle = color;
      ctx.lineWidth = 2;
      ctx.beginPath();

      data.forEach((point, index) => {
        const x = padding + (width - 2 * padding) * (index / (data.length - 1));
        const y = height - padding - ((point.value - minValue) / valueRange) * (height - 2 * padding);

        if (index === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      });

      ctx.stroke();

      // Draw data points
      ctx.fillStyle = color;
      data.forEach((point, index) => {
        const x = padding + (width - 2 * padding) * (index / (data.length - 1));
        const y = height - padding - ((point.value - minValue) / valueRange) * (height - 2 * padding);

        ctx.beginPath();
        ctx.arc(x, y, 3, 0, 2 * Math.PI);
        ctx.fill();
      });

      // Fill area under curve
      ctx.globalAlpha = 0.1;
      ctx.fillStyle = color;
      ctx.beginPath();
      
      data.forEach((point, index) => {
        const x = padding + (width - 2 * padding) * (index / (data.length - 1));
        const y = height - padding - ((point.value - minValue) / valueRange) * (height - 2 * padding);

        if (index === 0) {
          ctx.moveTo(x, height - padding);
          ctx.lineTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      });

      ctx.lineTo(width - padding, height - padding);
      ctx.closePath();
      ctx.fill();
      ctx.globalAlpha = 1;
    }

    // Draw labels
    ctx.fillStyle = '#666';
    ctx.font = '12px Arial';
    ctx.textAlign = 'center';

    // Y-axis labels
    for (let i = 0; i <= 5; i++) {
      const value = minValue + (valueRange * (5 - i) / 5);
      const y = padding + (height - 2 * padding) * (i / 5);
      ctx.textAlign = 'right';
      ctx.fillText(value.toFixed(0), padding - 10, y + 4);
    }

    // X-axis labels (time)
    ctx.textAlign = 'center';
    for (let i = 0; i <= 6; i++) {
      const dataIndex = Math.floor((data.length - 1) * (i / 6));
      if (data[dataIndex]) {
        const x = padding + (width - 2 * padding) * (i / 6);
        const time = data[dataIndex].timestamp.toLocaleTimeString([], { 
          hour: '2-digit', 
          minute: '2-digit' 
        });
        ctx.fillText(time, x, height - padding + 20);
      }
    }
  };

  // Redraw chart when data changes
  useEffect(() => {
    drawChart();
  }, [data, color, threshold]);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      setTimeout(drawChart, 100);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Get current value
  const currentValue = data.length > 0 ? data[data.length - 1].value : 0;

  // Get trend
  const getTrend = () => {
    if (data.length < 2) return 'stable';
    const current = data[data.length - 1].value;
    const previous = data[data.length - 2].value;
    const change = ((current - previous) / previous) * 100;
    
    if (change > 5) return 'increasing';
    if (change < -5) return 'decreasing';
    return 'stable';
  };

  const trend = getTrend();

  return (
    <Card>
      <CardHeader>
        <Flex justifyContent="space-between" alignItems="center">
          <Flex alignItems="center" gap={2}>
            <MetricsIcon />
            <Typography variant="delta">{title}</Typography>
          </Flex>
          
          {isRealTime && (
            <Flex alignItems="center" gap={1}>
              <Box
                width="8px"
                height="8px"
                borderRadius="50%"
                background="success600"
                style={{ animation: 'pulse 2s infinite' }}
              />
              <Typography variant="pi" textColor="success600">
                Live
              </Typography>
            </Flex>
          )}
        </Flex>
      </CardHeader>
      <CardBody>
        <ChartContainer>
          <ChartCanvas ref={canvasRef} />
          
          <MetricValue>
            <Stack spacing={1}>
              <Typography variant="beta" textColor="neutral800">
                {currentValue.toFixed(0)}{unit}
              </Typography>
              <Typography 
                variant="pi" 
                textColor={
                  trend === 'increasing' ? 'danger600' : 
                  trend === 'decreasing' ? 'success600' : 
                  'neutral600'
                }
              >
                {trend === 'increasing' ? '↗' : trend === 'decreasing' ? '↘' : '→'} {trend}
              </Typography>
            </Stack>
          </MetricValue>
        </ChartContainer>

        {/* Chart Statistics */}
        <Stack spacing={2} marginTop={3}>
          <Flex justifyContent="space-between">
            <Typography variant="pi" textColor="neutral600">
              Current
            </Typography>
            <Typography variant="pi" textColor="neutral800">
              {currentValue.toFixed(1)}{unit}
            </Typography>
          </Flex>
          
          {data.length > 0 && (
            <>
              <Flex justifyContent="space-between">
                <Typography variant="pi" textColor="neutral600">
                  Average
                </Typography>
                <Typography variant="pi" textColor="neutral800">
                  {(data.reduce((sum, d) => sum + d.value, 0) / data.length).toFixed(1)}{unit}
                </Typography>
              </Flex>
              
              <Flex justifyContent="space-between">
                <Typography variant="pi" textColor="neutral600">
                  Peak
                </Typography>
                <Typography variant="pi" textColor="neutral800">
                  {Math.max(...data.map(d => d.value)).toFixed(1)}{unit}
                </Typography>
              </Flex>
            </>
          )}
          
          {threshold && (
            <Flex justifyContent="space-between">
              <Typography variant="pi" textColor="neutral600">
                Threshold
              </Typography>
              <Typography variant="pi" textColor="danger600">
                {threshold.toFixed(1)}{unit}
              </Typography>
            </Flex>
          )}
        </Stack>
      </CardBody>
    </Card>
  );
};

export default MetricsChart;
