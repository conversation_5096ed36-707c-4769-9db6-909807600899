/**
 * Test Conclusion Workflow - <PERSON>les test conclusion, winner promotion, and deployment
 */

import {
	<PERSON>ert,
	Badge,
	Box,
	Button,
	Card,
	CardBody,
	CardHeader,
	Checkbox,
	Divider,
	Flex,
	ModalBody,
	Modal<PERSON>ooter,
	ModalHeader,
	ModalLayout,
	Option,
	Select,
	Stack,
	Textarea,
	Typography,
} from "@strapi/design-system";
import { useNotification } from "@strapi/helper-plugin";
import { CheckCircle, Crown } from "@strapi/icons";
import type React from "react";
import { useState } from "react";
import { useIntl } from "react-intl";
import styled from "styled-components";

const WorkflowCard = styled(Card)`
  margin-bottom: 16px;
`;

const StepIndicator = styled(Box)`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: ${({ theme, active }) => (active ? theme.colors.primary600 : theme.colors.neutral200)};
  color: ${({ theme, active }) => (active ? theme.colors.neutral0 : theme.colors.neutral600)};
  font-weight: bold;
`;

interface TestConclusionWorkflowProps {
	testId: string;
	test: any;
	variations: any[];
	isOpen: boolean;
	onClose: () => void;
	onComplete: (result: any) => void;
}

const TestConclusionWorkflow: React.FC<TestConclusionWorkflowProps> = ({
	testId,
	test,
	variations,
	isOpen,
	onClose,
	onComplete,
}) => {
	const { formatMessage } = useIntl();
	const toggleNotification = useNotification();
	const [currentStep, setCurrentStep] = useState(1);
	const [selectedWinner, setSelectedWinner] = useState(null);
	const [conclusionNotes, setConclusionNotes] = useState("");
	const [deploymentOption, setDeploymentOption] = useState("immediate");
	const [confirmations, setConfirmations] = useState({
		dataReviewed: false,
		stakeholdersNotified: false,
		deploymentApproved: false,
	});
	const [loading, setLoading] = useState(false);

	const steps = [
		{
			id: 1,
			title: "Review Results",
			description: "Analyze test performance and select winner",
		},
		{
			id: 2,
			title: "Add Conclusion",
			description: "Document findings and recommendations",
		},
		{
			id: 3,
			title: "Configure Deployment",
			description: "Set up winner deployment strategy",
		},
		{
			id: 4,
			title: "Final Confirmation",
			description: "Confirm and execute conclusion",
		},
	];

	const getWinningVariation = () => {
		if (!variations || variations.length === 0) return null;
		return variations.reduce((winner, current) =>
			(current.conversionRate || 0) > (winner.conversionRate || 0)
				? current
				: winner,
		);
	};

	const suggestedWinner = getWinningVariation();

	const handleWinnerSelection = (variationId: string) => {
		const variation = variations.find((v) => v.id === variationId);
		setSelectedWinner(variation);
	};

	const handleConfirmationChange = (key: string, value: boolean) => {
		setConfirmations((prev) => ({ ...prev, [key]: value }));
	};

	const canProceedToNextStep = () => {
		switch (currentStep) {
			case 1:
				return selectedWinner !== null;
			case 2:
				return conclusionNotes.trim().length > 0;
			case 3:
				return deploymentOption !== "";
			case 4:
				return Object.values(confirmations).every(Boolean);
			default:
				return false;
		}
	};

	const handleNext = () => {
		if (canProceedToNextStep() && currentStep < 4) {
			setCurrentStep((prev) => prev + 1);
		}
	};

	const handlePrevious = () => {
		if (currentStep > 1) {
			setCurrentStep((prev) => prev - 1);
		}
	};

	const handleComplete = async () => {
		if (!canProceedToNextStep()) return;

		setLoading(true);
		try {
			const conclusionData = {
				testId,
				winnerId: selectedWinner.id,
				conclusionNotes,
				deploymentOption,
				conclusionDate: new Date().toISOString(),
				performanceMetrics: {
					winnerConversionRate: selectedWinner.conversionRate,
					improvement: calculateImprovement(),
					sampleSize: selectedWinner.sampleSize,
				},
			};

			// API call to conclude test
			const response = await fetch(`/api/ab-tests/${testId}/conclude`, {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify(conclusionData),
			});

			if (!response.ok) throw new Error("Failed to conclude test");

			const result = await response.json();

			toggleNotification({
				type: "success",
				message: "A/B test concluded successfully",
			});

			onComplete(result);
			onClose();
		} catch (_error) {
			toggleNotification({
				type: "warning",
				message: "Failed to conclude test",
			});
		} finally {
			setLoading(false);
		}
	};

	const calculateImprovement = () => {
		if (!selectedWinner || !variations) return 0;
		const control = variations.find((v) => v.isControl);
		if (!control) return 0;

		const controlRate = control.conversionRate || 0;
		const winnerRate = selectedWinner.conversionRate || 0;

		return controlRate > 0
			? ((winnerRate - controlRate) / controlRate) * 100
			: 0;
	};

	const renderStepContent = () => {
		switch (currentStep) {
			case 1:
				return (
					<Stack spacing={4}>
						<Typography variant="beta">Select Test Winner</Typography>

						{suggestedWinner && (
							<Alert variant="success" title="Recommended Winner">
								Based on conversion rates,{" "}
								<strong>{suggestedWinner.name}</strong> is performing best with{" "}
								{suggestedWinner.conversionRate?.toFixed(2)}% conversion rate.
							</Alert>
						)}

						<Stack spacing={3}>
							{variations.map((variation) => (
								<Card
									key={variation.id}
									style={{
										border:
											selectedWinner?.id === variation.id
												? "2px solid #4945ff"
												: "1px solid #dcdce4",
										cursor: "pointer",
									}}
									onClick={() => handleWinnerSelection(variation.id)}
								>
									<CardBody>
										<Flex justifyContent="space-between" alignItems="center">
											<Box>
												<Flex alignItems="center" gap={2}>
													<Typography variant="delta" fontWeight="bold">
														{variation.name}
													</Typography>
													{variation.isControl && (
														<Badge backgroundColor="neutral100">Control</Badge>
													)}
													{suggestedWinner?.id === variation.id && (
														<Badge
															backgroundColor="success100"
															textColor="success700"
														>
															<Crown width="12px" height="12px" />
															Recommended
														</Badge>
													)}
												</Flex>
												<Typography variant="omega" textColor="neutral600">
													Sample: {variation.sampleSize?.toLocaleString()} users
												</Typography>
											</Box>
											<Box textAlign="right">
												<Typography variant="alpha" fontWeight="bold">
													{variation.conversionRate?.toFixed(2)}%
												</Typography>
												<Typography variant="pi" textColor="neutral600">
													Conversion Rate
												</Typography>
											</Box>
										</Flex>
									</CardBody>
								</Card>
							))}
						</Stack>
					</Stack>
				);

			case 2:
				return (
					<Stack spacing={4}>
						<Typography variant="beta">Document Test Conclusion</Typography>

						{selectedWinner && (
							<Alert variant="default" title="Selected Winner">
								<strong>{selectedWinner.name}</strong> with{" "}
								{selectedWinner.conversionRate?.toFixed(2)}% conversion rate (
								{calculateImprovement().toFixed(1)}% improvement over control)
							</Alert>
						)}

						<Box>
							<Typography variant="delta" marginBottom={2}>
								Conclusion Notes
							</Typography>
							<Textarea
								placeholder="Document your findings, insights, and recommendations for future tests..."
								value={conclusionNotes}
								onChange={(e) => setConclusionNotes(e.target.value)}
								rows={6}
							/>
						</Box>
					</Stack>
				);

			case 3:
				return (
					<Stack spacing={4}>
						<Typography variant="beta">Configure Deployment</Typography>

						<Box>
							<Typography variant="delta" marginBottom={2}>
								Deployment Strategy
							</Typography>
							<Select
								value={deploymentOption}
								onChange={setDeploymentOption}
								placeholder="Select deployment option"
							>
								<Option value="immediate">Deploy Immediately</Option>
								<Option value="scheduled">Schedule Deployment</Option>
								<Option value="manual">Manual Deployment Later</Option>
								<Option value="gradual">Gradual Rollout</Option>
							</Select>
						</Box>

						{deploymentOption === "immediate" && (
							<Alert variant="warning" title="Immediate Deployment">
								The winning variation will be deployed to all users immediately
								after conclusion.
							</Alert>
						)}

						{deploymentOption === "gradual" && (
							<Alert variant="default" title="Gradual Rollout">
								The winning variation will be gradually rolled out over time to
								minimize risk.
							</Alert>
						)}
					</Stack>
				);

			case 4:
				return (
					<Stack spacing={4}>
						<Typography variant="beta">Final Confirmation</Typography>

						<Card>
							<CardHeader>
								<Typography variant="delta">Test Summary</Typography>
							</CardHeader>
							<CardBody>
								<Stack spacing={2}>
									<Flex justifyContent="space-between">
										<Typography variant="omega">Test Name:</Typography>
										<Typography variant="omega" fontWeight="bold">
											{test.name}
										</Typography>
									</Flex>
									<Flex justifyContent="space-between">
										<Typography variant="omega">Winner:</Typography>
										<Typography variant="omega" fontWeight="bold">
											{selectedWinner?.name}
										</Typography>
									</Flex>
									<Flex justifyContent="space-between">
										<Typography variant="omega">Improvement:</Typography>
										<Typography variant="omega" fontWeight="bold">
											+{calculateImprovement().toFixed(1)}%
										</Typography>
									</Flex>
									<Flex justifyContent="space-between">
										<Typography variant="omega">Deployment:</Typography>
										<Typography variant="omega" fontWeight="bold">
											{deploymentOption}
										</Typography>
									</Flex>
								</Stack>
							</CardBody>
						</Card>

						<Stack spacing={3}>
							<Checkbox
								checked={confirmations.dataReviewed}
								onChange={(e) =>
									handleConfirmationChange("dataReviewed", e.target.checked)
								}
							>
								I have reviewed all test data and results
							</Checkbox>
							<Checkbox
								checked={confirmations.stakeholdersNotified}
								onChange={(e) =>
									handleConfirmationChange(
										"stakeholdersNotified",
										e.target.checked,
									)
								}
							>
								Relevant stakeholders have been notified
							</Checkbox>
							<Checkbox
								checked={confirmations.deploymentApproved}
								onChange={(e) =>
									handleConfirmationChange(
										"deploymentApproved",
										e.target.checked,
									)
								}
							>
								Deployment strategy has been approved
							</Checkbox>
						</Stack>
					</Stack>
				);

			default:
				return null;
		}
	};

	if (!isOpen) return null;

	return (
		<ModalLayout onClose={onClose} labelledBy="conclusion-workflow">
			<ModalHeader>
				<Typography variant="beta" id="conclusion-workflow">
					Conclude A/B Test: {test.name}
				</Typography>
			</ModalHeader>

			<ModalBody>
				{/* Progress Steps */}
				<Flex justifyContent="space-between" marginBottom={6}>
					{steps.map((step, index) => (
						<Flex key={step.id} alignItems="center" gap={2}>
							<StepIndicator active={currentStep >= step.id}>
								{currentStep > step.id ? (
									<CheckCircle width="16px" height="16px" />
								) : (
									step.id
								)}
							</StepIndicator>
							<Box>
								<Typography
									variant="sigma"
									fontWeight={currentStep >= step.id ? "bold" : "normal"}
								>
									{step.title}
								</Typography>
								<Typography variant="pi" textColor="neutral600">
									{step.description}
								</Typography>
							</Box>
							{index < steps.length - 1 && <Divider />}
						</Flex>
					))}
				</Flex>

				{/* Step Content */}
				<WorkflowCard>
					<CardBody>{renderStepContent()}</CardBody>
				</WorkflowCard>
			</ModalBody>

			<ModalFooter
				startActions={
					<Button variant="tertiary" onClick={onClose}>
						Cancel
					</Button>
				}
				endActions={
					<Flex gap={2}>
						{currentStep > 1 && (
							<Button variant="secondary" onClick={handlePrevious}>
								Previous
							</Button>
						)}
						{currentStep < 4 ? (
							<Button
								variant="default"
								onClick={handleNext}
								disabled={!canProceedToNextStep()}
							>
								Next
							</Button>
						) : (
							<Button
								variant="success"
								onClick={handleComplete}
								disabled={!canProceedToNextStep()}
								loading={loading}
								startIcon={<CheckCircle />}
							>
								Conclude Test
							</Button>
						)}
					</Flex>
				}
			/>
		</ModalLayout>
	);
};

export default TestConclusionWorkflow;
