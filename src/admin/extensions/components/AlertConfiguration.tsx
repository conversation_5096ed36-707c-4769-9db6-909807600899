/**
 * Alert Configuration Component
 * Configurable alert threshold settings for Chrome MCP monitoring
 * Part of Story 2.1: Chrome MCP Server Health Monitoring Dashboard
 */

import {
  Box,
  Button,
  Card,
  CardBody,
  CardHeader,
  Flex,
  Grid,
  GridItem,
  NumberInput,
  Select,
  Option,
  Stack,
  Typography,
  ToggleInput,
} from "@strapi/design-system";
import { useNotification } from "@strapi/helper-plugin";
import {
  Cog as SettingsIcon,
  Check as SaveIcon,
  Refresh as ResetIcon,
} from "@strapi/icons";
import type React from "react";
import { useState, useEffect } from "react";
import { useIntl } from "react-intl";
import styled from "styled-components";

// Styled Components
const ConfigurationContainer = styled(Box)`
  padding: 24px;
  background: ${({ theme }) => theme.colors.neutral0};
`;

const ConfigCard = styled(Card)`
  height: 100%;
  transition: all 0.2s ease;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
`;

// Types
interface AlertThresholds {
  responseTime: {
    warning: number;
    critical: number;
    enabled: boolean;
  };
  connectionSuccessRate: {
    warning: number;
    critical: number;
    enabled: boolean;
  };
  errorCount: {
    warning: number;
    critical: number;
    enabled: boolean;
  };
  memoryUsage: {
    warning: number;
    critical: number;
    enabled: boolean;
  };
  cpuUsage: {
    warning: number;
    critical: number;
    enabled: boolean;
  };
  serverStatus: {
    enabled: boolean;
    notifyOnDegraded: boolean;
    notifyOnUnhealthy: boolean;
  };
}

interface NotificationSettings {
  email: {
    enabled: boolean;
    recipients: string[];
  };
  webhook: {
    enabled: boolean;
    url: string;
  };
  inApp: {
    enabled: boolean;
  };
}

interface AlertConfigurationProps {
  onSave?: (thresholds: AlertThresholds, notifications: NotificationSettings) => void;
  onReset?: () => void;
}

const AlertConfiguration: React.FC<AlertConfigurationProps> = ({
  onSave,
  onReset
}) => {
  const { formatMessage } = useIntl();
  const { toggleNotification } = useNotification();

  // Default thresholds based on Story 2.1 requirements
  const defaultThresholds: AlertThresholds = {
    responseTime: {
      warning: 2000, // 2 seconds
      critical: 5000, // 5 seconds
      enabled: true,
    },
    connectionSuccessRate: {
      warning: 90, // 90%
      critical: 75, // 75%
      enabled: true,
    },
    errorCount: {
      warning: 3,
      critical: 10,
      enabled: true,
    },
    memoryUsage: {
      warning: 800, // 800 MB
      critical: 1000, // 1 GB
      enabled: true,
    },
    cpuUsage: {
      warning: 70, // 70%
      critical: 90, // 90%
      enabled: true,
    },
    serverStatus: {
      enabled: true,
      notifyOnDegraded: true,
      notifyOnUnhealthy: true,
    },
  };

  const defaultNotifications: NotificationSettings = {
    email: {
      enabled: false,
      recipients: [],
    },
    webhook: {
      enabled: false,
      url: '',
    },
    inApp: {
      enabled: true,
    },
  };

  const [thresholds, setThresholds] = useState<AlertThresholds>(defaultThresholds);
  const [notifications, setNotifications] = useState<NotificationSettings>(defaultNotifications);
  const [hasChanges, setHasChanges] = useState(false);

  // Load saved configuration
  useEffect(() => {
    loadConfiguration();
  }, []);

  const loadConfiguration = async () => {
    try {
      // In a real implementation, this would load from API
      const savedThresholds = localStorage.getItem('chrome-mcp-alert-thresholds');
      const savedNotifications = localStorage.getItem('chrome-mcp-notifications');

      if (savedThresholds) {
        setThresholds(JSON.parse(savedThresholds));
      }

      if (savedNotifications) {
        setNotifications(JSON.parse(savedNotifications));
      }
    } catch (error) {
      console.error('Failed to load alert configuration:', error);
    }
  };

  const saveConfiguration = async () => {
    try {
      // Save to localStorage (in real implementation, would save to API)
      localStorage.setItem('chrome-mcp-alert-thresholds', JSON.stringify(thresholds));
      localStorage.setItem('chrome-mcp-notifications', JSON.stringify(notifications));

      setHasChanges(false);

      toggleNotification({
        type: 'success',
        message: 'Alert configuration saved successfully',
      });

      if (onSave) {
        onSave(thresholds, notifications);
      }
    } catch (error) {
      console.error('Failed to save alert configuration:', error);
      toggleNotification({
        type: 'warning',
        message: 'Failed to save alert configuration',
      });
    }
  };

  const resetConfiguration = () => {
    setThresholds(defaultThresholds);
    setNotifications(defaultNotifications);
    setHasChanges(true);

    if (onReset) {
      onReset();
    }
  };

  const updateThreshold = (metric: keyof AlertThresholds, field: string, value: any) => {
    setThresholds(prev => ({
      ...prev,
      [metric]: {
        ...prev[metric],
        [field]: value,
      },
    }));
    setHasChanges(true);
  };

  const updateNotification = (type: keyof NotificationSettings, field: string, value: any) => {
    setNotifications(prev => ({
      ...prev,
      [type]: {
        ...prev[type],
        [field]: value,
      },
    }));
    setHasChanges(true);
  };

  return (
    <ConfigurationContainer>
      {/* Header */}
      <Flex justifyContent="space-between" alignItems="center" marginBottom={6}>
        <Stack spacing={2}>
          <Typography variant="alpha">Alert Configuration</Typography>
          <Typography variant="omega" textColor="neutral600">
            Configure alert thresholds and notification settings for Chrome MCP monitoring
          </Typography>
        </Stack>
        
        <Flex gap={2}>
          <Button
            variant="secondary"
            onClick={resetConfiguration}
            startIcon={<ResetIcon />}
          >
            Reset to Defaults
          </Button>
          
          <Button
            variant="default"
            onClick={saveConfiguration}
            startIcon={<SaveIcon />}
            disabled={!hasChanges}
          >
            Save Configuration
          </Button>
        </Flex>
      </Flex>

      {/* Alert Thresholds */}
      <Grid gap={6}>
        <GridItem col={8}>
          <ConfigCard>
            <CardHeader>
              <Flex alignItems="center" gap={2}>
                <SettingsIcon />
                <Typography variant="delta">Alert Thresholds</Typography>
              </Flex>
            </CardHeader>
            <CardBody>
              <Stack spacing={4}>
                {/* Response Time Thresholds */}
                <Box>
                  <Flex justifyContent="space-between" alignItems="center" marginBottom={2}>
                    <Typography variant="epsilon">Response Time (ms)</Typography>
                    <ToggleInput
                      checked={thresholds.responseTime.enabled}
                      onChange={(e) => updateThreshold('responseTime', 'enabled', e.target.checked)}
                      label="Enable alerts"
                    />
                  </Flex>
                  <Grid gap={2}>
                    <GridItem col={6}>
                      <NumberInput
                        label="Warning Threshold"
                        value={thresholds.responseTime.warning}
                        onValueChange={(value) => updateThreshold('responseTime', 'warning', value)}
                        disabled={!thresholds.responseTime.enabled}
                      />
                    </GridItem>
                    <GridItem col={6}>
                      <NumberInput
                        label="Critical Threshold"
                        value={thresholds.responseTime.critical}
                        onValueChange={(value) => updateThreshold('responseTime', 'critical', value)}
                        disabled={!thresholds.responseTime.enabled}
                      />
                    </GridItem>
                  </Grid>
                </Box>

                {/* Connection Success Rate Thresholds */}
                <Box>
                  <Flex justifyContent="space-between" alignItems="center" marginBottom={2}>
                    <Typography variant="epsilon">Connection Success Rate (%)</Typography>
                    <ToggleInput
                      checked={thresholds.connectionSuccessRate.enabled}
                      onChange={(e) => updateThreshold('connectionSuccessRate', 'enabled', e.target.checked)}
                      label="Enable alerts"
                    />
                  </Flex>
                  <Grid gap={2}>
                    <GridItem col={6}>
                      <NumberInput
                        label="Warning Threshold"
                        value={thresholds.connectionSuccessRate.warning}
                        onValueChange={(value) => updateThreshold('connectionSuccessRate', 'warning', value)}
                        disabled={!thresholds.connectionSuccessRate.enabled}
                        step={1}
                        min={0}
                        max={100}
                      />
                    </GridItem>
                    <GridItem col={6}>
                      <NumberInput
                        label="Critical Threshold"
                        value={thresholds.connectionSuccessRate.critical}
                        onValueChange={(value) => updateThreshold('connectionSuccessRate', 'critical', value)}
                        disabled={!thresholds.connectionSuccessRate.enabled}
                        step={1}
                        min={0}
                        max={100}
                      />
                    </GridItem>
                  </Grid>
                </Box>

                {/* Error Count Thresholds */}
                <Box>
                  <Flex justifyContent="space-between" alignItems="center" marginBottom={2}>
                    <Typography variant="epsilon">Error Count</Typography>
                    <ToggleInput
                      checked={thresholds.errorCount.enabled}
                      onChange={(e) => updateThreshold('errorCount', 'enabled', e.target.checked)}
                      label="Enable alerts"
                    />
                  </Flex>
                  <Grid gap={2}>
                    <GridItem col={6}>
                      <NumberInput
                        label="Warning Threshold"
                        value={thresholds.errorCount.warning}
                        onValueChange={(value) => updateThreshold('errorCount', 'warning', value)}
                        disabled={!thresholds.errorCount.enabled}
                        step={1}
                        min={0}
                      />
                    </GridItem>
                    <GridItem col={6}>
                      <NumberInput
                        label="Critical Threshold"
                        value={thresholds.errorCount.critical}
                        onValueChange={(value) => updateThreshold('errorCount', 'critical', value)}
                        disabled={!thresholds.errorCount.enabled}
                        step={1}
                        min={0}
                      />
                    </GridItem>
                  </Grid>
                </Box>

                {/* Memory Usage Thresholds */}
                <Box>
                  <Flex justifyContent="space-between" alignItems="center" marginBottom={2}>
                    <Typography variant="epsilon">Memory Usage (MB)</Typography>
                    <ToggleInput
                      checked={thresholds.memoryUsage.enabled}
                      onChange={(e) => updateThreshold('memoryUsage', 'enabled', e.target.checked)}
                      label="Enable alerts"
                    />
                  </Flex>
                  <Grid gap={2}>
                    <GridItem col={6}>
                      <NumberInput
                        label="Warning Threshold"
                        value={thresholds.memoryUsage.warning}
                        onValueChange={(value) => updateThreshold('memoryUsage', 'warning', value)}
                        disabled={!thresholds.memoryUsage.enabled}
                        step={50}
                        min={0}
                      />
                    </GridItem>
                    <GridItem col={6}>
                      <NumberInput
                        label="Critical Threshold"
                        value={thresholds.memoryUsage.critical}
                        onValueChange={(value) => updateThreshold('memoryUsage', 'critical', value)}
                        disabled={!thresholds.memoryUsage.enabled}
                        step={50}
                        min={0}
                      />
                    </GridItem>
                  </Grid>
                </Box>

                {/* CPU Usage Thresholds */}
                <Box>
                  <Flex justifyContent="space-between" alignItems="center" marginBottom={2}>
                    <Typography variant="epsilon">CPU Usage (%)</Typography>
                    <ToggleInput
                      checked={thresholds.cpuUsage.enabled}
                      onChange={(e) => updateThreshold('cpuUsage', 'enabled', e.target.checked)}
                      label="Enable alerts"
                    />
                  </Flex>
                  <Grid gap={2}>
                    <GridItem col={6}>
                      <NumberInput
                        label="Warning Threshold"
                        value={thresholds.cpuUsage.warning}
                        onValueChange={(value) => updateThreshold('cpuUsage', 'warning', value)}
                        disabled={!thresholds.cpuUsage.enabled}
                        step={5}
                        min={0}
                        max={100}
                      />
                    </GridItem>
                    <GridItem col={6}>
                      <NumberInput
                        label="Critical Threshold"
                        value={thresholds.cpuUsage.critical}
                        onValueChange={(value) => updateThreshold('cpuUsage', 'critical', value)}
                        disabled={!thresholds.cpuUsage.enabled}
                        step={5}
                        min={0}
                        max={100}
                      />
                    </GridItem>
                  </Grid>
                </Box>
              </Stack>
            </CardBody>
          </ConfigCard>
        </GridItem>

        {/* Notification Settings */}
        <GridItem col={4}>
          <ConfigCard>
            <CardHeader>
              <Typography variant="delta">Notification Settings</Typography>
            </CardHeader>
            <CardBody>
              <Stack spacing={4}>
                {/* In-App Notifications */}
                <Box>
                  <ToggleInput
                    checked={notifications.inApp.enabled}
                    onChange={(e) => updateNotification('inApp', 'enabled', e.target.checked)}
                    label="In-App Notifications"
                  />
                  <Typography variant="pi" textColor="neutral600">
                    Show alerts in the admin interface
                  </Typography>
                </Box>

                {/* Server Status Alerts */}
                <Box>
                  <Typography variant="epsilon" marginBottom={2}>Server Status Alerts</Typography>
                  <Stack spacing={2}>
                    <ToggleInput
                      checked={thresholds.serverStatus.enabled}
                      onChange={(e) => updateThreshold('serverStatus', 'enabled', e.target.checked)}
                      label="Enable server status alerts"
                    />
                    <ToggleInput
                      checked={thresholds.serverStatus.notifyOnDegraded}
                      onChange={(e) => updateThreshold('serverStatus', 'notifyOnDegraded', e.target.checked)}
                      label="Notify on degraded status"
                      disabled={!thresholds.serverStatus.enabled}
                    />
                    <ToggleInput
                      checked={thresholds.serverStatus.notifyOnUnhealthy}
                      onChange={(e) => updateThreshold('serverStatus', 'notifyOnUnhealthy', e.target.checked)}
                      label="Notify on unhealthy status"
                      disabled={!thresholds.serverStatus.enabled}
                    />
                  </Stack>
                </Box>

                {/* Configuration Summary */}
                <Box>
                  <Typography variant="epsilon" marginBottom={2}>Configuration Summary</Typography>
                  <Stack spacing={1}>
                    <Typography variant="pi" textColor="neutral600">
                      Active Thresholds: {Object.values(thresholds).filter(t => t.enabled).length}
                    </Typography>
                    <Typography variant="pi" textColor="neutral600">
                      Notification Methods: {Object.values(notifications).filter(n => n.enabled).length}
                    </Typography>
                    <Typography variant="pi" textColor={hasChanges ? 'warning600' : 'success600'}>
                      Status: {hasChanges ? 'Unsaved Changes' : 'Saved'}
                    </Typography>
                  </Stack>
                </Box>
              </Stack>
            </CardBody>
          </ConfigCard>
        </GridItem>
      </Grid>
    </ConfigurationContainer>
  );
};

export default AlertConfiguration;
