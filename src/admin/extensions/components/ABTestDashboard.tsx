/**
 * A/B Test Dashboard - Real-time performance monitoring dashboard with conversion metrics
 */

import {
	<PERSON><PERSON>,
	Bad<PERSON>,
	Box,
	Button,
	Card,
	CardBody,
	CardHeader,
	Flex,
	Grid,
	GridItem,
	Loader,
	Option,
	Select,
	Stack,
	Typography,
} from "@strapi/design-system";
import { useNotification } from "@strapi/helper-plugin";
import {
	ChartCircle as <PERSON><PERSON><PERSON>,
	Download,
	<PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>,
	Refresh,
	ArrowDown as TrendDown,
	ArrowUp as TrendUp,
} from "@strapi/icons";
import type React from "react";
import { useEffect, useState } from "react";
import { useIntl } from "react-intl";
import styled from "styled-components";

const MetricCard = styled(Card)`
  height: 100%;
  transition: all 0.2s ease;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
`;

const ChartContainer = styled(Box)`
  height: 300px;
  border: 1px solid ${({ theme }) => theme.colors.neutral200};
  border-radius: 4px;
  padding: 16px;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const TrendIndicator = styled(Flex)`
  align-items: center;
  gap: 4px;
`;

interface DashboardMetrics {
	totalTests: number;
	activeTests: number;
	completedTests: number;
	avgConversionRate: number;
	totalRevenue: number;
	totalImpressions: number;
	totalConversions: number;
	trends: {
		conversionRate: number;
		revenue: number;
		tests: number;
	};
}

interface TestPerformance {
	testId: string;
	testName: string;
	status: string;
	conversionRate: number;
	revenue: number;
	impressions: number;
	conversions: number;
	confidence: number;
	startDate: string;
	variations: number;
}

interface ABTestDashboardProps {
	selectedTestId?: string;
	dateRange?: {
		start: string;
		end: string;
	};
}

const ABTestDashboard: React.FC<ABTestDashboardProps> = ({
	selectedTestId,
	dateRange,
}) => {
	const { formatMessage } = useIntl();
	const toggleNotification = useNotification();
	const [metrics, setMetrics] = useState<DashboardMetrics | null>(null);
	const [testPerformance, setTestPerformance] = useState<TestPerformance[]>([]);
	const [loading, setLoading] = useState(true);
	const [timeRange, setTimeRange] = useState("7d");
	const [_refreshInterval, setRefreshInterval] =
		useState<NodeJS.Timeout | null>(null);

	useEffect(() => {
		fetchDashboardData();

		// Set up auto-refresh every 30 seconds
		const interval = setInterval(fetchDashboardData, 30000);
		setRefreshInterval(interval);

		return () => {
			if (interval) clearInterval(interval);
		};
	}, [fetchDashboardData]);

	const fetchDashboardData = async () => {
		try {
			setLoading(true);

			const params = new URLSearchParams({
				timeRange,
				...(selectedTestId && { testId: selectedTestId }),
				...(dateRange && {
					startDate: dateRange.start,
					endDate: dateRange.end,
				}),
			});

			const [metricsResponse, performanceResponse] = await Promise.all([
				fetch(`/api/ab-tests/metrics?${params}`),
				fetch(`/api/ab-tests/performance?${params}`),
			]);

			const metricsData = await metricsResponse.json();
			const performanceData = await performanceResponse.json();

			setMetrics(metricsData.data);
			setTestPerformance(performanceData.data || []);
		} catch (error) {
			console.error("Error fetching dashboard data:", error);
			toggleNotification({
				type: "warning",
				message: "Failed to load dashboard data",
			});
		} finally {
			setLoading(false);
		}
	};

	const exportData = async () => {
		try {
			const params = new URLSearchParams({
				timeRange,
				format: "csv",
				...(selectedTestId && { testId: selectedTestId }),
			});

			const response = await fetch(`/api/ab-tests/export?${params}`);
			const blob = await response.blob();

			const url = window.URL.createObjectURL(blob);
			const a = document.createElement("a");
			a.href = url;
			a.download = `ab-test-data-${new Date().toISOString().split("T")[0]}.csv`;
			document.body.appendChild(a);
			a.click();
			window.URL.revokeObjectURL(url);
			document.body.removeChild(a);

			toggleNotification({
				type: "success",
				message: "Data exported successfully",
			});
		} catch (error) {
			console.error("Error exporting data:", error);
			toggleNotification({
				type: "warning",
				message: "Failed to export data",
			});
		}
	};

	const formatTrend = (value: number) => {
		const isPositive = value >= 0;
		return {
			value: Math.abs(value).toFixed(1),
			isPositive,
			icon: isPositive ? <TrendUp /> : <TrendDown />,
			color: isPositive ? "success600" : "danger600",
		};
	};

	const getStatusColor = (status: string) => {
		switch (status) {
			case "running":
				return "success";
			case "paused":
				return "warning";
			case "completed":
				return "secondary";
			default:
				return "neutral";
		}
	};

	if (loading && !metrics) {
		return (
			<Box padding={8}>
				<Flex justifyContent="center">
					<Loader>Loading dashboard...</Loader>
				</Flex>
			</Box>
		);
	}

	return (
		<Box>
			{/* Header */}
			<Flex justifyContent="space-between" alignItems="center" marginBottom={6}>
				<Typography variant="alpha">A/B Test Performance Dashboard</Typography>
				<Flex gap={2}>
					<Select
						value={timeRange}
						onChange={setTimeRange}
						placeholder="Time Range"
					>
						<Option value="1d">Last 24 Hours</Option>
						<Option value="7d">Last 7 Days</Option>
						<Option value="30d">Last 30 Days</Option>
						<Option value="90d">Last 90 Days</Option>
					</Select>
					<Button
						variant="tertiary"
						startIcon={<Refresh />}
						onClick={fetchDashboardData}
						loading={loading}
					>
						Refresh
					</Button>
					<Button
						variant="secondary"
						startIcon={<Download />}
						onClick={exportData}
					>
						Export Data
					</Button>
				</Flex>
			</Flex>

			{/* Key Metrics */}
			{metrics && (
				<Grid gap={4} marginBottom={6}>
					<GridItem col={3}>
						<MetricCard>
							<CardBody>
								<Stack spacing={2}>
									<Typography variant="omega" textColor="neutral600">
										Total Tests
									</Typography>
									<Typography variant="alpha">{metrics.totalTests}</Typography>
									<TrendIndicator>
										{formatTrend(metrics.trends.tests).icon}
										<Typography
											variant="pi"
											textColor={formatTrend(metrics.trends.tests).color}
										>
											{formatTrend(metrics.trends.tests).value}% vs last period
										</Typography>
									</TrendIndicator>
								</Stack>
							</CardBody>
						</MetricCard>
					</GridItem>

					<GridItem col={3}>
						<MetricCard>
							<CardBody>
								<Stack spacing={2}>
									<Typography variant="omega" textColor="neutral600">
										Avg. Conversion Rate
									</Typography>
									<Typography variant="alpha">
										{metrics.avgConversionRate.toFixed(2)}%
									</Typography>
									<TrendIndicator>
										{formatTrend(metrics.trends.conversionRate).icon}
										<Typography
											variant="pi"
											textColor={
												formatTrend(metrics.trends.conversionRate).color
											}
										>
											{formatTrend(metrics.trends.conversionRate).value}% vs
											last period
										</Typography>
									</TrendIndicator>
								</Stack>
							</CardBody>
						</MetricCard>
					</GridItem>

					<GridItem col={3}>
						<MetricCard>
							<CardBody>
								<Stack spacing={2}>
									<Typography variant="omega" textColor="neutral600">
										Total Revenue
									</Typography>
									<Typography variant="alpha">
										${metrics.totalRevenue.toLocaleString()}
									</Typography>
									<TrendIndicator>
										{formatTrend(metrics.trends.revenue).icon}
										<Typography
											variant="pi"
											textColor={formatTrend(metrics.trends.revenue).color}
										>
											{formatTrend(metrics.trends.revenue).value}% vs last
											period
										</Typography>
									</TrendIndicator>
								</Stack>
							</CardBody>
						</MetricCard>
					</GridItem>

					<GridItem col={3}>
						<MetricCard>
							<CardBody>
								<Stack spacing={2}>
									<Typography variant="omega" textColor="neutral600">
										Active Tests
									</Typography>
									<Typography variant="alpha">{metrics.activeTests}</Typography>
									<Typography variant="pi" textColor="neutral600">
										{metrics.completedTests} completed
									</Typography>
								</Stack>
							</CardBody>
						</MetricCard>
					</GridItem>
				</Grid>
			)}

			{/* Charts Section */}
			<Grid gap={4} marginBottom={6}>
				<GridItem col={6}>
					<Card>
						<CardHeader>
							<Typography variant="delta">Conversion Rate Trends</Typography>
						</CardHeader>
						<CardBody>
							<ChartContainer>
								<Stack spacing={2} alignItems="center">
									<LineChart size="L" />
									<Typography variant="omega" textColor="neutral500">
										Conversion rate over time chart would be rendered here
									</Typography>
								</Stack>
							</ChartContainer>
						</CardBody>
					</Card>
				</GridItem>

				<GridItem col={6}>
					<Card>
						<CardHeader>
							<Typography variant="delta">Revenue Performance</Typography>
						</CardHeader>
						<CardBody>
							<ChartContainer>
								<Stack spacing={2} alignItems="center">
									<BarChart size="L" />
									<Typography variant="omega" textColor="neutral500">
										Revenue comparison chart would be rendered here
									</Typography>
								</Stack>
							</ChartContainer>
						</CardBody>
					</Card>
				</GridItem>
			</Grid>

			{/* Test Performance Table */}
			<Card>
				<CardHeader>
					<Typography variant="delta">Test Performance Overview</Typography>
				</CardHeader>
				<CardBody>
					<Stack spacing={4}>
						{testPerformance.map((test) => (
							<Box
								key={test.testId}
								padding={4}
								backgroundColor="neutral100"
								borderRadius="4px"
							>
								<Grid gap={4}>
									<GridItem col={4}>
										<Stack spacing={1}>
											<Flex alignItems="center" gap={2}>
												<Typography variant="epsilon">
													{test.testName}
												</Typography>
												<Badge backgroundColor={getStatusColor(test.status)}>
													{test.status}
												</Badge>
											</Flex>
											<Typography variant="pi" textColor="neutral600">
												{test.variations} variations • Started{" "}
												{new Date(test.startDate).toLocaleDateString()}
											</Typography>
										</Stack>
									</GridItem>

									<GridItem col={2}>
										<Stack spacing={1}>
											<Typography variant="pi" textColor="neutral600">
												Conversion Rate
											</Typography>
											<Typography variant="epsilon">
												{test.conversionRate.toFixed(2)}%
											</Typography>
										</Stack>
									</GridItem>

									<GridItem col={2}>
										<Stack spacing={1}>
											<Typography variant="pi" textColor="neutral600">
												Revenue
											</Typography>
											<Typography variant="epsilon">
												${test.revenue.toLocaleString()}
											</Typography>
										</Stack>
									</GridItem>

									<GridItem col={2}>
										<Stack spacing={1}>
											<Typography variant="pi" textColor="neutral600">
												Impressions
											</Typography>
											<Typography variant="epsilon">
												{test.impressions.toLocaleString()}
											</Typography>
										</Stack>
									</GridItem>

									<GridItem col={2}>
										<Stack spacing={1}>
											<Typography variant="pi" textColor="neutral600">
												Confidence
											</Typography>
											<Typography
												variant="epsilon"
												textColor={
													test.confidence >= 95
														? "success600"
														: test.confidence >= 90
															? "warning600"
															: "danger600"
												}
											>
												{test.confidence.toFixed(1)}%
											</Typography>
										</Stack>
									</GridItem>
								</Grid>
							</Box>
						))}

						{testPerformance.length === 0 && (
							<Box padding={8} textAlign="center">
								<Typography variant="omega" textColor="neutral500">
									No test data available for the selected time range
								</Typography>
							</Box>
						)}
					</Stack>
				</CardBody>
			</Card>

			{/* Real-time Status */}
			<Box marginTop={4}>
				<Alert variant="default" title="Real-time Monitoring">
					<Typography>
						Dashboard updates automatically every 30 seconds. Last updated:{" "}
						{new Date().toLocaleTimeString()}
					</Typography>
				</Alert>
			</Box>
		</Box>
	);
};

export default ABTestDashboard;
