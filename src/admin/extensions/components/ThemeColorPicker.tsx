/**
 * Custom color picker component for theme configuration
 */

import {
	Box,
	Flex,
	Grid,
	GridItem,
	TextInput,
	Typography,
} from "@strapi/design-system";
import { useCMEditViewDataManager } from "@strapi/helper-plugin";
import { useEffect, useState } from "react";
import { ChromePicker } from "react-color";
import { useIntl } from "react-intl";
import styled from "styled-components";

// Styled components for the color picker
const ColorPickerContainer = styled(Box)`
  margin-bottom: 16px;
`;

const ColorPreview = styled.div`
  width: 36px;
  height: 36px;
  border-radius: 4px;
  border: 1px solid ${({ theme }) => theme.colors.neutral200};
  background-color: ${({ color }) => color};
  cursor: pointer;
  margin-right: 8px;
`;

const PopoverContainer = styled.div`
  position: absolute;
  z-index: 2;
  margin-top: 8px;
`;

const PopoverCover = styled.div`
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
`;

const ThemeColorPicker = ({ name, attribute, value, onChange }) => {
	const { formatMessage } = useIntl();
	const { modifiedData, layout } = useCMEditViewDataManager();
	const [showPicker, setShowPicker] = useState({
		primary: false,
		background: false,
		text: false,
	});
	const [colors, setColors] = useState({
		primary_color: "#007AFF",
		background_color: "#FFFFFF",
		text_color: "#000000",
	});

	// Initialize colors from value or modifiedData
	useEffect(() => {
		if (value) {
			setColors({
				primary_color: value.primary_color || "#007AFF",
				background_color: value.background_color || "#FFFFFF",
				text_color: value.text_color || "#000000",
			});
		} else if (modifiedData?.theme) {
			setColors({
				primary_color: modifiedData.theme.primary_color || "#007AFF",
				background_color: modifiedData.theme.background_color || "#FFFFFF",
				text_color: modifiedData.theme.text_color || "#000000",
			});
		}
	}, [value, modifiedData]);

	// Handle color change
	const handleColorChange = (colorType, color) => {
		const newColors = { ...colors, [colorType]: color.hex };
		setColors(newColors);

		// Update form value
		const newValue = { ...(value || {}), [colorType]: color.hex };
		onChange({ target: { name, value: newValue } });
	};

	// Toggle color picker visibility
	const togglePicker = (colorType) => {
		setShowPicker({
			...showPicker,
			[colorType]: !showPicker[colorType],
		});
	};

	// Handle manual color input
	const handleColorInput = (colorType, e) => {
		const colorValue = e.target.value;

		// Validate hex color format
		if (/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(colorValue)) {
			const newColors = { ...colors, [colorType]: colorValue };
			setColors(newColors);

			// Update form value
			const newValue = { ...(value || {}), [colorType]: colorValue };
			onChange({ target: { name, value: newValue } });
		}
	};

	return (
		<ColorPickerContainer>
			<Typography variant="beta" marginBottom={4}>
				{formatMessage({
					id: "app.components.ThemeColorPicker.title",
					defaultMessage: "Theme Colors",
				})}
			</Typography>

			<Grid gap={4}>
				{/* Primary Color */}
				<GridItem col={6}>
					<Box marginBottom={4}>
						<Typography variant="pi" fontWeight="bold">
							{formatMessage({
								id: "app.components.ThemeColorPicker.primary",
								defaultMessage: "Primary Color",
							})}
						</Typography>
						<Flex marginTop={2}>
							<ColorPreview
								color={colors.primary_color}
								onClick={() => togglePicker("primary_color")}
							/>
							<TextInput
								aria-label="Primary color hex"
								name="primary_color"
								value={colors.primary_color}
								onChange={(e) => handleColorInput("primary_color", e)}
								placeholder="#007AFF"
							/>
						</Flex>
						{showPicker.primary_color && (
							<PopoverContainer>
								<PopoverCover onClick={() => togglePicker("primary_color")} />
								<ChromePicker
									color={colors.primary_color}
									onChange={(color) =>
										handleColorChange("primary_color", color)
									}
									disableAlpha
								/>
							</PopoverContainer>
						)}
					</Box>
				</GridItem>

				{/* Background Color */}
				<GridItem col={6}>
					<Box marginBottom={4}>
						<Typography variant="pi" fontWeight="bold">
							{formatMessage({
								id: "app.components.ThemeColorPicker.background",
								defaultMessage: "Background Color",
							})}
						</Typography>
						<Flex marginTop={2}>
							<ColorPreview
								color={colors.background_color}
								onClick={() => togglePicker("background_color")}
							/>
							<TextInput
								aria-label="Background color hex"
								name="background_color"
								value={colors.background_color}
								onChange={(e) => handleColorInput("background_color", e)}
								placeholder="#FFFFFF"
							/>
						</Flex>
						{showPicker.background_color && (
							<PopoverContainer>
								<PopoverCover
									onClick={() => togglePicker("background_color")}
								/>
								<ChromePicker
									color={colors.background_color}
									onChange={(color) =>
										handleColorChange("background_color", color)
									}
									disableAlpha
								/>
							</PopoverContainer>
						)}
					</Box>
				</GridItem>

				{/* Text Color */}
				<GridItem col={6}>
					<Box marginBottom={4}>
						<Typography variant="pi" fontWeight="bold">
							{formatMessage({
								id: "app.components.ThemeColorPicker.text",
								defaultMessage: "Text Color",
							})}
						</Typography>
						<Flex marginTop={2}>
							<ColorPreview
								color={colors.text_color}
								onClick={() => togglePicker("text_color")}
							/>
							<TextInput
								aria-label="Text color hex"
								name="text_color"
								value={colors.text_color}
								onChange={(e) => handleColorInput("text_color", e)}
								placeholder="#000000"
							/>
						</Flex>
						{showPicker.text_color && (
							<PopoverContainer>
								<PopoverCover onClick={() => togglePicker("text_color")} />
								<ChromePicker
									color={colors.text_color}
									onChange={(color) => handleColorChange("text_color", color)}
									disableAlpha
								/>
							</PopoverContainer>
						)}
					</Box>
				</GridItem>
			</Grid>

			{/* Color Preview */}
			<Box
				marginTop={4}
				padding={4}
				background={colors.background_color}
				borderRadius="4px"
				borderColor="neutral200"
				borderStyle="solid"
				borderWidth="1px"
			>
				<Typography textColor={colors.text_color} fontWeight="bold">
					Sample Text
				</Typography>
				<Box
					marginTop={2}
					padding={2}
					background={colors.primary_color}
					borderRadius="4px"
					width="120px"
					textAlign="center"
				>
					<Typography textColor="white">Button</Typography>
				</Box>
			</Box>
		</ColorPickerContainer>
	);
};

export default ThemeColorPicker;
