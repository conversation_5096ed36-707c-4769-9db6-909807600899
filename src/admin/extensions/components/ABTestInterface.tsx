/**
 * A/B Test Interface - Main navigation and management interface for A/B testing
 */

import {
	Alert,
	Box,
	Button,
	Card,
	CardBody,
	Flex,
	Grid,
	GridItem,
	Stack,
	// TabList, // Not available in current version
	Tab,
	TabGroup,
	TabPanel,
	TabPanels,
	Typography,
} from "@strapi/design-system";
import { useNotification } from "@strapi/helper-plugin";
import { Refresh } from "@strapi/icons";
import type React from "react";
import { useEffect, useState } from "react";
import { useIntl } from "react-intl";
import styled from "styled-components";
import ABTestDashboard from "./ABTestDashboard";
import ABTestManager from "./ABTestManager";
import VariationComparison from "./VariationComparison";

const InterfaceContainer = styled(Box)`
  padding: 24px;
  background: ${({ theme }) => theme.colors.neutral0};
  min-height: 100vh;
`;

const TabContainer = styled(Box)`
  margin-top: 16px;
`;

interface ABTestInterfaceProps {
	testId?: string;
	initialTab?: "manager" | "dashboard" | "comparison";
}

const ABTestInterface: React.FC<ABTestInterfaceProps> = ({
	testId,
	initialTab = "manager",
}) => {
	const { formatMessage } = useIntl();
	const toggleNotification = useNotification();
	const [_activeTab, setActiveTab] = useState(initialTab);
	const [tests, setTests] = useState([]);
	const [selectedTest, setSelectedTest] = useState(testId || null);
	const [loading, setLoading] = useState(false);
	const [_refreshKey, setRefreshKey] = useState(0);

	// Fetch A/B tests
	const fetchTests = async () => {
		setLoading(true);
		try {
			const response = await fetch("/api/ab-tests?populate=*");
			const data = await response.json();
			setTests(data.data || []);
		} catch (_error) {
			toggleNotification({
				type: "warning",
				message: "Failed to fetch A/B tests",
			});
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		fetchTests();
	}, [fetchTests]);

	const handleRefresh = () => {
		setRefreshKey((prev) => prev + 1);
	};

	const handleTestSelect = (test) => {
		setSelectedTest(test.id);
	};

	const handleTestCreated = (newTest) => {
		setTests((prev) => [...prev, newTest]);
		setSelectedTest(newTest.id);
		setActiveTab("dashboard");
		toggleNotification({
			type: "success",
			message: "A/B test created successfully",
		});
	};

	const getTabLabel = (tab: string) => {
		const labels = {
			manager: formatMessage({
				id: "app.components.ABTestInterface.manager",
				defaultMessage: "Test Manager",
			}),
			dashboard: formatMessage({
				id: "app.components.ABTestInterface.dashboard",
				defaultMessage: "Performance Dashboard",
			}),
			comparison: formatMessage({
				id: "app.components.ABTestInterface.comparison",
				defaultMessage: "Variation Comparison",
			}),
		};
		return labels[tab];
	};

	const activeTests = tests.filter(
		(test) => test.attributes.status === "active",
	);
	const completedTests = tests.filter(
		(test) => test.attributes.status === "completed",
	);

	return (
		<InterfaceContainer>
			{/* Header */}
			<Flex justifyContent="space-between" alignItems="center">
				<Box>
					<Typography variant="alpha" fontWeight="bold">
						{formatMessage({
							id: "app.components.ABTestInterface.title",
							defaultMessage: "A/B Test Management",
						})}
					</Typography>
					<Typography variant="omega" textColor="neutral600">
						{formatMessage({
							id: "app.components.ABTestInterface.subtitle",
							defaultMessage: "Create, manage, and analyze paywall A/B tests",
						})}
					</Typography>
				</Box>
				<Flex gap={2}>
					<Button
						variant="secondary"
						startIcon={<Refresh />}
						onClick={handleRefresh}
						loading={loading}
					>
						{formatMessage({
							id: "app.components.ABTestInterface.refresh",
							defaultMessage: "Refresh",
						})}
					</Button>
				</Flex>
			</Flex>

			{/* Quick Stats */}
			<Grid gap={4} gridCols={4} style={{ marginTop: "24px" }}>
				<GridItem>
					<Card>
						<CardBody>
							<Stack spacing={2}>
								<Typography variant="sigma" textColor="neutral600">
									Total Tests
								</Typography>
								<Typography variant="alpha" fontWeight="bold">
									{tests.length}
								</Typography>
							</Stack>
						</CardBody>
					</Card>
				</GridItem>
				<GridItem>
					<Card>
						<CardBody>
							<Stack spacing={2}>
								<Typography variant="sigma" textColor="neutral600">
									Active Tests
								</Typography>
								<Typography
									variant="alpha"
									fontWeight="bold"
									textColor="success600"
								>
									{activeTests.length}
								</Typography>
							</Stack>
						</CardBody>
					</Card>
				</GridItem>
				<GridItem>
					<Card>
						<CardBody>
							<Stack spacing={2}>
								<Typography variant="sigma" textColor="neutral600">
									Completed Tests
								</Typography>
								<Typography
									variant="alpha"
									fontWeight="bold"
									textColor="neutral600"
								>
									{completedTests.length}
								</Typography>
							</Stack>
						</CardBody>
					</Card>
				</GridItem>
				<GridItem>
					<Card>
						<CardBody>
							<Stack spacing={2}>
								<Typography variant="sigma" textColor="neutral600">
									Avg. Conversion Rate
								</Typography>
								<Typography
									variant="alpha"
									fontWeight="bold"
									textColor="primary600"
								>
									{activeTests.length > 0
										? `${(activeTests.reduce((acc, test) => acc + (test.attributes.conversionRate || 0), 0) / activeTests.length).toFixed(1)}%`
										: "0%"}
								</Typography>
							</Stack>
						</CardBody>
					</Card>
				</GridItem>
			</Grid>

			{/* Main Interface */}
			<TabContainer>
				<TabGroup
					label="A/B Test Management Tabs"
					id="ab-test-tabs"
					onTabChange={(index) => {
						const tabs = ["manager", "dashboard", "comparison"];
						setActiveTab(tabs[index]);
					}}
				>
					<TabList>
						<Tab>{getTabLabel("manager")}</Tab>
						<Tab disabled={!selectedTest}>{getTabLabel("dashboard")}</Tab>
						<Tab disabled={!selectedTest}>{getTabLabel("comparison")}</Tab>
					</TabList>
					<TabPanels>
						<TabPanel>
							<ABTestManager
								tests={tests}
								onTestSelect={handleTestSelect}
								onTestCreated={handleTestCreated}
								onRefresh={handleRefresh}
							/>
						</TabPanel>
						<TabPanel>
							{selectedTest ? (
								<ABTestDashboard
									testId={selectedTest}
									onRefresh={handleRefresh}
								/>
							) : (
								<Alert variant="default" title="No test selected">
									Please select a test from the Test Manager to view its
									dashboard.
								</Alert>
							)}
						</TabPanel>
						<TabPanel>
							{selectedTest ? (
								<VariationComparison
									testId={selectedTest}
									onRefresh={handleRefresh}
								/>
							) : (
								<Alert variant="default" title="No test selected">
									Please select a test from the Test Manager to compare
									variations.
								</Alert>
							)}
						</TabPanel>
					</TabPanels>
				</TabGroup>
			</TabContainer>
		</InterfaceContainer>
	);
};

export default ABTestInterface;
