/**
 * Bulk operations component for managing multiple paywalls
 */

import {
	Alert,
	Badge,
	Box,
	Button,
	Checkbox,
	Flex,
	IconButton,
	ModalBody,
	Modal<PERSON>ooter,
	ModalHeader,
	ModalLayout,
	Option,
	Select,
	Stack,
	Table,
	Tbody,
	Td,
	Th,
	Thead,
	Tr,
	Typography,
} from "@strapi/design-system";
import { useNotification } from "@strapi/helper-plugin";
import { Archive, Duplicate, Eye, EyeStriked } from "@strapi/icons";
import type React from "react";
import { useState } from "react";
import { useIntl } from "react-intl";

interface Paywall {
	id: number;
	name: string;
	placement_id: string;
	status: string;
	title: string;
	adapty_sync_status: string;
	publishedAt?: string;
}

interface BulkPaywallOperationsProps {
	paywalls: Paywall[];
	onRefresh: () => void;
}

const BulkPaywallOperations: React.FC<BulkPaywallOperationsProps> = ({
	paywalls,
	onRefresh,
}) => {
	const { formatMessage } = useIntl();
	const toggleNotification = useNotification();
	const [selectedPaywalls, setSelectedPaywalls] = useState<number[]>([]);
	const [bulkOperation, setBulkOperation] = useState<string>("");
	const [showConfirmModal, setShowConfirmModal] = useState(false);
	const [isLoading, setIsLoading] = useState(false);

	// Handle select all/none
	const handleSelectAll = () => {
		if (selectedPaywalls.length === paywalls.length) {
			setSelectedPaywalls([]);
		} else {
			setSelectedPaywalls(paywalls.map((p) => p.id));
		}
	};

	// Handle individual selection
	const handleSelectPaywall = (paywallId: number) => {
		setSelectedPaywalls((prev) =>
			prev.includes(paywallId)
				? prev.filter((id) => id !== paywallId)
				: [...prev, paywallId],
		);
	};

	// Execute bulk operation
	const executeBulkOperation = async () => {
		if (!bulkOperation || selectedPaywalls.length === 0) return;

		setIsLoading(true);
		try {
			const promises = selectedPaywalls.map(async (paywallId) => {
				switch (bulkOperation) {
					case "publish":
						return strapi.entityService.update(
							"api::paywall.paywall",
							paywallId,
							{
								data: { publishedAt: new Date() },
							},
						);
					case "unpublish":
						return strapi.entityService.update(
							"api::paywall.paywall",
							paywallId,
							{
								data: { publishedAt: null },
							},
						);
					case "archive":
						return strapi.entityService.update(
							"api::paywall.paywall",
							paywallId,
							{
								data: { status: "archived" },
							},
						);
					case "delete":
						return strapi.entityService.delete(
							"api::paywall.paywall",
							paywallId,
						);
					case "sync":
						// Trigger Adapty sync for selected paywalls
						return fetch(`/api/paywalls/${paywallId}/sync`, { method: "POST" });
					default:
						return Promise.resolve();
				}
			});

			await Promise.all(promises);

			toggleNotification({
				type: "success",
				message: formatMessage({
					id: "bulk-operations.success",
					defaultMessage: `Successfully ${bulkOperation}ed ${selectedPaywalls.length} paywall(s)`,
				}),
			});

			setSelectedPaywalls([]);
			setBulkOperation("");
			setShowConfirmModal(false);
			onRefresh();
		} catch (_error) {
			toggleNotification({
				type: "warning",
				message: formatMessage({
					id: "bulk-operations.error",
					defaultMessage:
						"Some operations failed. Please check individual paywall status.",
				}),
			});
		} finally {
			setIsLoading(false);
		}
	};

	const getStatusBadge = (status: string) => {
		const statusConfig = {
			draft: { variant: "secondary", children: "Draft" },
			review: { variant: "alternative", children: "Review" },
			approved: { variant: "success", children: "Approved" },
			published: { variant: "success", children: "Published" },
			archived: { variant: "danger", children: "Archived" },
		};
		return statusConfig[status] || { variant: "secondary", children: status };
	};

	const getSyncStatusBadge = (syncStatus: string) => {
		const statusConfig = {
			pending: { variant: "alternative", children: "Pending" },
			synced: { variant: "success", children: "Synced" },
			error: { variant: "danger", children: "Error" },
		};
		return (
			statusConfig[syncStatus] || { variant: "secondary", children: syncStatus }
		);
	};

	return (
		<Box>
			{/* Bulk Operations Toolbar */}
			<Box padding={4} background="neutral100" marginBottom={4}>
				<Flex justifyContent="space-between" alignItems="center">
					<Typography variant="omega" fontWeight="bold">
						{formatMessage({
							id: "bulk-operations.title",
							defaultMessage: "Bulk Operations",
						})}
					</Typography>

					<Flex gap={2}>
						<Select
							placeholder={formatMessage({
								id: "bulk-operations.select-operation",
								defaultMessage: "Select operation",
							})}
							value={bulkOperation}
							onChange={setBulkOperation}
							disabled={selectedPaywalls.length === 0}
						>
							<Option value="publish">
								{formatMessage({
									id: "bulk-operations.publish",
									defaultMessage: "Publish",
								})}
							</Option>
							<Option value="unpublish">
								{formatMessage({
									id: "bulk-operations.unpublish",
									defaultMessage: "Unpublish",
								})}
							</Option>
							<Option value="archive">
								{formatMessage({
									id: "bulk-operations.archive",
									defaultMessage: "Archive",
								})}
							</Option>
							<Option value="sync">
								{formatMessage({
									id: "bulk-operations.sync",
									defaultMessage: "Sync with Adapty",
								})}
							</Option>
							<Option value="delete">
								{formatMessage({
									id: "bulk-operations.delete",
									defaultMessage: "Delete",
								})}
							</Option>
						</Select>

						<Button
							variant="default"
							onClick={() => setShowConfirmModal(true)}
							disabled={!bulkOperation || selectedPaywalls.length === 0}
						>
							{formatMessage({
								id: "bulk-operations.execute",
								defaultMessage: "Execute ({count})",
								values: { count: selectedPaywalls.length },
							})}
						</Button>
					</Flex>
				</Flex>

				{selectedPaywalls.length > 0 && (
					<Box paddingTop={2}>
						<Alert
							variant="default"
							title={formatMessage({
								id: "bulk-operations.selected-info",
								defaultMessage: "{count} paywall(s) selected",
								values: { count: selectedPaywalls.length },
							})}
						>
							{formatMessage({
								id: "bulk-operations.selected-description",
								defaultMessage:
									"The selected operation will be applied to all selected paywalls.",
							})}
						</Alert>
					</Box>
				)}
			</Box>

			{/* Paywall Table */}
			<Table colCount={7} rowCount={paywalls.length + 1}>
				<Thead>
					<Tr>
						<Th>
							<Checkbox
								checked={
									selectedPaywalls.length === paywalls.length &&
									paywalls.length > 0
								}
								indeterminate={
									selectedPaywalls.length > 0 &&
									selectedPaywalls.length < paywalls.length
								}
								onChange={handleSelectAll}
							/>
						</Th>
						<Th>
							<Typography variant="sigma">
								{formatMessage({
									id: "bulk-operations.table.name",
									defaultMessage: "Name",
								})}
							</Typography>
						</Th>
						<Th>
							<Typography variant="sigma">
								{formatMessage({
									id: "bulk-operations.table.placement",
									defaultMessage: "Placement ID",
								})}
							</Typography>
						</Th>
						<Th>
							<Typography variant="sigma">
								{formatMessage({
									id: "bulk-operations.table.status",
									defaultMessage: "Status",
								})}
							</Typography>
						</Th>
						<Th>
							<Typography variant="sigma">
								{formatMessage({
									id: "bulk-operations.table.sync-status",
									defaultMessage: "Sync Status",
								})}
							</Typography>
						</Th>
						<Th>
							<Typography variant="sigma">
								{formatMessage({
									id: "bulk-operations.table.published",
									defaultMessage: "Published",
								})}
							</Typography>
						</Th>
						<Th>
							<Typography variant="sigma">
								{formatMessage({
									id: "bulk-operations.table.actions",
									defaultMessage: "Actions",
								})}
							</Typography>
						</Th>
					</Tr>
				</Thead>
				<Tbody>
					{paywalls.map((paywall) => (
						<Tr key={paywall.id}>
							<Td>
								<Checkbox
									checked={selectedPaywalls.includes(paywall.id)}
									onChange={() => handleSelectPaywall(paywall.id)}
								/>
							</Td>
							<Td>
								<Typography textColor="neutral800" fontWeight="bold">
									{paywall.name}
								</Typography>
								<Typography variant="pi" textColor="neutral600">
									{paywall.title}
								</Typography>
							</Td>
							<Td>
								<Typography variant="omega" fontFamily="mono">
									{paywall.placement_id}
								</Typography>
							</Td>
							<Td>
								<Badge {...getStatusBadge(paywall.status)} />
							</Td>
							<Td>
								<Badge {...getSyncStatusBadge(paywall.adapty_sync_status)} />
							</Td>
							<Td>
								{paywall.publishedAt ? (
									<Flex alignItems="center" gap={1}>
										<Eye width="12px" height="12px" />
										<Typography variant="pi">
											{formatMessage({
												id: "bulk-operations.published",
												defaultMessage: "Yes",
											})}
										</Typography>
									</Flex>
								) : (
									<Flex alignItems="center" gap={1}>
										<EyeStriked width="12px" height="12px" />
										<Typography variant="pi">
											{formatMessage({
												id: "bulk-operations.unpublished",
												defaultMessage: "No",
											})}
										</Typography>
									</Flex>
								)}
							</Td>
							<Td>
								<Flex gap={1}>
									<IconButton
										label={formatMessage({
											id: "bulk-operations.duplicate",
											defaultMessage: "Duplicate",
										})}
										icon={<Duplicate />}
										onClick={() => {
											// Handle duplicate action
										}}
									/>
									<IconButton
										label={formatMessage({
											id: "bulk-operations.archive",
											defaultMessage: "Archive",
										})}
										icon={<Archive />}
										onClick={() => {
											// Handle archive action
										}}
									/>
								</Flex>
							</Td>
						</Tr>
					))}
				</Tbody>
			</Table>

			{/* Confirmation Modal */}
			{showConfirmModal && (
				<ModalLayout
					onClose={() => setShowConfirmModal(false)}
					labelledBy="confirm-bulk-operation"
				>
					<ModalHeader>
						<Typography
							fontWeight="bold"
							textColor="neutral800"
							as="h2"
							id="confirm-bulk-operation"
						>
							{formatMessage({
								id: "bulk-operations.confirm.title",
								defaultMessage: "Confirm Bulk Operation",
							})}
						</Typography>
					</ModalHeader>
					<ModalBody>
						<Stack spacing={4}>
							<Alert
								variant={bulkOperation === "delete" ? "danger" : "default"}
								title={formatMessage({
									id: "bulk-operations.confirm.warning",
									defaultMessage: "Are you sure?",
								})}
							>
								{formatMessage({
									id: "bulk-operations.confirm.description",
									defaultMessage:
										"You are about to {operation} {count} paywall(s). This action cannot be undone.",
									values: {
										operation: bulkOperation,
										count: selectedPaywalls.length,
									},
								})}
							</Alert>

							<Box>
								<Typography variant="pi" fontWeight="bold">
									{formatMessage({
										id: "bulk-operations.confirm.affected-paywalls",
										defaultMessage: "Affected paywalls:",
									})}
								</Typography>
								<Box paddingTop={2}>
									{paywalls
										.filter((p) => selectedPaywalls.includes(p.id))
										.map((paywall) => (
											<Typography key={paywall.id} variant="pi">
												• {paywall.name} ({paywall.placement_id})
											</Typography>
										))}
								</Box>
							</Box>
						</Stack>
					</ModalBody>
					<ModalFooter
						startActions={
							<Button
								variant="tertiary"
								onClick={() => setShowConfirmModal(false)}
							>
								{formatMessage({
									id: "bulk-operations.cancel",
									defaultMessage: "Cancel",
								})}
							</Button>
						}
						endActions={
							<Button
								variant={bulkOperation === "delete" ? "danger" : "default"}
								onClick={executeBulkOperation}
								loading={isLoading}
							>
								{formatMessage({
									id: "bulk-operations.confirm.execute",
									defaultMessage: "Execute {operation}",
									values: { operation: bulkOperation },
								})}
							</Button>
						}
					/>
				</ModalLayout>
			)}
		</Box>
	);
};

export default BulkPaywallOperations;
