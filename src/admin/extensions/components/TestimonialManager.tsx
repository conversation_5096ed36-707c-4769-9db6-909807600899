/**
 * Testimonial management component
 */

import {
	Box,
	Button,
	Card,
	CardAction,
	CardBody,
	CardContent,
	CardSubtitle,
	CardTitle,
	Flex,
	IconButton,
	ModalBody,
	Modal<PERSON>ooter,
	ModalHeader,
	ModalLayout,
	Stack,
	Textarea,
	TextInput,
	Typography,
} from "@strapi/design-system";
import { Drag, Pencil, Plus, Star, Trash } from "@strapi/icons";
import { useEffect, useState } from "react";
import { DragDropContext, Draggable, Droppable } from "react-beautiful-dnd";
import { useIntl } from "react-intl";
import styled from "styled-components";

// Styled components for testimonial management
const TestimonialCard = styled(Card)`
  margin-bottom: 16px;
  cursor: ${({ isDragging }) => (isDragging ? "grabbing" : "grab")};
  opacity: ${({ isDragging }) => (isDragging ? 0.8 : 1)};
`;

const DragHandle = styled(Box)`
  cursor: grab;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  color: ${({ theme }) => theme.colors.neutral500};
  
  &:hover {
    color: ${({ theme }) => theme.colors.neutral800};
  }
`;

const StarRating = styled(Flex)`
  margin-top: 8px;
`;

const StarIcon = styled(Star)`
  color: ${({ active, theme }) => (active ? theme.colors.warning500 : theme.colors.neutral300)};
  cursor: pointer;
  margin-right: 4px;
`;

const TestimonialManager = ({ name, attribute, value, onChange }) => {
	const { formatMessage } = useIntl();
	const [testimonials, setTestimonials] = useState([]);
	const [isModalVisible, setIsModalVisible] = useState(false);
	const [editingIndex, setEditingIndex] = useState(-1);
	const [newTestimonial, setNewTestimonial] = useState({
		author_name: "",
		author_title: "",
		company: "",
		content: "",
		rating: 5,
		order: 0,
	});

	// Initialize testimonials from value
	useEffect(() => {
		if (value && Array.isArray(value)) {
			// Ensure all testimonials have an order property
			const testimonialsWithOrder = value.map((testimonial, index) => ({
				...testimonial,
				order: testimonial.order !== undefined ? testimonial.order : index,
			}));

			// Sort by order
			setTestimonials(testimonialsWithOrder.sort((a, b) => a.order - b.order));
		} else {
			setTestimonials([]);
		}
	}, [value]);

	// Handle drag end
	const handleDragEnd = (result) => {
		if (!result.destination) return;

		const items = Array.from(testimonials);
		const [reorderedItem] = items.splice(result.source.index, 1);
		items.splice(result.destination.index, 0, reorderedItem);

		// Update order values
		const updatedItems = items.map((item, index) => ({
			...item,
			order: index,
		}));

		setTestimonials(updatedItems);
		onChange({ target: { name, value: updatedItems } });
	};

	// Handle add testimonial
	const handleAddTestimonial = () => {
		if (!newTestimonial.author_name || !newTestimonial.content) return;

		const updatedTestimonials = [
			...testimonials,
			{
				...newTestimonial,
				order: testimonials.length,
			},
		];

		setTestimonials(updatedTestimonials);
		onChange({ target: { name, value: updatedTestimonials } });
		resetForm();
	};

	// Handle edit testimonial
	const handleEditTestimonial = (index) => {
		setEditingIndex(index);
		setNewTestimonial({ ...testimonials[index] });
		setIsModalVisible(true);
	};

	// Handle update testimonial
	const handleUpdateTestimonial = () => {
		if (!newTestimonial.author_name || !newTestimonial.content) return;

		const updatedTestimonials = [...testimonials];
		updatedTestimonials[editingIndex] = newTestimonial;

		setTestimonials(updatedTestimonials);
		onChange({ target: { name, value: updatedTestimonials } });
		resetForm();
	};

	// Handle delete testimonial
	const handleDeleteTestimonial = (index) => {
		const updatedTestimonials = testimonials
			.filter((_, i) => i !== index)
			.map((testimonial, i) => ({ ...testimonial, order: i }));

		setTestimonials(updatedTestimonials);
		onChange({ target: { name, value: updatedTestimonials } });

		// If deleting the testimonial being edited, close the modal
		if (editingIndex === index) {
			resetForm();
		}
	};

	// Handle form input change
	const handleInputChange = (field, value) => {
		setNewTestimonial({
			...newTestimonial,
			[field]: value,
		});
	};

	// Handle rating change
	const handleRatingChange = (rating) => {
		setNewTestimonial({
			...newTestimonial,
			rating,
		});
	};

	// Reset form
	const resetForm = () => {
		setIsModalVisible(false);
		setEditingIndex(-1);
		setNewTestimonial({
			author_name: "",
			author_title: "",
			company: "",
			content: "",
			rating: 5,
			order: 0,
		});
	};

	return (
		<Box>
			<Flex justifyContent="space-between" alignItems="center" marginBottom={4}>
				<Typography variant="beta">
					{formatMessage({
						id: "app.components.TestimonialManager.title",
						defaultMessage: "Testimonial Management",
					})}
				</Typography>
				<Button startIcon={<Plus />} onClick={() => setIsModalVisible(true)}>
					{formatMessage({
						id: "app.components.TestimonialManager.addTestimonial",
						defaultMessage: "Add Testimonial",
					})}
				</Button>
			</Flex>

			{testimonials.length === 0 && (
				<Box
					padding={6}
					background="neutral100"
					borderRadius="4px"
					textAlign="center"
				>
					<Typography textColor="neutral600">
						No testimonials added yet. Click "Add Testimonial" to create your
						first testimonial.
					</Typography>
				</Box>
			)}

			{testimonials.length > 0 && (
				<Box marginTop={4}>
					<Typography variant="pi" fontWeight="bold" marginBottom={2}>
						{formatMessage({
							id: "app.components.FeatureManager.reorder",
							defaultMessage: "Drag to Reorder",
						})}
					</Typography>

					<DragDropContext onDragEnd={handleDragEnd}>
						<Droppable droppableId="testimonials">
							{(provided) => (
								<div {...provided.droppableProps} ref={provided.innerRef}>
									{testimonials.map((testimonial, index) => (
										<Draggable
											key={index}
											draggableId={`testimonial-${index}`}
											index={index}
										>
											{(provided, snapshot) => (
												<TestimonialCard
													ref={provided.innerRef}
													{...provided.draggableProps}
													isDragging={snapshot.isDragging}
												>
													<CardBody>
														<Flex>
															<DragHandle {...provided.dragHandleProps}>
																<Drag width="12px" height="12px" />
															</DragHandle>

															<CardContent>
																<CardTitle>{testimonial.author_name}</CardTitle>
																<CardSubtitle>
																	{testimonial.author_title}
																	{testimonial.company &&
																		testimonial.author_title &&
																		", "}
																	{testimonial.company}
																</CardSubtitle>
																<Box marginTop={2}>
																	<Typography variant="pi">
																		"
																		{testimonial.content.length > 100
																			? `${testimonial.content.substring(0, 100)}...`
																			: testimonial.content}
																		"
																	</Typography>
																</Box>
																<StarRating>
																	{[1, 2, 3, 4, 5].map((star) => (
																		<StarIcon
																			key={star}
																			active={star <= testimonial.rating}
																			width="14px"
																			height="14px"
																		/>
																	))}
																</StarRating>
															</CardContent>

															<CardAction position="end">
																<Stack horizontal spacing={2}>
																	<IconButton
																		onClick={() => handleEditTestimonial(index)}
																		label="Edit"
																		icon={<Pencil />}
																	/>
																	<IconButton
																		onClick={() =>
																			handleDeleteTestimonial(index)
																		}
																		label="Delete"
																		icon={<Trash />}
																	/>
																</Stack>
															</CardAction>
														</Flex>
													</CardBody>
												</TestimonialCard>
											)}
										</Draggable>
									))}
									{provided.placeholder}
								</div>
							)}
						</Droppable>
					</DragDropContext>
				</Box>
			)}

			{isModalVisible && (
				<ModalLayout onClose={resetForm} labelledBy="testimonial-modal-title">
					<ModalHeader>
						<Typography id="testimonial-modal-title" fontWeight="bold">
							{editingIndex >= 0 ? "Edit Testimonial" : "Add New Testimonial"}
						</Typography>
					</ModalHeader>
					<ModalBody>
						<Box marginBottom={4}>
							<TextInput
								label="Author Name"
								name="author_name"
								placeholder="John Doe"
								value={newTestimonial.author_name}
								onChange={(e) =>
									handleInputChange("author_name", e.target.value)
								}
								required
							/>
						</Box>

						<Box marginBottom={4}>
							<TextInput
								label="Author Title"
								name="author_title"
								placeholder="CEO"
								value={newTestimonial.author_title}
								onChange={(e) =>
									handleInputChange("author_title", e.target.value)
								}
							/>
						</Box>

						<Box marginBottom={4}>
							<TextInput
								label="Company"
								name="company"
								placeholder="Acme Inc."
								value={newTestimonial.company}
								onChange={(e) => handleInputChange("company", e.target.value)}
							/>
						</Box>

						<Box marginBottom={4}>
							<Textarea
								label="Testimonial Content"
								name="content"
								placeholder="This product has transformed our business..."
								value={newTestimonial.content}
								onChange={(e) => handleInputChange("content", e.target.value)}
								required
							/>
						</Box>

						<Box marginBottom={4}>
							<Typography variant="pi" fontWeight="bold">
								Rating
							</Typography>
							<Flex marginTop={2}>
								{[1, 2, 3, 4, 5].map((star) => (
									<StarIcon
										key={star}
										active={star <= newTestimonial.rating}
										width="24px"
										height="24px"
										onClick={() => handleRatingChange(star)}
									/>
								))}
							</Flex>
						</Box>
					</ModalBody>
					<ModalFooter
						startActions={
							<Button variant="tertiary" onClick={resetForm}>
								Cancel
							</Button>
						}
						endActions={
							<Button
								onClick={
									editingIndex >= 0
										? handleUpdateTestimonial
										: handleAddTestimonial
								}
								disabled={
									!newTestimonial.author_name || !newTestimonial.content
								}
							>
								{editingIndex >= 0 ? "Update Testimonial" : "Add Testimonial"}
							</Button>
						}
					/>
				</ModalLayout>
			)}
		</Box>
	);
};

export default TestimonialManager;
