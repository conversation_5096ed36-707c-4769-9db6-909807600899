{"collectionName": "components_shared_themes", "info": {"displayName": "Theme", "description": "Theme configuration for paywall appearance and styling"}, "options": {}, "attributes": {"name": {"type": "string", "required": true, "maxLength": 255}, "primary_color": {"type": "string", "required": true, "regex": "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$"}, "background_color": {"type": "string", "required": true, "regex": "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$"}, "text_color": {"type": "string", "required": true, "regex": "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$"}, "button_style": {"type": "enumeration", "enum": ["rounded", "square"], "default": "rounded"}, "gradient_colors": {"type": "json", "required": false}, "header_style": {"type": "enumeration", "enum": ["minimal", "hero", "gradient"], "default": "hero"}, "product_display_style": {"type": "enumeration", "enum": ["list", "grid", "carousel"], "default": "list"}, "show_features": {"type": "boolean", "default": true}, "show_testimonials": {"type": "boolean", "default": false}, "custom_styles": {"type": "text", "required": false}, "background_image": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}, "logo": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}}}