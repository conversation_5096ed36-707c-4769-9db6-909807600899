/**
 * Chrome MCP Monitoring Plugin Routes
 */

export default {
  type: 'admin',
  routes: [
    {
      method: 'GET',
      path: '/plugin-status',
      handler: 'monitoring.getPluginStatus',
      config: {
        policies: [],
        auth: false,
      },
    },
    {
      method: 'GET',
      path: '/plugin-config',
      handler: 'monitoring.getPluginConfig',
      config: {
        policies: [],
        auth: false,
      },
    },
  ],
};
