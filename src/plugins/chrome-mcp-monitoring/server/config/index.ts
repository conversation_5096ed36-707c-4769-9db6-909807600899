/**
 * Chrome MCP Monitoring Plugin Configuration
 */

export default {
  default: {
    // Default monitoring interval (30 seconds as per Story 2.1)
    monitoringInterval: 30000,
    
    // Alert thresholds
    alertThresholds: {
      responseTime: 2000, // 2 seconds
      connectionSuccessRate: 0.90, // 90%
      errorCount: 5,
      memoryUsage: 800, // 800 MB
      cpuUsage: 80, // 80%
    },
    
    // Data retention
    dataRetention: {
      metricsHistory: 100, // Keep last 100 metrics
      alertHistory: 50, // Keep last 50 alerts
    },
    
    // Auto-start monitoring
    autoStart: true,
  },
  validator() {},
};
