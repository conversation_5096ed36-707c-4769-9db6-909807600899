/**
 * Chrome MCP Monitoring Plugin Registration
 * Server-side plugin registration for Chrome MCP health monitoring
 */

import type { Core } from '@strapi/strapi';

export default ({ strapi }: { strapi: Core.Strapi }) => {
  // Register plugin
  console.log('🔌 Registering Chrome MCP Monitoring plugin...');
  
  // Plugin registration logic here
  // This is where you would register any custom content types, services, etc.
  
  console.log('✅ Chrome MCP Monitoring plugin registered');
};
