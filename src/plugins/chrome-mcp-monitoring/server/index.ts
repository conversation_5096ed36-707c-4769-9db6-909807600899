/**
 * Chrome MCP Monitoring Plugin Server
 * Server-side configuration for Chrome MCP health monitoring
 * Implements Story 2.1: Chrome MCP Server Health Monitoring Dashboard
 */

import register from './register';
import bootstrap from './bootstrap';
import destroy from './destroy';
import config from './config';
import contentTypes from './content-types';
import controllers from './controllers';
import routes from './routes';
import services from './services';
import middlewares from './middlewares';
import policies from './policies';

export default {
  register,
  bootstrap,
  destroy,
  config,
  controllers,
  routes,
  services,
  contentTypes,
  policies,
  middlewares,
};
