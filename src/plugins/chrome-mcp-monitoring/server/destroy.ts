/**
 * Chrome MCP Monitoring Plugin Destroy
 * Cleanup when plugin is destroyed
 */

import type { Core } from '@strapi/strapi';

export default ({ strapi }: { strapi: Core.Strapi }) => {
  console.log('🛑 Destroying Chrome MCP Monitoring plugin...');
  
  // Stop monitoring when plugin is destroyed
  try {
    const monitoringService = strapi.service('api::chrome-mcp-monitoring.monitoring');
    
    if (monitoringService && typeof monitoringService.stopMonitoring === 'function') {
      monitoringService.stopMonitoring();
      console.log('✅ Chrome MCP monitoring stopped');
    }
  } catch (error) {
    console.error('❌ Error stopping Chrome MCP monitoring:', error);
  }
  
  console.log('✅ Chrome MCP Monitoring plugin destroyed');
};
