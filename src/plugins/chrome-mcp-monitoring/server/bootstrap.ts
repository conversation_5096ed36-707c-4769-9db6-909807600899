/**
 * Chrome MCP Monitoring Plugin Bootstrap
 * Server-side plugin bootstrap for Chrome MCP health monitoring
 */

import type { Core } from '@strapi/strapi';

export default ({ strapi }: { strapi: Core.Strapi }) => {
  console.log('🚀 Bootstrapping Chrome MCP Monitoring plugin...');
  
  // Auto-start monitoring when the plugin loads
  setTimeout(async () => {
    try {
      // Get the monitoring service
      const monitoringService = strapi.service('api::chrome-mcp-monitoring.monitoring');
      
      if (monitoringService && typeof monitoringService.startMonitoring === 'function') {
        await monitoringService.startMonitoring();
        console.log('✅ Chrome MCP monitoring auto-started');
      } else {
        console.log('⚠️  Chrome MCP monitoring service not found, will start manually');
      }
    } catch (error) {
      console.error('❌ Failed to auto-start Chrome MCP monitoring:', error);
    }
  }, 5000); // Wait 5 seconds for Strapi to fully initialize
  
  console.log('✅ Chrome MCP Monitoring plugin bootstrapped');
};
