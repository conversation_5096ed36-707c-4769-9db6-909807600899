/**
 * Chrome MCP Monitoring Plugin Controller
 * Plugin-specific controller for Chrome MCP health monitoring
 */

import type { Core } from '@strapi/strapi';

export default ({ strapi }: { strapi: Core.Strapi }) => ({
  /**
   * Get plugin status
   */
  async getPluginStatus(ctx: any) {
    try {
      ctx.body = {
        success: true,
        data: {
          plugin: 'chrome-mcp-monitoring',
          version: '1.0.0',
          status: 'active',
          description: 'Chrome MCP Server Health Monitoring Dashboard'
        },
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      ctx.status = 500;
      ctx.body = {
        success: false,
        error: 'Failed to get plugin status',
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  },

  /**
   * Get plugin configuration
   */
  async getPluginConfig(ctx: any) {
    try {
      const config = strapi.config.get('plugin.chrome-mcp-monitoring', {});
      
      ctx.body = {
        success: true,
        data: config,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      ctx.status = 500;
      ctx.body = {
        success: false,
        error: 'Failed to get plugin configuration',
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
});
