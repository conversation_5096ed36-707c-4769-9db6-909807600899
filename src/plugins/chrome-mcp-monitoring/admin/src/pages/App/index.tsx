/**
 * Chrome MCP Monitoring Plugin App
 * Main application page for Chrome MCP health monitoring dashboard
 * Implements Story 2.1: Chrome MCP Server Health Monitoring Dashboard
 */

import { CheckPagePermissions } from '@strapi/helper-plugin';
import React from 'react';
import ChromeMCPDashboard from '../../../../../../admin/extensions/components/ChromeMCPDashboard';
import pluginPermissions from '../../permissions';

const App: React.FC = () => {
  return (
    <CheckPagePermissions permissions={pluginPermissions.main}>
      <ChromeMCPDashboard />
    </CheckPagePermissions>
  );
};

export default App;
