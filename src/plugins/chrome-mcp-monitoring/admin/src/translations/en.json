{"plugin.name": "Chrome MCP Monitoring", "plugin.description": "Real-time health monitoring dashboard for Chrome MCP server infrastructure", "dashboard.title": "Chrome MCP Health Monitoring", "dashboard.description": "Monitor Chrome MCP server performance, uptime, and connection reliability in real-time", "status.healthy": "Healthy", "status.degraded": "Degraded", "status.unhealthy": "Unhealthy", "metrics.serverStatus": "Server Status", "metrics.responseTime": "Response Time", "metrics.memoryUsage": "Memory Usage", "metrics.cpuUsage": "CPU Usage", "metrics.connectionReliability": "Connection Reliability", "metrics.performanceTrends": "Performance Trends", "metrics.activeAlerts": "Active Alerts", "metrics.uptime": "Uptime", "metrics.successRate": "Success Rate", "metrics.activeConnections": "Active Connections", "metrics.errorCount": "Error Count", "controls.liveMode": "Live Mode", "controls.enableLive": "Enable Live", "controls.refresh": "Refresh", "controls.startMonitoring": "Start Monitoring", "controls.stopMonitoring": "Stop Monitoring", "alerts.highResponseTime": "High Response Time", "alerts.lowConnectionRate": "Low Connection Success Rate", "alerts.errorsDetected": "Errors Detected", "alerts.serverStatus": "Server Status Alert", "story.implementationStatus": "Story 2.1 Implementation Status", "story.completedTasks": "Completed Tasks", "story.acceptanceCriteria": "Acceptance Criteria Status", "story.task1": "Task 1: Health Monitoring Infrastructure", "story.task2": "Task 2: Dashboard Backend Services", "story.task3": "Task 3: Visual Dashboard Frontend", "story.ac1": "Real-time Health Monitoring", "story.ac2": "Server Uptime Tracking", "story.ac3": "Performance Metrics", "story.ac4": "Connection Reliability", "story.ac8": "Zero Impact Testing"}