/**
 * Chrome MCP Monitoring Plugin
 * Admin plugin for Chrome MCP health monitoring dashboard
 * Implements Story 2.1: Chrome MCP Server Health Monitoring Dashboard
 */

import { prefixPluginTranslations } from '@strapi/helper-plugin';
import { Monitor } from '@strapi/icons';
import pluginPkg from '../../package.json';
import ChromeMCPDashboard from '../../../../admin/extensions/components/ChromeMCPDashboard';
import pluginId from './pluginId';

const name = pluginPkg.strapi.name;

export default {
  register(app: any) {
    // Register the plugin
    app.addMenuLink({
      to: `/plugins/${pluginId}`,
      icon: Monitor,
      intlLabel: {
        id: `${pluginId}.plugin.name`,
        defaultMessage: 'Chrome MCP Monitoring',
      },
      Component: async () => {
        const component = await import('./pages/App');
        return component;
      },
      permissions: [
        // Add permissions if needed
      ],
    });

    // Register the main dashboard page
    app.registerPlugin({
      id: pluginId,
      initializer: () => null,
      isReady: false,
      name,
    });
  },

  bootstrap(app: any) {
    // Plugin bootstrap logic
  },

  async registerTrads({ locales }: { locales: string[] }) {
    const importedTrads = await Promise.all(
      locales.map((locale) => {
        return import(`./translations/${locale}.json`)
          .then(({ default: data }) => {
            return {
              data: prefixPluginTranslations(data, pluginId),
              locale,
            };
          })
          .catch(() => {
            return {
              data: {},
              locale,
            };
          });
      })
    );

    return Promise.resolve(importedTrads);
  },
};
