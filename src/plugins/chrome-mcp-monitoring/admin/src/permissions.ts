/**
 * Chrome MCP Monitoring Plugin Permissions
 */

import pluginId from './pluginId';

const permissions = {
  // Main permission to access the plugin
  main: [
    {
      action: `plugin::${pluginId}.read`,
      subject: null,
    },
  ],
  // Permission to view monitoring data
  read: [
    {
      action: `plugin::${pluginId}.read`,
      subject: null,
    },
  ],
  // Permission to control monitoring (start/stop)
  control: [
    {
      action: `plugin::${pluginId}.control`,
      subject: null,
    },
  ],
};

export default permissions;
