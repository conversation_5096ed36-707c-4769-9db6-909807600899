/**
 * Chrome MCP Health Data Collector
 * Collects and stores health metrics data with minimal performance impact
 */

import { chromeMCPMonitor, ChromeMCPHealthMetrics, ChromeMCPUptimeRecord } from './chrome-mcp-monitor';

export interface HealthDataStorage {
  saveHealthMetrics(metrics: ChromeMCPHealthMetrics): Promise<void>;
  getRecentMetrics(limit?: number): Promise<ChromeMCPHealthMetrics[]>;
  getMetricsByTimeRange(startTime: Date, endTime: Date): Promise<ChromeMCPHealthMetrics[]>;
  saveUptimeRecord(record: ChromeMCPUptimeRecord): Promise<void>;
  getUptimeHistory(days?: number): Promise<ChromeMCPUptimeRecord[]>;
  cleanupOldData(retentionDays: number): Promise<void>;
}

export interface HealthCollectorConfig {
  retentionDays: number;
  batchSize: number;
  flushInterval: number; // milliseconds
  enableCompression: boolean;
}

class HealthDataCollector {
  private config: HealthCollectorConfig;
  private storage: HealthDataStorage;
  private metricsBuffer: ChromeMCPHealthMetrics[] = [];
  private uptimeBuffer: ChromeMCPUptimeRecord[] = [];
  private flushTimer?: NodeJS.Timeout;
  private isCollecting: boolean = false;

  constructor(storage: HealthDataStorage, config: HealthCollectorConfig) {
    this.storage = storage;
    this.config = config;
  }

  /**
   * Start collecting health data
   */
  startCollection(): void {
    if (this.isCollecting) {
      console.warn('Health data collection is already running');
      return;
    }

    console.log('Starting Chrome MCP health data collection...');
    this.isCollecting = true;

    // Listen to monitoring events
    chromeMCPMonitor.on('metrics-updated', this.handleMetricsUpdate.bind(this));
    chromeMCPMonitor.on('health-check-success', this.handleHealthCheckSuccess.bind(this));
    chromeMCPMonitor.on('health-check-failure', this.handleHealthCheckFailure.bind(this));

    // Start periodic flush timer
    this.flushTimer = setInterval(
      () => this.flushBuffers(),
      this.config.flushInterval
    );

    // Start daily uptime calculation
    this.scheduleUptimeCalculation();
  }

  /**
   * Stop collecting health data
   */
  stopCollection(): void {
    if (!this.isCollecting) {
      return;
    }

    console.log('Stopping Chrome MCP health data collection...');
    this.isCollecting = false;

    // Remove event listeners
    chromeMCPMonitor.removeAllListeners('metrics-updated');
    chromeMCPMonitor.removeAllListeners('health-check-success');
    chromeMCPMonitor.removeAllListeners('health-check-failure');

    // Clear flush timer
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = undefined;
    }

    // Flush remaining data
    this.flushBuffers();
  }

  /**
   * Handle metrics update from monitor
   */
  private handleMetricsUpdate(metrics: ChromeMCPHealthMetrics): void {
    // Add to buffer for batch processing
    this.metricsBuffer.push(metrics);

    // Flush if buffer is full
    if (this.metricsBuffer.length >= this.config.batchSize) {
      this.flushMetricsBuffer();
    }
  }

  /**
   * Handle successful health check
   */
  private handleHealthCheckSuccess(metrics: ChromeMCPHealthMetrics): void {
    // Log successful health check for uptime calculation
    console.debug(`Chrome MCP health check successful: ${metrics.server_status}`);
  }

  /**
   * Handle failed health check
   */
  private handleHealthCheckFailure(metrics: ChromeMCPHealthMetrics): void {
    // Log failed health check for uptime calculation
    console.warn(`Chrome MCP health check failed: ${metrics.server_status}`);
  }

  /**
   * Flush all buffers to storage
   */
  private async flushBuffers(): Promise<void> {
    await Promise.all([
      this.flushMetricsBuffer(),
      this.flushUptimeBuffer()
    ]);
  }

  /**
   * Flush metrics buffer to storage
   */
  private async flushMetricsBuffer(): Promise<void> {
    if (this.metricsBuffer.length === 0) {
      return;
    }

    const metricsToFlush = [...this.metricsBuffer];
    this.metricsBuffer = [];

    try {
      // Save metrics in batch
      await Promise.all(
        metricsToFlush.map(metrics => this.storage.saveHealthMetrics(metrics))
      );

      console.debug(`Flushed ${metricsToFlush.length} health metrics to storage`);
    } catch (error) {
      console.error('Failed to flush health metrics:', error);
      // Re-add to buffer for retry
      this.metricsBuffer.unshift(...metricsToFlush);
    }
  }

  /**
   * Flush uptime buffer to storage
   */
  private async flushUptimeBuffer(): Promise<void> {
    if (this.uptimeBuffer.length === 0) {
      return;
    }

    const uptimeToFlush = [...this.uptimeBuffer];
    this.uptimeBuffer = [];

    try {
      // Save uptime records in batch
      await Promise.all(
        uptimeToFlush.map(record => this.storage.saveUptimeRecord(record))
      );

      console.debug(`Flushed ${uptimeToFlush.length} uptime records to storage`);
    } catch (error) {
      console.error('Failed to flush uptime records:', error);
      // Re-add to buffer for retry
      this.uptimeBuffer.unshift(...uptimeToFlush);
    }
  }

  /**
   * Calculate and store daily uptime record
   */
  private async calculateDailyUptime(date: Date): Promise<void> {
    try {
      const startOfDay = new Date(date);
      startOfDay.setHours(0, 0, 0, 0);
      
      const endOfDay = new Date(date);
      endOfDay.setHours(23, 59, 59, 999);

      // Get all metrics for the day
      const dayMetrics = await this.storage.getMetricsByTimeRange(startOfDay, endOfDay);

      if (dayMetrics.length === 0) {
        return;
      }

      // Calculate uptime statistics
      const totalChecks = dayMetrics.length;
      const healthyChecks = dayMetrics.filter(m => m.server_status === 'healthy').length;
      const uptimePercentage = (healthyChecks / totalChecks) * 100;

      // Calculate downtime
      const unhealthyChecks = dayMetrics.filter(m => m.server_status === 'unhealthy');
      const downtimeMinutes = unhealthyChecks.length * 0.5; // Assuming 30-second intervals

      // Count incidents (consecutive unhealthy periods)
      let incidentCount = 0;
      let inIncident = false;
      
      for (const metric of dayMetrics) {
        if (metric.server_status === 'unhealthy' && !inIncident) {
          incidentCount++;
          inIncident = true;
        } else if (metric.server_status === 'healthy') {
          inIncident = false;
        }
      }

      const uptimeRecord: ChromeMCPUptimeRecord = {
        id: `uptime-${date.toISOString().split('T')[0]}`,
        date: startOfDay,
        uptime_percentage: Math.round(uptimePercentage * 100) / 100,
        total_downtime_minutes: Math.round(downtimeMinutes * 100) / 100,
        incident_count: incidentCount,
      };

      // Add to buffer for storage
      this.uptimeBuffer.push(uptimeRecord);

      console.log(`Calculated uptime for ${date.toDateString()}: ${uptimePercentage.toFixed(2)}%`);
    } catch (error) {
      console.error('Failed to calculate daily uptime:', error);
    }
  }

  /**
   * Schedule daily uptime calculation
   */
  private scheduleUptimeCalculation(): void {
    // Calculate uptime for yesterday at midnight
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);

    const msUntilMidnight = tomorrow.getTime() - now.getTime();

    setTimeout(() => {
      // Calculate uptime for yesterday
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      this.calculateDailyUptime(yesterday);

      // Schedule daily calculation
      setInterval(() => {
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        this.calculateDailyUptime(yesterday);
      }, 24 * 60 * 60 * 1000); // 24 hours
    }, msUntilMidnight);
  }

  /**
   * Get recent health metrics
   */
  async getRecentMetrics(limit: number = 100): Promise<ChromeMCPHealthMetrics[]> {
    return await this.storage.getRecentMetrics(limit);
  }

  /**
   * Get metrics for time range
   */
  async getMetricsByTimeRange(startTime: Date, endTime: Date): Promise<ChromeMCPHealthMetrics[]> {
    return await this.storage.getMetricsByTimeRange(startTime, endTime);
  }

  /**
   * Get uptime history
   */
  async getUptimeHistory(days: number = 30): Promise<ChromeMCPUptimeRecord[]> {
    return await this.storage.getUptimeHistory(days);
  }

  /**
   * Cleanup old data based on retention policy
   */
  async cleanupOldData(): Promise<void> {
    try {
      await this.storage.cleanupOldData(this.config.retentionDays);
      console.log(`Cleaned up health data older than ${this.config.retentionDays} days`);
    } catch (error) {
      console.error('Failed to cleanup old health data:', error);
    }
  }

  /**
   * Get current buffer sizes (for monitoring)
   */
  getBufferSizes(): { metrics: number; uptime: number } {
    return {
      metrics: this.metricsBuffer.length,
      uptime: this.uptimeBuffer.length,
    };
  }

  /**
   * Force flush buffers (for testing/manual operations)
   */
  async forceFlush(): Promise<void> {
    await this.flushBuffers();
  }
}

// In-memory storage implementation for development/testing
class InMemoryHealthStorage implements HealthDataStorage {
  private metrics: ChromeMCPHealthMetrics[] = [];
  private uptimeRecords: ChromeMCPUptimeRecord[] = [];

  async saveHealthMetrics(metrics: ChromeMCPHealthMetrics): Promise<void> {
    this.metrics.push(metrics);
    // Keep only recent metrics to prevent memory issues
    if (this.metrics.length > 10000) {
      this.metrics = this.metrics.slice(-5000);
    }
  }

  async getRecentMetrics(limit: number = 100): Promise<ChromeMCPHealthMetrics[]> {
    return this.metrics
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  }

  async getMetricsByTimeRange(startTime: Date, endTime: Date): Promise<ChromeMCPHealthMetrics[]> {
    return this.metrics.filter(m => 
      m.timestamp >= startTime && m.timestamp <= endTime
    );
  }

  async saveUptimeRecord(record: ChromeMCPUptimeRecord): Promise<void> {
    // Remove existing record for the same date
    this.uptimeRecords = this.uptimeRecords.filter(r => 
      r.date.toDateString() !== record.date.toDateString()
    );
    this.uptimeRecords.push(record);
  }

  async getUptimeHistory(days: number = 30): Promise<ChromeMCPUptimeRecord[]> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);
    
    return this.uptimeRecords
      .filter(r => r.date >= cutoffDate)
      .sort((a, b) => b.date.getTime() - a.date.getTime());
  }

  async cleanupOldData(retentionDays: number): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

    this.metrics = this.metrics.filter(m => m.timestamp >= cutoffDate);
    this.uptimeRecords = this.uptimeRecords.filter(r => r.date >= cutoffDate);
  }
}

// Default configuration
const defaultCollectorConfig: HealthCollectorConfig = {
  retentionDays: 30,
  batchSize: 10,
  flushInterval: 60000, // 1 minute
  enableCompression: false,
};

// Create default storage and collector instances
const defaultStorage = new InMemoryHealthStorage();
export const healthDataCollector = new HealthDataCollector(defaultStorage, defaultCollectorConfig);

// Export classes and interfaces
export { HealthDataCollector, InMemoryHealthStorage };
export type { HealthCollectorConfig };