/**
 * Performance Monitoring Service
 * Comprehensive system health and performance tracking
 */

import { EventEmitter } from "node:events";

interface SystemHealthMetrics {
	timestamp: Date;
	uptime: number;
	memoryUsage: {
		used: number;
		total: number;
		percentage: number;
	};
	cpuUsage: number;
	responseTime: number;
	activeConnections: number;
	errorRate: number;
	status: "healthy" | "warning" | "critical" | "down";
}

interface APIPerformanceMetrics {
	endpoint: string;
	method: string;
	responseTime: number;
	statusCode: number;
	timestamp: Date;
	userAgent?: string;
	ipAddress?: string;
	requestSize: number;
	responseSize: number;
	errorMessage?: string;
}

interface SyncOperationMetrics {
	operationType:
	| "adapty_sync"
	| "remote_config_deploy"
	| "ab_test_sync"
	| "analytics_sync";
	operationId: string;
	startTime: Date;
	endTime?: Date;
	duration?: number;
	status: "running" | "completed" | "failed" | "timeout";
	recordsProcessed: number;
	errorCount: number;
	successRate: number;
	errorDetails?: string[];
}

interface UserActivityMetrics {
	userId: string;
	action: string;
	contentType: string;
	contentId?: string;
	timestamp: Date;
	duration: number;
	success: boolean;
	errorMessage?: string;
	ipAddress: string;
	userAgent: string;
}

interface PerformanceThresholds {
	responseTimeWarning: number; // ms
	responseTimeCritical: number; // ms
	memoryUsageWarning: number; // percentage
	memoryUsageCritical: number; // percentage
	errorRateWarning: number; // percentage
	errorRateCritical: number; // percentage
	syncFailureThreshold: number; // percentage
}

class PerformanceMonitoringService extends EventEmitter {
	private isMonitoring = false;
	private monitoringInterval: NodeJS.Timeout | null = null;
	private metricsBuffer: Map<string, any[]> = new Map();
	private thresholds: PerformanceThresholds = {
		responseTimeWarning: 1000, // 1 second
		responseTimeCritical: 5000, // 5 seconds
		memoryUsageWarning: 80, // 80%
		memoryUsageCritical: 95, // 95%
		errorRateWarning: 5, // 5%
		errorRateCritical: 15, // 15%
		syncFailureThreshold: 20, // 20%
	};

	/**
	 * Initialize performance monitoring
	 */
	async initialize(intervalSeconds: number = 30): Promise<void> {
		try {
			// Set up system health monitoring
			this.startSystemHealthMonitoring(intervalSeconds);

			// Set up API performance monitoring middleware
			this.setupAPIMonitoring();

			// Set up sync operation monitoring
			this.setupSyncMonitoring();

			// Set up user activity monitoring
			this.setupUserActivityMonitoring();

			// Set up automated cleanup
			this.setupMetricsCleanup();

			this.isMonitoring = true;
			strapi.log.info(
				`Performance monitoring initialized with ${intervalSeconds}s interval`,
			);
		} catch (error) {
			strapi.log.error("Failed to initialize performance monitoring:", error);
			throw error;
		}
	}

	/**
	 * Start system health monitoring
	 */
	private startSystemHealthMonitoring(intervalSeconds: number): void {
		this.monitoringInterval = setInterval(async () => {
			try {
				const metrics = await this.collectSystemHealthMetrics();
				await this.storeSystemHealthMetrics(metrics);
				await this.checkSystemHealthThresholds(metrics);
			} catch (error) {
				strapi.log.error("System health monitoring error:", error);
			}
		}, intervalSeconds * 1000);
	}

	/**
	 * Collect system health metrics
	 */
	private async collectSystemHealthMetrics(): Promise<SystemHealthMetrics> {
		const process = require("node:process");
		const memUsage = process.memoryUsage();
		const uptime = process.uptime();

		// Calculate memory usage percentage
		const totalMemory = require("node:os").totalmem();
		const memoryPercentage = (memUsage.heapUsed / totalMemory) * 100;

		// Measure response time by making internal health check
		const responseTime = await this.measureResponseTime();

		// Get active connections (simplified)
		const activeConnections = this.getActiveConnections();

		// Calculate error rate from recent API calls
		const errorRate = await this.calculateErrorRate();

		// Determine overall status
		const status = this.determineSystemStatus(
			responseTime,
			memoryPercentage,
			errorRate,
		);

		return {
			timestamp: new Date(),
			uptime,
			memoryUsage: {
				used: memUsage.heapUsed,
				total: totalMemory,
				percentage: memoryPercentage,
			},
			cpuUsage: await this.getCPUUsage(),
			responseTime,
			activeConnections,
			errorRate,
			status,
		};
	}

	/**
	 * Measure response time with internal health check
	 */
	private async measureResponseTime(): Promise<number> {
		const startTime = Date.now();
		try {
			// Make internal health check request
			await fetch("http://localhost:1337/_health");
			return Date.now() - startTime;
		} catch (_error) {
			return Date.now() - startTime; // Return time even if failed
		}
	}

	/**
	 * Get CPU usage percentage
	 */
	private async getCPUUsage(): Promise<number> {
		return new Promise((resolve) => {
			const startUsage = process.cpuUsage();
			setTimeout(() => {
				const endUsage = process.cpuUsage(startUsage);
				const totalUsage = endUsage.user + endUsage.system;
				const percentage = (totalUsage / 1000000) * 100; // Convert to percentage
				resolve(Math.min(percentage, 100));
			}, 100);
		});
	}

	/**
	 * Get active connections count
	 */
	private getActiveConnections(): number {
		// This would typically integrate with the HTTP server
		// For now, return a placeholder value
		return Math.floor(Math.random() * 100);
	}

	/**
	 * Calculate error rate from recent API performance data
	 */
	private async calculateErrorRate(): Promise<number> {
		try {
			const recentMetrics = await strapi.entityService.findMany(
				"api::api-performance.api-performance",
				{
					filters: {
						timestamp: {
							$gte: new Date(Date.now() - 5 * 60 * 1000).toISOString(), // Last 5 minutes
						},
					},
				},
			);

			if (recentMetrics.length === 0) return 0;

			const errorCount = recentMetrics.filter(
				(m) => m.statusCode >= 400,
			).length;
			return (errorCount / recentMetrics.length) * 100;
		} catch (error) {
			strapi.log.error("Failed to calculate error rate:", error);
			return 0;
		}
	}

	/**
	 * Determine system status based on metrics
	 */
	private determineSystemStatus(
		responseTime: number,
		memoryPercentage: number,
		errorRate: number,
	): "healthy" | "warning" | "critical" | "down" {
		if (
			responseTime > this.thresholds.responseTimeCritical ||
			memoryPercentage > this.thresholds.memoryUsageCritical ||
			errorRate > this.thresholds.errorRateCritical
		) {
			return "critical";
		}

		if (
			responseTime > this.thresholds.responseTimeWarning ||
			memoryPercentage > this.thresholds.memoryUsageWarning ||
			errorRate > this.thresholds.errorRateWarning
		) {
			return "warning";
		}

		return "healthy";
	}

	/**
	 * Store system health metrics
	 */
	private async storeSystemHealthMetrics(
		metrics: SystemHealthMetrics,
	): Promise<void> {
		try {
			await strapi.entityService.create("api::system-health.system-health", {
				data: {
					timestamp: metrics.timestamp,
					uptime: metrics.uptime,
					memoryUsed: metrics.memoryUsage.used,
					memoryTotal: metrics.memoryUsage.total,
					memoryPercentage: metrics.memoryUsage.percentage,
					cpuUsage: metrics.cpuUsage,
					responseTime: metrics.responseTime,
					activeConnections: metrics.activeConnections,
					errorRate: metrics.errorRate,
					status: metrics.status,
					publishedAt: new Date(),
				},
			});
		} catch (error) {
			strapi.log.error("Failed to store system health metrics:", error);
		}
	}

	/**
	 * Check system health thresholds and create alerts
	 */
	private async checkSystemHealthThresholds(
		metrics: SystemHealthMetrics,
	): Promise<void> {
		const alerts = [];

		// Check response time
		if (metrics.responseTime > this.thresholds.responseTimeCritical) {
			alerts.push({
				type: "response_time_critical",
				severity: "critical",
				message: `Response time is critically high: ${metrics.responseTime}ms`,
				currentValue: metrics.responseTime,
				threshold: this.thresholds.responseTimeCritical,
			});
		} else if (metrics.responseTime > this.thresholds.responseTimeWarning) {
			alerts.push({
				type: "response_time_warning",
				severity: "warning",
				message: `Response time is elevated: ${metrics.responseTime}ms`,
				currentValue: metrics.responseTime,
				threshold: this.thresholds.responseTimeWarning,
			});
		}

		// Check memory usage
		if (metrics.memoryUsage.percentage > this.thresholds.memoryUsageCritical) {
			alerts.push({
				type: "memory_usage_critical",
				severity: "critical",
				message: `Memory usage is critically high: ${metrics.memoryUsage.percentage.toFixed(1)}%`,
				currentValue: metrics.memoryUsage.percentage,
				threshold: this.thresholds.memoryUsageCritical,
			});
		}

		// Check error rate
		if (metrics.errorRate > this.thresholds.errorRateCritical) {
			alerts.push({
				type: "error_rate_critical",
				severity: "critical",
				message: `Error rate is critically high: ${metrics.errorRate.toFixed(1)}%`,
				currentValue: metrics.errorRate,
				threshold: this.thresholds.errorRateCritical,
			});
		}

		// Create alerts
		for (const alert of alerts) {
			await this.createPerformanceAlert(alert);
		}
	}

	/**
	 * Set up API performance monitoring middleware
	 */
	private setupAPIMonitoring(): void {
		// This would be integrated with Strapi's middleware system
		strapi.server.use(async (ctx, next) => {
			const startTime = Date.now();
			const requestSize = ctx.request.length || 0;

			try {
				await next();

				const endTime = Date.now();
				const responseTime = endTime - startTime;
				const responseSize = ctx.response.length || 0;

				// Store API performance metrics
				await this.storeAPIPerformanceMetrics({
					endpoint: ctx.request.url,
					method: ctx.request.method,
					responseTime,
					statusCode: ctx.response.status,
					timestamp: new Date(),
					userAgent: ctx.request.headers["user-agent"],
					ipAddress: ctx.request.ip,
					requestSize,
					responseSize,
				});

				// Check for performance issues
				if (responseTime > this.thresholds.responseTimeWarning) {
					await this.createPerformanceAlert({
						type: "slow_api_response",
						severity:
							responseTime > this.thresholds.responseTimeCritical
								? "critical"
								: "warning",
						message: `Slow API response: ${ctx.request.method} ${ctx.request.url} took ${responseTime}ms`,
						currentValue: responseTime,
						threshold: this.thresholds.responseTimeWarning,
					});
				}
			} catch (error) {
				const endTime = Date.now();
				const responseTime = endTime - startTime;

				// Store error metrics
				await this.storeAPIPerformanceMetrics({
					endpoint: ctx.request.url,
					method: ctx.request.method,
					responseTime,
					statusCode: ctx.response.status || 500,
					timestamp: new Date(),
					userAgent: ctx.request.headers["user-agent"],
					ipAddress: ctx.request.ip,
					requestSize,
					responseSize: 0,
					errorMessage: error.message,
				});

				throw error;
			}
		});
	}

	/**
	 * Store API performance metrics
	 */
	private async storeAPIPerformanceMetrics(
		metrics: APIPerformanceMetrics,
	): Promise<void> {
		try {
			await strapi.entityService.create(
				"api::api-performance.api-performance",
				{
					data: {
						endpoint: metrics.endpoint,
						method: metrics.method,
						responseTime: metrics.responseTime,
						statusCode: metrics.statusCode,
						timestamp: metrics.timestamp,
						userAgent: metrics.userAgent,
						ipAddress: metrics.ipAddress,
						requestSize: metrics.requestSize,
						responseSize: metrics.responseSize,
						errorMessage: metrics.errorMessage,
						publishedAt: new Date(),
					},
				},
			);
		} catch (error) {
			strapi.log.error("Failed to store API performance metrics:", error);
		}
	}

	/**
	 * Set up sync operation monitoring
	 */
	private setupSyncMonitoring(): void {
		// Listen for sync operation events
		this.on("syncStarted", this.handleSyncStarted.bind(this));
		this.on("syncCompleted", this.handleSyncCompleted.bind(this));
		this.on("syncFailed", this.handleSyncFailed.bind(this));
	}

	/**
	 * Handle sync operation started
	 */
	private async handleSyncStarted(data: {
		operationType: string;
		operationId: string;
	}): Promise<void> {
		try {
			await strapi.entityService.create("api::sync-operation.sync-operation", {
				data: {
					operationType: data.operationType,
					operationId: data.operationId,
					startTime: new Date(),
					status: "running",
					recordsProcessed: 0,
					errorCount: 0,
					successRate: 0,
					publishedAt: new Date(),
				},
			});
		} catch (error) {
			strapi.log.error("Failed to record sync start:", error);
		}
	}

	/**
	 * Handle sync operation completed
	 */
	private async handleSyncCompleted(data: {
		operationId: string;
		recordsProcessed: number;
		errorCount: number;
	}): Promise<void> {
		try {
			const syncRecord = await strapi.entityService.findMany(
				"api::sync-operation.sync-operation",
				{
					filters: { operationId: data.operationId },
				},
			);

			if (syncRecord[0]) {
				const startTime = new Date(syncRecord[0].startTime);
				const endTime = new Date();
				const duration = endTime.getTime() - startTime.getTime();
				const successRate =
					data.recordsProcessed > 0
						? ((data.recordsProcessed - data.errorCount) /
							data.recordsProcessed) *
						100
						: 100;

				await strapi.entityService.update(
					"api::sync-operation.sync-operation",
					syncRecord[0].id,
					{
						data: {
							// endTime, // Not in schema
							// duration, // Not in schema
							// status: "completed", // Not in schema
							// recordsProcessed: data.recordsProcessed, // Not in schema
							// errorCount: data.errorCount, // Not in schema
							successRate,
						} as any,
					},
				);

				// Check for sync performance issues
				if (successRate < 100 - this.thresholds.syncFailureThreshold) {
					await this.createPerformanceAlert({
						type: "sync_failure_rate_high",
						severity: "warning",
						message: `Sync operation has high failure rate: ${(100 - successRate).toFixed(1)}%`,
						currentValue: 100 - successRate,
						threshold: this.thresholds.syncFailureThreshold,
					});
				}
			}
		} catch (error) {
			strapi.log.error("Failed to record sync completion:", error);
		}
	}

	/**
	 * Handle sync operation failed
	 */
	private async handleSyncFailed(data: {
		operationId: string;
		errorDetails: string[];
	}): Promise<void> {
		try {
			const syncRecord = await strapi.entityService.findMany(
				"api::sync-operation.sync-operation",
				{
					filters: { operationId: data.operationId },
				},
			);

			if (syncRecord[0]) {
				const startTime = new Date(syncRecord[0].startTime);
				const endTime = new Date();
				const duration = endTime.getTime() - startTime.getTime();

				await strapi.entityService.update(
					"api::sync-operation.sync-operation",
					syncRecord[0].id,
					{
						data: {
							// endTime, // Not in schema
							// duration, // Not in schema
							// status: "failed", // Not in schema
							// errorDetails: data.errorDetails, // Not in schema
							successRate: 0,
						} as any,
					},
				);

				// Create alert for sync failure
				await this.createPerformanceAlert({
					type: "sync_operation_failed",
					severity: "high",
					message: `Sync operation failed: ${data.operationId}`,
					currentValue: 0,
					threshold: 100,
				});
			}
		} catch (error) {
			strapi.log.error("Failed to record sync failure:", error);
		}
	}

	/**
	 * Set up user activity monitoring
	 */
	private setupUserActivityMonitoring(): void {
		// This would integrate with Strapi's admin panel and API
		// Monitor content management operations
	}

	/**
	 * Create performance alert
	 */
	private async createPerformanceAlert(alertData: any): Promise<void> {
		try {
			await strapi.entityService.create(
				"api::performance-alert.performance-alert",
				{
					data: {
						alertId: `perf_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
						type: alertData.type,
						severity: alertData.severity,
						message: alertData.message,
						currentValue: alertData.currentValue,
						previousValue: 0,
						threshold: alertData.threshold,
						timestamp: new Date(),
						affectedMetrics: [alertData.type],
						recommendedActions: this.getRecommendedActions(alertData.type),
						status: "active",
						publishedAt: new Date(),
					},
				},
			);

			// Emit alert event for real-time notifications
			this.emit("performanceAlert", alertData);
		} catch (error) {
			strapi.log.error("Failed to create performance alert:", error);
		}
	}

	/**
	 * Get recommended actions for alert type
	 */
	private getRecommendedActions(alertType: string): string[] {
		const actionMap: Record<string, string[]> = {
			response_time_critical: [
				"Check database performance",
				"Review recent code changes",
				"Monitor server resources",
				"Consider scaling infrastructure",
			],
			memory_usage_critical: [
				"Restart application if necessary",
				"Check for memory leaks",
				"Review recent deployments",
				"Scale server resources",
			],
			error_rate_critical: [
				"Check application logs",
				"Review recent changes",
				"Verify external service status",
				"Check database connectivity",
			],
			sync_operation_failed: [
				"Check Adapty API status",
				"Verify API credentials",
				"Review sync configuration",
				"Check network connectivity",
			],
		};

		return (
			actionMap[alertType] || [
				"Investigate the issue",
				"Check system logs",
				"Contact support if needed",
			]
		);
	}

	/**
	 * Set up automated metrics cleanup
	 */
	private setupMetricsCleanup(): void {
		// Clean up old metrics daily
		setInterval(
			async () => {
				await this.cleanupOldMetrics();
			},
			24 * 60 * 60 * 1000,
		); // 24 hours
	}

	/**
	 * Clean up old metrics data
	 */
	private async cleanupOldMetrics(): Promise<void> {
		try {
			const cutoffDate = new Date();
			cutoffDate.setDate(cutoffDate.getDate() - 30); // Keep 30 days

			// Clean up system health metrics
			await strapi.db.query("api::system-health.system-health").deleteMany({
				where: {
					timestamp: {
						$lt: cutoffDate,
					},
				},
			});

			// Clean up API performance metrics
			await strapi.db.query("api::api-performance.api-performance").deleteMany({
				where: {
					timestamp: {
						$lt: cutoffDate,
					},
				},
			});

			// Clean up completed sync operations
			await strapi.db.query("api::sync-operation.sync-operation").deleteMany({
				where: {
					endTime: {
						$lt: cutoffDate,
					},
					status: "completed",
				},
			});

			strapi.log.info("Performance metrics cleanup completed");
		} catch (error) {
			strapi.log.error("Failed to cleanup old metrics:", error);
		}
	}

	/**
	 * Get performance summary
	 */
	async getPerformanceSummary(hours: number = 24): Promise<any> {
		try {
			const since = new Date(Date.now() - hours * 60 * 60 * 1000);

			// Get system health summary
			const healthMetrics = await strapi.entityService.findMany(
				"api::system-health.system-health",
				{
					filters: {
						timestamp: { $gte: since.toISOString() },
					},
					sort: "timestamp:desc",
				},
			);

			// Get API performance summary
			const apiMetrics = await strapi.entityService.findMany(
				"api::api-performance.api-performance",
				{
					filters: {
						timestamp: { $gte: since.toISOString() },
					},
				},
			);

			// Get sync operation summary
			const syncMetrics = await strapi.entityService.findMany(
				"api::sync-operation.sync-operation",
				{
					filters: {
						startTime: { $gte: since.toISOString() },
					},
				},
			);

			return {
				systemHealth: this.summarizeSystemHealth(Array.isArray(healthMetrics) ? healthMetrics : []),
				apiPerformance: this.summarizeAPIPerformance(Array.isArray(apiMetrics) ? apiMetrics : []),
				syncOperations: this.summarizeSyncOperations(Array.isArray(syncMetrics) ? syncMetrics : []),
				timeRange: { since, hours },
			};
		} catch (error) {
			strapi.log.error("Failed to get performance summary:", error);
			throw error;
		}
	}

	/**
	 * Summarize system health metrics
	 */
	private summarizeSystemHealth(metrics: any[]): any {
		if (metrics.length === 0) return null;

		const avgResponseTime =
			metrics.reduce((sum, m) => sum + m.responseTime, 0) / metrics.length;
		const avgMemoryUsage =
			metrics.reduce((sum, m) => sum + m.memoryPercentage, 0) / metrics.length;
		const avgCpuUsage =
			metrics.reduce((sum, m) => sum + m.cpuUsage, 0) / metrics.length;
		const avgErrorRate =
			metrics.reduce((sum, m) => sum + m.errorRate, 0) / metrics.length;

		const statusCounts = metrics.reduce((acc, m) => {
			acc[m.status] = (acc[m.status] || 0) + 1;
			return acc;
		}, {});

		return {
			averageResponseTime: avgResponseTime,
			averageMemoryUsage: avgMemoryUsage,
			averageCpuUsage: avgCpuUsage,
			averageErrorRate: avgErrorRate,
			statusDistribution: statusCounts,
			totalMeasurements: metrics.length,
			currentStatus: metrics[0]?.status || "unknown",
		};
	}

	/**
	 * Summarize API performance metrics
	 */
	private summarizeAPIPerformance(metrics: any[]): any {
		if (metrics.length === 0) return null;

		const totalRequests = metrics.length;
		const errorRequests = metrics.filter((m) => m.statusCode >= 400).length;
		const avgResponseTime =
			metrics.reduce((sum, m) => sum + m.responseTime, 0) / metrics.length;

		const endpointStats = metrics.reduce((acc, m) => {
			const key = `${m.method} ${m.endpoint}`;
			if (!acc[key]) {
				acc[key] = { count: 0, totalTime: 0, errors: 0 };
			}
			acc[key].count++;
			acc[key].totalTime += m.responseTime;
			if (m.statusCode >= 400) acc[key].errors++;
			return acc;
		}, {});

		return {
			totalRequests,
			errorRequests,
			errorRate: (errorRequests / totalRequests) * 100,
			averageResponseTime: avgResponseTime,
			endpointStats: Object.entries(endpointStats).map(
				([endpoint, stats]: [string, any]) => ({
					endpoint,
					requestCount: stats.count,
					averageResponseTime: stats.totalTime / stats.count,
					errorRate: (stats.errors / stats.count) * 100,
				}),
			),
		};
	}

	/**
	 * Summarize sync operation metrics
	 */
	private summarizeSyncOperations(metrics: any[]): any {
		if (metrics.length === 0) return null;

		const completedOps = metrics.filter((m) => m.status === "completed");
		const failedOps = metrics.filter((m) => m.status === "failed");
		const runningOps = metrics.filter((m) => m.status === "running");

		const avgSuccessRate =
			completedOps.length > 0
				? completedOps.reduce((sum, m) => sum + m.successRate, 0) /
				completedOps.length
				: 0;

		const avgDuration =
			completedOps.length > 0
				? completedOps.reduce((sum, m) => sum + (m.duration || 0), 0) /
				completedOps.length
				: 0;

		return {
			totalOperations: metrics.length,
			completedOperations: completedOps.length,
			failedOperations: failedOps.length,
			runningOperations: runningOps.length,
			averageSuccessRate: avgSuccessRate,
			averageDuration: avgDuration,
			operationTypes: metrics.reduce((acc, m) => {
				acc[m.operationType] = (acc[m.operationType] || 0) + 1;
				return acc;
			}, {}),
		};
	}

	/**
	 * Stop monitoring
	 */
	stop(): void {
		if (this.monitoringInterval) {
			clearInterval(this.monitoringInterval);
			this.monitoringInterval = null;
		}
		this.isMonitoring = false;
		strapi.log.info("Performance monitoring stopped");
	}

	/**
	 * Update monitoring thresholds
	 */
	updateThresholds(newThresholds: Partial<PerformanceThresholds>): void {
		this.thresholds = { ...this.thresholds, ...newThresholds };
		strapi.log.info("Performance monitoring thresholds updated");
	}

	/**
	 * Get monitoring status
	 */
	getStatus(): any {
		return {
			isMonitoring: this.isMonitoring,
			thresholds: this.thresholds,
			bufferSizes: Object.fromEntries(
				Array.from(this.metricsBuffer.entries()).map(([key, value]) => [
					key,
					value.length,
				]),
			),
		};
	}
}

export const performanceMonitor = new PerformanceMonitoringService();
