/**
 * Chrome MCP Server Health Monitoring Service
 * Provides real-time health monitoring and metrics collection for Chrome MCP server
 */

import { EventEmitter } from 'events';

// Types for Chrome MCP Health Monitoring
export interface ChromeMCPHealthMetrics {
  id: string;
  timestamp: Date;
  server_status: 'healthy' | 'degraded' | 'unhealthy';
  uptime_seconds: number;
  response_time_ms: number;
  memory_usage_mb: number;
  cpu_usage_percent: number;
  active_connections: number;
  connection_success_rate: number;
  error_count: number;
}

export interface ChromeMCPUptimeRecord {
  id: string;
  date: Date;
  uptime_percentage: number;
  total_downtime_minutes: number;
  incident_count: number;
}

export interface ChromeMCPServerConfig {
  serverUrl: string;
  healthCheckInterval: number; // milliseconds
  metricsCollectionInterval: number; // milliseconds
  retryAttempts: number;
  timeout: number; // milliseconds
}

export interface HealthCheckResponse {
  status: 'healthy' | 'degraded' | 'unhealthy';
  uptime: number;
  memory: {
    used: number;
    total: number;
  };
  cpu: {
    usage: number;
  };
  connections: {
    active: number;
    total: number;
  };
  errors: {
    count: number;
    recent: string[];
  };
}

class ChromeMCPMonitor extends EventEmitter {
  private config: ChromeMCPServerConfig;
  private isMonitoring: boolean = false;
  private healthCheckTimer?: NodeJS.Timeout;
  private metricsTimer?: NodeJS.Timeout;
  private consecutiveFailures: number = 0;
  private lastHealthCheck?: Date;
  private currentMetrics?: ChromeMCPHealthMetrics;

  constructor(config: ChromeMCPServerConfig) {
    super();
    this.config = config;
  }

  /**
   * Start monitoring Chrome MCP server
   */
  async startMonitoring(): Promise<void> {
    if (this.isMonitoring) {
      console.warn('Chrome MCP monitoring is already running');
      return;
    }

    console.log('Starting Chrome MCP server monitoring...');
    this.isMonitoring = true;

    // Start health check interval
    this.healthCheckTimer = setInterval(
      () => this.performHealthCheck(),
      this.config.healthCheckInterval
    );

    // Start metrics collection interval
    this.metricsTimer = setInterval(
      () => this.collectMetrics(),
      this.config.metricsCollectionInterval
    );

    // Perform initial health check
    await this.performHealthCheck();

    this.emit('monitoring-started');
  }

  /**
   * Stop monitoring Chrome MCP server
   */
  stopMonitoring(): void {
    if (!this.isMonitoring) {
      return;
    }

    console.log('Stopping Chrome MCP server monitoring...');
    this.isMonitoring = false;

    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
      this.healthCheckTimer = undefined;
    }

    if (this.metricsTimer) {
      clearInterval(this.metricsTimer);
      this.metricsTimer = undefined;
    }

    this.emit('monitoring-stopped');
  }

  /**
   * Perform health check on Chrome MCP server
   */
  private async performHealthCheck(): Promise<void> {
    try {
      const startTime = Date.now();
      const response = await this.makeHealthRequest();
      const responseTime = Date.now() - startTime;

      if (response) {
        this.handleHealthCheckSuccess(response, responseTime);
      } else {
        this.handleHealthCheckFailure();
      }
    } catch (error) {
      this.handleHealthCheckError(error);
    }
  }

  /**
   * Make HTTP request to Chrome MCP server health endpoint
   */
  private async makeHealthRequest(): Promise<HealthCheckResponse | null> {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

      const response = await fetch(`${this.config.serverUrl}/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`Health check failed with status: ${response.status}`);
      }

      return await response.json() as HealthCheckResponse;
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Health check request timed out');
      }
      throw error;
    }
  }

  /**
   * Handle successful health check response
   */
  private handleHealthCheckSuccess(response: HealthCheckResponse, responseTime: number): void {
    this.consecutiveFailures = 0;
    this.lastHealthCheck = new Date();

    const metrics: ChromeMCPHealthMetrics = {
      id: this.generateId(),
      timestamp: new Date(),
      server_status: response.status,
      uptime_seconds: response.uptime,
      response_time_ms: responseTime,
      memory_usage_mb: response.memory.used,
      cpu_usage_percent: response.cpu.usage,
      active_connections: response.connections.active,
      connection_success_rate: this.calculateConnectionSuccessRate(),
      error_count: response.errors.count,
    };

    this.currentMetrics = metrics;
    this.emit('health-check-success', metrics);
    this.emit('metrics-updated', metrics);

    // Check for performance degradation
    this.checkPerformanceThresholds(metrics);
  }

  /**
   * Handle health check failure
   */
  private handleHealthCheckFailure(): void {
    this.consecutiveFailures++;
    
    const metrics: ChromeMCPHealthMetrics = {
      id: this.generateId(),
      timestamp: new Date(),
      server_status: 'unhealthy',
      uptime_seconds: 0,
      response_time_ms: this.config.timeout,
      memory_usage_mb: 0,
      cpu_usage_percent: 0,
      active_connections: 0,
      connection_success_rate: this.calculateConnectionSuccessRate(),
      error_count: this.consecutiveFailures,
    };

    this.currentMetrics = metrics;
    this.emit('health-check-failure', metrics);
    this.emit('metrics-updated', metrics);

    // Emit alert if consecutive failures exceed threshold
    if (this.consecutiveFailures >= 3) {
      this.emit('server-down-alert', {
        consecutiveFailures: this.consecutiveFailures,
        lastSuccessfulCheck: this.lastHealthCheck,
      });
    }
  }

  /**
   * Handle health check error
   */
  private handleHealthCheckError(error: unknown): void {
    console.error('Chrome MCP health check error:', error);
    this.handleHealthCheckFailure();
    this.emit('health-check-error', error);
  }

  /**
   * Collect detailed metrics from Chrome MCP server
   */
  private async collectMetrics(): Promise<void> {
    try {
      const response = await this.makeMetricsRequest();
      if (response) {
        this.emit('detailed-metrics', response);
      }
    } catch (error) {
      console.error('Chrome MCP metrics collection error:', error);
      this.emit('metrics-collection-error', error);
    }
  }

  /**
   * Make HTTP request to Chrome MCP server metrics endpoint
   */
  private async makeMetricsRequest(): Promise<any> {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

      const response = await fetch(`${this.config.serverUrl}/metrics`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`Metrics request failed with status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Metrics request timed out');
      }
      throw error;
    }
  }

  /**
   * Calculate connection success rate based on recent history
   */
  private calculateConnectionSuccessRate(): number {
    // This would typically be calculated from stored historical data
    // For now, return a calculated rate based on consecutive failures
    if (this.consecutiveFailures === 0) {
      return 100;
    } else if (this.consecutiveFailures < 3) {
      return 75;
    } else if (this.consecutiveFailures < 5) {
      return 50;
    } else {
      return 25;
    }
  }

  /**
   * Check performance thresholds and emit alerts
   */
  private checkPerformanceThresholds(metrics: ChromeMCPHealthMetrics): void {
    const thresholds = {
      responseTime: 2000, // 2 seconds
      memoryUsage: 1024, // 1GB
      cpuUsage: 80, // 80%
      connectionSuccessRate: 95, // 95%
    };

    const alerts: string[] = [];

    if (metrics.response_time_ms > thresholds.responseTime) {
      alerts.push(`High response time: ${metrics.response_time_ms}ms`);
    }

    if (metrics.memory_usage_mb > thresholds.memoryUsage) {
      alerts.push(`High memory usage: ${metrics.memory_usage_mb}MB`);
    }

    if (metrics.cpu_usage_percent > thresholds.cpuUsage) {
      alerts.push(`High CPU usage: ${metrics.cpu_usage_percent}%`);
    }

    if (metrics.connection_success_rate < thresholds.connectionSuccessRate) {
      alerts.push(`Low connection success rate: ${metrics.connection_success_rate}%`);
    }

    if (alerts.length > 0) {
      this.emit('performance-alert', {
        metrics,
        alerts,
        timestamp: new Date(),
      });
    }
  }

  /**
   * Generate unique ID for metrics
   */
  private generateId(): string {
    return `chrome-mcp-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get current server status
   */
  getServerStatus(): string {
    return this.currentMetrics?.server_status || 'unknown';
  }

  /**
   * Get current metrics
   */
  getCurrentMetrics(): ChromeMCPHealthMetrics | undefined {
    return this.currentMetrics;
  }

  /**
   * Get monitoring status
   */
  isMonitoringActive(): boolean {
    return this.isMonitoring;
  }

  /**
   * Get consecutive failure count
   */
  getConsecutiveFailures(): number {
    return this.consecutiveFailures;
  }

  /**
   * Get last successful health check time
   */
  getLastHealthCheck(): Date | undefined {
    return this.lastHealthCheck;
  }
}

// Default configuration
const defaultConfig: ChromeMCPServerConfig = {
  serverUrl: process.env.CHROME_MCP_SERVER_URL || 'http://localhost:8080',
  healthCheckInterval: 30000, // 30 seconds
  metricsCollectionInterval: 60000, // 1 minute
  retryAttempts: 3,
  timeout: 5000, // 5 seconds
};

// Export singleton instance
export const chromeMCPMonitor = new ChromeMCPMonitor(defaultConfig);

// Export class for testing
export { ChromeMCPMonitor };

// Export types
export type {
  ChromeMCPServerConfig,
  HealthCheckResponse,
};