/**
 * Chrome MCP Metrics Aggregator
 * Aggregates and processes health metrics for dashboard display and alerting
 */

import { ChromeMCPHealthMetrics, ChromeMCPUptimeRecord } from './chrome-mcp-monitor';
import { healthDataCollector } from './health-collector';

export interface AggregatedMetrics {
  timeRange: {
    start: Date;
    end: Date;
  };
  summary: {
    averageResponseTime: number;
    averageMemoryUsage: number;
    averageCpuUsage: number;
    averageConnectionSuccessRate: number;
    totalErrors: number;
    uptimePercentage: number;
  };
  trends: {
    responseTimeTrend: 'improving' | 'stable' | 'degrading';
    memoryUsageTrend: 'improving' | 'stable' | 'degrading';
    cpuUsageTrend: 'improving' | 'stable' | 'degrading';
    errorRateTrend: 'improving' | 'stable' | 'degrading';
  };
  alerts: MetricAlert[];
}

export interface MetricAlert {
  id: string;
  type: 'performance' | 'availability' | 'error_rate';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: Date;
  metrics: Partial<ChromeMCPHealthMetrics>;
  acknowledged: boolean;
}

export interface AggregationConfig {
  alertThresholds: {
    responseTime: {
      warning: number;
      critical: number;
    };
    memoryUsage: {
      warning: number;
      critical: number;
    };
    cpuUsage: {
      warning: number;
      critical: number;
    };
    connectionSuccessRate: {
      warning: number;
      critical: number;
    };
    errorRate: {
      warning: number;
      critical: number;
    };
    uptimePercentage: {
      warning: number;
      critical: number;
    };
  };
  trendAnalysis: {
    windowSize: number; // number of data points to analyze
    significanceThreshold: number; // percentage change to consider significant
  };
}

class MetricsAggregator {
  private config: AggregationConfig;
  private activeAlerts: Map<string, MetricAlert> = new Map();

  constructor(config: AggregationConfig) {
    this.config = config;
  }

  /**
   * Aggregate metrics for a specific time range
   */
  async aggregateMetrics(startTime: Date, endTime: Date): Promise<AggregatedMetrics> {
    const metrics = await healthDataCollector.getMetricsByTimeRange(startTime, endTime);
    
    if (metrics.length === 0) {
      return this.createEmptyAggregation(startTime, endTime);
    }

    const summary = this.calculateSummary(metrics);
    const trends = await this.calculateTrends(metrics, startTime, endTime);
    const alerts = this.generateAlerts(metrics);

    return {
      timeRange: { start: startTime, end: endTime },
      summary,
      trends,
      alerts,
    };
  }

  /**
   * Get real-time aggregated metrics (last hour)
   */
  async getRealTimeMetrics(): Promise<AggregatedMetrics> {
    const endTime = new Date();
    const startTime = new Date(endTime.getTime() - 60 * 60 * 1000); // 1 hour ago
    
    return await this.aggregateMetrics(startTime, endTime);
  }

  /**
   * Get daily aggregated metrics
   */
  async getDailyMetrics(date: Date): Promise<AggregatedMetrics> {
    const startTime = new Date(date);
    startTime.setHours(0, 0, 0, 0);
    
    const endTime = new Date(date);
    endTime.setHours(23, 59, 59, 999);
    
    return await this.aggregateMetrics(startTime, endTime);
  }

  /**
   * Get weekly aggregated metrics
   */
  async getWeeklyMetrics(weekStart: Date): Promise<AggregatedMetrics> {
    const startTime = new Date(weekStart);
    const endTime = new Date(weekStart.getTime() + 7 * 24 * 60 * 60 * 1000);
    
    return await this.aggregateMetrics(startTime, endTime);
  }

  /**
   * Calculate summary statistics from metrics
   */
  private calculateSummary(metrics: ChromeMCPHealthMetrics[]): AggregatedMetrics['summary'] {
    if (metrics.length === 0) {
      return {
        averageResponseTime: 0,
        averageMemoryUsage: 0,
        averageCpuUsage: 0,
        averageConnectionSuccessRate: 0,
        totalErrors: 0,
        uptimePercentage: 0,
      };
    }

    const totals = metrics.reduce(
      (acc, metric) => ({
        responseTime: acc.responseTime + metric.response_time_ms,
        memoryUsage: acc.memoryUsage + metric.memory_usage_mb,
        cpuUsage: acc.cpuUsage + metric.cpu_usage_percent,
        connectionSuccessRate: acc.connectionSuccessRate + metric.connection_success_rate,
        errors: acc.errors + metric.error_count,
      }),
      { responseTime: 0, memoryUsage: 0, cpuUsage: 0, connectionSuccessRate: 0, errors: 0 }
    );

    const healthyMetrics = metrics.filter(m => m.server_status === 'healthy').length;
    const uptimePercentage = (healthyMetrics / metrics.length) * 100;

    return {
      averageResponseTime: Math.round(totals.responseTime / metrics.length),
      averageMemoryUsage: Math.round(totals.memoryUsage / metrics.length),
      averageCpuUsage: Math.round((totals.cpuUsage / metrics.length) * 100) / 100,
      averageConnectionSuccessRate: Math.round((totals.connectionSuccessRate / metrics.length) * 100) / 100,
      totalErrors: totals.errors,
      uptimePercentage: Math.round(uptimePercentage * 100) / 100,
    };
  }

  /**
   * Calculate trends by comparing current period with previous period
   */
  private async calculateTrends(
    currentMetrics: ChromeMCPHealthMetrics[],
    startTime: Date,
    endTime: Date
  ): Promise<AggregatedMetrics['trends']> {
    const periodDuration = endTime.getTime() - startTime.getTime();
    const previousStartTime = new Date(startTime.getTime() - periodDuration);
    const previousEndTime = new Date(startTime);

    const previousMetrics = await healthDataCollector.getMetricsByTimeRange(
      previousStartTime,
      previousEndTime
    );

    if (previousMetrics.length === 0) {
      return {
        responseTimeTrend: 'stable',
        memoryUsageTrend: 'stable',
        cpuUsageTrend: 'stable',
        errorRateTrend: 'stable',
      };
    }

    const currentSummary = this.calculateSummary(currentMetrics);
    const previousSummary = this.calculateSummary(previousMetrics);

    return {
      responseTimeTrend: this.calculateTrend(
        currentSummary.averageResponseTime,
        previousSummary.averageResponseTime
      ),
      memoryUsageTrend: this.calculateTrend(
        currentSummary.averageMemoryUsage,
        previousSummary.averageMemoryUsage
      ),
      cpuUsageTrend: this.calculateTrend(
        currentSummary.averageCpuUsage,
        previousSummary.averageCpuUsage
      ),
      errorRateTrend: this.calculateTrend(
        currentSummary.totalErrors,
        previousSummary.totalErrors,
        true // reverse trend for errors (lower is better)
      ),
    };
  }

  /**
   * Calculate trend direction based on current vs previous values
   */
  private calculateTrend(
    current: number,
    previous: number,
    reverse: boolean = false
  ): 'improving' | 'stable' | 'degrading' {
    if (previous === 0) {
      return 'stable';
    }

    const changePercent = ((current - previous) / previous) * 100;
    const threshold = this.config.trendAnalysis.significanceThreshold;

    if (Math.abs(changePercent) < threshold) {
      return 'stable';
    }

    const isImproving = reverse ? changePercent < 0 : changePercent > 0;
    return isImproving ? 'improving' : 'degrading';
  }

  /**
   * Generate alerts based on current metrics
   */
  private generateAlerts(metrics: ChromeMCPHealthMetrics[]): MetricAlert[] {
    const alerts: MetricAlert[] = [];
    const latestMetric = metrics[metrics.length - 1];

    if (!latestMetric) {
      return alerts;
    }

    // Response time alerts
    if (latestMetric.response_time_ms >= this.config.alertThresholds.responseTime.critical) {
      alerts.push(this.createAlert(
        'performance',
        'critical',
        `Critical response time: ${latestMetric.response_time_ms}ms`,
        latestMetric
      ));
    } else if (latestMetric.response_time_ms >= this.config.alertThresholds.responseTime.warning) {
      alerts.push(this.createAlert(
        'performance',
        'medium',
        `High response time: ${latestMetric.response_time_ms}ms`,
        latestMetric
      ));
    }

    // Memory usage alerts
    if (latestMetric.memory_usage_mb >= this.config.alertThresholds.memoryUsage.critical) {
      alerts.push(this.createAlert(
        'performance',
        'critical',
        `Critical memory usage: ${latestMetric.memory_usage_mb}MB`,
        latestMetric
      ));
    } else if (latestMetric.memory_usage_mb >= this.config.alertThresholds.memoryUsage.warning) {
      alerts.push(this.createAlert(
        'performance',
        'medium',
        `High memory usage: ${latestMetric.memory_usage_mb}MB`,
        latestMetric
      ));
    }

    // CPU usage alerts
    if (latestMetric.cpu_usage_percent >= this.config.alertThresholds.cpuUsage.critical) {
      alerts.push(this.createAlert(
        'performance',
        'critical',
        `Critical CPU usage: ${latestMetric.cpu_usage_percent}%`,
        latestMetric
      ));
    } else if (latestMetric.cpu_usage_percent >= this.config.alertThresholds.cpuUsage.warning) {
      alerts.push(this.createAlert(
        'performance',
        'medium',
        `High CPU usage: ${latestMetric.cpu_usage_percent}%`,
        latestMetric
      ));
    }

    // Connection success rate alerts
    if (latestMetric.connection_success_rate <= this.config.alertThresholds.connectionSuccessRate.critical) {
      alerts.push(this.createAlert(
        'availability',
        'critical',
        `Critical connection success rate: ${latestMetric.connection_success_rate}%`,
        latestMetric
      ));
    } else if (latestMetric.connection_success_rate <= this.config.alertThresholds.connectionSuccessRate.warning) {
      alerts.push(this.createAlert(
        'availability',
        'medium',
        `Low connection success rate: ${latestMetric.connection_success_rate}%`,
        latestMetric
      ));
    }

    // Server status alerts
    if (latestMetric.server_status === 'unhealthy') {
      alerts.push(this.createAlert(
        'availability',
        'critical',
        'Chrome MCP server is unhealthy',
        latestMetric
      ));
    } else if (latestMetric.server_status === 'degraded') {
      alerts.push(this.createAlert(
        'availability',
        'medium',
        'Chrome MCP server performance is degraded',
        latestMetric
      ));
    }

    return alerts;
  }

  /**
   * Create a metric alert
   */
  private createAlert(
    type: MetricAlert['type'],
    severity: MetricAlert['severity'],
    message: string,
    metrics: ChromeMCPHealthMetrics
  ): MetricAlert {
    const alertId = `${type}-${severity}-${Date.now()}`;
    
    return {
      id: alertId,
      type,
      severity,
      message,
      timestamp: new Date(),
      metrics,
      acknowledged: false,
    };
  }

  /**
   * Create empty aggregation for when no data is available
   */
  private createEmptyAggregation(startTime: Date, endTime: Date): AggregatedMetrics {
    return {
      timeRange: { start: startTime, end: endTime },
      summary: {
        averageResponseTime: 0,
        averageMemoryUsage: 0,
        averageCpuUsage: 0,
        averageConnectionSuccessRate: 0,
        totalErrors: 0,
        uptimePercentage: 0,
      },
      trends: {
        responseTimeTrend: 'stable',
        memoryUsageTrend: 'stable',
        cpuUsageTrend: 'stable',
        errorRateTrend: 'stable',
      },
      alerts: [],
    };
  }

  /**
   * Get active alerts
   */
  getActiveAlerts(): MetricAlert[] {
    return Array.from(this.activeAlerts.values());
  }

  /**
   * Acknowledge an alert
   */
  acknowledgeAlert(alertId: string): boolean {
    const alert = this.activeAlerts.get(alertId);
    if (alert) {
      alert.acknowledged = true;
      return true;
    }
    return false;
  }

  /**
   * Clear acknowledged alerts older than specified time
   */
  clearOldAlerts(maxAgeHours: number = 24): void {
    const cutoffTime = new Date(Date.now() - maxAgeHours * 60 * 60 * 1000);
    
    for (const [id, alert] of this.activeAlerts.entries()) {
      if (alert.acknowledged && alert.timestamp < cutoffTime) {
        this.activeAlerts.delete(id);
      }
    }
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<AggregationConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Get current configuration
   */
  getConfig(): AggregationConfig {
    return { ...this.config };
  }
}

// Default configuration
const defaultAggregationConfig: AggregationConfig = {
  alertThresholds: {
    responseTime: {
      warning: 2000, // 2 seconds
      critical: 5000, // 5 seconds
    },
    memoryUsage: {
      warning: 1024, // 1GB
      critical: 2048, // 2GB
    },
    cpuUsage: {
      warning: 70, // 70%
      critical: 90, // 90%
    },
    connectionSuccessRate: {
      warning: 95, // 95%
      critical: 85, // 85%
    },
    errorRate: {
      warning: 5, // 5 errors
      critical: 10, // 10 errors
    },
    uptimePercentage: {
      warning: 99.0, // 99%
      critical: 95.0, // 95%
    },
  },
  trendAnalysis: {
    windowSize: 10,
    significanceThreshold: 10, // 10% change
  },
};

// Export singleton instance
export const metricsAggregator = new MetricsAggregator(defaultAggregationConfig);

// Export class and types
export { MetricsAggregator };
export type { AggregationConfig };