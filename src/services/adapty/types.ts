/**
 * TypeScript definitions for Adapty API client
 */

// Base API Response Types
export interface AdaptyApiResponse<T = any> {
	data: T;
	errors?: AdaptyError[];
	meta?: {
		pagination?: {
			page: number;
			per_page: number;
			total: number;
			total_pages: number;
		};
	};
}

export interface AdaptyError {
	code: string;
	message: string;
	details?: Record<string, any>;
}

// Authentication Types
export interface AdaptyAuthConfig {
	apiKey: string;
	baseUrl?: string;
	timeout?: number;
	retryAttempts?: number;
	retryDelay?: number;
}

// Placement Types
export interface AdaptyPlacement {
	id: string;
	name: string;
	remote_config_id?: string;
	paywall_id?: string;
	audience_id?: string;
	created_at: string;
	updated_at: string;
	is_fallback: boolean;
	variation_id?: string;
}

export interface CreatePlacementRequest {
	name: string;
	paywall_id?: string;
	audience_id?: string;
	is_fallback?: boolean;
}

export interface UpdatePlacementRequest {
	name?: string;
	paywall_id?: string;
	audience_id?: string;
	is_fallback?: boolean;
}

// Product Types
export interface AdaptyProduct {
	id: string;
	vendor_product_id: string;
	type: "subscription" | "non_consumable" | "consumable";
	name: string;
	store: "app_store" | "play_store";
	price?: AdaptyPrice;
	subscription_period?: AdaptySubscriptionPeriod;
	intro_offer?: AdaptyIntroOffer;
	promotional_offer?: AdaptyPromotionalOffer;
	created_at: string;
	updated_at: string;
}

export interface AdaptyPrice {
	amount: number;
	currency_code: string;
	currency_symbol: string;
	localized_string: string;
}

export interface AdaptySubscriptionPeriod {
	unit: "day" | "week" | "month" | "year";
	number_of_units: number;
}

export interface AdaptyIntroOffer {
	type: "free_trial" | "pay_as_you_go" | "pay_up_front";
	price?: AdaptyPrice;
	subscription_period: AdaptySubscriptionPeriod;
	number_of_periods?: number;
}

export interface AdaptyPromotionalOffer {
	identifier: string;
	type: "free_trial" | "pay_as_you_go" | "pay_up_front";
	price?: AdaptyPrice;
	subscription_period: AdaptySubscriptionPeriod;
	number_of_periods?: number;
}

// Remote Config Types
export interface AdaptyRemoteConfig {
	id: string;
	placement_id: string;
	lang: string;
	data: AdaptyRemoteConfigData;
	created_at: string;
	updated_at: string;
	version: number;
}

export interface AdaptyRemoteConfigData {
	paywall: {
		id: string;
		name: string;
		title: string;
		subtitle?: string;
		description?: string;
		cta_text: string;
		cta_secondary_text?: string;
		theme: AdaptyThemeConfig;
		features?: AdaptyFeatureConfig[];
		testimonials?: AdaptyTestimonialConfig[];
		product_labels?: AdaptyProductLabelConfig[];
	};
	products: AdaptyProduct[];
	ab_test?: AdaptyABTestConfig;
	version: `v${number}-${string}`;
	locale: string;
	generated_at: string;
}

export interface AdaptyThemeConfig {
	name: string;
	primary_color: string;
	background_color: string;
	text_color: string;
	button_style: "rounded" | "square";
	header_style: "minimal" | "hero" | "gradient";
	product_display_style: "list" | "grid" | "carousel";
	show_features: boolean;
	show_testimonials: boolean;
	custom_styles?: string;
	background_image?: string;
	logo?: string;
}

export interface AdaptyFeatureConfig {
	icon: string;
	title: string;
	description: string;
	order: number;
	is_highlighted: boolean;
	icon_image?: string;
}

export interface AdaptyTestimonialConfig {
	author_name: string;
	author_title?: string;
	content: string;
	rating?: number;
	author_avatar?: string;
	company?: string;
	order: number;
}

export interface AdaptyProductLabelConfig {
	product_id: string;
	badge_text?: string;
	badge_color?: string;
	subtitle?: string;
	highlight: boolean;
	savings_percentage?: number;
	badge_position: "top-left" | "top-right" | "bottom-left" | "bottom-right";
}

export interface AdaptyABTestConfig {
	id: string;
	name: string;
	variation_id: string;
	traffic_percentage: number;
}

export interface CreateRemoteConfigRequest {
	placement_id: string;
	lang: string;
	data: AdaptyRemoteConfigData;
}

export interface UpdateRemoteConfigRequest {
	data: AdaptyRemoteConfigData;
	version?: number;
}

// Paywall Types (Adapty-specific)
export interface AdaptyPaywall {
	id: string;
	name: string;
	products: AdaptyProduct[];
	remote_config?: AdaptyRemoteConfig;
	created_at: string;
	updated_at: string;
}

export interface CreatePaywallRequest {
	name: string;
	products: string[]; // Product IDs
}

export interface UpdatePaywallRequest {
	name?: string;
	products?: string[]; // Product IDs
}

// A/B Testing Types
export interface AdaptyABTest {
	id: string;
	name: string;
	placement_id: string;
	variations: AdaptyABTestVariation[];
	traffic_allocation: Record<string, number>;
	status: "draft" | "running" | "paused" | "completed";
	start_date?: string;
	end_date?: string;
	winner_variation_id?: string;
	created_at: string;
	updated_at: string;
}

export interface AdaptyABTestVariation {
	id: string;
	name: string;
	paywall_id: string;
	traffic_percentage: number;
	is_control: boolean;
}

export interface CreateABTestRequest {
	name: string;
	placement_id: string;
	variations: {
		name: string;
		paywall_id: string;
		traffic_percentage: number;
		is_control?: boolean;
	}[];
}

export interface UpdateABTestRequest {
	name?: string;
	variations?: {
		id?: string;
		name?: string;
		paywall_id?: string;
		traffic_percentage?: number;
		is_control?: boolean;
	}[];
	status?: "draft" | "running" | "paused" | "completed";
	start_date?: string;
	end_date?: string;
	winner_variation_id?: string;
}

// Analytics Types
export interface AdaptyAnalytics {
	placement_id: string;
	period: {
		start_date: string;
		end_date: string;
	};
	metrics: {
		impressions: number;
		conversions: number;
		conversion_rate: number;
		revenue: number;
		arpu: number;
		arppu: number;
	};
	breakdown?: {
		by_country?: Record<string, AdaptyAnalyticsMetrics>;
		by_product?: Record<string, AdaptyAnalyticsMetrics>;
		by_variation?: Record<string, AdaptyAnalyticsMetrics>;
	};
}

export interface AdaptyAnalyticsMetrics {
	impressions: number;
	conversions: number;
	conversion_rate: number;
	revenue: number;
	arpu: number;
	arppu: number;
}

export interface AnalyticsRequest {
	placement_id?: string;
	start_date: string;
	end_date: string;
	breakdown?: ("country" | "product" | "variation")[];
	currency?: string;
}

// Webhook Types
export interface AdaptyWebhookEvent {
	type: string;
	id: string;
	created_at: string;
	data: Record<string, any>;
}

export interface AdaptyWebhookPayload {
	events: AdaptyWebhookEvent[];
}

// API Client Configuration
export interface AdaptyClientConfig extends AdaptyAuthConfig {
	enableRetry?: boolean;
	enableCircuitBreaker?: boolean;
	circuitBreakerThreshold?: number;
	circuitBreakerTimeout?: number;
	enableLogging?: boolean;
	logLevel?: "debug" | "info" | "warn" | "error";
	enableTokenRefresh?: boolean;
	tokenRefreshThreshold?: number;
	refreshToken?: string;
	onTokenRefresh?: (newToken: string) => void;
}

// Circuit Breaker Types
export interface CircuitBreakerState {
	state: "closed" | "open" | "half-open";
	failureCount: number;
	lastFailureTime?: number;
	nextAttemptTime?: number;
}

// Request/Response Types for API Methods
export interface ListPlacementsRequest {
	page?: number;
	per_page?: number;
	name?: string;
}

export interface ListProductsRequest {
	page?: number;
	per_page?: number;
	store?: "app_store" | "play_store";
	type?: "subscription" | "non_consumable" | "consumable";
}

export interface ListPaywallsRequest {
	page?: number;
	per_page?: number;
	name?: string;
}

export interface ListRemoteConfigsRequest {
	placement_id?: string;
	lang?: string;
	page?: number;
	per_page?: number;
}

// Error Types
export class AdaptyApiError extends Error {
	public code: string;
	public statusCode?: number;
	public details?: Record<string, any>;

	constructor(
		message: string,
		code: string,
		statusCode?: number,
		details?: Record<string, any>,
	) {
		super(message);
		this.name = "AdaptyApiError";
		this.code = code;
		this.statusCode = statusCode;
		this.details = details;
	}
}

export class AdaptyNetworkError extends Error {
	public originalError: Error;

	constructor(message: string, originalError: Error) {
		super(message);
		this.name = "AdaptyNetworkError";
		this.originalError = originalError;
	}
}

export class AdaptyAuthenticationError extends Error {
	constructor(message: string = "Authentication failed") {
		super(message);
		this.name = "AdaptyAuthenticationError";
	}
}

export class AdaptyRateLimitError extends Error {
	public retryAfter?: number;

	constructor(message: string, retryAfter?: number) {
		super(message);
		this.name = "AdaptyRateLimitError";
		this.retryAfter = retryAfter;
	}
}

export class AdaptyValidationError extends Error {
	public validationErrors: Record<string, string[]>;

	constructor(
		message: string,
		validationErrors: Record<string, string[]> = {},
	) {
		super(message);
		this.name = "AdaptyValidationError";
		this.validationErrors = validationErrors;
	}
}

export class AdaptyConflictError extends Error {
	public conflictDetails?: Record<string, any>;

	constructor(message: string, conflictDetails?: Record<string, any>) {
		super(message);
		this.name = "AdaptyConflictError";
		this.conflictDetails = conflictDetails;
	}
}

export class AdaptyServiceUnavailableError extends Error {
	public estimatedRecoveryTime?: number;

	constructor(message: string, estimatedRecoveryTime?: number) {
		super(message);
		this.name = "AdaptyServiceUnavailableError";
		this.estimatedRecoveryTime = estimatedRecoveryTime;
	}
}

// Utility Types
export type AdaptyApiMethod = "GET" | "POST" | "PUT" | "PATCH" | "DELETE";

export interface AdaptyRequestConfig {
	method: AdaptyApiMethod;
	url: string;
	data?: any;
	params?: Record<string, any>;
	headers?: Record<string, string>;
	timeout?: number;
}

export interface AdaptyRetryConfig {
	attempts: number;
	delay: number;
	backoff: "linear" | "exponential";
	maxDelay?: number;
	retryCondition?: (error: any) => boolean;
}
