/**
 * Adapty API Client with TypeScript support
 */

import axios, {
	type AxiosError,
	type AxiosInstance,
	type AxiosResponse,
} from "axios";
import {
	type AdaptyABTest,
	type AdaptyAnalytics,
	AdaptyApiError,
	type AdaptyApiResponse,
	AdaptyAuthenticationError,
	type AdaptyClientConfig,
	AdaptyConflictError,
	AdaptyNetworkError,
	type AdaptyPaywall,
	type AdaptyPlacement,
	type AdaptyProduct,
	AdaptyRateLimitError,
	type AdaptyRemoteConfig,
	type AdaptyRequestConfig,
	type AdaptyRetryConfig,
	AdaptyServiceUnavailableError,
	AdaptyValidationError,
	type AnalyticsRequest,
	type CircuitBreakerState,
	type CreateABTestRequest,
	type CreatePaywallRequest,
	type CreatePlacementRequest,
	type CreateRemoteConfigRequest,
	type ListPaywallsRequest,
	type ListPlacementsRequest,
	type ListProductsRequest,
	type ListRemoteConfigsRequest,
	type UpdateABTestRequest,
	type UpdatePaywallRequest,
	type UpdatePlacementRequest,
	type UpdateRemoteConfigRequest,
} from "./types";

export class AdaptyApiClient {
	private client: AxiosInstance;
	private config: AdaptyClientConfig;
	private circuitBreakerState: CircuitBreakerState;
	private retryConfig: AdaptyRetryConfig;
	private tokenExpiryTime?: number;
	private refreshPromise?: Promise<string>;

	constructor(config: AdaptyClientConfig) {
		this.config = {
			baseUrl: "https://api.adapty.io/api/v1",
			timeout: 30000,
			retryAttempts: 3,
			retryDelay: 1000,
			enableRetry: true,
			enableCircuitBreaker: true,
			circuitBreakerThreshold: 5,
			circuitBreakerTimeout: 60000,
			enableLogging: true,
			logLevel: "info",
			enableTokenRefresh: false,
			tokenRefreshThreshold: 300000, // 5 minutes
			...config,
		};

		this.circuitBreakerState = {
			state: "closed",
			failureCount: 0,
		};

		this.retryConfig = {
			attempts: this.config.retryAttempts || 3,
			delay: this.config.retryDelay || 1000,
			backoff: "exponential",
			maxDelay: 30000,
			retryCondition: (error: any) => {
				// Retry on network errors and 5xx status codes
				return (
					!error.response ||
					(error.response.status >= 500 && error.response.status < 600)
				);
			},
		};

		this.client = this.createAxiosInstance();
		this.setupInterceptors();
	}

	/**
	 * Create and configure Axios instance
	 */
	private createAxiosInstance(): AxiosInstance {
		const instance = axios.create({
			baseURL: this.config.baseUrl,
			timeout: this.config.timeout,
			headers: {
				Authorization: `Api-Key ${this.config.apiKey}`,
				"Content-Type": "application/json",
				"User-Agent": "Strapi-Adapty-CMS/1.0.0",
			},
		});

		return instance;
	}

	/**
	 * Setup request and response interceptors
	 */
	private setupInterceptors(): void {
		// Request interceptor
		this.client.interceptors.request.use(
			async (config) => {
				if (this.config.enableLogging && this.config.logLevel === "debug") {
					console.log(
						`[Adapty API] ${config.method?.toUpperCase()} ${config.url}`,
						{
							params: config.params,
							data: config.data,
						},
					);
				}

				// Check circuit breaker
				if (this.config.enableCircuitBreaker && this.isCircuitBreakerOpen()) {
					throw new AdaptyApiError(
						"Circuit breaker is open. Service temporarily unavailable.",
						"CIRCUIT_BREAKER_OPEN",
					);
				}

				// Handle token refresh if enabled
				if (this.config.enableTokenRefresh && this.shouldRefreshToken()) {
					await this.refreshTokenIfNeeded();
				}

				return config;
			},
			(error) => {
				this.log("error", "Request interceptor error:", error);
				return Promise.reject(error);
			},
		);

		// Response interceptor
		this.client.interceptors.response.use(
			(response) => {
				// Reset circuit breaker on successful response
				if (this.config.enableCircuitBreaker) {
					this.resetCircuitBreaker();
				}

				if (this.config.enableLogging && this.config.logLevel === "debug") {
					console.log(
						`[Adapty API] Response ${response.status}:`,
						response.data,
					);
				}

				return response;
			},
			async (error: AxiosError) => {
				// Handle circuit breaker
				if (this.config.enableCircuitBreaker) {
					this.recordFailure();
				}

				// Handle retry logic
				if (this.config.enableRetry && this.shouldRetry(error)) {
					return this.retryRequest(error);
				}

				// Transform error
				const transformedError = this.transformError(error);
				this.log("error", "API Error:", transformedError);

				throw transformedError;
			},
		);
	}

	/**
	 * Check if circuit breaker is open
	 */
	private isCircuitBreakerOpen(): boolean {
		const now = Date.now();

		if (this.circuitBreakerState.state === "open") {
			if (
				this.circuitBreakerState.nextAttemptTime &&
				now >= this.circuitBreakerState.nextAttemptTime
			) {
				this.circuitBreakerState.state = "half-open";
				return false;
			}
			return true;
		}

		return false;
	}

	/**
	 * Record failure for circuit breaker
	 */
	private recordFailure(): void {
		this.circuitBreakerState.failureCount++;
		this.circuitBreakerState.lastFailureTime = Date.now();

		if (
			this.circuitBreakerState.failureCount >=
			(this.config.circuitBreakerThreshold || 5)
		) {
			this.circuitBreakerState.state = "open";
			this.circuitBreakerState.nextAttemptTime =
				Date.now() + (this.config.circuitBreakerTimeout || 60000);
			this.log("warn", "Circuit breaker opened due to repeated failures");
		}
	}

	/**
	 * Reset circuit breaker
	 */
	private resetCircuitBreaker(): void {
		if (this.circuitBreakerState.state !== "closed") {
			this.circuitBreakerState.state = "closed";
			this.circuitBreakerState.failureCount = 0;
			this.circuitBreakerState.lastFailureTime = undefined;
			this.circuitBreakerState.nextAttemptTime = undefined;
			this.log("info", "Circuit breaker reset");
		}
	}

	/**
	 * Check if request should be retried
	 */
	private shouldRetry(error: AxiosError): boolean {
		if (
			!error.config ||
			(error.config as any).__retryCount >= this.retryConfig.attempts
		) {
			return false;
		}

		return this.retryConfig.retryCondition
			? this.retryConfig.retryCondition(error)
			: false;
	}

	/**
	 * Retry failed request
	 */
	private async retryRequest(error: AxiosError): Promise<AxiosResponse> {
		const config = error.config as any;
		config.__retryCount = config.__retryCount || 0;
		config.__retryCount++;

		const delay = this.calculateRetryDelay(config.__retryCount);

		this.log(
			"info",
			`Retrying request (attempt ${config.__retryCount}/${this.retryConfig.attempts}) after ${delay}ms`,
		);

		await this.sleep(delay);

		return this.client.request(config);
	}

	/**
	 * Calculate retry delay with backoff
	 */
	private calculateRetryDelay(attempt: number): number {
		let delay = this.retryConfig.delay;

		if (this.retryConfig.backoff === "exponential") {
			delay = delay * 2 ** (attempt - 1);
		} else {
			delay = delay * attempt;
		}

		return Math.min(delay, this.retryConfig.maxDelay || 30000);
	}

	/**
	 * Sleep utility
	 */
	private sleep(ms: number): Promise<void> {
		return new Promise((resolve) => setTimeout(resolve, ms));
	}

	/**
	 * Transform Axios error to Adapty error
	 */
	private transformError(error: AxiosError): Error {
		return this.handleSpecificErrors(error);
	}

	/**
	 * Logging utility
	 */
	private log(
		level: "debug" | "info" | "warn" | "error",
		message: string,
		...args: any[]
	): void {
		if (!this.config.enableLogging) return;

		const logLevels = { debug: 0, info: 1, warn: 2, error: 3 };
		const currentLevel = logLevels[this.config.logLevel || "info"];
		const messageLevel = logLevels[level];

		if (messageLevel >= currentLevel) {
			console[level](`[Adapty API] ${message}`, ...args);
		}
	}

	/**
	 * Make API request
	 */
	private async request<T>(
		config: AdaptyRequestConfig,
	): Promise<AdaptyApiResponse<T>> {
		const response = await this.client.request({
			method: config.method,
			url: config.url,
			data: config.data,
			params: config.params,
			headers: config.headers,
			timeout: config.timeout,
		});

		return response.data;
	}

	// ==================== PLACEMENT METHODS ====================

	/**
	 * List all placements
	 */
	async listPlacements(
		params?: ListPlacementsRequest,
	): Promise<AdaptyApiResponse<AdaptyPlacement[]>> {
		return this.request<AdaptyPlacement[]>({
			method: "GET",
			url: "/placements",
			params,
		});
	}

	/**
	 * Get placement by ID
	 */
	async getPlacement(
		placementId: string,
	): Promise<AdaptyApiResponse<AdaptyPlacement>> {
		return this.request<AdaptyPlacement>({
			method: "GET",
			url: `/placements/${placementId}`,
		});
	}

	/**
	 * Create new placement
	 */
	async createPlacement(
		data: CreatePlacementRequest,
	): Promise<AdaptyApiResponse<AdaptyPlacement>> {
		return this.request<AdaptyPlacement>({
			method: "POST",
			url: "/placements",
			data,
		});
	}

	/**
	 * Update placement
	 */
	async updatePlacement(
		placementId: string,
		data: UpdatePlacementRequest,
	): Promise<AdaptyApiResponse<AdaptyPlacement>> {
		return this.request<AdaptyPlacement>({
			method: "PUT",
			url: `/placements/${placementId}`,
			data,
		});
	}

	/**
	 * Delete placement
	 */
	async deletePlacement(placementId: string): Promise<AdaptyApiResponse<void>> {
		return this.request<void>({
			method: "DELETE",
			url: `/placements/${placementId}`,
		});
	}

	// ==================== PRODUCT METHODS ====================

	/**
	 * List all products
	 */
	async listProducts(
		params?: ListProductsRequest,
	): Promise<AdaptyApiResponse<AdaptyProduct[]>> {
		return this.request<AdaptyProduct[]>({
			method: "GET",
			url: "/products",
			params,
		});
	}

	/**
	 * Get product by ID
	 */
	async getProduct(
		productId: string,
	): Promise<AdaptyApiResponse<AdaptyProduct>> {
		return this.request<AdaptyProduct>({
			method: "GET",
			url: `/products/${productId}`,
		});
	}

	/**
	 * Sync products from app stores
	 */
	async syncProducts(): Promise<AdaptyApiResponse<{ synced_count: number }>> {
		return this.request<{ synced_count: number }>({
			method: "POST",
			url: "/products/sync",
		});
	}

	// ==================== PAYWALL METHODS ====================

	/**
	 * List all paywalls
	 */
	async listPaywalls(
		params?: ListPaywallsRequest,
	): Promise<AdaptyApiResponse<AdaptyPaywall[]>> {
		return this.request<AdaptyPaywall[]>({
			method: "GET",
			url: "/paywalls",
			params,
		});
	}

	/**
	 * Get paywall by ID
	 */
	async getPaywall(
		paywallId: string,
	): Promise<AdaptyApiResponse<AdaptyPaywall>> {
		return this.request<AdaptyPaywall>({
			method: "GET",
			url: `/paywalls/${paywallId}`,
		});
	}

	/**
	 * Create new paywall
	 */
	async createPaywall(
		data: CreatePaywallRequest,
	): Promise<AdaptyApiResponse<AdaptyPaywall>> {
		return this.request<AdaptyPaywall>({
			method: "POST",
			url: "/paywalls",
			data,
		});
	}

	/**
	 * Update paywall
	 */
	async updatePaywall(
		paywallId: string,
		data: UpdatePaywallRequest,
	): Promise<AdaptyApiResponse<AdaptyPaywall>> {
		return this.request<AdaptyPaywall>({
			method: "PUT",
			url: `/paywalls/${paywallId}`,
			data,
		});
	}

	/**
	 * Delete paywall
	 */
	async deletePaywall(paywallId: string): Promise<AdaptyApiResponse<void>> {
		return this.request<void>({
			method: "DELETE",
			url: `/paywalls/${paywallId}`,
		});
	}

	// ==================== REMOTE CONFIG METHODS ====================

	/**
	 * List remote configs
	 */
	async listRemoteConfigs(
		params?: ListRemoteConfigsRequest,
	): Promise<AdaptyApiResponse<AdaptyRemoteConfig[]>> {
		return this.request<AdaptyRemoteConfig[]>({
			method: "GET",
			url: "/remote-configs",
			params,
		});
	}

	/**
	 * Get remote config by ID
	 */
	async getRemoteConfig(
		configId: string,
	): Promise<AdaptyApiResponse<AdaptyRemoteConfig>> {
		return this.request<AdaptyRemoteConfig>({
			method: "GET",
			url: `/remote-configs/${configId}`,
		});
	}

	/**
	 * Get remote config by placement ID
	 */
	async getRemoteConfigByPlacement(
		placementId: string,
		lang?: string,
	): Promise<AdaptyApiResponse<AdaptyRemoteConfig>> {
		return this.request<AdaptyRemoteConfig>({
			method: "GET",
			url: `/placements/${placementId}/remote-config`,
			params: lang ? { lang } : undefined,
		});
	}

	/**
	 * Create remote config
	 */
	async createRemoteConfig(
		data: CreateRemoteConfigRequest,
	): Promise<AdaptyApiResponse<AdaptyRemoteConfig>> {
		return this.request<AdaptyRemoteConfig>({
			method: "POST",
			url: "/remote-configs",
			data,
		});
	}

	/**
	 * Update remote config
	 */
	async updateRemoteConfig(
		configId: string,
		data: UpdateRemoteConfigRequest,
	): Promise<AdaptyApiResponse<AdaptyRemoteConfig>> {
		return this.request<AdaptyRemoteConfig>({
			method: "PUT",
			url: `/remote-configs/${configId}`,
			data,
		});
	}

	/**
	 * Update remote config by placement
	 */
	async updateRemoteConfigByPlacement(
		placementId: string,
		data: UpdateRemoteConfigRequest,
	): Promise<AdaptyApiResponse<AdaptyRemoteConfig>> {
		return this.request<AdaptyRemoteConfig>({
			method: "PUT",
			url: `/placements/${placementId}/remote-config`,
			data,
		});
	}

	/**
	 * Delete remote config
	 */
	async deleteRemoteConfig(configId: string): Promise<AdaptyApiResponse<void>> {
		return this.request<void>({
			method: "DELETE",
			url: `/remote-configs/${configId}`,
		});
	}

	// ==================== A/B TESTING METHODS ====================

	/**
	 * List A/B tests
	 */
	async listABTests(): Promise<AdaptyApiResponse<AdaptyABTest[]>> {
		return this.request<AdaptyABTest[]>({
			method: "GET",
			url: "/ab-tests",
		});
	}

	/**
	 * Get A/B test by ID
	 */
	async getABTest(testId: string): Promise<AdaptyApiResponse<AdaptyABTest>> {
		return this.request<AdaptyABTest>({
			method: "GET",
			url: `/ab-tests/${testId}`,
		});
	}

	/**
	 * Create A/B test
	 */
	async createABTest(
		data: CreateABTestRequest,
	): Promise<AdaptyApiResponse<AdaptyABTest>> {
		return this.request<AdaptyABTest>({
			method: "POST",
			url: "/ab-tests",
			data,
		});
	}

	/**
	 * Update A/B test
	 */
	async updateABTest(
		testId: string,
		data: UpdateABTestRequest,
	): Promise<AdaptyApiResponse<AdaptyABTest>> {
		return this.request<AdaptyABTest>({
			method: "PUT",
			url: `/ab-tests/${testId}`,
			data,
		});
	}

	/**
	 * Start A/B test
	 */
	async startABTest(testId: string): Promise<AdaptyApiResponse<AdaptyABTest>> {
		return this.request<AdaptyABTest>({
			method: "POST",
			url: `/ab-tests/${testId}/start`,
		});
	}

	/**
	 * Stop A/B test
	 */
	async stopABTest(
		testId: string,
		winnerVariationId?: string,
	): Promise<AdaptyApiResponse<AdaptyABTest>> {
		return this.request<AdaptyABTest>({
			method: "POST",
			url: `/ab-tests/${testId}/stop`,
			data: winnerVariationId
				? { winner_variation_id: winnerVariationId }
				: undefined,
		});
	}

	/**
	 * Delete A/B test
	 */
	async deleteABTest(testId: string): Promise<AdaptyApiResponse<void>> {
		return this.request<void>({
			method: "DELETE",
			url: `/ab-tests/${testId}`,
		});
	}

	// ==================== ANALYTICS METHODS ====================

	/**
	 * Get analytics data
	 */
	async getAnalytics(
		params: AnalyticsRequest,
	): Promise<AdaptyApiResponse<AdaptyAnalytics>> {
		return this.request<AdaptyAnalytics>({
			method: "GET",
			url: "/analytics",
			params,
		});
	}

	/**
	 * Get placement analytics
	 */
	async getPlacementAnalytics(
		placementId: string,
		params: Omit<AnalyticsRequest, "placement_id">,
	): Promise<AdaptyApiResponse<AdaptyAnalytics>> {
		return this.request<AdaptyAnalytics>({
			method: "GET",
			url: `/placements/${placementId}/analytics`,
			params,
		});
	}

	// ==================== UTILITY METHODS ====================

	/**
	 * Test API connection
	 */
	async testConnection(): Promise<boolean> {
		try {
			await this.request<any>({
				method: "GET",
				url: "/health",
			});
			return true;
		} catch (error) {
			this.log("error", "Connection test failed:", error);
			return false;
		}
	}

	/**
	 * Get API status
	 */
	async getApiStatus(): Promise<
		AdaptyApiResponse<{ status: string; version: string }>
	> {
		return this.request<{ status: string; version: string }>({
			method: "GET",
			url: "/status",
		});
	}

	/**
	 * Update API key
	 */
	updateApiKey(newApiKey: string): void {
		this.config.apiKey = newApiKey;
		this.client.defaults.headers.Authorization = `Api-Key ${newApiKey}`;
		this.log("info", "API key updated");
	}

	/**
	 * Get current configuration
	 */
	getConfig(): AdaptyClientConfig {
		return { ...this.config };
	}

	/**
	 * Get circuit breaker state
	 */
	getCircuitBreakerState(): CircuitBreakerState {
		return { ...this.circuitBreakerState };
	}

	// ==================== TOKEN MANAGEMENT ====================

	/**
	 * Check if token should be refreshed
	 */
	private shouldRefreshToken(): boolean {
		if (!this.config.enableTokenRefresh || !this.tokenExpiryTime) {
			return false;
		}

		const now = Date.now();
		const threshold = this.config.tokenRefreshThreshold || 300000; // 5 minutes default

		return this.tokenExpiryTime - now <= threshold;
	}

	/**
	 * Refresh token if needed
	 */
	private async refreshTokenIfNeeded(): Promise<void> {
		if (this.refreshPromise) {
			await this.refreshPromise;
			return;
		}

		if (!this.config.refreshToken) {
			throw new AdaptyAuthenticationError("No refresh token available");
		}

		this.refreshPromise = this.performTokenRefresh();

		try {
			const newToken = await this.refreshPromise;
			this.updateApiKey(newToken);

			if (this.config.onTokenRefresh) {
				this.config.onTokenRefresh(newToken);
			}

			this.log("info", "Token refreshed successfully");
		} catch (error) {
			this.log("error", "Token refresh failed:", error);
			throw new AdaptyAuthenticationError("Token refresh failed");
		} finally {
			this.refreshPromise = undefined;
		}
	}

	/**
	 * Perform actual token refresh
	 */
	private async performTokenRefresh(): Promise<string> {
		try {
			const response = await axios.post(
				`${this.config.baseUrl}/auth/refresh`,
				{
					refresh_token: this.config.refreshToken,
				},
				{
					timeout: this.config.timeout,
					headers: {
						"Content-Type": "application/json",
						"User-Agent": "Strapi-Adapty-CMS/1.0.0",
					},
				},
			);

			const { access_token, expires_in } = response.data;

			// Set token expiry time
			this.tokenExpiryTime = Date.now() + expires_in * 1000;

			return access_token;
		} catch (_error) {
			throw new AdaptyAuthenticationError("Failed to refresh token");
		}
	}

	/**
	 * Set token expiry time manually
	 */
	setTokenExpiry(expiryTime: number): void {
		this.tokenExpiryTime = expiryTime;
	}

	// ==================== ENHANCED ERROR HANDLING ====================

	/**
	 * Handle specific API errors with enhanced context
	 */
	private handleSpecificErrors(error: AxiosError): Error {
		const { response } = error;

		if (!response) {
			return new AdaptyNetworkError("Network error occurred", error);
		}

		const { status, data } = response;
		const errorData = data as any;

		switch (status) {
			case 400:
				return new AdaptyApiError(
					errorData?.message || "Bad request - invalid parameters",
					"BAD_REQUEST",
					status,
					errorData,
				);

			case 401:
				return new AdaptyAuthenticationError(
					errorData?.message || "Invalid API key or authentication failed",
				);

			case 403:
				return new AdaptyApiError(
					errorData?.message || "Forbidden - insufficient permissions",
					"FORBIDDEN",
					status,
					errorData,
				);

			case 404:
				return new AdaptyApiError(
					errorData?.message || "Resource not found",
					"NOT_FOUND",
					status,
					errorData,
				);

			case 409:
				return new AdaptyConflictError(
					errorData?.message || "Conflict - resource already exists",
					errorData,
				);

			case 422:
				return new AdaptyValidationError(
					errorData?.message || "Validation error",
					errorData?.validation_errors || {},
				);

			case 429: {
				const retryAfter = response.headers["retry-after"];
				return new AdaptyRateLimitError(
					errorData?.message || "Rate limit exceeded",
					retryAfter ? parseInt(retryAfter) * 1000 : undefined,
				);
			}

			case 500:
				return new AdaptyApiError(
					errorData?.message || "Internal server error",
					"INTERNAL_SERVER_ERROR",
					status,
					errorData,
				);

			case 502:
				return new AdaptyApiError(
					errorData?.message || "Bad gateway",
					"BAD_GATEWAY",
					status,
					errorData,
				);

			case 503:
				return new AdaptyServiceUnavailableError(
					errorData?.message || "Service unavailable",
					errorData?.estimated_recovery_time,
				);

			case 504:
				return new AdaptyApiError(
					errorData?.message || "Gateway timeout",
					"GATEWAY_TIMEOUT",
					status,
					errorData,
				);

			default:
				return new AdaptyApiError(
					errorData?.message || `HTTP ${status} error`,
					errorData?.code || `HTTP_${status}`,
					status,
					errorData,
				);
		}
	}

	// ==================== ADDITIONAL API METHODS ====================

	/**
	 * Get API health status
	 */
	async getHealth(): Promise<
		AdaptyApiResponse<{ status: string; timestamp: string }>
	> {
		return this.request<{ status: string; timestamp: string }>({
			method: "GET",
			url: "/health",
		});
	}

	/**
	 * Get API version information
	 */
	async getVersion(): Promise<
		AdaptyApiResponse<{ version: string; build: string }>
	> {
		return this.request<{ version: string; build: string }>({
			method: "GET",
			url: "/version",
		});
	}

	/**
	 * Validate API key
	 */
	async validateApiKey(): Promise<
		AdaptyApiResponse<{ valid: boolean; permissions: string[] }>
	> {
		return this.request<{ valid: boolean; permissions: string[] }>({
			method: "GET",
			url: "/auth/validate",
		});
	}

	/**
	 * Get account information
	 */
	async getAccount(): Promise<
		AdaptyApiResponse<{ id: string; name: string; plan: string }>
	> {
		return this.request<{ id: string; name: string; plan: string }>({
			method: "GET",
			url: "/account",
		});
	}

	/**
	 * Bulk operation for placements
	 */
	async bulkUpdatePlacements(
		operations: Array<{
			id: string;
			operation: "update" | "delete";
			data?: UpdatePlacementRequest;
		}>,
	): Promise<
		AdaptyApiResponse<{ success: number; failed: number; errors: any[] }>
	> {
		return this.request<{ success: number; failed: number; errors: any[] }>({
			method: "POST",
			url: "/placements/bulk",
			data: { operations },
		});
	}

	/**
	 * Export configuration for backup
	 */
	async exportConfiguration(): Promise<
		AdaptyApiResponse<{ config: any; timestamp: string }>
	> {
		return this.request<{ config: any; timestamp: string }>({
			method: "GET",
			url: "/export",
		});
	}

	/**
	 * Import configuration from backup
	 */
	async importConfiguration(
		config: any,
	): Promise<AdaptyApiResponse<{ imported: number; skipped: number }>> {
		return this.request<{ imported: number; skipped: number }>({
			method: "POST",
			url: "/import",
			data: { config },
		});
	}
}

// Factory function for creating client instance
export function createAdaptyClient(
	config: AdaptyClientConfig,
): AdaptyApiClient {
	return new AdaptyApiClient(config);
}

// Default export
export default AdaptyApiClient;
