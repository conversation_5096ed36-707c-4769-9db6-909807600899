/**
 * Adapty service factory and exports
 */

import { type AdaptyApiClient, createAdaptyClient } from "./client";
import type { AdaptyClientConfig } from "./types";

export { AdaptyApiClient } from "./client";
export { RemoteConfigService, createRemoteConfigService } from "./remote-config";
// Export all types
export * from "./types";

// Export specific error types for easier importing
export {
	AdaptyApiError,
	AdaptyAuthenticationError,
	AdaptyConflictError,
	AdaptyNetworkError,
	AdaptyRateLimitError,
	AdaptyServiceUnavailableError,
	AdaptyValidationError,
} from "./types";

// Service instance
let adaptyClient: AdaptyApiClient | null = null;

/**
 * Initialize Adapty service with configuration
 */
export function initializeAdaptyService(
	config: AdaptyClientConfig,
): AdaptyApiClient {
	adaptyClient = createAdaptyClient(config);
	return adaptyClient;
}

/**
 * Get the current Adapty service instance
 */
export function getAdaptyService(): AdaptyApiClient {
	if (!adaptyClient) {
		throw new Error(
			"Adapty service not initialized. Call initializeAdaptyService() first.",
		);
	}
	return adaptyClient;
}

/**
 * Create a new Adapty client instance (for testing or multiple configs)
 */
export function createAdaptyService(
	config: AdaptyClientConfig,
): AdaptyApiClient {
	return createAdaptyClient(config);
}

/**
 * Reset the service instance (for testing)
 */
export function resetAdaptyService(): void {
	adaptyClient = null;
}

/**
 * Check if service is initialized
 */
export function isAdaptyServiceInitialized(): boolean {
	return adaptyClient !== null;
}

// Default export
export default {
	initialize: initializeAdaptyService,
	getInstance: getAdaptyService,
	create: createAdaptyService,
	reset: resetAdaptyService,
	isInitialized: isAdaptyServiceInitialized,
};
