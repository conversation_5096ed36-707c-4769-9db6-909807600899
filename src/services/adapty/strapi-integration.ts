/**
 * Strapi integration service for Adapty API
 */

import { getAdaptyService } from "./index";
import type {
	AdaptyPlacement,
	AdaptyProduct,
	AdaptyRemoteConfigData,
} from "./types";

// Note: Generated Strapi types would be imported here when available
// import type { ApiPaywallPaywall, ApiProductProduct, ApiSyncStatusSyncStatus } from "../../../types/generated/contentTypes";

/**
 * Sync status tracking interface
 */
interface SyncStatus {
	entity_type: "paywall" | "product" | "placement";
	entity_id: string;
	last_sync: Date;
	sync_status: "pending" | "synced" | "error" | "conflict";
	error_message?: string;
	retry_count: number;
	adapty_last_modified?: Date;
	strapi_last_modified?: Date;
}

/**
 * Conflict resolution strategy
 */
type ConflictResolution = "adapty_wins" | "strapi_wins" | "manual" | "merge";

/**
 * Webhook event types
 */
interface WebhookEvent {
	type:
	| "placement.updated"
	| "product.updated"
	| "placement.created"
	| "product.created";
	data: any;
	timestamp: string;
	id: string;
}

/**
 * Service for integrating Adapty API with Strapi paywall content
 */
export class AdaptyIntegrationService {
	/**
	 * Sync Strapi paywall to Adapty placement
	 */
	async syncPaywallToAdapty(
		paywallId: number,
	): Promise<{ success: boolean; errors?: string[] }> {
		try {
			const adaptyClient = getAdaptyService();

			// Get paywall data from Strapi
			const paywall = await strapi.entityService.findOne(
				"api::paywall.paywall",
				paywallId,
				{
					populate: {
						theme: true,
						features: true,
						testimonials: true,
						product_labels: true,
					},
				},
			);

			if (!paywall) {
				return { success: false, errors: ["Paywall not found"] };
			}

			// Check if placement exists in Adapty
			try {
				await adaptyClient.getPlacement(paywall.placement_id);
			} catch (_error) {
				// Create placement if it doesn't exist
				await adaptyClient.createPlacement({
					name: paywall.name,
					is_fallback: false,
				});
			}

			// Transform Strapi paywall to Adapty remote config format
			const remoteConfigData = this.transformPaywallToRemoteConfig(paywall);

			// Update or create remote config
			try {
				await adaptyClient.updateRemoteConfigByPlacement(paywall.placement_id, {
					data: remoteConfigData,
				});
			} catch (_error) {
				// Create if update fails
				await adaptyClient.createRemoteConfig({
					placement_id: paywall.placement_id,
					lang: "en", // Default language
					data: remoteConfigData,
				});
			}

			// Update sync status in Strapi
			await strapi.entityService.update("api::paywall.paywall", paywallId, {
				data: {
					adapty_sync_status: "synced",
					adapty_last_sync: new Date(),
				} as any,
			});

			return { success: true };
		} catch (error) {
			strapi.log.error("Failed to sync paywall to Adapty:", error);

			// Update sync status to error
			await strapi.entityService.update("api::paywall.paywall", paywallId, {
				data: {
					adapty_sync_status: "error",
					adapty_last_sync: new Date(),
				} as any,
			});

			return {
				success: false,
				errors: [error.message || "Unknown error occurred during sync"],
			};
		}
	}

	/**
	 * Transform Strapi paywall data to Adapty remote config format
	 */
	private transformPaywallToRemoteConfig(paywall: any): AdaptyRemoteConfigData {
		return {
			paywall: {
				id: paywall.id.toString(),
				name: paywall.name,
				title: paywall.title,
				subtitle: paywall.subtitle,
				description: paywall.description_text,
				cta_text: paywall.cta_text,
				cta_secondary_text: paywall.cta_secondary_text,
				theme: paywall.theme
					? {
						name: paywall.theme.name,
						primary_color: paywall.theme.primary_color,
						background_color: paywall.theme.background_color,
						text_color: paywall.theme.text_color,
						button_style: paywall.theme.button_style,
						header_style: paywall.theme.header_style,
						product_display_style: paywall.theme.product_display_style,
						show_features: paywall.theme.show_features,
						show_testimonials: paywall.theme.show_testimonials,
						custom_styles: paywall.theme.custom_styles,
						background_image: paywall.theme.background_image?.url,
						logo: paywall.theme.logo?.url,
					}
					: {
						name: "Default",
						primary_color: "#007bff",
						background_color: "#ffffff",
						text_color: "#000000",
						button_style: "rounded",
						header_style: "hero",
						product_display_style: "list",
						show_features: true,
						show_testimonials: false,
					},
				features:
					paywall.features?.map((feature: any) => ({
						icon: feature.icon,
						title: feature.title,
						description: feature.description,
						order: feature.order,
						is_highlighted: feature.is_highlighted,
						icon_image: feature.icon_image?.url,
					})) || [],
				testimonials:
					paywall.testimonials?.map((testimonial: any) => ({
						author_name: testimonial.author_name,
						author_title: testimonial.author_title,
						content: testimonial.content,
						rating: testimonial.rating,
						author_avatar: testimonial.author_avatar?.url,
						company: testimonial.company,
						order: testimonial.order,
					})) || [],
				product_labels:
					paywall.product_labels?.map((label: any) => ({
						product_id: label.product_id,
						badge_text: label.badge_text,
						badge_color: label.badge_color,
						subtitle: label.subtitle,
						highlight: label.highlight,
						savings_percentage: label.savings_percentage,
						badge_position: label.badge_position,
					})) || [],
			},
			products: [], // Will be populated from Adapty
			ab_test: undefined, // Will be set if A/B testing is active
			version: `v1-${Date.now()}`,
			locale: "en",
			generated_at: new Date().toISOString(),
		};
	}

	/**
	 * Sync products from Adapty to Strapi
	 */
	async syncProductsFromAdapty(): Promise<{
		success: boolean;
		synced_count: number;
		errors?: string[];
	}> {
		try {
			const adaptyClient = getAdaptyService();

			// Get products from Adapty
			const productsResponse = await adaptyClient.listProducts();
			const adaptyProducts = productsResponse.data;

			let syncedCount = 0;
			const errors: string[] = [];

			for (const product of adaptyProducts) {
				try {
					// Check if product already exists in Strapi
					const existingProducts = await strapi.entityService.findMany(
						"api::product.product",
						{
							filters: { adapty_product_id: product.id },
						},
					);

					if (existingProducts.length === 0) {
						// Create new product in Strapi
						await strapi.entityService.create("api::product.product", {
							data: {
								adapty_product_id: product.id,
								vendor_product_id: product.vendor_product_id,
								name: product.name,
								type: product.type,
								store: product.store,
								price_amount: product.price?.amount,
								price_currency: product.price?.currency_code,
								price_localized: product.price?.localized_string,
								subscription_period_unit: product.subscription_period?.unit,
								subscription_period_count:
									product.subscription_period?.number_of_units,
								last_synced: new Date(),
							},
						});
						syncedCount++;
					} else {
						// Update existing product
						await strapi.entityService.update(
							"api::product.product",
							existingProducts[0].id,
							{
								data: {
									name: product.name,
									price_amount: product.price?.amount,
									price_currency: product.price?.currency_code,
									price_localized: product.price?.localized_string,
									last_synced: new Date(),
								} as any,
							},
						);
						syncedCount++;
					}
				} catch (error) {
					errors.push(`Failed to sync product ${product.id}: ${error.message}`);
				}
			}

			return {
				success: true,
				synced_count: syncedCount,
				errors: errors.length > 0 ? errors : undefined,
			};
		} catch (error) {
			strapi.log.error("Failed to sync products from Adapty:", error);
			return {
				success: false,
				synced_count: 0,
				errors: [error.message || "Unknown error occurred during product sync"],
			};
		}
	}

	/**
	 * Get placement analytics from Adapty
	 */
	async getPlacementAnalytics(
		placementId: string,
		startDate: string,
		endDate: string,
	) {
		try {
			const adaptyClient = getAdaptyService();

			const analyticsResponse = await adaptyClient.getPlacementAnalytics(
				placementId,
				{
					start_date: startDate,
					end_date: endDate,
					breakdown: ["country", "product"],
				},
			);

			return { success: true, data: analyticsResponse.data };
		} catch (error) {
			strapi.log.error("Failed to get placement analytics:", error);
			return {
				success: false,
				error:
					error.message || "Unknown error occurred while fetching analytics",
			};
		}
	}

	/**
	 * Test Adapty connection
	 */
	async testConnection(): Promise<{ success: boolean; error?: string }> {
		try {
			const adaptyClient = getAdaptyService();
			const isConnected = await adaptyClient.testConnection();

			return { success: isConnected };
		} catch (error) {
			return {
				success: false,
				error: error.message || "Connection test failed",
			};
		}
	}

	/**
	 * Get Adapty API status
	 */
	async getApiStatus() {
		try {
			const adaptyClient = getAdaptyService();
			const statusResponse = await adaptyClient.getApiStatus();

			return { success: true, data: statusResponse.data };
		} catch (error) {
			return {
				success: false,
				error: error.message || "Failed to get API status",
			};
		}
	}

	/**
	 * Sync placements from Adapty to Strapi
	 */
	async syncPlacementsFromAdapty(): Promise<{
		success: boolean;
		synced_count: number;
		errors?: string[];
	}> {
		try {
			const adaptyClient = getAdaptyService();

			// Get placements from Adapty
			const placementsResponse = await adaptyClient.listPlacements();
			const adaptyPlacements = placementsResponse.data;

			let syncedCount = 0;
			const errors: string[] = [];

			for (const placement of adaptyPlacements) {
				try {
					// Check if placement already exists in Strapi
					const existingPaywalls = await strapi.entityService.findMany(
						"api::paywall.paywall",
						{
							filters: { placement_id: placement.id },
						},
					);

					if (existingPaywalls.length === 0) {
						// Create new paywall in Strapi based on placement
						await strapi.entityService.create("api::paywall.paywall", {
							data: {
								name: placement.name,
								placement_id: placement.id,
								title: placement.name,
								subtitle: "",
								description_text: "",
								cta_text: "Subscribe Now",
								theme: {
									name: "Default",
									primary_color: "#007bff",
									background_color: "#ffffff",
									text_color: "#000000",
									button_style: "rounded",
									header_style: "hero",
									product_display_style: "list",
									show_features: true,
									show_testimonials: false,
								},
								adapty_sync_status: "synced",
								adapty_last_sync: new Date(),
							},
						});
						syncedCount++;
					} else {
						// Update existing paywall with placement data
						await strapi.entityService.update(
							"api::paywall.paywall",
							existingPaywalls[0].id,
							{
								data: {
									name: placement.name,
									adapty_last_sync: new Date(),
								} as any,
							},
						);
						syncedCount++;
					}

					// Track sync status
					await this.updateSyncStatus("placement", placement.id, "synced");
				} catch (error) {
					errors.push(
						`Failed to sync placement ${placement.id}: ${error.message}`,
					);
					await this.updateSyncStatus(
						"placement",
						placement.id,
						"error",
						error.message,
					);
				}
			}

			return {
				success: true,
				synced_count: syncedCount,
				errors: errors.length > 0 ? errors : undefined,
			};
		} catch (error) {
			strapi.log.error("Failed to sync placements from Adapty:", error);
			return {
				success: false,
				synced_count: 0,
				errors: [
					error.message || "Unknown error occurred during placement sync",
				],
			};
		}
	}

	/**
	 * Bidirectional sync with conflict resolution
	 */
	async performBidirectionalSync(
		resolution: ConflictResolution = "manual",
	): Promise<{
		success: boolean;
		conflicts: Array<{
			entity_type: string;
			entity_id: string;
			conflict_reason: string;
		}>;
		synced: { paywalls: number; products: number; placements: number };
		errors?: string[];
	}> {
		const conflicts = [];
		const synced = { paywalls: 0, products: 0, placements: 0 };
		const errors = [];

		try {
			// 1. Sync products from Adapty to Strapi
			const productSync = await this.syncProductsFromAdapty();
			if (productSync.success) {
				synced.products = productSync.synced_count;
			} else {
				errors.push(...(productSync.errors || []));
			}

			// 2. Sync placements from Adapty to Strapi
			const placementSync = await this.syncPlacementsFromAdapty();
			if (placementSync.success) {
				synced.placements = placementSync.synced_count;
			} else {
				errors.push(...(placementSync.errors || []));
			}

			// 3. Sync paywalls from Strapi to Adapty (with conflict detection)
			const paywalls = await strapi.entityService.findMany(
				"api::paywall.paywall",
				{
					populate: ["theme", "features", "testimonials", "product_labels"],
				},
			);

			const paywallArray = Array.isArray(paywalls) ? paywalls : [];
			for (const paywall of paywallArray) {
				try {
					// Check for conflicts
					const conflict = await this.detectConflicts(
						"paywall",
						paywall.id.toString(),
					);
					if (conflict) {
						if (resolution === "manual") {
							conflicts.push({
								entity_type: "paywall",
								entity_id: paywall.id.toString(),
								conflict_reason: conflict.reason,
							});
							continue;
						} else {
							await this.resolveConflict(
								"paywall",
								paywall.id.toString(),
								resolution,
							);
						}
					}

					const syncResult = await this.syncPaywallToAdapty(Number(paywall.id));
					if (syncResult.success) {
						synced.paywalls++;
					} else {
						errors.push(...(syncResult.errors || []));
					}
				} catch (error) {
					errors.push(`Failed to sync paywall ${paywall.id}: ${error.message}`);
				}
			}

			return {
				success: errors.length === 0,
				conflicts,
				synced,
				errors: errors.length > 0 ? errors : undefined,
			};
		} catch (error) {
			strapi.log.error("Bidirectional sync failed:", error);
			return {
				success: false,
				conflicts,
				synced,
				errors: [error.message || "Unknown error during bidirectional sync"],
			};
		}
	}

	/**
	 * Detect conflicts between Strapi and Adapty data
	 */
	private async detectConflicts(
		entityType: "paywall" | "product" | "placement",
		entityId: string,
	): Promise<{ reason: string } | null> {
		try {
			const syncStatus = await this.getSyncStatus(entityType, entityId);
			if (!syncStatus) return null;

			if (entityType === "paywall") {
				const paywall = await strapi.entityService.findOne(
					"api::paywall.paywall",
					parseInt(entityId),
				);
				if (!paywall) return null;

				// Check if Strapi data was modified after last sync
				const strapiModified = new Date(paywall.updatedAt);
				const lastSync = new Date(syncStatus.last_sync);

				if (strapiModified > lastSync && syncStatus.adapty_last_modified) {
					const adaptyModified = new Date(syncStatus.adapty_last_modified);
					if (adaptyModified > lastSync) {
						return {
							reason:
								"Both Strapi and Adapty have been modified since last sync",
						};
					}
				}
			}

			return null;
		} catch (error) {
			strapi.log.error("Error detecting conflicts:", error);
			return null;
		}
	}

	/**
	 * Resolve conflicts based on strategy
	 */
	private async resolveConflict(
		entityType: "paywall" | "product" | "placement",
		entityId: string,
		resolution: ConflictResolution,
	): Promise<void> {
		switch (resolution) {
			case "adapty_wins":
				// Pull latest data from Adapty and overwrite Strapi
				if (entityType === "paywall") {
					await this.syncPlacementsFromAdapty();
				}
				break;
			case "strapi_wins":
				// Push Strapi data to Adapty
				if (entityType === "paywall") {
					await this.syncPaywallToAdapty(parseInt(entityId));
				}
				break;
			case "merge":
				// Implement merge logic (basic implementation)
				strapi.log.warn(
					`Merge conflict resolution not fully implemented for ${entityType}:${entityId}`,
				);
				break;
		}
	}

	/**
	 * Handle webhook events from Adapty
	 */
	async handleWebhook(
		event: WebhookEvent,
	): Promise<{ success: boolean; error?: string }> {
		try {
			strapi.log.info(
				`Processing webhook event: ${event.type} for ${event.id}`,
			);

			switch (event.type) {
				case "placement.updated":
				case "placement.created":
					await this.handlePlacementWebhook(event.data);
					break;
				case "product.updated":
				case "product.created":
					await this.handleProductWebhook(event.data);
					break;
				default:
					strapi.log.warn(`Unknown webhook event type: ${event.type}`);
			}

			return { success: true };
		} catch (error) {
			strapi.log.error("Webhook processing failed:", error);
			return { success: false, error: error.message };
		}
	}

	/**
	 * Handle placement webhook events
	 */
	private async handlePlacementWebhook(
		placementData: AdaptyPlacement,
	): Promise<void> {
		const existingPaywalls = await strapi.entityService.findMany(
			"api::paywall.paywall",
			{
				filters: { placement_id: placementData.id },
			},
		);

		if (existingPaywalls.length > 0) {
			// Update existing paywall
			await strapi.entityService.update(
				"api::paywall.paywall",
				existingPaywalls[0].id,
				{
					data: {
						name: placementData.name,
						adapty_last_sync: new Date(),
					} as any,
				},
			);
		} else {
			// Create new paywall
			await strapi.entityService.create("api::paywall.paywall", {
				data: {
					name: placementData.name,
					placement_id: placementData.id,
					title: placementData.name,
					subtitle: "",
					description_text: "",
					cta_text: "Subscribe Now",
					theme: {
						name: "Default",
						primary_color: "#007bff",
						background_color: "#ffffff",
						text_color: "#000000",
						button_style: "rounded",
						header_style: "hero",
						product_display_style: "list",
						show_features: true,
						show_testimonials: false,
					},
					adapty_sync_status: "synced",
					adapty_last_sync: new Date(),
				},
			});
		}

		await this.updateSyncStatus("placement", placementData.id, "synced");
	}

	/**
	 * Handle product webhook events
	 */
	private async handleProductWebhook(
		productData: AdaptyProduct,
	): Promise<void> {
		const existingProducts = await strapi.entityService.findMany(
			"api::product.product",
			{
				filters: { adapty_product_id: productData.id },
			},
		);

		if (existingProducts.length > 0) {
			// Update existing product
			await strapi.entityService.update(
				"api::product.product",
				existingProducts[0].id,
				{
					data: {
						name: productData.name,
						price_amount: productData.price?.amount,
						price_currency: productData.price?.currency_code,
						price_localized: productData.price?.localized_string,
						last_synced: new Date(),
					} as any,
				},
			);
		} else {
			// Create new product
			await strapi.entityService.create("api::product.product", {
				data: {
					adapty_product_id: productData.id,
					vendor_product_id: productData.vendor_product_id,
					name: productData.name,
					type: productData.type,
					store: productData.store,
					price_amount: productData.price?.amount,
					price_currency: productData.price?.currency_code,
					price_localized: productData.price?.localized_string,
					subscription_period_unit: productData.subscription_period?.unit,
					subscription_period_count:
						productData.subscription_period?.number_of_units,
					last_synced: new Date(),
				},
			});
		}

		await this.updateSyncStatus("product", productData.id, "synced");
	}

	/**
	 * Retry failed sync operations with exponential backoff
	 */
	async retryFailedSyncs(
		maxRetries: number = 3,
	): Promise<{ success: boolean; retried_count: number; errors?: string[] }> {
		try {
			// Get failed sync statuses
			const failedSyncs = await strapi.entityService.findMany(
				"api::sync-status.sync-status",
				{
					filters: {
						sync_status: "error",
						retry_count: { $lt: maxRetries },
					},
				},
			);

			let retriedCount = 0;
			const errors = [];

			const failedSyncsArray = Array.isArray(failedSyncs) ? failedSyncs : [];
			for (const syncStatus of failedSyncsArray) {
				try {
					// Calculate exponential backoff delay
					const delay = 2 ** syncStatus.retry_count * 1000; // 1s, 2s, 4s, 8s...
					await new Promise((resolve) => setTimeout(resolve, delay));

					let success = false;

					switch (syncStatus.entity_type) {
						case "paywall": {
							const paywallResult = await this.syncPaywallToAdapty(
								parseInt(syncStatus.entity_id),
							);
							success = paywallResult.success;
							if (!success) {
								errors.push(
									`Paywall ${syncStatus.entity_id}: ${paywallResult.errors?.join(", ")}`,
								);
							}
							break;
						}
						case "product": {
							const productResult = await this.syncProductsFromAdapty();
							success = productResult.success;
							if (!success) {
								errors.push(
									`Product sync: ${productResult.errors?.join(", ")}`,
								);
							}
							break;
						}
						case "placement": {
							const placementResult = await this.syncPlacementsFromAdapty();
							success = placementResult.success;
							if (!success) {
								errors.push(
									`Placement sync: ${placementResult.errors?.join(", ")}`,
								);
							}
							break;
						}
					}

					if (success) {
						await this.updateSyncStatus(
							syncStatus.entity_type,
							syncStatus.entity_id,
							"synced",
						);
						retriedCount++;
					} else {
						await this.updateSyncStatus(
							syncStatus.entity_type,
							syncStatus.entity_id,
							"error",
							"Retry failed",
							syncStatus.retry_count + 1,
						);
					}
				} catch (error) {
					errors.push(
						`Retry failed for ${syncStatus.entity_type}:${syncStatus.entity_id} - ${error.message}`,
					);
					await this.updateSyncStatus(
						syncStatus.entity_type,
						syncStatus.entity_id,
						"error",
						error.message,
						syncStatus.retry_count + 1,
					);
				}
			}

			return {
				success: retriedCount > 0,
				retried_count: retriedCount,
				errors: errors.length > 0 ? errors : undefined,
			};
		} catch (error) {
			strapi.log.error("Failed to retry sync operations:", error);
			return {
				success: false,
				retried_count: 0,
				errors: [error.message || "Unknown error during retry operations"],
			};
		}
	}

	/**
	 * Update sync status tracking
	 */
	private async updateSyncStatus(
		entityType: "paywall" | "product" | "placement",
		entityId: string,
		status: "pending" | "synced" | "error" | "conflict",
		errorMessage?: string,
		retryCount: number = 0,
	): Promise<void> {
		try {
			const existingStatus = await strapi.entityService.findMany(
				"api::sync-status.sync-status",
				{
					filters: {
						entity_type: entityType,
						entity_id: entityId,
					},
				},
			);

			const statusData = {
				entity_type: entityType,
				entity_id: entityId,
				last_sync: new Date(),
				sync_status: status,
				error_message: errorMessage,
				retry_count: retryCount,
			};

			if (existingStatus.length > 0) {
				await strapi.entityService.update(
					"api::sync-status.sync-status",
					existingStatus[0].id,
					{
						data: statusData as any,
					},
				);
			} else {
				await strapi.entityService.create("api::sync-status.sync-status", {
					data: statusData as any,
				});
			}
		} catch (error) {
			strapi.log.error("Failed to update sync status:", error);
		}
	}

	/**
	 * Get sync status for entity
	 */
	private async getSyncStatus(
		entityType: "paywall" | "product" | "placement",
		entityId: string,
	): Promise<SyncStatus | null> {
		try {
			const statuses = await strapi.entityService.findMany(
				"api::sync-status.sync-status",
				{
					filters: {
						entity_type: entityType,
						entity_id: entityId,
					},
				},
			);

			if (statuses.length > 0) {
				const status = statuses[0];
				return {
					entity_type: status.entity_type,
					entity_id: status.entity_id,
					last_sync: new Date(status.last_sync),
					sync_status: status.sync_status,
					error_message: status.error_message,
					retry_count: status.retry_count,
					adapty_last_modified: status.adapty_last_modified ? new Date(status.adapty_last_modified) : undefined,
					strapi_last_modified: status.strapi_last_modified ? new Date(status.strapi_last_modified) : undefined,
				};
			}

			return null;
		} catch (error) {
			strapi.log.error("Failed to get sync status:", error);
			return null;
		}
	}

	/**
	 * Get sync statistics
	 */
	async getSyncStatistics(): Promise<{
		total_entities: number;
		synced: number;
		pending: number;
		errors: number;
		conflicts: number;
		last_sync: Date | null;
	}> {
		try {
			const allStatuses = await strapi.entityService.findMany(
				"api::sync-status.sync-status",
			);

			const stats = {
				total_entities: allStatuses.length,
				synced: allStatuses.filter((s) => s.sync_status === "synced").length,
				pending: allStatuses.filter((s) => s.sync_status === "pending").length,
				errors: allStatuses.filter((s) => s.sync_status === "error").length,
				conflicts: allStatuses.filter((s) => s.sync_status === "conflict")
					.length,
				last_sync:
					allStatuses.length > 0
						? new Date(
							Math.max(
								...allStatuses.map((s) => new Date(s.last_sync).getTime()),
							),
						)
						: null,
			};

			return stats;
		} catch (error) {
			strapi.log.error("Failed to get sync statistics:", error);
			return {
				total_entities: 0,
				synced: 0,
				pending: 0,
				errors: 0,
				conflicts: 0,
				last_sync: null,
			};
		}
	}

	/**
	 * Bulk sync multiple paywalls
	 */
	async bulkSyncPaywalls(paywallIds: number[]): Promise<{
		success: boolean;
		results: Array<{ id: number; success: boolean; error?: string }>;
		summary: { total: number; succeeded: number; failed: number };
	}> {
		const results = [];
		let succeeded = 0;
		let failed = 0;

		for (const paywallId of paywallIds) {
			try {
				const result = await this.syncPaywallToAdapty(paywallId);
				if (result.success) {
					results.push({ id: paywallId, success: true });
					succeeded++;
				} else {
					results.push({
						id: paywallId,
						success: false,
						error: result.errors?.join(", ") || "Unknown error",
					});
					failed++;
				}
			} catch (error) {
				results.push({
					id: paywallId,
					success: false,
					error: error.message || "Unknown error",
				});
				failed++;
			}
		}

		return {
			success: succeeded > 0,
			results,
			summary: {
				total: paywallIds.length,
				succeeded,
				failed,
			},
		};
	}
}

// Export singleton instance
export const adaptyIntegration = new AdaptyIntegrationService();
