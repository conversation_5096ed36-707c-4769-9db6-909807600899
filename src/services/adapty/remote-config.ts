/**
 * Remote Config Management Service for Adapty Integration
 */

import { getAdaptyService } from "./index";
import type { AdaptyProduct, AdaptyRemoteConfigData, AdaptyThemeConfig } from "./types";
import type { Strapi } from '@strapi/strapi';

/**
 * Remote config version interface
 */
interface RemoteConfigVersion {
	id: string;
	version: string;
	config_data: AdaptyRemoteConfigData;
	created_at: Date;
	deployed_at?: Date;
	status: "draft" | "deployed" | "rolled_back";
	deployment_notes?: string;
	created_by: string;
	paywall_id: number;
	placement_id: string;
	locale?: string;
}

/**
 * Deployment result interface
 */
interface DeploymentResult {
	success: boolean;
	version_id: string;
	deployed_at: Date;
	rollback_version?: string;
	errors?: string[];
	warnings?: string[];
}

/**
 * Validation result interface
 */
interface ValidationResult {
	valid: boolean;
	errors: string[];
	warnings: string[];
	mobile_compatibility: {
		ios: boolean;
		android: boolean;
		react_native: boolean;
	};
}

/**
 * Preview configuration interface
 */
interface PreviewConfig {
	paywall_id: number;
	locale?: string;
	test_products?: AdaptyProduct[];
	preview_url?: string;
	expires_at: Date;
}

/**
 * Remote Config Management Service
 */
export class RemoteConfigService {
	private strapi: Strapi;

	constructor(strapi: Strapi) {
		this.strapi = strapi;
	}
	/**
	 * Convert Strapi paywall content to Adapty remote config format
	 */
	async convertPaywallToRemoteConfig(
		paywallId: number,
		locale: string = "en",
		includeProducts: boolean = true,
	): Promise<{
		success: boolean;
		config?: AdaptyRemoteConfigData;
		errors?: string[];
	}> {
		try {
			// Get paywall data from Strapi with all relations
			const paywall = await this.strapi.entityService.findOne(
				"api::paywall.paywall",
				paywallId,
				{
					populate: {
						theme: true,
						features: {
							populate: ["icon_image"],
						},
						testimonials: {
							populate: ["author_avatar"],
						},
						product_labels: true,
						background_image: true,
						logo: true,
					},
				},
			);

			if (!paywall) {
				return { success: false, errors: ["Paywall not found"] };
			}

			// Get products from Adapty if needed
			let products: AdaptyProduct[] = [];
			if (includeProducts && paywall.placement_id) {
				try {
					const adaptyClient = getAdaptyService();
					// const productsResponse = await adaptyClient.getProductsByPlacement(paywall.placement_id); // Method not available
					const productsResponse = { data: [] };
					products = productsResponse.data;
				} catch (error) {
					this.strapi.log.warn(
						`Failed to fetch products for placement ${paywall.placement_id}:`,
						error,
					);
				}
			}

			// Transform to remote config format
			const remoteConfig: AdaptyRemoteConfigData = {
				paywall: {
					id: paywall.id.toString(),
					name: paywall.name,
					title: paywall.title || paywall.name,
					subtitle: paywall.subtitle || "",
					description: paywall.description_text || "",
					cta_text: paywall.cta_text || "Subscribe Now",
					cta_secondary_text: paywall.cta_secondary_text || "",
					theme: this.transformTheme(paywall.theme),
					features: this.transformFeatures(paywall.features || []),
					testimonials: this.transformTestimonials(paywall.testimonials || []),
					product_labels: this.transformProductLabels(
						paywall.product_labels || [],
					),
				},
				products: products.map((product) => ({
					id: product.id,
					vendor_product_id: product.vendor_product_id,
					name: product.name,
					type: product.type,
					store: product.store,
					created_at: product.created_at || new Date().toISOString(),
					updated_at: product.updated_at || new Date().toISOString(),
					price: product.price,
					subscription_period: product.subscription_period,
				})),
				ab_test: undefined, // Will be populated if A/B testing is active
				version: this.generateVersion(),
				locale: locale,
				generated_at: new Date().toISOString(),
			};

			return { success: true, config: remoteConfig };
		} catch (error) {
			this.strapi.log.error("Failed to convert paywall to remote config:", error);
			return {
				success: false,
				errors: [error.message || "Unknown error during conversion"],
			};
		}
	}

	/**
	 * Validate remote config for mobile app compatibility
	 */
	async validateRemoteConfig(
		config: AdaptyRemoteConfigData,
	): Promise<ValidationResult> {
		const errors: string[] = [];
		const warnings: string[] = [];

		// Required field validation
		if (!config.paywall.id) errors.push("Paywall ID is required");
		if (!config.paywall.name) errors.push("Paywall name is required");
		if (!config.paywall.title) errors.push("Paywall title is required");
		if (!config.paywall.cta_text) errors.push("CTA text is required");

		// Theme validation
		if (config.paywall.theme) {
			if (!this.isValidColor(config.paywall.theme.primary_color)) {
				errors.push("Invalid primary color format");
			}
			if (!this.isValidColor(config.paywall.theme.background_color)) {
				errors.push("Invalid background color format");
			}
			if (!this.isValidColor(config.paywall.theme.text_color)) {
				errors.push("Invalid text color format");
			}
		}

		// Features validation
		if (config.paywall.features) {
			config.paywall.features.forEach((feature, index) => {
				if (!feature.title)
					errors.push(`Feature ${index + 1}: Title is required`);
				if (!feature.description)
					warnings.push(`Feature ${index + 1}: Description is recommended`);
			});
		}

		// Product validation
		if (!config.products || config.products.length === 0) {
			warnings.push(
				"No products configured - paywall may not function properly",
			);
		}

		// Mobile compatibility checks
		const mobileCompatibility = {
			ios: this.validateiOSCompatibility(config),
			android: this.validateAndroidCompatibility(config),
			react_native: this.validateReactNativeCompatibility(config),
		};

		// Text length validation for mobile
		if (config.paywall.title && config.paywall.title.length > 50) {
			warnings.push("Title may be too long for mobile display");
		}
		if (config.paywall.subtitle && config.paywall.subtitle.length > 100) {
			warnings.push("Subtitle may be too long for mobile display");
		}

		// Image URL validation
		if (
			config.paywall.theme?.background_image &&
			!this.isValidImageUrl(config.paywall.theme.background_image)
		) {
			errors.push("Invalid background image URL");
		}
		if (
			config.paywall.theme?.logo &&
			!this.isValidImageUrl(config.paywall.theme.logo)
		) {
			errors.push("Invalid logo URL");
		}

		return {
			valid: errors.length === 0,
			errors,
			warnings,
			mobile_compatibility: mobileCompatibility,
		};
	}

	/**
	 * Create a new version of remote config
	 */
	async createVersion(
		paywallId: number,
		locale: string = "en",
		deploymentNotes?: string,
		createdBy: string = "system",
	): Promise<{
		success: boolean;
		version?: RemoteConfigVersion;
		errors?: string[];
	}> {
		try {
			// Convert paywall to remote config
			const conversionResult = await this.convertPaywallToRemoteConfig(
				paywallId,
				locale,
			);
			if (!conversionResult.success || !conversionResult.config) {
				return { success: false, errors: conversionResult.errors };
			}

			// Validate the config
			const validation = await this.validateRemoteConfig(
				conversionResult.config,
			);
			if (!validation.valid) {
				return { success: false, errors: validation.errors };
			}

			// Get paywall placement_id
			const paywall = await this.strapi.entityService.findOne(
				"api::paywall.paywall",
				paywallId,
			);
			if (!paywall) {
				return { success: false, errors: ["Paywall not found"] };
			}

			// Create version record
			const version: RemoteConfigVersion = {
				id: this.generateVersionId(),
				version: "1.0.0", // Default version since property not available
				config_data: conversionResult.config,
				created_at: new Date(),
				status: "draft",
				deployment_notes: deploymentNotes,
				created_by: createdBy,
				paywall_id: paywallId,
				placement_id: paywall.placement_id,
				locale: locale,
			};

			// Store version in Strapi
			await this.strapi.entityService.create(
				"api::remote-config-version.remote-config-version",
				{
					data: {
						version_id: version.id,
						version_number: version.version,
						config_data: JSON.stringify(version.config_data),
						status: version.status,
						deployment_notes: version.deployment_notes,
						created_by: version.created_by,
						paywall: paywallId,
						placement_id: version.placement_id,
						locale: version.locale,
					},
				},
			);

			return { success: true, version };
		} catch (error) {
			this.strapi.log.error("Failed to create remote config version:", error);
			return {
				success: false,
				errors: [error.message || "Unknown error during version creation"],
			};
		}
	}

	/**
	 * Deploy remote config version to Adapty
	 */
	async deployVersion(
		versionId: string,
		force: boolean = false,
	): Promise<DeploymentResult> {
		try {
			// Get version from database
			const versionRecords = await this.strapi.entityService.findMany(
				"api::remote-config-version.remote-config-version",
				{
					filters: { version_id: versionId },
					populate: ["paywall"],
				},
			);

			if (versionRecords.length === 0) {
				return {
					success: false,
					version_id: versionId,
					deployed_at: new Date(),
					errors: ["Version not found"],
				};
			}

			const versionRecord = versionRecords[0];
			const configData = JSON.parse(versionRecord.config_data as string) as AdaptyRemoteConfigData;

			// Validate before deployment
			if (!force) {
				const validation = await this.validateRemoteConfig(configData);
				if (!validation.valid) {
					return {
						success: false,
						version_id: versionId,
						deployed_at: new Date(),
						errors: validation.errors,
						warnings: validation.warnings,
					};
				}
			}

			// Deploy to Adapty
			const adaptyClient = getAdaptyService();

			try {
				// Update existing remote config or create new one
				await adaptyClient.updateRemoteConfigByPlacement(
					versionRecord.placement_id,
					{
						data: configData,
						// lang: versionRecord.locale || "en", // Not in UpdateRemoteConfigRequest
					},
				);
			} catch (_updateError) {
				// If update fails, try to create
				await adaptyClient.createRemoteConfig({
					placement_id: versionRecord.placement_id,
					lang: versionRecord.locale || "en",
					data: configData,
				});
			}

			// Update version status
			const deployedAt = new Date();
			await this.strapi.entityService.update(
				"api::remote-config-version.remote-config-version",
				versionRecord.id,
				{
					data: {
						status: 'deployed',
						deployed_at: deployedAt,
					} as any,
				},
			);

			// Mark previous versions as rolled back
			await this.markPreviousVersionsAsRolledBack(
				typeof versionRecord.paywall === 'object' && versionRecord.paywall?.id 
					? Number(versionRecord.paywall.id) 
					: Number(versionRecord.paywall),
				versionRecord.locale,
				versionId,
			);

			return {
				success: true,
				version_id: versionId,
				deployed_at: deployedAt,
			};
		} catch (error) {
			this.strapi.log.error("Failed to deploy remote config version:", error);
			return {
				success: false,
				version_id: versionId,
				deployed_at: new Date(),
				errors: [error.message || "Unknown error during deployment"],
			};
		}
	}

	/**
	 * Rollback to a previous version
	 */
	async rollbackToVersion(
		versionId: string,
		reason?: string,
	): Promise<DeploymentResult> {
		try {
			// Get the version to rollback to
			const versionRecords = await this.strapi.entityService.findMany(
				"api::remote-config-version.remote-config-version",
				{
					filters: { version_id: versionId },
				},
			);

			if (versionRecords.length === 0) {
				return {
					success: false,
					version_id: versionId,
					deployed_at: new Date(),
					errors: ["Version not found"],
				};
			}

			const versionRecord = versionRecords[0];

			// Get current deployed version for rollback reference
			const currentVersions = await this.strapi.entityService.findMany(
				"api::remote-config-version.remote-config-version",
				{
					filters: {
						paywall: {
							id: typeof versionRecord.paywall === 'object' && versionRecord.paywall?.id 
								? Number(versionRecord.paywall.id) 
								: Number(versionRecord.paywall)
						},
						locale: versionRecord.locale,
						status: "deployed",
					},
					sort: ["deployed_at:desc"],
					pagination: { limit: 1 },
				},
			);

			const currentVersion =
				currentVersions.length > 0 ? currentVersions[0] : null;

			// Deploy the rollback version
			const deployResult = await this.deployVersion(versionId, true);

			if (deployResult.success) {
				// Log rollback action
				await this.strapi.entityService.create(
					"api::deployment-log.deployment-log",
					{
						data: {
							type: "rollback",
							action: "rollback",
							version_id: versionId,
							previous_version_id: currentVersion?.version_id,
							reason: reason,
							timestamp: new Date(),
						},
					},
				);

				return {
					...deployResult,
					rollback_version: currentVersion?.version_id,
				};
			}

			return deployResult;
		} catch (error) {
			this.strapi.log.error("Failed to rollback remote config version:", error);
			return {
				success: false,
				version_id: versionId,
				deployed_at: new Date(),
				errors: [error.message || "Unknown error during rollback"],
			};
		}
	}

	/**
	 * Create preview configuration for testing
	 */
	async createPreview(
		paywallId: number,
		locale: string = "en",
		expiresInMinutes: number = 60,
	): Promise<{ success: boolean; preview?: PreviewConfig; errors?: string[] }> {
		try {
			// Convert paywall to remote config
			const conversionResult = await this.convertPaywallToRemoteConfig(
				paywallId,
				locale,
			);
			if (!conversionResult.success || !conversionResult.config) {
				return { success: false, errors: conversionResult.errors };
			}

			// Generate preview URL (this would typically be a signed URL to a preview endpoint)
			const previewId = this.generatePreviewId();
			const expiresAt = new Date(Date.now() + expiresInMinutes * 60 * 1000);

			// Store preview configuration
			await this.strapi.entityService.create("api::preview-config.preview-config", {
				data: {
					preview_id: previewId,
					paywall: paywallId,
					locale: locale,
					config_data: JSON.stringify(conversionResult.config),
					expires_at: expiresAt,
					created_at: new Date(),
				},
			});

			const preview: PreviewConfig = {
				paywall_id: paywallId,
				locale: locale,
				test_products: conversionResult.config.products,
				preview_url: `${process.env.STRAPI_URL || "http://localhost:1337"}/api/preview/${previewId}`,
				expires_at: expiresAt,
			};

			return { success: true, preview };
		} catch (error) {
			this.strapi.log.error("Failed to create preview configuration:", error);
			return {
				success: false,
				errors: [error.message || "Unknown error during preview creation"],
			};
		}
	}

	/**
	 * Monitor remote config delivery and performance
	 */
	async getDeploymentMetrics(
		placementId: string,
		startDate: Date,
		endDate: Date,
	): Promise<{
		success: boolean;
		metrics?: {
			total_requests: number;
			successful_deliveries: number;
			error_rate: number;
			average_response_time: number;
			cache_hit_rate: number;
			versions_deployed: number;
			rollbacks: number;
		};
		errors?: string[];
	}> {
		try {
			// Get deployment logs
			const deploymentLogs = await this.strapi.entityService.findMany(
				"api::deployment-log.deployment-log",
				{
					filters: {
						timestamp: {
							$gte: startDate,
							$lte: endDate,
						},
					},
				},
			);

			// Get analytics from Adapty (if available)
			const adaptyClient = getAdaptyService();
			let adaptyMetrics = null;
			try {
				const analyticsResponse = await adaptyClient.getPlacementAnalytics(
					placementId,
					{
						start_date: startDate.toISOString(),
						end_date: endDate.toISOString(),
					},
				);
				adaptyMetrics = analyticsResponse.data;
			} catch (error) {
				this.strapi.log.warn("Failed to fetch Adapty analytics:", error);
			}

			// Calculate metrics
			const totalDeployments = deploymentLogs.filter(
				(log) => log.action === "deploy",
			).length;
			const totalRollbacks = deploymentLogs.filter(
				(log) => log.action === "rollback",
			).length;

			const metrics = {
				total_requests: adaptyMetrics?.total_requests || 0,
				successful_deliveries: adaptyMetrics?.successful_deliveries || 0,
				error_rate: adaptyMetrics?.error_rate || 0,
				average_response_time: adaptyMetrics?.average_response_time || 0,
				cache_hit_rate: adaptyMetrics?.cache_hit_rate || 0,
				versions_deployed: totalDeployments,
				rollbacks: totalRollbacks,
			};

			return { success: true, metrics };
		} catch (error) {
			this.strapi.log.error("Failed to get deployment metrics:", error);
			return {
				success: false,
				errors: [error.message || "Unknown error fetching metrics"],
			};
		}
	}

	// Private helper methods

	private transformTheme(theme: any): AdaptyThemeConfig {
		if (!theme) {
			return {
				name: "Default",
				primary_color: "#007bff",
				background_color: "#ffffff",
				text_color: "#000000",
				button_style: "rounded" as const,
				header_style: "hero",
				product_display_style: "list",
				show_features: true,
				show_testimonials: false,
			};
		}

		return {
			name: theme.name || "Custom",
			primary_color: theme.primary_color || "#007bff",
			background_color: theme.background_color || "#ffffff",
			text_color: theme.text_color || "#000000",
			button_style: (theme.button_style || "rounded") as "rounded" | "square",
			header_style: theme.header_style || "hero",
			product_display_style: theme.product_display_style || "list",
			show_features: theme.show_features !== false,
			show_testimonials: theme.show_testimonials === true,
			custom_styles: theme.custom_styles,
			background_image: theme.background_image,
			logo: theme.logo,
		};
	}

	private transformFeatures(features: any[]) {
		return features.map((feature) => ({
			icon: feature.icon || "check",
			title: feature.title,
			description: feature.description,
			order: feature.order || 0,
			is_highlighted: feature.is_highlighted || false,
			icon_image: feature.icon_image?.url,
		}));
	}

	private transformTestimonials(testimonials: any[]) {
		return testimonials.map((testimonial) => ({
			author_name: testimonial.author_name,
			author_title: testimonial.author_title,
			content: testimonial.content,
			rating: testimonial.rating || 5,
			author_avatar: testimonial.author_avatar?.url,
			company: testimonial.company,
			order: testimonial.order || 0,
		}));
	}

	private transformProductLabels(productLabels: any[]) {
		return productLabels.map((label) => ({
			product_id: label.product_id,
			badge_text: label.badge_text,
			badge_color: label.badge_color || "#ff6b6b",
			subtitle: label.subtitle,
			highlight: label.highlight || false,
			savings_percentage: label.savings_percentage,
			badge_position: label.badge_position || "top-right",
		}));
	}

	private generateVersion() {
		const timestamp = Date.now();
		const random = Math.random().toString(36).substring(2, 8);
		return `v${timestamp}-${random}` as const;
	}

	private generateVersionId(): string {
		return `version_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
	}

	private generatePreviewId(): string {
		return `preview_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
	}

	private isValidColor(color: string): boolean {
		const hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
		const rgbRegex = /^rgb\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*\)$/;
		const rgbaRegex = /^rgba\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*,\s*[\d.]+\s*\)$/;

		return (
			hexRegex.test(color) || rgbRegex.test(color) || rgbaRegex.test(color)
		);
	}

	private isValidImageUrl(url: string): boolean {
		try {
			new URL(url);
			return /\.(jpg|jpeg|png|gif|webp|svg)$/i.test(url);
		} catch {
			return false;
		}
	}

	private validateiOSCompatibility(_config: AdaptyRemoteConfigData): boolean {
		// iOS-specific validation rules
		return true; // Implement iOS-specific checks
	}

	private validateAndroidCompatibility(
		_config: AdaptyRemoteConfigData,
	): boolean {
		// Android-specific validation rules
		return true; // Implement Android-specific checks
	}

	private validateReactNativeCompatibility(
		_config: AdaptyRemoteConfigData,
	): boolean {
		// React Native-specific validation rules
		return true; // Implement React Native-specific checks
	}

	private async markPreviousVersionsAsRolledBack(
		paywallId: number,
		locale: string,
		excludeVersionId: string,
	): Promise<void> {
		const previousVersions = await this.strapi.entityService.findMany(
			"api::remote-config-version.remote-config-version",
			{
				filters: {
					paywall: {
						id: paywallId
					},
					locale: locale,
					status: "deployed",
					version_id: { $ne: excludeVersionId },
				},
			},
		);

		const versionsArray = Array.isArray(previousVersions) ? previousVersions : [previousVersions];
		for (const version of versionsArray) {
			await this.strapi.entityService.update(
				"api::remote-config-version.remote-config-version",
				version.id,
				{
					data: {
						status: "rolled_back",
					} as any,
				},
			);
		}
	}
}

// Export factory function
export const createRemoteConfigService = (strapi: Strapi) => new RemoteConfigService(strapi);
