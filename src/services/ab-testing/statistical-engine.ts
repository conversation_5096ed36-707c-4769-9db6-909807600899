/**
 * Statistical Engine for A/B Testing
 * Handles statistical calculations, significance testing, and confidence intervals
 */

/**
 * Statistical test result interface
 */
export interface StatisticalTestResult {
	p_value: number;
	statistical_significance: number;
	confidence_interval: {
		lower: number;
		upper: number;
		level: number;
	};
	effect_size: number;
	power: number;
	sample_size_adequate: boolean;
	winner?: string;
	recommendation:
	| "continue"
	| "stop_winner"
	| "stop_inconclusive"
	| "extend_test";
}

/**
 * Variation metrics interface
 */
export interface VariationMetrics {
	id: string;
	name: string;
	impressions: number;
	conversions: number;
	revenue: number;
	unique_users: number;
	conversion_rate: number;
	revenue_per_user: number;
}

/**
 * Test configuration interface
 */
export interface TestConfiguration {
	confidence_level: number;
	statistical_power: number;
	minimum_detectable_effect: number;
	minimum_sample_size: number;
	primary_metric:
	| "conversion_rate"
	| "revenue_per_user"
	| "trial_conversion"
	| "retention_rate"
	| "ltv";
}

/**
 * Statistical Engine for A/B Testing
 */
export class StatisticalEngine {
	/**
	 * Calculate statistical significance between two variations
	 */
	calculateSignificance(
		control: VariationMetrics,
		variant: VariationMetrics,
		config: TestConfiguration,
	): StatisticalTestResult {
		const metric = config.primary_metric;

		switch (metric) {
			case "conversion_rate":
				return this.calculateConversionRateSignificance(
					control,
					variant,
					config,
				);
			case "revenue_per_user":
				return this.calculateRevenueSignificance(control, variant, config);
			default:
				return this.calculateConversionRateSignificance(
					control,
					variant,
					config,
				);
		}
	}

	/**
	 * Calculate significance for conversion rate metrics
	 */
	private calculateConversionRateSignificance(
		control: VariationMetrics,
		variant: VariationMetrics,
		config: TestConfiguration,
	): StatisticalTestResult {
		// Calculate conversion rates
		const p1 = control.conversions / control.impressions;
		const p2 = variant.conversions / variant.impressions;

		// Pooled proportion
		const pooledP =
			(control.conversions + variant.conversions) /
			(control.impressions + variant.impressions);

		// Standard error
		const se = Math.sqrt(
			pooledP *
			(1 - pooledP) *
			(1 / control.impressions + 1 / variant.impressions),
		);

		// Z-score
		const zScore = Math.abs(p2 - p1) / se;

		// P-value (two-tailed test)
		const pValue = 2 * (1 - this.normalCDF(Math.abs(zScore)));

		// Statistical significance percentage
		const significance = (1 - pValue) * 100;

		// Effect size (Cohen's h for proportions)
		const effectSize =
			2 * (Math.asin(Math.sqrt(p2)) - Math.asin(Math.sqrt(p1)));

		// Confidence interval for difference in proportions
		const criticalValue = this.getZCritical(config.confidence_level);
		const seDiff = Math.sqrt(
			(p1 * (1 - p1)) / control.impressions +
			(p2 * (1 - p2)) / variant.impressions,
		);
		const diff = p2 - p1;
		const marginOfError = criticalValue * seDiff;

		const confidenceInterval = {
			lower: diff - marginOfError,
			upper: diff + marginOfError,
			level: config.confidence_level,
		};

		// Power calculation
		const power = this.calculatePower(
			control.impressions,
			variant.impressions,
			effectSize,
			config.confidence_level,
		);

		// Sample size adequacy
		const sampleSizeAdequate =
			control.impressions >= config.minimum_sample_size &&
			variant.impressions >= config.minimum_sample_size;

		// Determine winner and recommendation
		const { winner, recommendation } = this.determineWinnerAndRecommendation(
			control,
			variant,
			significance,
			config,
			sampleSizeAdequate,
		);

		return {
			p_value: pValue,
			statistical_significance: significance,
			confidence_interval: confidenceInterval,
			effect_size: effectSize,
			power: power,
			sample_size_adequate: sampleSizeAdequate,
			winner: winner,
			recommendation: recommendation,
		};
	}

	/**
	 * Calculate significance for revenue metrics
	 */
	private calculateRevenueSignificance(
		control: VariationMetrics,
		variant: VariationMetrics,
		config: TestConfiguration,
	): StatisticalTestResult {
		// For revenue metrics, we use Welch's t-test
		const mean1 = control.revenue_per_user;
		const mean2 = variant.revenue_per_user;
		const n1 = control.unique_users;
		const n2 = variant.unique_users;

		// Estimate standard deviations (simplified approach)
		// In practice, you'd want actual variance data
		const std1 = Math.sqrt(control.revenue / control.unique_users); // Simplified
		const std2 = Math.sqrt(variant.revenue / variant.unique_users); // Simplified

		// Welch's t-test
		const se = Math.sqrt((std1 * std1) / n1 + (std2 * std2) / n2);
		const tStat = Math.abs(mean2 - mean1) / se;

		// Degrees of freedom (Welch-Satterthwaite equation)
		const df =
			se ** 4 /
			(((std1 * std1) / n1) ** 2 / (n1 - 1) +
				((std2 * std2) / n2) ** 2 / (n2 - 1));

		// P-value using t-distribution approximation
		const pValue = 2 * (1 - this.tCDF(Math.abs(tStat), df));

		const significance = (1 - pValue) * 100;
		const effectSize =
			(mean2 - mean1) / Math.sqrt((std1 * std1 + std2 * std2) / 2);

		// Confidence interval for difference in means
		const criticalValue = this.getTCritical(config.confidence_level, df);
		const diff = mean2 - mean1;
		const marginOfError = criticalValue * se;

		const confidenceInterval = {
			lower: diff - marginOfError,
			upper: diff + marginOfError,
			level: config.confidence_level,
		};

		const power = this.calculatePower(
			n1,
			n2,
			effectSize,
			config.confidence_level,
		);
		const sampleSizeAdequate =
			n1 >= config.minimum_sample_size && n2 >= config.minimum_sample_size;

		const { winner, recommendation } = this.determineWinnerAndRecommendation(
			control,
			variant,
			significance,
			config,
			sampleSizeAdequate,
		);

		return {
			p_value: pValue,
			statistical_significance: significance,
			confidence_interval: confidenceInterval,
			effect_size: effectSize,
			power: power,
			sample_size_adequate: sampleSizeAdequate,
			winner: winner,
			recommendation: recommendation,
		};
	}

	/**
	 * Calculate required sample size for a test
	 */
	calculateRequiredSampleSize(
		baselineRate: number,
		minimumDetectableEffect: number,
		confidenceLevel: number = 95,
		statisticalPower: number = 80,
	): number {
		const _alpha = (100 - confidenceLevel) / 100;
		const _beta = (100 - statisticalPower) / 100;

		const zAlpha = this.getZCritical(confidenceLevel);
		const zBeta = this.getZCritical(statisticalPower);

		const p1 = baselineRate;
		const p2 = baselineRate * (1 + minimumDetectableEffect / 100);

		const pooledP = (p1 + p2) / 2;
		const pooledQ = 1 - pooledP;

		const numerator =
			(zAlpha * Math.sqrt(2 * pooledP * pooledQ) +
				zBeta * Math.sqrt(p1 * (1 - p1) + p2 * (1 - p2))) **
			2;
		const denominator = (p2 - p1) ** 2;

		return Math.ceil(numerator / denominator);
	}

	/**
	 * Calculate test duration based on traffic and sample size requirements
	 */
	calculateTestDuration(
		requiredSampleSize: number,
		dailyTraffic: number,
		trafficAllocation: number = 100,
	): number {
		const effectiveTraffic = dailyTraffic * (trafficAllocation / 100);
		const daysRequired = Math.ceil(requiredSampleSize / effectiveTraffic);
		return Math.max(daysRequired, 7); // Minimum 7 days for weekly patterns
	}

	/**
	 * Validate traffic allocation percentages
	 */
	validateTrafficAllocation(
		variations: Array<{ traffic_percentage: number }>,
	): {
		valid: boolean;
		total: number;
		errors: string[];
	} {
		const errors: string[] = [];
		const total = variations.reduce((sum, v) => sum + v.traffic_percentage, 0);

		if (Math.abs(total - 100) > 0.01) {
			errors.push(
				`Traffic allocation must sum to 100%, currently ${total.toFixed(2)}%`,
			);
		}

		variations.forEach((variation, index) => {
			if (variation.traffic_percentage < 0.1) {
				errors.push(
					`Variation ${index + 1} has insufficient traffic allocation (minimum 0.1%)`,
				);
			}
			if (variation.traffic_percentage > 99.9) {
				errors.push(
					`Variation ${index + 1} has excessive traffic allocation (maximum 99.9%)`,
				);
			}
		});

		return {
			valid: errors.length === 0,
			total: total,
			errors: errors,
		};
	}

	/**
	 * Calculate confidence intervals for conversion rates
	 */
	calculateConversionRateCI(
		conversions: number,
		impressions: number,
		confidenceLevel: number = 95,
	): { lower: number; upper: number; level: number } {
		const rate = conversions / impressions;
		const z = this.getZCritical(confidenceLevel);
		const se = Math.sqrt((rate * (1 - rate)) / impressions);
		const marginOfError = z * se;

		return {
			lower: Math.max(0, rate - marginOfError),
			upper: Math.min(1, rate + marginOfError),
			level: confidenceLevel,
		};
	}

	// Private helper methods

	private determineWinnerAndRecommendation(
		control: VariationMetrics,
		variant: VariationMetrics,
		significance: number,
		config: TestConfiguration,
		sampleSizeAdequate: boolean,
	): {
		winner?: string;
		recommendation:
		| "continue"
		| "stop_winner"
		| "stop_inconclusive"
		| "extend_test";
	} {
		const requiredSignificance = config.confidence_level;
		const isSignificant = significance >= requiredSignificance;

		if (!sampleSizeAdequate) {
			return { recommendation: "continue" };
		}

		if (isSignificant) {
			const controlMetric =
				config.primary_metric === "conversion_rate"
					? control.conversion_rate
					: control.revenue_per_user;
			const variantMetric =
				config.primary_metric === "conversion_rate"
					? variant.conversion_rate
					: variant.revenue_per_user;

			const winner = variantMetric > controlMetric ? variant.id : control.id;
			return { winner, recommendation: "stop_winner" };
		}

		// Check if we should extend the test or stop as inconclusive
		const totalSample = control.impressions + variant.impressions;
		const maxRecommendedSample = config.minimum_sample_size * 4; // 4x minimum as max

		if (totalSample >= maxRecommendedSample) {
			return { recommendation: "stop_inconclusive" };
		}

		return { recommendation: "continue" };
	}

	private normalCDF(x: number): number {
		// Approximation of the cumulative distribution function for standard normal distribution
		const t = 1 / (1 + 0.2316419 * Math.abs(x));
		const d = 0.3989423 * Math.exp((-x * x) / 2);
		const prob =
			d *
			t *
			(0.3193815 +
				t * (-0.3565638 + t * (1.781478 + t * (-1.821256 + t * 1.330274))));
		return x > 0 ? 1 - prob : prob;
	}

	private tCDF(t: number, df: number): number {
		// Simplified t-distribution CDF approximation
		// For production use, consider a more accurate implementation
		if (df >= 30) {
			return this.normalCDF(t);
		}

		// Very simplified approximation for small df
		const x = t / Math.sqrt(df);
		return this.normalCDF(x);
	}

	private getZCritical(confidenceLevel: number): number {
		const alpha = (100 - confidenceLevel) / 100;
		const alphaHalf = alpha / 2;

		// Common z-critical values
		const zValues: { [key: number]: number } = {
			0.025: 1.96, // 95% confidence
			0.005: 2.576, // 99% confidence
			0.05: 1.645, // 90% confidence
			0.01: 2.326, // 98% confidence
		};

		return zValues[alphaHalf] || 1.96;
	}

	private getTCritical(confidenceLevel: number, df: number): number {
		// Simplified t-critical value lookup
		// For production, use a proper t-table or statistical library
		if (df >= 30) {
			return this.getZCritical(confidenceLevel);
		}

		// Approximate t-critical values for common confidence levels and df
		const alpha = (100 - confidenceLevel) / 100;
		if (alpha <= 0.05) {
			return 2.0 + (30 - df) * 0.1; // Rough approximation
		}

		return this.getZCritical(confidenceLevel);
	}

	private calculatePower(
		n1: number,
		n2: number,
		effectSize: number,
		confidenceLevel: number,
	): number {
		// Simplified power calculation
		// For production, use proper statistical power formulas
		const _alpha = (100 - confidenceLevel) / 100;
		const zAlpha = this.getZCritical(confidenceLevel);
		const harmonicMean = 2 / (1 / n1 + 1 / n2);
		const ncp = effectSize * Math.sqrt(harmonicMean / 2); // Non-centrality parameter

		// Approximate power calculation
		const zBeta = ncp - zAlpha;
		const power = this.normalCDF(zBeta) * 100;

		return Math.max(0, Math.min(100, power));
	}
}

// Export singleton instance
export const statisticalEngine = new StatisticalEngine();
