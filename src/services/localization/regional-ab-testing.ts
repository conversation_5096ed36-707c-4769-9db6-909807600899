/**
 * Regional A/B Testing Service
 * Handles locale-specific A/B test variations and cultural adaptations
 */

import { localeConfigService } from "./locale-config";
import {
	type CulturalAdaptation,
	type RegionalABTestConfig,
	regionalCustomizationService,
} from "./regional-customization";

export interface RegionalTestVariation {
	id: string;
	testId: string;
	region: string;
	locale: string;
	name: string;
	description: string;
	culturalAdaptations: {
		colorScheme?: {
			primary?: string;
			secondary?: string;
			accent?: string;
		};
		messaging?: {
			title?: string;
			subtitle?: string;
			ctaText?: string;
			features?: string[];
		};
		layout?: {
			direction?: "ltr" | "rtl";
			spacing?: "compact" | "normal" | "spacious";
			imagePosition?: "top" | "left" | "right" | "background";
		};
		pricing?: {
			displayFormat?: "monthly" | "yearly" | "lifetime";
			emphasizeDiscount?: boolean;
			showComparison?: boolean;
		};
	};
	trafficAllocation: number; // 0-100 percentage
	isControl: boolean;
	status: "draft" | "active" | "paused" | "completed";
	performance?: RegionalVariationPerformance;
}

export interface RegionalVariationPerformance {
	region: string;
	locale: string;
	impressions: number;
	conversions: number;
	conversionRate: number;
	revenue: number;
	averageRevenuePerUser: number;
	culturalInsights: {
		colorPerformance: { [color: string]: number };
		messagingPerformance: { [message: string]: number };
		layoutPerformance: { [layout: string]: number };
	};
	statisticalSignificance: number;
	confidenceInterval: {
		lower: number;
		upper: number;
	};
}

export interface RegionalABTest {
	id: string;
	name: string;
	description: string;
	regions: string[];
	locales: string[];
	variations: RegionalTestVariation[];
	startDate: Date;
	endDate?: Date;
	status: "draft" | "active" | "paused" | "completed" | "archived";
	testType: "color" | "messaging" | "layout" | "pricing" | "comprehensive";
	hypothesis: string;
	successMetrics: string[];
	minimumDetectableEffect: number;
	culturalConsiderations: {
		avoidedElements: string[];
		preferredElements: string[];
		complianceRequirements: string[];
	};
	results?: RegionalTestResults;
}

export interface RegionalTestResults {
	testId: string;
	completedDate: Date;
	winningVariation?: string;
	statisticalSignificance: number;
	regionalPerformance: { [region: string]: RegionalVariationPerformance };
	culturalInsights: {
		colorPreferences: { [region: string]: string[] };
		messagingPreferences: { [region: string]: string[] };
		layoutPreferences: { [region: string]: string[] };
		pricingPreferences: { [region: string]: string[] };
	};
	recommendations: {
		region: string;
		recommendedVariation: string;
		confidence: number;
		reasoning: string;
	}[];
}

class RegionalABTestingService {
	private regionalTests: Map<string, RegionalABTest> = new Map();
	private testHistory: RegionalABTest[] = [];
	private activeTests: Set<string> = new Set();

	/**
	 * Create a new regional A/B test
	 */
	async createRegionalTest(
		testConfig: Omit<RegionalABTest, "id" | "variations">,
	): Promise<string> {
		const testId = this.generateTestId();

		// Validate regions and locales
		for (const region of testConfig.regions) {
			if (!regionalCustomizationService.isValidRegion(region)) {
				throw new Error(`Invalid region: ${region}`);
			}
		}

		for (const locale of testConfig.locales) {
			if (!localeConfigService.isValidLocale(locale)) {
				throw new Error(`Invalid locale: ${locale}`);
			}
		}

		const test: RegionalABTest = {
			...testConfig,
			id: testId,
			variations: [],
			status: "draft",
		};

		// Generate default variations based on cultural adaptations
		test.variations = await this.generateCulturalVariations(test);

		this.regionalTests.set(testId, test);
		return testId;
	}

	/**
	 * Generate cultural variations for a test
	 */
	private async generateCulturalVariations(
		test: RegionalABTest,
	): Promise<RegionalTestVariation[]> {
		const variations: RegionalTestVariation[] = [];

		for (const region of test.regions) {
			const regionConfig =
				regionalCustomizationService.getRegionalABTestConfig(region);
			const culturalAdaptation =
				regionalCustomizationService.getCulturalAdaptation(region);

			if (!regionConfig || !culturalAdaptation) {
				continue;
			}

			// Get locales for this region
			const regionLocales = test.locales.filter((locale) => {
				const localeRegion =
					regionalCustomizationService.getRegionFromLocale(locale);
				return localeRegion === region;
			});

			for (const locale of regionLocales) {
				// Create control variation
				const controlVariation = this.createControlVariation(
					test.id,
					region,
					locale,
					culturalAdaptation,
				);
				variations.push(controlVariation);

				// Create test variations based on test type
				if (
					test.testType === "color" &&
					regionConfig.culturalVariations.colorTests
				) {
					variations.push(
						...this.createColorVariations(
							test.id,
							region,
							locale,
							culturalAdaptation,
						),
					);
				}

				if (
					test.testType === "messaging" &&
					regionConfig.culturalVariations.messagingTests
				) {
					variations.push(
						...this.createMessagingVariations(test.id, region, locale),
					);
				}

				if (
					test.testType === "layout" &&
					regionConfig.culturalVariations.layoutTests
				) {
					variations.push(
						...this.createLayoutVariations(
							test.id,
							region,
							locale,
							culturalAdaptation,
						),
					);
				}

				if (
					test.testType === "pricing" &&
					regionConfig.culturalVariations.pricingTests
				) {
					variations.push(
						...this.createPricingVariations(test.id, region, locale),
					);
				}

				if (test.testType === "comprehensive") {
					// Create comprehensive variations combining multiple elements
					variations.push(
						...this.createComprehensiveVariations(
							test.id,
							region,
							locale,
							culturalAdaptation,
							regionConfig,
						),
					);
				}
			}
		}

		// Distribute traffic allocation
		this.distributeTrafficAllocation(variations);

		return variations;
	}

	/**
	 * Create control variation
	 */
	private createControlVariation(
		testId: string,
		region: string,
		locale: string,
		adaptation: CulturalAdaptation,
	): RegionalTestVariation {
		return {
			id: this.generateVariationId(),
			testId,
			region,
			locale,
			name: `Control - ${region} (${locale})`,
			description: "Original design with regional adaptations",
			culturalAdaptations: {
				colorScheme: {
					primary: adaptation.colorScheme.primary,
					secondary: adaptation.colorScheme.secondary,
					accent: adaptation.colorScheme.accent,
				},
				layout: {
					direction: adaptation.layout.direction,
					spacing: adaptation.layout.spacing,
				},
			},
			trafficAllocation: 0, // Will be set later
			isControl: true,
			status: "draft",
		};
	}

	/**
	 * Create color variations
	 */
	private createColorVariations(
		testId: string,
		region: string,
		locale: string,
		adaptation: CulturalAdaptation,
	): RegionalTestVariation[] {
		const variations: RegionalTestVariation[] = [];
		const alternativeColors = this.getAlternativeColors(region);

		alternativeColors.forEach((colorScheme, index) => {
			variations.push({
				id: this.generateVariationId(),
				testId,
				region,
				locale,
				name: `Color Variation ${index + 1} - ${region} (${locale})`,
				description: `Alternative color scheme for ${region}`,
				culturalAdaptations: {
					colorScheme,
					layout: {
						direction: adaptation.layout.direction,
						spacing: adaptation.layout.spacing,
					},
				},
				trafficAllocation: 0,
				isControl: false,
				status: "draft",
			});
		});

		return variations;
	}

	/**
	 * Create messaging variations
	 */
	private createMessagingVariations(
		testId: string,
		region: string,
		locale: string,
	): RegionalTestVariation[] {
		const variations: RegionalTestVariation[] = [];
		const messagingOptions = this.getRegionalMessaging(region, locale);

		messagingOptions.forEach((messaging, index) => {
			variations.push({
				id: this.generateVariationId(),
				testId,
				region,
				locale,
				name: `Messaging Variation ${index + 1} - ${region} (${locale})`,
				description: `Cultural messaging adaptation for ${region}`,
				culturalAdaptations: {
					messaging,
				},
				trafficAllocation: 0,
				isControl: false,
				status: "draft",
			});
		});

		return variations;
	}

	/**
	 * Create layout variations
	 */
	private createLayoutVariations(
		testId: string,
		region: string,
		locale: string,
		adaptation: CulturalAdaptation,
	): RegionalTestVariation[] {
		const variations: RegionalTestVariation[] = [];
		const layoutOptions = this.getLayoutOptions(region);

		layoutOptions.forEach((layout, index) => {
			variations.push({
				id: this.generateVariationId(),
				testId,
				region,
				locale,
				name: `Layout Variation ${index + 1} - ${region} (${locale})`,
				description: `Layout adaptation for ${region} cultural preferences`,
				culturalAdaptations: {
					layout,
					colorScheme: {
						primary: adaptation.colorScheme.primary,
						secondary: adaptation.colorScheme.secondary,
						accent: adaptation.colorScheme.accent,
					},
				},
				trafficAllocation: 0,
				isControl: false,
				status: "draft",
			});
		});

		return variations;
	}

	/**
	 * Create pricing variations
	 */
	private createPricingVariations(
		testId: string,
		region: string,
		locale: string,
	): RegionalTestVariation[] {
		const variations: RegionalTestVariation[] = [];
		const pricingOptions = this.getPricingOptions(region);

		pricingOptions.forEach((pricing, index) => {
			variations.push({
				id: this.generateVariationId(),
				testId,
				region,
				locale,
				name: `Pricing Variation ${index + 1} - ${region} (${locale})`,
				description: `Pricing display adaptation for ${region}`,
				culturalAdaptations: {
					pricing,
				},
				trafficAllocation: 0,
				isControl: false,
				status: "draft",
			});
		});

		return variations;
	}

	/**
	 * Create comprehensive variations
	 */
	private createComprehensiveVariations(
		testId: string,
		region: string,
		locale: string,
		_adaptation: CulturalAdaptation,
		regionConfig: RegionalABTestConfig,
	): RegionalTestVariation[] {
		const variations: RegionalTestVariation[] = [];
		const combinations = this.generateCombinations(region, regionConfig);

		combinations.forEach((combination, index) => {
			variations.push({
				id: this.generateVariationId(),
				testId,
				region,
				locale,
				name: `Comprehensive Variation ${index + 1} - ${region} (${locale})`,
				description: `Multi-element cultural adaptation for ${region}`,
				culturalAdaptations: combination,
				trafficAllocation: 0,
				isControl: false,
				status: "draft",
			});
		});

		return variations;
	}

	/**
	 * Get alternative color schemes for region
	 */
	private getAlternativeColors(
		region: string,
	): Array<{ primary: string; secondary: string; accent: string }> {
		const colorSchemes: {
			[region: string]: Array<{
				primary: string;
				secondary: string;
				accent: string;
			}>;
		} = {
			US: [
				{ primary: "#1E40AF", secondary: "#10B981", accent: "#F59E0B" },
				{ primary: "#DC2626", secondary: "#059669", accent: "#D97706" },
			],
			JP: [
				{ primary: "#7C2D12", secondary: "#1F2937", accent: "#374151" },
				{ primary: "#1E3A8A", secondary: "#991B1B", accent: "#92400E" },
			],
			EU: [
				{ primary: "#1E40AF", secondary: "#7C2D12", accent: "#059669" },
				{ primary: "#991B1B", secondary: "#1E3A8A", accent: "#92400E" },
			],
		};

		return colorSchemes[region] || colorSchemes.US;
	}

	/**
	 * Get regional messaging options
	 */
	private getRegionalMessaging(
		region: string,
		locale: string,
	): Array<{
		title?: string;
		subtitle?: string;
		ctaText?: string;
		features?: string[];
	}> {
		const messaging: { [key: string]: Array<any> } = {
			"US-en": [
				{
					title: "Unlock Premium Features",
					subtitle: "Get unlimited access to all features",
					ctaText: "Start Free Trial",
					features: [
						"Unlimited access",
						"Premium support",
						"Advanced features",
					],
				},
				{
					title: "Upgrade Now",
					subtitle: "Join thousands of satisfied users",
					ctaText: "Get Started",
					features: ["Full access", "24/7 support", "Exclusive content"],
				},
			],
			"JP-ja": [
				{
					title: "プレミアム機能をアンロック",
					subtitle: "すべての機能への無制限アクセス",
					ctaText: "無料トライアル開始",
					features: ["無制限アクセス", "プレミアムサポート", "高度な機能"],
				},
			],
		};

		return messaging[`${region}-${locale}`] || messaging["US-en"];
	}

	/**
	 * Get layout options for region
	 */
	private getLayoutOptions(region: string): Array<{
		direction?: "ltr" | "rtl";
		spacing?: "compact" | "normal" | "spacious";
		imagePosition?: "top" | "left" | "right" | "background";
	}> {
		const layouts: { [region: string]: Array<any> } = {
			US: [
				{ spacing: "normal", imagePosition: "top" },
				{ spacing: "spacious", imagePosition: "left" },
			],
			JP: [
				{ spacing: "compact", imagePosition: "top" },
				{ spacing: "normal", imagePosition: "background" },
			],
		};

		return layouts[region] || layouts.US;
	}

	/**
	 * Get pricing options for region
	 */
	private getPricingOptions(_region: string): Array<{
		displayFormat?: "monthly" | "yearly" | "lifetime";
		emphasizeDiscount?: boolean;
		showComparison?: boolean;
	}> {
		return [
			{
				displayFormat: "monthly",
				emphasizeDiscount: false,
				showComparison: true,
			},
			{
				displayFormat: "yearly",
				emphasizeDiscount: true,
				showComparison: false,
			},
			{
				displayFormat: "lifetime",
				emphasizeDiscount: true,
				showComparison: true,
			},
		];
	}

	/**
	 * Generate combinations for comprehensive testing
	 */
	private generateCombinations(
		region: string,
		_config: RegionalABTestConfig,
	): Array<any> {
		const combinations: Array<any> = [];
		const colors = this.getAlternativeColors(region);
		const layouts = this.getLayoutOptions(region);
		const pricing = this.getPricingOptions(region);

		// Generate a few strategic combinations
		for (let i = 0; i < Math.min(3, colors.length); i++) {
			combinations.push({
				colorScheme: colors[i],
				layout: layouts[i % layouts.length],
				pricing: pricing[i % pricing.length],
			});
		}

		return combinations;
	}

	/**
	 * Distribute traffic allocation among variations
	 */
	private distributeTrafficAllocation(
		variations: RegionalTestVariation[],
	): void {
		if (variations.length === 0) return;

		const allocationPerVariation = Math.floor(100 / variations.length);
		let remainder = 100 % variations.length;

		variations.forEach((variation, _index) => {
			variation.trafficAllocation = allocationPerVariation;
			if (remainder > 0) {
				variation.trafficAllocation += 1;
				remainder -= 1;
			}
		});
	}

	/**
	 * Start a regional test
	 */
	async startRegionalTest(testId: string): Promise<void> {
		const test = this.regionalTests.get(testId);
		if (!test || test.status !== "draft") {
			throw new Error("Test not found or not in draft status");
		}

		// Validate test configuration
		this.validateTestConfiguration(test);

		test.status = "active";
		test.startDate = new Date();
		this.activeTests.add(testId);

		// Activate all variations
		test.variations.forEach((variation) => {
			variation.status = "active";
		});

		console.log(`Started regional A/B test: ${test.name} (${testId})`);
	}

	/**
	 * Validate test configuration
	 */
	private validateTestConfiguration(test: RegionalABTest): void {
		if (test.variations.length === 0) {
			throw new Error("Test must have at least one variation");
		}

		const totalAllocation = test.variations.reduce(
			(sum, v) => sum + v.trafficAllocation,
			0,
		);
		if (Math.abs(totalAllocation - 100) > 1) {
			throw new Error("Traffic allocation must sum to 100%");
		}

		// Check for control variation in each region/locale combination
		const regionLocales = new Set(
			test.variations.map((v) => `${v.region}-${v.locale}`),
		);
		for (const regionLocale of regionLocales) {
			const hasControl = test.variations.some(
				(v) => `${v.region}-${v.locale}` === regionLocale && v.isControl,
			);
			if (!hasControl) {
				throw new Error(`Missing control variation for ${regionLocale}`);
			}
		}
	}

	/**
	 * Update variation performance
	 */
	updateVariationPerformance(
		variationId: string,
		performance: Partial<RegionalVariationPerformance>,
	): void {
		for (const test of this.regionalTests.values()) {
			const variation = test.variations.find((v) => v.id === variationId);
			if (variation) {
				variation.performance = {
					...variation.performance,
					...performance,
					region: variation.region,
					locale: variation.locale,
				} as RegionalVariationPerformance;
				break;
			}
		}
	}

	/**
	 * Complete a regional test
	 */
	async completeRegionalTest(testId: string): Promise<RegionalTestResults> {
		const test = this.regionalTests.get(testId);
		if (!test || test.status !== "active") {
			throw new Error("Test not found or not active");
		}

		const results = await this.analyzeTestResults(test);

		test.status = "completed";
		test.endDate = new Date();
		test.results = results;
		this.activeTests.delete(testId);

		// Move to history
		this.testHistory.push(test);
		this.regionalTests.delete(testId);

		return results;
	}

	/**
	 * Analyze test results
	 */
	private async analyzeTestResults(
		test: RegionalABTest,
	): Promise<RegionalTestResults> {
		const regionalPerformance: {
			[region: string]: RegionalVariationPerformance;
		} = {};
		const culturalInsights: RegionalTestResults["culturalInsights"] = {
			colorPreferences: {},
			messagingPreferences: {},
			layoutPreferences: {},
			pricingPreferences: {},
		};

		// Analyze performance by region
		for (const region of test.regions) {
			const regionVariations = test.variations.filter(
				(v) => v.region === region,
			);
			const bestVariation = regionVariations.reduce((best, current) =>
				(current.performance?.conversionRate || 0) >
				(best.performance?.conversionRate || 0)
					? current
					: best,
			);

			if (bestVariation.performance) {
				regionalPerformance[region] = bestVariation.performance;
			}

			// Extract cultural insights
			this.extractCulturalInsights(regionVariations, region, culturalInsights);
		}

		// Generate recommendations
		const recommendations = this.generateRecommendations(
			test,
			regionalPerformance,
		);

		// Find overall winning variation
		const winningVariation = test.variations.reduce((best, current) =>
			(current.performance?.conversionRate || 0) >
			(best.performance?.conversionRate || 0)
				? current
				: best,
		);

		return {
			testId: test.id,
			completedDate: new Date(),
			winningVariation: winningVariation.id,
			statisticalSignificance: this.calculateOverallSignificance(
				test.variations,
			),
			regionalPerformance,
			culturalInsights,
			recommendations,
		};
	}

	/**
	 * Extract cultural insights from variations
	 */
	private extractCulturalInsights(
		variations: RegionalTestVariation[],
		region: string,
		insights: RegionalTestResults["culturalInsights"],
	): void {
		// Analyze color preferences
		const colorPerformance = variations
			.filter((v) => v.culturalAdaptations.colorScheme)
			.map((v) => ({
				color: v.culturalAdaptations.colorScheme?.primary!,
				performance: v.performance?.conversionRate || 0,
			}))
			.sort((a, b) => b.performance - a.performance);

		insights.colorPreferences[region] = colorPerformance.map((c) => c.color);

		// Similar analysis for messaging, layout, and pricing...
		// Implementation would continue for other cultural elements
	}

	/**
	 * Generate recommendations
	 */
	private generateRecommendations(
		test: RegionalABTest,
		performance: { [region: string]: RegionalVariationPerformance },
	): RegionalTestResults["recommendations"] {
		const recommendations: RegionalTestResults["recommendations"] = [];

		for (const region of test.regions) {
			const regionPerformance = performance[region];
			if (!regionPerformance) continue;

			const regionVariations = test.variations.filter(
				(v) => v.region === region,
			);
			const bestVariation = regionVariations.reduce((best, current) =>
				(current.performance?.conversionRate || 0) >
				(best.performance?.conversionRate || 0)
					? current
					: best,
			);

			recommendations.push({
				region,
				recommendedVariation: bestVariation.id,
				confidence: regionPerformance.statisticalSignificance,
				reasoning: this.generateRecommendationReasoning(
					bestVariation,
					regionPerformance,
				),
			});
		}

		return recommendations;
	}

	/**
	 * Generate recommendation reasoning
	 */
	private generateRecommendationReasoning(
		variation: RegionalTestVariation,
		performance: RegionalVariationPerformance,
	): string {
		const reasons: string[] = [];

		if (performance.conversionRate > 0.1) {
			reasons.push(
				`High conversion rate of ${(performance.conversionRate * 100).toFixed(1)}%`,
			);
		}

		if (variation.culturalAdaptations.colorScheme) {
			reasons.push("Optimized color scheme for regional preferences");
		}

		if (variation.culturalAdaptations.messaging) {
			reasons.push("Culturally adapted messaging");
		}

		return reasons.join(", ") || "Best performing variation for this region";
	}

	/**
	 * Calculate overall statistical significance
	 */
	private calculateOverallSignificance(
		variations: RegionalTestVariation[],
	): number {
		const significances = variations
			.map((v) => v.performance?.statisticalSignificance || 0)
			.filter((s) => s > 0);

		return significances.length > 0
			? significances.reduce((sum, s) => sum + s, 0) / significances.length
			: 0;
	}

	/**
	 * Get regional test
	 */
	getRegionalTest(testId: string): RegionalABTest | null {
		return (
			this.regionalTests.get(testId) ||
			this.testHistory.find((t) => t.id === testId) ||
			null
		);
	}

	/**
	 * Get active regional tests
	 */
	getActiveRegionalTests(): RegionalABTest[] {
		return Array.from(this.activeTests).map(
			(id) => this.regionalTests.get(id)!,
		);
	}

	/**
	 * Generate test ID
	 */
	private generateTestId(): string {
		return `regional_test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
	}

	/**
	 * Generate variation ID
	 */
	private generateVariationId(): string {
		return `variation_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
	}
}

export const regionalABTestingService = new RegionalABTestingService();
