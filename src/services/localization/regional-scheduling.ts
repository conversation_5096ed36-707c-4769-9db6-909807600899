/**
 * Regional Scheduling Service
 * Handles timezone-aware scheduling for regional content deployment
 */

import { regionalCustomizationService } from "./regional-customization";

export interface ScheduledDeployment {
	id: string;
	contentId: string;
	contentType: "paywall" | "ab-test" | "variation";
	regions: string[];
	scheduledTime: Date;
	actualDeploymentTime?: Date;
	status: "pending" | "deploying" | "deployed" | "failed" | "cancelled";
	deploymentStrategy: "immediate" | "optimal" | "business-hours" | "custom";
	customTimezone?: string;
	rollbackPlan?: {
		enabled: boolean;
		triggerConditions: string[];
		rollbackContentId?: string;
	};
	notifications: {
		onSuccess: string[];
		onFailure: string[];
		beforeDeployment: number; // minutes before
	};
	metadata?: {
		createdBy: string;
		reason: string;
		priority: "low" | "medium" | "high" | "urgent";
	};
}

export interface RegionalDeploymentWindow {
	region: string;
	timezone: string;
	startTime: Date;
	endTime: Date;
	isBusinessHours: boolean;
	isOptimalTime: boolean;
	conflictingDeployments: string[];
}

export interface DeploymentConflict {
	deploymentId: string;
	conflictingDeploymentId: string;
	region: string;
	conflictType: "time-overlap" | "content-conflict" | "resource-conflict";
	severity: "low" | "medium" | "high";
	resolution: "auto-resolve" | "manual-review" | "block-deployment";
}

class RegionalSchedulingService {
	private scheduledDeployments: Map<string, ScheduledDeployment> = new Map();
	private deploymentHistory: ScheduledDeployment[] = [];
	private activeDeployments: Set<string> = new Set();

	/**
	 * Schedule a deployment for multiple regions
	 */
	async scheduleDeployment(
		deployment: Omit<ScheduledDeployment, "id">,
	): Promise<string> {
		const id = this.generateDeploymentId();
		const scheduledDeployment: ScheduledDeployment = {
			...deployment,
			id,
			status: "pending",
		};

		// Validate regions
		for (const region of deployment.regions) {
			if (!regionalCustomizationService.isValidRegion(region)) {
				throw new Error(`Invalid region: ${region}`);
			}
		}

		// Check for conflicts
		const conflicts = await this.checkDeploymentConflicts(scheduledDeployment);
		if (
			conflicts.some(
				(c) => c.severity === "high" && c.resolution === "block-deployment",
			)
		) {
			throw new Error(
				`Deployment blocked due to high-severity conflicts: ${conflicts.map((c) => c.conflictType).join(", ")}`,
			);
		}

		// Optimize deployment times if strategy is 'optimal'
		if (deployment.deploymentStrategy === "optimal") {
			scheduledDeployment.scheduledTime = this.calculateOptimalDeploymentTime(
				deployment.regions,
			);
		}

		this.scheduledDeployments.set(id, scheduledDeployment);

		// Schedule the actual deployment
		this.scheduleDeploymentExecution(scheduledDeployment);

		return id;
	}

	/**
	 * Calculate optimal deployment time across multiple regions
	 */
	private calculateOptimalDeploymentTime(regions: string[]): Date {
		const now = new Date();
		const deploymentWindows: RegionalDeploymentWindow[] = [];

		// Get deployment windows for each region
		for (const region of regions) {
			const window = this.getRegionalDeploymentWindow(region, now);
			if (window) {
				deploymentWindows.push(window);
			}
		}

		if (deploymentWindows.length === 0) {
			return now;
		}

		// Find overlapping optimal times
		const optimalWindows = deploymentWindows.filter((w) => w.isOptimalTime);
		if (optimalWindows.length > 0) {
			// Find the earliest common optimal time
			const latestStart = new Date(
				Math.max(...optimalWindows.map((w) => w.startTime.getTime())),
			);
			const earliestEnd = new Date(
				Math.min(...optimalWindows.map((w) => w.endTime.getTime())),
			);

			if (latestStart < earliestEnd) {
				return latestStart;
			}
		}

		// Fallback to business hours overlap
		const businessHoursWindows = deploymentWindows.filter(
			(w) => w.isBusinessHours,
		);
		if (businessHoursWindows.length > 0) {
			const latestStart = new Date(
				Math.max(...businessHoursWindows.map((w) => w.startTime.getTime())),
			);
			const earliestEnd = new Date(
				Math.min(...businessHoursWindows.map((w) => w.endTime.getTime())),
			);

			if (latestStart < earliestEnd) {
				return latestStart;
			}
		}

		// Fallback to next optimal time for primary region
		const primaryRegion = regions[0];
		return regionalCustomizationService.getOptimalDeploymentTime(
			primaryRegion,
			now,
		);
	}

	/**
	 * Get deployment window for a specific region
	 */
	private getRegionalDeploymentWindow(
		region: string,
		baseTime: Date,
	): RegionalDeploymentWindow | null {
		const timezoneConfig =
			regionalCustomizationService.getTimezoneConfig(region);
		if (!timezoneConfig) return null;

		const isBusinessHours = regionalCustomizationService.isBusinessHours(
			region,
			baseTime,
		);
		const optimalTime = regionalCustomizationService.getOptimalDeploymentTime(
			region,
			baseTime,
		);
		const isOptimalTime =
			Math.abs(baseTime.getTime() - optimalTime.getTime()) < 60 * 60 * 1000; // Within 1 hour

		// Get conflicting deployments
		const conflictingDeployments = this.getConflictingDeployments(
			region,
			baseTime,
		);

		return {
			region,
			timezone: timezoneConfig.timezone,
			startTime: baseTime,
			endTime: new Date(baseTime.getTime() + 60 * 60 * 1000), // 1 hour window
			isBusinessHours,
			isOptimalTime,
			conflictingDeployments,
		};
	}

	/**
	 * Check for deployment conflicts
	 */
	private async checkDeploymentConflicts(
		deployment: ScheduledDeployment,
	): Promise<DeploymentConflict[]> {
		const conflicts: DeploymentConflict[] = [];
		const deploymentTime = deployment.scheduledTime;
		const timeWindow = 30 * 60 * 1000; // 30 minutes

		for (const [existingId, existing] of this.scheduledDeployments) {
			if (existing.status === "cancelled" || existing.status === "failed") {
				continue;
			}

			// Check time overlap
			const timeDiff = Math.abs(
				deploymentTime.getTime() - existing.scheduledTime.getTime(),
			);
			if (timeDiff < timeWindow) {
				// Check region overlap
				const overlappingRegions = deployment.regions.filter((r) =>
					existing.regions.includes(r),
				);

				for (const region of overlappingRegions) {
					conflicts.push({
						deploymentId: deployment.id,
						conflictingDeploymentId: existingId,
						region,
						conflictType: "time-overlap",
						severity: this.calculateConflictSeverity(deployment, existing),
						resolution: this.determineConflictResolution(deployment, existing),
					});
				}
			}

			// Check content conflicts
			if (
				deployment.contentId === existing.contentId &&
				deployment.contentType === existing.contentType
			) {
				const overlappingRegions = deployment.regions.filter((r) =>
					existing.regions.includes(r),
				);

				for (const region of overlappingRegions) {
					conflicts.push({
						deploymentId: deployment.id,
						conflictingDeploymentId: existingId,
						region,
						conflictType: "content-conflict",
						severity: "high",
						resolution: "block-deployment",
					});
				}
			}
		}

		return conflicts;
	}

	/**
	 * Calculate conflict severity
	 */
	private calculateConflictSeverity(
		deployment1: ScheduledDeployment,
		deployment2: ScheduledDeployment,
	): "low" | "medium" | "high" {
		const priority1 = deployment1.metadata?.priority || "medium";
		const priority2 = deployment2.metadata?.priority || "medium";

		if (priority1 === "urgent" || priority2 === "urgent") {
			return "high";
		}

		if (priority1 === "high" || priority2 === "high") {
			return "medium";
		}

		return "low";
	}

	/**
	 * Determine conflict resolution strategy
	 */
	private determineConflictResolution(
		deployment1: ScheduledDeployment,
		deployment2: ScheduledDeployment,
	): "auto-resolve" | "manual-review" | "block-deployment" {
		const severity = this.calculateConflictSeverity(deployment1, deployment2);

		if (severity === "high") {
			return "block-deployment";
		}

		if (severity === "medium") {
			return "manual-review";
		}

		return "auto-resolve";
	}

	/**
	 * Get conflicting deployments for a region and time
	 */
	private getConflictingDeployments(region: string, time: Date): string[] {
		const conflicts: string[] = [];
		const timeWindow = 30 * 60 * 1000; // 30 minutes

		for (const [id, deployment] of this.scheduledDeployments) {
			if (deployment.status === "cancelled" || deployment.status === "failed") {
				continue;
			}

			if (deployment.regions.includes(region)) {
				const timeDiff = Math.abs(
					time.getTime() - deployment.scheduledTime.getTime(),
				);
				if (timeDiff < timeWindow) {
					conflicts.push(id);
				}
			}
		}

		return conflicts;
	}

	/**
	 * Schedule deployment execution
	 */
	private scheduleDeploymentExecution(deployment: ScheduledDeployment): void {
		const delay = deployment.scheduledTime.getTime() - Date.now();

		if (delay <= 0) {
			// Execute immediately
			this.executeDeployment(deployment.id);
		} else {
			// Schedule for later
			setTimeout(() => {
				this.executeDeployment(deployment.id);
			}, delay);
		}

		// Schedule pre-deployment notification
		if (deployment.notifications.beforeDeployment > 0) {
			const notificationDelay =
				delay - deployment.notifications.beforeDeployment * 60 * 1000;
			if (notificationDelay > 0) {
				setTimeout(() => {
					this.sendPreDeploymentNotification(deployment);
				}, notificationDelay);
			}
		}
	}

	/**
	 * Execute deployment
	 */
	private async executeDeployment(deploymentId: string): Promise<void> {
		const deployment = this.scheduledDeployments.get(deploymentId);
		if (!deployment || deployment.status !== "pending") {
			return;
		}

		try {
			deployment.status = "deploying";
			deployment.actualDeploymentTime = new Date();
			this.activeDeployments.add(deploymentId);

			// Simulate deployment process
			await this.performDeployment(deployment);

			deployment.status = "deployed";
			this.activeDeployments.delete(deploymentId);

			// Move to history
			this.deploymentHistory.push(deployment);
			this.scheduledDeployments.delete(deploymentId);

			// Send success notifications
			this.sendDeploymentNotification(deployment, "success");
		} catch (error) {
			deployment.status = "failed";
			this.activeDeployments.delete(deploymentId);

			// Send failure notifications
			this.sendDeploymentNotification(deployment, "failure", error.message);

			// Execute rollback if configured
			if (deployment.rollbackPlan?.enabled) {
				await this.executeRollback(deployment);
			}
		}
	}

	/**
	 * Perform the actual deployment
	 */
	private async performDeployment(
		deployment: ScheduledDeployment,
	): Promise<void> {
		// This would integrate with the actual deployment system
		// For now, we'll simulate the process

		console.log(
			`Deploying ${deployment.contentType} ${deployment.contentId} to regions: ${deployment.regions.join(", ")}`,
		);

		// Simulate deployment time
		await new Promise((resolve) => setTimeout(resolve, 2000));

		// Simulate potential failure
		if (Math.random() < 0.05) {
			// 5% failure rate
			throw new Error("Deployment failed due to network error");
		}
	}

	/**
	 * Execute rollback
	 */
	private async executeRollback(
		deployment: ScheduledDeployment,
	): Promise<void> {
		if (!deployment.rollbackPlan?.rollbackContentId) {
			return;
		}

		console.log(`Executing rollback for deployment ${deployment.id}`);

		// Create rollback deployment
		const rollbackDeployment: Omit<ScheduledDeployment, "id"> = {
			contentId: deployment.rollbackPlan.rollbackContentId,
			contentType: deployment.contentType,
			regions: deployment.regions,
			scheduledTime: new Date(),
			deploymentStrategy: "immediate",
			status: "pending",
			notifications: deployment.notifications,
			metadata: {
				...deployment.metadata,
				reason: `Rollback for failed deployment ${deployment.id}`,
			},
		};

		await this.scheduleDeployment(rollbackDeployment);
	}

	/**
	 * Send pre-deployment notification
	 */
	private sendPreDeploymentNotification(deployment: ScheduledDeployment): void {
		console.log(
			`Pre-deployment notification for ${deployment.id}: Deployment scheduled in ${deployment.notifications.beforeDeployment} minutes`,
		);
		// Implementation would integrate with notification service
	}

	/**
	 * Send deployment notification
	 */
	private sendDeploymentNotification(
		deployment: ScheduledDeployment,
		type: "success" | "failure",
		error?: string,
	): void {
		const recipients =
			type === "success"
				? deployment.notifications.onSuccess
				: deployment.notifications.onFailure;
		const message =
			type === "success"
				? `Deployment ${deployment.id} completed successfully`
				: `Deployment ${deployment.id} failed: ${error}`;

		console.log(`Notification to ${recipients.join(", ")}: ${message}`);
		// Implementation would integrate with notification service
	}

	/**
	 * Cancel scheduled deployment
	 */
	cancelDeployment(deploymentId: string): boolean {
		const deployment = this.scheduledDeployments.get(deploymentId);
		if (!deployment || deployment.status !== "pending") {
			return false;
		}

		deployment.status = "cancelled";
		return true;
	}

	/**
	 * Get deployment status
	 */
	getDeploymentStatus(deploymentId: string): ScheduledDeployment | null {
		return (
			this.scheduledDeployments.get(deploymentId) ||
			this.deploymentHistory.find((d) => d.id === deploymentId) ||
			null
		);
	}

	/**
	 * Get all scheduled deployments
	 */
	getScheduledDeployments(): ScheduledDeployment[] {
		return Array.from(this.scheduledDeployments.values());
	}

	/**
	 * Get deployment history
	 */
	getDeploymentHistory(limit?: number): ScheduledDeployment[] {
		const history = [...this.deploymentHistory].sort(
			(a, b) =>
				(b.actualDeploymentTime || b.scheduledTime).getTime() -
				(a.actualDeploymentTime || a.scheduledTime).getTime(),
		);

		return limit ? history.slice(0, limit) : history;
	}

	/**
	 * Generate unique deployment ID
	 */
	private generateDeploymentId(): string {
		return `deploy_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
	}

	/**
	 * Get regional deployment statistics
	 */
	getRegionalDeploymentStats(
		region: string,
		days: number = 30,
	): {
		totalDeployments: number;
		successfulDeployments: number;
		failedDeployments: number;
		averageDeploymentTime: number;
		optimalTimeUsage: number;
	} {
		const cutoffDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
		const regionDeployments = this.deploymentHistory.filter(
			(d) =>
				d.regions.includes(region) &&
				(d.actualDeploymentTime || d.scheduledTime) > cutoffDate,
		);

		const successful = regionDeployments.filter((d) => d.status === "deployed");
		const failed = regionDeployments.filter((d) => d.status === "failed");

		const deploymentTimes = successful
			.filter((d) => d.actualDeploymentTime)
			.map(
				(d) => d.actualDeploymentTime?.getTime() - d.scheduledTime.getTime(),
			);

		const averageDeploymentTime =
			deploymentTimes.length > 0
				? deploymentTimes.reduce((a, b) => a + b, 0) / deploymentTimes.length
				: 0;

		const optimalTimeDeployments = regionDeployments.filter((d) => {
			const optimalTime = regionalCustomizationService.getOptimalDeploymentTime(
				region,
				d.scheduledTime,
			);
			return (
				Math.abs(d.scheduledTime.getTime() - optimalTime.getTime()) <
				60 * 60 * 1000
			); // Within 1 hour
		});

		return {
			totalDeployments: regionDeployments.length,
			successfulDeployments: successful.length,
			failedDeployments: failed.length,
			averageDeploymentTime,
			optimalTimeUsage:
				regionDeployments.length > 0
					? optimalTimeDeployments.length / regionDeployments.length
					: 0,
		};
	}
}

export const regionalSchedulingService = new RegionalSchedulingService();
