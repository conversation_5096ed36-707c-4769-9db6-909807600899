/**
 * Translation Approval Workflow Service
 * Manages approval processes, review workflows, and quality gates
 */

import {
	type TranslationAssignment,
	translationAssignmentService,
} from "./translation-assignment-service";
import {
	type QualityReport,
	translationQualityService,
} from "./translation-quality-service";

export interface ApprovalWorkflow {
	id: string;
	name: string;
	description: string;
	steps: ApprovalStep[];
	conditions: WorkflowConditions;
	isActive: boolean;
}

export interface ApprovalStep {
	id: string;
	name: string;
	type:
		| "quality_check"
		| "manual_review"
		| "stakeholder_approval"
		| "automated_validation";
	order: number;
	required: boolean;
	assignedRole?: string;
	assignedUser?: number;
	timeoutHours?: number;
	conditions?: {
		minQualityScore?: number;
		requiredChecks?: string[];
		skipIfPreviousApproved?: boolean;
	};
}

export interface WorkflowConditions {
	contentTypes?: string[];
	priorities?: string[];
	locales?: string[];
	wordCountRange?: { min?: number; max?: number };
}

export interface ApprovalInstance {
	id: number;
	assignmentId: number;
	workflowId: string;
	currentStep: number;
	status: "pending" | "in_progress" | "approved" | "rejected" | "cancelled";
	steps: ApprovalStepInstance[];
	createdAt: Date;
	updatedAt: Date;
	completedAt?: Date;
}

export interface ApprovalStepInstance {
	stepId: string;
	status: "pending" | "in_progress" | "approved" | "rejected" | "skipped";
	assignedTo?: number;
	startedAt?: Date;
	completedAt?: Date;
	comments?: string;
	qualityReport?: QualityReport;
	metadata?: any;
}

class TranslationApprovalWorkflow {
	private workflows: ApprovalWorkflow[] = [
		{
			id: "standard-paywall",
			name: "Standard Paywall Approval",
			description: "Standard approval workflow for paywall translations",
			steps: [
				{
					id: "quality-check",
					name: "Automated Quality Check",
					type: "quality_check",
					order: 1,
					required: true,
					conditions: {
						minQualityScore: 80,
						requiredChecks: ["completeness", "placeholder-preservation"],
					},
				},
				{
					id: "translator-review",
					name: "Translator Self-Review",
					type: "manual_review",
					order: 2,
					required: true,
					assignedRole: "translator",
					timeoutHours: 24,
				},
				{
					id: "senior-review",
					name: "Senior Translator Review",
					type: "manual_review",
					order: 3,
					required: true,
					assignedRole: "senior-translator",
					timeoutHours: 48,
					conditions: {
						minQualityScore: 85,
					},
				},
				{
					id: "final-approval",
					name: "Translation Manager Approval",
					type: "stakeholder_approval",
					order: 4,
					required: true,
					assignedRole: "translation-manager",
					timeoutHours: 72,
				},
			],
			conditions: {
				contentTypes: ["api::paywall.paywall"],
				priorities: ["medium", "high", "urgent"],
			},
			isActive: true,
		},
		{
			id: "urgent-fast-track",
			name: "Urgent Fast-Track Approval",
			description: "Expedited approval for urgent translations",
			steps: [
				{
					id: "quality-check",
					name: "Automated Quality Check",
					type: "quality_check",
					order: 1,
					required: true,
					conditions: {
						minQualityScore: 75,
						requiredChecks: ["completeness"],
					},
				},
				{
					id: "senior-review",
					name: "Senior Translator Review",
					type: "manual_review",
					order: 2,
					required: true,
					assignedRole: "senior-translator",
					timeoutHours: 12,
				},
			],
			conditions: {
				priorities: ["urgent"],
			},
			isActive: true,
		},
		{
			id: "ab-test-workflow",
			name: "A/B Test Translation Workflow",
			description: "Specialized workflow for A/B test content",
			steps: [
				{
					id: "quality-check",
					name: "Automated Quality Check",
					type: "quality_check",
					order: 1,
					required: true,
					conditions: {
						minQualityScore: 85,
						requiredChecks: ["completeness", "terminology-consistency"],
					},
				},
				{
					id: "variation-review",
					name: "Variation Consistency Review",
					type: "manual_review",
					order: 2,
					required: true,
					assignedRole: "senior-translator",
					timeoutHours: 24,
				},
				{
					id: "product-approval",
					name: "Product Manager Approval",
					type: "stakeholder_approval",
					order: 3,
					required: true,
					assignedRole: "product-manager",
					timeoutHours: 48,
				},
			],
			conditions: {
				contentTypes: [
					"api::ab-test.ab-test",
					"api::paywall-variation.paywall-variation",
				],
			},
			isActive: true,
		},
	];

	private activeInstances: Map<number, ApprovalInstance> = new Map();

	/**
	 * Start approval workflow for assignment
	 */
	async startApprovalWorkflow(
		assignmentId: number,
	): Promise<ApprovalInstance | null> {
		try {
			const assignment =
				await translationAssignmentService.getAssignment(assignmentId);
			if (!assignment) {
				throw new Error(`Assignment ${assignmentId} not found`);
			}

			// Find matching workflow
			const workflow = this.findMatchingWorkflow(assignment);
			if (!workflow) {
				strapi.log.info(
					`No matching workflow found for assignment ${assignmentId}`,
				);
				return null;
			}

			// Create approval instance
			const instance: ApprovalInstance = {
				id: Date.now(), // In production, use proper ID generation
				assignmentId,
				workflowId: workflow.id,
				currentStep: 0,
				status: "pending",
				steps: workflow.steps.map((step) => ({
					stepId: step.id,
					status: "pending",
				})),
				createdAt: new Date(),
				updatedAt: new Date(),
			};

			this.activeInstances.set(instance.id, instance);

			// Start first step
			await this.processNextStep(instance.id);

			strapi.log.info(
				`Started approval workflow ${workflow.id} for assignment ${assignmentId}`,
			);

			return instance;
		} catch (error) {
			strapi.log.error("Failed to start approval workflow:", error);
			throw error;
		}
	}

	/**
	 * Process next step in workflow
	 */
	async processNextStep(instanceId: number): Promise<boolean> {
		try {
			const instance = this.activeInstances.get(instanceId);
			if (!instance) {
				throw new Error(`Approval instance ${instanceId} not found`);
			}

			const workflow = this.workflows.find((w) => w.id === instance.workflowId);
			if (!workflow) {
				throw new Error(`Workflow ${instance.workflowId} not found`);
			}

			// Check if workflow is complete
			if (instance.currentStep >= workflow.steps.length) {
				instance.status = "approved";
				instance.completedAt = new Date();
				await this.completeApprovalWorkflow(instance);
				return true;
			}

			const currentStep = workflow.steps[instance.currentStep];
			const stepInstance = instance.steps[instance.currentStep];

			// Process step based on type
			switch (currentStep.type) {
				case "quality_check":
					await this.processQualityCheckStep(
						instance,
						currentStep,
						stepInstance,
					);
					break;
				case "manual_review":
					await this.processManualReviewStep(
						instance,
						currentStep,
						stepInstance,
					);
					break;
				case "stakeholder_approval":
					await this.processStakeholderApprovalStep(
						instance,
						currentStep,
						stepInstance,
					);
					break;
				case "automated_validation":
					await this.processAutomatedValidationStep(
						instance,
						currentStep,
						stepInstance,
					);
					break;
			}

			instance.updatedAt = new Date();
			return false; // Not complete yet
		} catch (error) {
			strapi.log.error("Failed to process workflow step:", error);
			throw error;
		}
	}

	/**
	 * Process quality check step
	 */
	private async processQualityCheckStep(
		instance: ApprovalInstance,
		step: ApprovalStep,
		stepInstance: ApprovalStepInstance,
	): Promise<void> {
		stepInstance.status = "in_progress";
		stepInstance.startedAt = new Date();

		// Run quality assurance
		const qualityReport = await translationQualityService.runQualityAssurance(
			instance.assignmentId,
		);
		stepInstance.qualityReport = qualityReport;

		// Check if quality meets requirements
		const meetsRequirements = this.checkQualityRequirements(
			qualityReport,
			step.conditions,
		);

		if (meetsRequirements) {
			stepInstance.status = "approved";
			stepInstance.completedAt = new Date();
			instance.currentStep++;

			// Auto-proceed to next step
			await this.processNextStep(instance.id);
		} else {
			stepInstance.status = "rejected";
			stepInstance.completedAt = new Date();
			stepInstance.comments = `Quality check failed. Score: ${qualityReport.overallScore}%. Issues: ${qualityReport.issues.length}`;

			instance.status = "rejected";
			await this.rejectApprovalWorkflow(
				instance,
				"Quality requirements not met",
			);
		}
	}

	/**
	 * Process manual review step
	 */
	private async processManualReviewStep(
		instance: ApprovalInstance,
		step: ApprovalStep,
		stepInstance: ApprovalStepInstance,
	): Promise<void> {
		stepInstance.status = "pending";

		// Assign reviewer
		const reviewerId = await this.assignReviewer(instance, step);
		if (reviewerId) {
			stepInstance.assignedTo = reviewerId;
			await this.notifyReviewer(reviewerId, instance, step);
		}

		// Set timeout if specified
		if (step.timeoutHours) {
			await this.scheduleTimeout(instance.id, step.timeoutHours);
		}
	}

	/**
	 * Process stakeholder approval step
	 */
	private async processStakeholderApprovalStep(
		instance: ApprovalInstance,
		step: ApprovalStep,
		stepInstance: ApprovalStepInstance,
	): Promise<void> {
		stepInstance.status = "pending";

		// Assign approver
		const approverId = await this.assignApprover(instance, step);
		if (approverId) {
			stepInstance.assignedTo = approverId;
			await this.notifyApprover(approverId, instance, step);
		}

		// Set timeout if specified
		if (step.timeoutHours) {
			await this.scheduleTimeout(instance.id, step.timeoutHours);
		}
	}

	/**
	 * Process automated validation step
	 */
	private async processAutomatedValidationStep(
		instance: ApprovalInstance,
		_step: ApprovalStep,
		stepInstance: ApprovalStepInstance,
	): Promise<void> {
		stepInstance.status = "in_progress";
		stepInstance.startedAt = new Date();

		// Run automated validations
		const validationResult = await this.runAutomatedValidations(instance);

		if (validationResult.passed) {
			stepInstance.status = "approved";
			stepInstance.completedAt = new Date();
			instance.currentStep++;

			// Auto-proceed to next step
			await this.processNextStep(instance.id);
		} else {
			stepInstance.status = "rejected";
			stepInstance.completedAt = new Date();
			stepInstance.comments = validationResult.message;

			instance.status = "rejected";
			await this.rejectApprovalWorkflow(instance, validationResult.message);
		}
	}

	/**
	 * Approve workflow step
	 */
	async approveStep(
		instanceId: number,
		stepIndex: number,
		approverId: number,
		comments?: string,
	): Promise<boolean> {
		try {
			const instance = this.activeInstances.get(instanceId);
			if (!instance) {
				throw new Error(`Approval instance ${instanceId} not found`);
			}

			if (stepIndex !== instance.currentStep) {
				throw new Error("Can only approve current step");
			}

			const stepInstance = instance.steps[stepIndex];
			if (stepInstance.assignedTo !== approverId) {
				throw new Error("Only assigned reviewer can approve this step");
			}

			stepInstance.status = "approved";
			stepInstance.completedAt = new Date();
			stepInstance.comments = comments;

			instance.currentStep++;
			instance.updatedAt = new Date();

			// Process next step
			const isComplete = await this.processNextStep(instanceId);

			strapi.log.info(
				`Step ${stepIndex} approved for instance ${instanceId} by user ${approverId}`,
			);

			return isComplete;
		} catch (error) {
			strapi.log.error("Failed to approve step:", error);
			throw error;
		}
	}

	/**
	 * Reject workflow step
	 */
	async rejectStep(
		instanceId: number,
		stepIndex: number,
		reviewerId: number,
		reason: string,
	): Promise<void> {
		try {
			const instance = this.activeInstances.get(instanceId);
			if (!instance) {
				throw new Error(`Approval instance ${instanceId} not found`);
			}

			const stepInstance = instance.steps[stepIndex];
			if (stepInstance.assignedTo !== reviewerId) {
				throw new Error("Only assigned reviewer can reject this step");
			}

			stepInstance.status = "rejected";
			stepInstance.completedAt = new Date();
			stepInstance.comments = reason;

			instance.status = "rejected";
			instance.updatedAt = new Date();

			await this.rejectApprovalWorkflow(instance, reason);

			strapi.log.info(
				`Step ${stepIndex} rejected for instance ${instanceId} by user ${reviewerId}: ${reason}`,
			);
		} catch (error) {
			strapi.log.error("Failed to reject step:", error);
			throw error;
		}
	}

	/**
	 * Get approval instance
	 */
	getApprovalInstance(instanceId: number): ApprovalInstance | null {
		return this.activeInstances.get(instanceId) || null;
	}

	/**
	 * Get approval instances for assignment
	 */
	getApprovalInstancesForAssignment(assignmentId: number): ApprovalInstance[] {
		return Array.from(this.activeInstances.values()).filter(
			(instance) => instance.assignmentId === assignmentId,
		);
	}

	/**
	 * Cancel approval workflow
	 */
	async cancelApprovalWorkflow(
		instanceId: number,
		reason: string,
	): Promise<void> {
		const instance = this.activeInstances.get(instanceId);
		if (instance) {
			instance.status = "cancelled";
			instance.updatedAt = new Date();

			// Notify stakeholders
			await this.notifyWorkflowCancellation(instance, reason);

			strapi.log.info(`Approval workflow ${instanceId} cancelled: ${reason}`);
		}
	}

	// Private helper methods

	private findMatchingWorkflow(
		assignment: TranslationAssignment,
	): ApprovalWorkflow | null {
		for (const workflow of this.workflows) {
			if (!workflow.isActive) continue;

			if (this.matchesWorkflowConditions(assignment, workflow.conditions)) {
				return workflow;
			}
		}
		return null;
	}

	private matchesWorkflowConditions(
		assignment: TranslationAssignment,
		conditions: WorkflowConditions,
	): boolean {
		if (
			conditions.contentTypes &&
			!conditions.contentTypes.includes(assignment.contentType)
		) {
			return false;
		}

		if (
			conditions.priorities &&
			!conditions.priorities.includes(assignment.priority)
		) {
			return false;
		}

		if (
			conditions.locales &&
			!conditions.locales.includes(assignment.targetLocale)
		) {
			return false;
		}

		if (conditions.wordCountRange) {
			const wordCount = assignment.estimatedWords || 0;
			if (
				conditions.wordCountRange.min &&
				wordCount < conditions.wordCountRange.min
			) {
				return false;
			}
			if (
				conditions.wordCountRange.max &&
				wordCount > conditions.wordCountRange.max
			) {
				return false;
			}
		}

		return true;
	}

	private checkQualityRequirements(
		report: QualityReport,
		conditions?: ApprovalStep["conditions"],
	): boolean {
		if (!conditions) return true;

		if (
			conditions.minQualityScore &&
			report.overallScore < conditions.minQualityScore
		) {
			return false;
		}

		if (conditions.requiredChecks) {
			for (const requiredCheck of conditions.requiredChecks) {
				if (!report.passedChecks.includes(requiredCheck)) {
					return false;
				}
			}
		}

		return true;
	}

	private async assignReviewer(
		_instance: ApprovalInstance,
		_step: ApprovalStep,
	): Promise<number | null> {
		// Implementation would find appropriate reviewer based on role and availability
		// For now, return null to indicate manual assignment needed
		return null;
	}

	private async assignApprover(
		_instance: ApprovalInstance,
		_step: ApprovalStep,
	): Promise<number | null> {
		// Implementation would find appropriate approver based on role and availability
		// For now, return null to indicate manual assignment needed
		return null;
	}

	private async runAutomatedValidations(
		_instance: ApprovalInstance,
	): Promise<{ passed: boolean; message: string }> {
		// Implementation would run automated validations
		return { passed: true, message: "All validations passed" };
	}

	private async completeApprovalWorkflow(
		instance: ApprovalInstance,
	): Promise<void> {
		// Update assignment status to approved
		await strapi.entityService.update(
			"api::translation-assignment.translation-assignment",
			instance.assignmentId,
			{
				data: {
					// status: "approved", // Not in schema
					// approved_at: new Date(), // Not in schema
				},
			},
		);

		// Notify stakeholders
		await this.notifyWorkflowCompletion(instance);

		strapi.log.info(
			`Approval workflow completed for assignment ${instance.assignmentId}`,
		);
	}

	private async rejectApprovalWorkflow(
		instance: ApprovalInstance,
		reason: string,
	): Promise<void> {
		// Update assignment status to rejected
		await strapi.entityService.update(
			"api::translation-assignment.translation-assignment",
			instance.assignmentId,
			{
				data: {
					// status: "rejected", // Not in schema
					// reviewer_notes: reason, // Not in schema
				},
			},
		);

		// Notify stakeholders
		await this.notifyWorkflowRejection(instance, reason);

		strapi.log.info(
			`Approval workflow rejected for assignment ${instance.assignmentId}: ${reason}`,
		);
	}

	private async notifyReviewer(
		reviewerId: number,
		instance: ApprovalInstance,
		step: ApprovalStep,
	): Promise<void> {
		strapi.log.info(
			`Notified reviewer ${reviewerId} for step ${step.name} in instance ${instance.id}`,
		);
	}

	private async notifyApprover(
		approverId: number,
		instance: ApprovalInstance,
		step: ApprovalStep,
	): Promise<void> {
		strapi.log.info(
			`Notified approver ${approverId} for step ${step.name} in instance ${instance.id}`,
		);
	}

	private async notifyWorkflowCompletion(
		instance: ApprovalInstance,
	): Promise<void> {
		strapi.log.info(
			`Workflow ${instance.workflowId} completed for assignment ${instance.assignmentId}`,
		);
	}

	private async notifyWorkflowRejection(
		instance: ApprovalInstance,
		reason: string,
	): Promise<void> {
		strapi.log.info(
			`Workflow ${instance.workflowId} rejected for assignment ${instance.assignmentId}: ${reason}`,
		);
	}

	private async notifyWorkflowCancellation(
		instance: ApprovalInstance,
		reason: string,
	): Promise<void> {
		strapi.log.info(
			`Workflow ${instance.workflowId} cancelled for assignment ${instance.assignmentId}: ${reason}`,
		);
	}

	private async scheduleTimeout(
		instanceId: number,
		hours: number,
	): Promise<void> {
		// Implementation would schedule timeout handling
		strapi.log.info(
			`Scheduled timeout for instance ${instanceId} in ${hours} hours`,
		);
	}
}

export const translationApprovalWorkflow = new TranslationApprovalWorkflow();
