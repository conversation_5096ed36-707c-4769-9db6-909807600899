/**
 * Translation Quality Assurance Service
 * Provides quality validation tools for translation workflows
 */

export interface QualityCheck {
	id: string;
	name: string;
	description: string;
	type: "automated" | "manual" | "hybrid";
	severity: "error" | "warning" | "info";
	category:
	| "grammar"
	| "terminology"
	| "consistency"
	| "formatting"
	| "completeness";
}

export interface QualityIssue {
	checkId: string;
	severity: "error" | "warning" | "info";
	message: string;
	field: string;
	position?: { start: number; end: number };
	suggestion?: string;
	context?: string;
}

export interface QualityReport {
	assignmentId: number;
	overallScore: number;
	issues: QualityIssue[];
	passedChecks: string[];
	failedChecks: string[];
	recommendations: string[];
	generatedAt: Date;
}

class TranslationQualityService {
	private qualityChecks: QualityCheck[] = [
		{
			id: "completeness",
			name: "Translation Completeness",
			description: "Ensures all required fields are translated",
			type: "automated",
			severity: "error",
			category: "completeness",
		},
		{
			id: "length-variance",
			name: "Length Variance Check",
			description: "Checks if translation length is within acceptable range",
			type: "automated",
			severity: "warning",
			category: "formatting",
		},
		{
			id: "terminology-consistency",
			name: "Terminology Consistency",
			description: "Validates consistent use of approved terminology",
			type: "hybrid",
			severity: "warning",
			category: "terminology",
		},
		{
			id: "placeholder-preservation",
			name: "Placeholder Preservation",
			description: "Ensures placeholders and variables are preserved",
			type: "automated",
			severity: "error",
			category: "formatting",
		},
		{
			id: "cultural-appropriateness",
			name: "Cultural Appropriateness",
			description: "Manual review for cultural sensitivity",
			type: "manual",
			severity: "warning",
			category: "consistency",
		},
	];

	/**
	 * Run quality assurance checks on a translation assignment
	 */
	async runQualityAssurance(assignmentId: number): Promise<QualityReport> {
		try {
			const assignment = await strapi.entityService.findOne(
				"api::translation-assignment.translation-assignment",
				assignmentId,
				{ populate: "*" },
			);

			if (!assignment) {
				throw new Error(`Assignment ${assignmentId} not found`);
			}

			// Get source and target content
			const sourceEntity = await strapi.entityService.findOne(
				assignment.content_type,
				assignment.entity_id,
				{ locale: assignment.source_locale, populate: "*" },
			);

			const targetEntity = await strapi.entityService.findOne(
				assignment.content_type,
				assignment.entity_id,
				{ locale: assignment.target_locale, populate: "*" },
			);

			const issues: QualityIssue[] = [];
			const passedChecks: string[] = [];
			const failedChecks: string[] = [];

			// Run automated checks
			for (const check of this.qualityChecks.filter(
				(c) => c.type === "automated" || c.type === "hybrid",
			)) {
				const checkResult = await this.runQualityCheck(
					check,
					sourceEntity,
					targetEntity,
					assignment.fields_to_translate,
				);

				if (checkResult.passed) {
					passedChecks.push(check.id);
				} else {
					failedChecks.push(check.id);
					issues.push(...checkResult.issues);
				}
			}

			// Calculate overall score
			const totalChecks = this.qualityChecks.length;
			const passedCount = passedChecks.length;
			const overallScore = Math.round((passedCount / totalChecks) * 100);

			// Generate recommendations
			const recommendations = this.generateRecommendations(issues);

			const report: QualityReport = {
				assignmentId,
				overallScore,
				issues,
				passedChecks,
				failedChecks,
				recommendations,
				generatedAt: new Date(),
			};

			// Store quality report
			await this.storeQualityReport(report);

			strapi.log.info(
				`Quality assurance completed for assignment ${assignmentId}: ${overallScore}% score`,
			);

			return report;
		} catch (error) {
			strapi.log.error("Failed to run quality assurance:", error);
			throw error;
		}
	}

	/**
	 * Run a specific quality check
	 */
	private async runQualityCheck(
		check: QualityCheck,
		sourceEntity: any,
		targetEntity: any,
		fields: string[],
	): Promise<{ passed: boolean; issues: QualityIssue[] }> {
		const _issues: QualityIssue[] = [];

		switch (check.id) {
			case "completeness":
				return this.checkCompleteness(
					check,
					sourceEntity,
					targetEntity,
					fields,
				);

			case "length-variance":
				return this.checkLengthVariance(
					check,
					sourceEntity,
					targetEntity,
					fields,
				);

			case "terminology-consistency":
				return this.checkTerminologyConsistency(
					check,
					sourceEntity,
					targetEntity,
					fields,
				);

			case "placeholder-preservation":
				return this.checkPlaceholderPreservation(
					check,
					sourceEntity,
					targetEntity,
					fields,
				);

			default:
				return { passed: true, issues: [] };
		}
	}

	/**
	 * Check translation completeness
	 */
	private checkCompleteness(
		check: QualityCheck,
		sourceEntity: any,
		targetEntity: any,
		fields: string[],
	): { passed: boolean; issues: QualityIssue[] } {
		const issues: QualityIssue[] = [];

		for (const field of fields) {
			const sourceValue = sourceEntity?.[field];
			const targetValue = targetEntity?.[field];

			if (sourceValue && !targetValue) {
				issues.push({
					checkId: check.id,
					severity: check.severity,
					message: `Field '${field}' is not translated`,
					field,
					suggestion: "Please provide a translation for this field",
				});
			}

			if (sourceValue && targetValue && targetValue.trim().length === 0) {
				issues.push({
					checkId: check.id,
					severity: check.severity,
					message: `Field '${field}' translation is empty`,
					field,
					suggestion: "Please provide a non-empty translation",
				});
			}
		}

		return { passed: issues.length === 0, issues };
	}

	/**
	 * Check length variance between source and target
	 */
	private checkLengthVariance(
		check: QualityCheck,
		sourceEntity: any,
		targetEntity: any,
		fields: string[],
	): { passed: boolean; issues: QualityIssue[] } {
		const issues: QualityIssue[] = [];
		const maxVariancePercentage = 50; // Allow 50% variance

		for (const field of fields) {
			const sourceValue = sourceEntity?.[field];
			const targetValue = targetEntity?.[field];

			if (sourceValue && targetValue) {
				const sourceLength = sourceValue.length;
				const targetLength = targetValue.length;
				const variance =
					(Math.abs(targetLength - sourceLength) / sourceLength) * 100;

				if (variance > maxVariancePercentage) {
					issues.push({
						checkId: check.id,
						severity: check.severity,
						message: `Field '${field}' has significant length variance (${Math.round(variance)}%)`,
						field,
						suggestion: `Consider reviewing translation length. Source: ${sourceLength} chars, Target: ${targetLength} chars`,
						context: `Variance: ${Math.round(variance)}%`,
					});
				}
			}
		}

		return { passed: issues.length === 0, issues };
	}

	/**
	 * Check terminology consistency
	 */
	private async checkTerminologyConsistency(
		check: QualityCheck,
		sourceEntity: any,
		targetEntity: any,
		fields: string[],
	): Promise<{ passed: boolean; issues: QualityIssue[] }> {
		const issues: QualityIssue[] = [];

		// Get approved terminology from translation memory
		const terminologyEntries = await strapi.entityService.findMany(
			"api::translation-memory.translation-memory",
			{
				filters: {
					source_locale: sourceEntity.locale,
					target_locale: targetEntity.locale,
					is_approved: true,
				},
			},
		);

		const terminologyArray = Array.isArray(terminologyEntries) ? terminologyEntries : [terminologyEntries];

		for (const field of fields) {
			const sourceValue = sourceEntity?.[field];
			const targetValue = targetEntity?.[field];

			if (sourceValue && targetValue) {
				// Check for terminology matches
				for (const term of terminologyArray) {
					if (sourceValue.includes(term.source_text)) {
						if (!targetValue.includes(term.target_text)) {
							issues.push({
								checkId: check.id,
								severity: check.severity,
								message: `Approved terminology not used in field '${field}'`,
								field,
								suggestion: `Use approved translation: "${term.target_text}" for "${term.source_text}"`,
								context: `Found "${term.source_text}" but expected "${term.target_text}"`,
							});
						}
					}
				}
			}
		}

		return { passed: issues.length === 0, issues };
	}

	/**
	 * Check placeholder preservation
	 */
	private checkPlaceholderPreservation(
		check: QualityCheck,
		sourceEntity: any,
		targetEntity: any,
		fields: string[],
	): { passed: boolean; issues: QualityIssue[] } {
		const issues: QualityIssue[] = [];
		const placeholderPatterns = [
			/\{\{[^}]+\}\}/g, // {{variable}}
			/\{[^}]+\}/g, // {variable}
			/%[a-zA-Z_][a-zA-Z0-9_]*%/g, // %variable%
			/\$\{[^}]+\}/g, // ${variable}
		];

		for (const field of fields) {
			const sourceValue = sourceEntity?.[field];
			const targetValue = targetEntity?.[field];

			if (sourceValue && targetValue) {
				for (const pattern of placeholderPatterns) {
					const sourcePlaceholders = sourceValue.match(pattern) || [];
					const targetPlaceholders = targetValue.match(pattern) || [];

					// Check if all source placeholders are present in target
					for (const placeholder of sourcePlaceholders) {
						if (!targetPlaceholders.includes(placeholder)) {
							issues.push({
								checkId: check.id,
								severity: check.severity,
								message: `Missing placeholder '${placeholder}' in field '${field}'`,
								field,
								suggestion: `Include placeholder '${placeholder}' in the translation`,
								context: `Source placeholders: ${sourcePlaceholders.join(", ")}`,
							});
						}
					}

					// Check for extra placeholders in target
					for (const placeholder of targetPlaceholders) {
						if (!sourcePlaceholders.includes(placeholder)) {
							issues.push({
								checkId: check.id,
								severity: "warning",
								message: `Extra placeholder '${placeholder}' in field '${field}'`,
								field,
								suggestion: `Remove or verify placeholder '${placeholder}'`,
								context: `Target placeholders: ${targetPlaceholders.join(", ")}`,
							});
						}
					}
				}
			}
		}

		return { passed: issues.length === 0, issues };
	}

	/**
	 * Generate recommendations based on issues
	 */
	private generateRecommendations(issues: QualityIssue[]): string[] {
		const recommendations: string[] = [];
		const errorCount = issues.filter((i) => i.severity === "error").length;
		const warningCount = issues.filter((i) => i.severity === "warning").length;

		if (errorCount > 0) {
			recommendations.push(
				`Fix ${errorCount} critical error(s) before approval`,
			);
		}

		if (warningCount > 0) {
			recommendations.push(
				`Review ${warningCount} warning(s) for quality improvement`,
			);
		}

		if (issues.length === 0) {
			recommendations.push("Translation meets all quality standards");
		}

		// Category-specific recommendations
		const categories = [
			...new Set(
				issues.map(
					(i) => this.qualityChecks.find((c) => c.id === i.checkId)?.category,
				),
			),
		];

		if (categories.includes("completeness")) {
			recommendations.push("Ensure all required fields are fully translated");
		}

		if (categories.includes("terminology")) {
			recommendations.push(
				"Review terminology consistency with approved glossary",
			);
		}

		if (categories.includes("formatting")) {
			recommendations.push("Check formatting and placeholder preservation");
		}

		return recommendations;
	}

	/**
	 * Store quality report in database
	 */
	private async storeQualityReport(report: QualityReport): Promise<void> {
		try {
			await strapi.entityService.create("api::deployment-log.deployment-log", {
				data: {
					type: "quality_report",
					assignment_id: report.assignmentId,
					data: {
						overallScore: report.overallScore,
						issues: report.issues,
						passedChecks: report.passedChecks,
						failedChecks: report.failedChecks,
						recommendations: report.recommendations,
					},
					occurred_at: report.generatedAt,
					status: report.overallScore >= 80 ? "success" : "warning",
				},
			});
		} catch (error) {
			strapi.log.error("Failed to store quality report:", error);
		}
	}

	/**
	 * Get quality report for assignment
	 */
	async getQualityReport(assignmentId: number): Promise<QualityReport | null> {
		try {
			const log = await strapi.entityService.findMany(
				"api::deployment-log.deployment-log",
				{
					filters: {
						type: "quality_report",
						assignment_id: assignmentId,
					},
					sort: { createdAt: "desc" },
					limit: 1,
				},
			);

			if (log.length === 0) {
				return null;
			}

			const logEntry = log[0];
			return {
				assignmentId,
				overallScore: logEntry.data.overallScore,
				issues: logEntry.data.issues,
				passedChecks: logEntry.data.passedChecks,
				failedChecks: logEntry.data.failedChecks,
				recommendations: logEntry.data.recommendations,
				generatedAt: new Date(logEntry.occurred_at),
			};
		} catch (error) {
			strapi.log.error("Failed to get quality report:", error);
			return null;
		}
	}

	/**
	 * Get available quality checks
	 */
	getQualityChecks(): QualityCheck[] {
		return [...this.qualityChecks];
	}

	/**
	 * Add custom quality check
	 */
	addQualityCheck(check: QualityCheck): void {
		this.qualityChecks.push(check);
	}

	/**
	 * Update quality check
	 */
	updateQualityCheck(checkId: string, updates: Partial<QualityCheck>): boolean {
		const index = this.qualityChecks.findIndex((c) => c.id === checkId);
		if (index !== -1) {
			this.qualityChecks[index] = { ...this.qualityChecks[index], ...updates };
			return true;
		}
		return false;
	}

	/**
	 * Remove quality check
	 */
	removeQualityCheck(checkId: string): boolean {
		const index = this.qualityChecks.findIndex((c) => c.id === checkId);
		if (index !== -1) {
			this.qualityChecks.splice(index, 1);
			return true;
		}
		return false;
	}
}

export const translationQualityService = new TranslationQualityService();
