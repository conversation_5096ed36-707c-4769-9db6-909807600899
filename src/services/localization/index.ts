/**
 * Localization Services Index
 * Exports all localization-related services and utilities
 */

export type {
	LocaleConfig,
	LocaleThemeCustomization,
} from "./locale-config";
export { localeConfigService } from "./locale-config";
// Re-export types from locale-manager for backward compatibility
export type {
	LocalizationMetrics,
	TranslationStatus as LegacyTranslationStatus,
} from "./locale-manager";
export type { LocaleThemeOverride } from "./locale-theme-manager";
export { localeThemeManager } from "./locale-theme-manager";
export type {
	RegionalABTest,
	RegionalTestResults,
	RegionalTestVariation,
	RegionalVariationPerformance,
} from "./regional-ab-testing";
export { regionalABTestingService } from "./regional-ab-testing";
export type {
	CulturalAdaptation,
	RegionalABTestConfig,
	RegionalAnalytics,
	RegionalCompliance,
	RegionalPricingConfig,
	TimezoneConfig,
} from "./regional-customization";
export { regionalCustomizationService } from "./regional-customization";
export type {
	DeploymentConflict,
	RegionalDeploymentWindow,
	ScheduledDeployment,
} from "./regional-scheduling";
export { regionalSchedulingService } from "./regional-scheduling";
export type {
	ApprovalInstance,
	ApprovalStep,
	ApprovalStepInstance,
	ApprovalWorkflow,
} from "./translation-approval-workflow";
export { translationApprovalWorkflow } from "./translation-approval-workflow";
export type {
	AssignmentFilters,
	TranslationAssignment,
	WorkflowRule,
} from "./translation-assignment-service";
export { translationAssignmentService } from "./translation-assignment-service";
export type {
	QualityCheck,
	QualityIssue,
	QualityReport,
} from "./translation-quality-service";
export { translationQualityService } from "./translation-quality-service";
export type {
	BulkTranslationOperation,
	TranslationStatus,
} from "./translation-status";
export { translationStatusService } from "./translation-status";
// export { localeManager } from './locale-manager'; // Not exported
export { translationWorkflow } from "./translation-workflow";
export type {
	TranslatorProfile,
	TranslatorRole,
} from "./translator-role-manager";
export { translatorRoleManager } from "./translator-role-manager";
