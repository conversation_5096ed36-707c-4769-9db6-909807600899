/**
 * Locale Theme Manager Service
 * Handles locale-specific theme and layout customizations
 */

import { localeConfigService } from "./locale-config";

export interface LocaleThemeOverride {
	id: string;
	locale: string;
	paywallId: string;
	themeOverrides: {
		primaryColor?: string;
		backgroundColor?: string;
		textColor?: string;
		buttonStyle?: "rounded" | "square";
		headerStyle?: "minimal" | "hero" | "gradient";
		layoutDirection?: "ltr" | "rtl";
		fontFamily?: string;
		customStyles?: string;
	};
	culturalAdaptations: {
		colorScheme?: string[];
		imageStyle?: string;
		spacing?: "compact" | "normal" | "spacious";
		borderRadius?: "none" | "small" | "medium" | "large";
	};
	createdAt: Date;
	updatedAt: Date;
}

class LocaleThemeManager {
	private themeOverrides: Map<string, LocaleThemeOverride> = new Map();

	/**
	 * Get theme customization for a specific locale and paywall
	 */
	async getLocaleTheme(
		paywallId: string,
		locale: string,
	): Promise<LocaleThemeOverride | null> {
		const key = `${paywallId}_${locale}`;

		// Check cache first
		if (this.themeOverrides.has(key)) {
			return this.themeOverrides.get(key)!;
		}

		try {
			// In a real implementation, this would query the database
			// For now, we'll generate based on locale configuration
			const localeConfig = localeConfigService.getLocaleConfig(locale);
			if (!localeConfig) return null;

			const baseTheme = await this.getBaseTheme(paywallId);
			const localeTheme = this.generateLocaleTheme(
				paywallId,
				locale,
				baseTheme,
			);

			this.themeOverrides.set(key, localeTheme);
			return localeTheme;
		} catch (error) {
			strapi.log.error("Failed to get locale theme:", error);
			return null;
		}
	}

	/**
	 * Create or update locale-specific theme customization
	 */
	async setLocaleTheme(
		paywallId: string,
		locale: string,
		themeOverrides: Partial<LocaleThemeOverride["themeOverrides"]>,
		culturalAdaptations?: Partial<LocaleThemeOverride["culturalAdaptations"]>,
	): Promise<LocaleThemeOverride> {
		const key = `${paywallId}_${locale}`;
		const now = new Date();

		const existingTheme = this.themeOverrides.get(key);

		const localeTheme: LocaleThemeOverride = {
			id:
				existingTheme?.id ||
				`theme_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
			locale,
			paywallId,
			themeOverrides: {
				...existingTheme?.themeOverrides,
				...themeOverrides,
			},
			culturalAdaptations: {
				...existingTheme?.culturalAdaptations,
				...culturalAdaptations,
			},
			createdAt: existingTheme?.createdAt || now,
			updatedAt: now,
		};

		this.themeOverrides.set(key, localeTheme);

		// In a real implementation, this would save to database
		strapi.log.info(
			`Updated locale theme for paywall ${paywallId} in locale ${locale}`,
		);

		return localeTheme;
	}

	/**
	 * Get all locale themes for a paywall
	 */
	async getAllLocaleThemes(paywallId: string): Promise<LocaleThemeOverride[]> {
		const activeLocales = localeConfigService.getActiveLocales();
		const themes: LocaleThemeOverride[] = [];

		for (const locale of activeLocales) {
			const theme = await this.getLocaleTheme(paywallId, locale.code);
			if (theme) {
				themes.push(theme);
			}
		}

		return themes;
	}

	/**
	 * Apply cultural adaptations to theme
	 */
	applyCulturalAdaptations(baseTheme: any, locale: string): any {
		const localeConfig = localeConfigService.getLocaleConfig(locale);
		if (!localeConfig?.culturalAdaptations) return baseTheme;

		const adaptations = localeConfig.culturalAdaptations;
		const adaptedTheme = { ...baseTheme };

		// Apply color preferences
		if (
			adaptations.colorPreferences &&
			adaptations.colorPreferences.length > 0
		) {
			adaptedTheme.primary_color = adaptations.colorPreferences[0];
			if (adaptations.colorPreferences.length > 1) {
				adaptedTheme.background_color = adaptations.colorPreferences[1];
			}
			if (adaptations.colorPreferences.length > 2) {
				adaptedTheme.text_color = adaptations.colorPreferences[2];
			}
		}

		// Apply layout direction
		if (adaptations.layoutDirection) {
			adaptedTheme.layout_direction = adaptations.layoutDirection;
		}

		// Apply font family
		if (adaptations.fontFamily) {
			adaptedTheme.font_family = adaptations.fontFamily;
		}

		// Apply RTL-specific adjustments
		if (localeConfig.rtl) {
			adaptedTheme.layout_direction = "rtl";
			adaptedTheme.text_align = "right";

			// Adjust custom styles for RTL
			if (adaptedTheme.custom_styles) {
				adaptedTheme.custom_styles = this.adaptStylesForRTL(
					adaptedTheme.custom_styles,
				);
			}
		}

		return adaptedTheme;
	}

	/**
	 * Generate CSS for locale-specific theme
	 */
	generateLocaleCSS(localeTheme: LocaleThemeOverride): string {
		const { themeOverrides, culturalAdaptations } = localeTheme;
		let css = "";

		// Base theme styles
		if (themeOverrides.primaryColor) {
			css += `--primary-color: ${themeOverrides.primaryColor};\n`;
		}
		if (themeOverrides.backgroundColor) {
			css += `--background-color: ${themeOverrides.backgroundColor};\n`;
		}
		if (themeOverrides.textColor) {
			css += `--text-color: ${themeOverrides.textColor};\n`;
		}
		if (themeOverrides.fontFamily) {
			css += `--font-family: ${themeOverrides.fontFamily};\n`;
		}

		// Layout direction
		if (themeOverrides.layoutDirection) {
			css += `direction: ${themeOverrides.layoutDirection};\n`;
			if (themeOverrides.layoutDirection === "rtl") {
				css += `text-align: right;\n`;
			}
		}

		// Cultural adaptations
		if (culturalAdaptations.spacing) {
			const spacingMap = {
				compact: "0.5rem",
				normal: "1rem",
				spacious: "1.5rem",
			};
			css += `--spacing: ${spacingMap[culturalAdaptations.spacing]};\n`;
		}

		if (culturalAdaptations.borderRadius) {
			const radiusMap = {
				none: "0",
				small: "4px",
				medium: "8px",
				large: "16px",
			};
			css += `--border-radius: ${radiusMap[culturalAdaptations.borderRadius]};\n`;
		}

		// Custom styles
		if (themeOverrides.customStyles) {
			css += themeOverrides.customStyles;
		}

		return css;
	}

	/**
	 * Validate theme configuration for locale
	 */
	validateLocaleTheme(
		locale: string,
		themeOverrides: Partial<LocaleThemeOverride["themeOverrides"]>,
	): { isValid: boolean; errors: string[] } {
		const errors: string[] = [];

		// Validate locale exists
		if (!localeConfigService.isValidLocale(locale)) {
			errors.push(`Invalid locale: ${locale}`);
		}

		// Validate color formats
		const colorFields = ["primaryColor", "backgroundColor", "textColor"];
		for (const field of colorFields) {
			const color = themeOverrides[field as keyof typeof themeOverrides];
			if (color && !this.isValidColor(color as string)) {
				errors.push(`Invalid color format for ${field}: ${color}`);
			}
		}

		// Validate layout direction for RTL locales
		const localeConfig = localeConfigService.getLocaleConfig(locale);
		if (localeConfig?.rtl && themeOverrides.layoutDirection === "ltr") {
			errors.push(
				`Layout direction 'ltr' is not recommended for RTL locale: ${locale}`,
			);
		}

		return {
			isValid: errors.length === 0,
			errors,
		};
	}

	/**
	 * Get base theme for a paywall
	 */
	private async getBaseTheme(paywallId: string): Promise<any> {
		try {
			const paywall = await strapi.entityService.findOne(
				"api::paywall.paywall",
				paywallId,
				{
					populate: ["theme"],
				},
			);
			return paywall?.theme || {};
		} catch (error) {
			strapi.log.error("Failed to get base theme:", error);
			return {};
		}
	}

	/**
	 * Generate locale-specific theme based on cultural preferences
	 */
	private generateLocaleTheme(
		paywallId: string,
		locale: string,
		_baseTheme: any,
	): LocaleThemeOverride {
		const localeConfig = localeConfigService.getLocaleConfig(locale);
		const now = new Date();

		const themeOverrides: LocaleThemeOverride["themeOverrides"] = {};
		const culturalAdaptations: LocaleThemeOverride["culturalAdaptations"] = {};

		if (localeConfig?.culturalAdaptations) {
			const adaptations = localeConfig.culturalAdaptations;

			// Apply color preferences
			if (
				adaptations.colorPreferences &&
				adaptations.colorPreferences.length > 0
			) {
				themeOverrides.primaryColor = adaptations.colorPreferences[0];
			}

			// Apply layout direction
			themeOverrides.layoutDirection = adaptations.layoutDirection;

			// Apply font family
			if (adaptations.fontFamily) {
				themeOverrides.fontFamily = adaptations.fontFamily;
			}

			// Set cultural adaptations
			culturalAdaptations.colorScheme = adaptations.colorPreferences;
			culturalAdaptations.spacing = "normal";
			culturalAdaptations.borderRadius = "medium";
		}

		return {
			id: `theme_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
			locale,
			paywallId,
			themeOverrides,
			culturalAdaptations,
			createdAt: now,
			updatedAt: now,
		};
	}

	/**
	 * Adapt CSS styles for RTL layout
	 */
	private adaptStylesForRTL(styles: string): string {
		return styles
			.replace(/margin-left/g, "margin-right")
			.replace(/padding-left/g, "padding-right")
			.replace(/border-left/g, "border-right")
			.replace(/left:/g, "right:")
			.replace(/text-align:\s*left/g, "text-align: right")
			.replace(/float:\s*left/g, "float: right")
			.replace(/float:\s*right/g, "float: left");
	}

	/**
	 * Validate color format
	 */
	private isValidColor(color: string): boolean {
		const hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
		return hexRegex.test(color);
	}

	/**
	 * Clear cache for a specific paywall
	 */
	clearCache(paywallId?: string): void {
		if (paywallId) {
			const keysToDelete = Array.from(this.themeOverrides.keys()).filter(
				(key) => key.startsWith(`${paywallId}_`),
			);
			keysToDelete.forEach((key) => this.themeOverrides.delete(key));
		} else {
			this.themeOverrides.clear();
		}
	}
}

export const localeThemeManager = new LocaleThemeManager();
