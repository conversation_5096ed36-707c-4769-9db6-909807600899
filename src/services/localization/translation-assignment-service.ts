/**
 * Translation Assignment Service
 * Manages translation assignments, progress tracking, and workflow automation
 */

import { translatorRoleManager } from "./translator-role-manager";

export interface TranslationAssignment {
	id: number;
	contentType: string;
	entityId: number;
	sourceLocale: string;
	targetLocale: string;
	status:
	| "pending"
	| "assigned"
	| "in_progress"
	| "completed"
	| "reviewed"
	| "approved"
	| "rejected";
	priority: "low" | "medium" | "high" | "urgent";
	assignedTranslator?: number;
	assignedReviewer?: number;
	deadline?: Date;
	estimatedWords?: number;
	actualWords?: number;
	progressPercentage: number;
	fieldsToTranslate: string[];
	completedFields: string[];
	notes?: string;
	reviewerNotes?: string;
	qualityScore?: number;
	startedAt?: Date;
	completedAt?: Date;
	reviewedAt?: Date;
	approvedAt?: Date;
	translationMemoryMatches?: any;
	externalServiceData?: any;
	createdAt: Date;
	updatedAt: Date;
}

export interface AssignmentFilters {
	status?: string[];
	priority?: string[];
	assignedTranslator?: number;
	assignedReviewer?: number;
	sourceLocale?: string;
	targetLocale?: string;
	contentType?: string;
	deadline?: {
		before?: Date;
		after?: Date;
	};
}

export interface WorkflowRule {
	id: string;
	name: string;
	description: string;
	conditions: {
		contentType?: string[];
		priority?: string[];
		wordCount?: { min?: number; max?: number };
		deadline?: { urgent?: number; normal?: number }; // days
	};
	actions: {
		autoAssign?: boolean;
		requireReview?: boolean;
		requireApproval?: boolean;
		notifyStakeholders?: boolean;
		escalateAfter?: number; // hours
	};
	isActive: boolean;
}

class TranslationAssignmentService {
	private workflowRules: WorkflowRule[] = [
		{
			id: "paywall-high-priority",
			name: "High Priority Paywall Translations",
			description:
				"Auto-assign high priority paywall translations to senior translators",
			conditions: {
				contentType: ["api::paywall.paywall"],
				priority: ["high", "urgent"],
			},
			actions: {
				autoAssign: true,
				requireReview: true,
				requireApproval: true,
				notifyStakeholders: true,
				escalateAfter: 24,
			},
			isActive: true,
		},
		{
			id: "ab-test-urgent",
			name: "Urgent A/B Test Translations",
			description: "Fast-track urgent A/B test translations",
			conditions: {
				contentType: [
					"api::ab-test.ab-test",
					"api::paywall-variation.paywall-variation",
				],
				priority: ["urgent"],
			},
			actions: {
				autoAssign: true,
				requireReview: false,
				requireApproval: true,
				notifyStakeholders: true,
				escalateAfter: 12,
			},
			isActive: true,
		},
		{
			id: "large-content",
			name: "Large Content Assignments",
			description: "Special handling for large content pieces",
			conditions: {
				wordCount: { min: 1000 },
			},
			actions: {
				autoAssign: false,
				requireReview: true,
				requireApproval: true,
				notifyStakeholders: true,
				escalateAfter: 72,
			},
			isActive: true,
		},
	];

	/**
	 * Create a new translation assignment
	 */
	async createAssignment(
		contentType: string,
		entityId: number,
		sourceLocale: string,
		targetLocale: string,
		options: {
			priority?: "low" | "medium" | "high" | "urgent";
			deadline?: Date;
			assignedTranslator?: number;
			assignedReviewer?: number;
			notes?: string;
		} = {},
	): Promise<TranslationAssignment> {
		try {
			// Get source entity to analyze content
			const sourceEntity = await strapi.entityService.findOne(
				contentType as any,
				entityId,
				{
					// locale: sourceLocale, // Not in query options
					populate: "*",
				},
			);

			if (!sourceEntity) {
				throw new Error(
					`Source entity not found: ${entityId} in locale ${sourceLocale}`,
				);
			}

			// Get localizable fields
			const schema = strapi.getModel(contentType as any);
			const fieldsToTranslate = this.getLocalizableFields(schema);

			// Estimate word count
			const estimatedWords = this.estimateWordCount(
				sourceEntity,
				fieldsToTranslate,
			);

			// Create assignment data
			const assignmentData = {
				content_type: contentType,
				entity_id: entityId,
				source_locale: sourceLocale,
				target_locale: targetLocale,
				status: "pending",
				priority: options.priority || "medium",
				deadline: options.deadline,
				assigned_translator: options.assignedTranslator,
				assigned_reviewer: options.assignedReviewer,
				estimated_words: estimatedWords,
				progress_percentage: 0,
				fields_to_translate: fieldsToTranslate,
				completed_fields: [],
				notes: options.notes,
			};

			// Create the assignment
			const assignment = await strapi.entityService.create(
				"api::translation-assignment.translation-assignment",
				{ data: assignmentData },
			);

			// Apply workflow rules
			await this.applyWorkflowRules(assignment);

			strapi.log.info(
				`Created translation assignment ${assignment.id} for ${contentType}:${entityId} (${sourceLocale} → ${targetLocale})`,
			);

			return this.formatAssignment(assignment);
		} catch (error) {
			strapi.log.error("Failed to create translation assignment:", error);
			throw error;
		}
	}

	/**
	 * Assign translator to assignment
	 */
	async assignTranslator(
		assignmentId: number,
		translatorId: number,
	): Promise<TranslationAssignment> {
		try {
			// Verify translator can handle this assignment
			const assignment = await this.getAssignment(assignmentId);
			if (!assignment) {
				throw new Error(`Assignment ${assignmentId} not found`);
			}

			const canAssign = await this.canTranslatorHandleAssignment(
				translatorId,
				assignment,
			);
			if (!canAssign) {
				throw new Error(
					`Translator ${translatorId} cannot handle this assignment`,
				);
			}

			// Update assignment
			const updatedAssignment = await strapi.entityService.update(
				"api::translation-assignment.translation-assignment",
				assignmentId,
				{
					data: {
						// assigned_translator: translatorId, // Not in schema
						// status: "assigned", // Not in schema
					},
				},
			);

			// Send notification to translator
			await this.notifyTranslator(translatorId, assignment, "assigned");

			strapi.log.info(
				`Assigned translator ${translatorId} to assignment ${assignmentId}`,
			);

			return this.formatAssignment(updatedAssignment);
		} catch (error) {
			strapi.log.error("Failed to assign translator:", error);
			throw error;
		}
	}

	/**
	 * Start translation work
	 */
	async startTranslation(
		assignmentId: number,
		translatorId: number,
	): Promise<TranslationAssignment> {
		try {
			const assignment = await this.getAssignment(assignmentId);
			if (!assignment) {
				throw new Error(`Assignment ${assignmentId} not found`);
			}

			if (assignment.assignedTranslator !== translatorId) {
				throw new Error("Only assigned translator can start translation");
			}

			const updatedAssignment = await strapi.entityService.update(
				"api::translation-assignment.translation-assignment",
				assignmentId,
				{
					data: {
						// status: "in_progress", // Not in schema
						// started_at: new Date(), // Not in schema
					},
				},
			);

			strapi.log.info(`Started translation work on assignment ${assignmentId}`);

			return this.formatAssignment(updatedAssignment);
		} catch (error) {
			strapi.log.error("Failed to start translation:", error);
			throw error;
		}
	}

	/**
	 * Update translation progress
	 */
	async updateProgress(
		assignmentId: number,
		translatorId: number,
		updates: {
			completedFields?: string[];
			progressPercentage?: number;
			notes?: string;
			actualWords?: number;
		},
	): Promise<TranslationAssignment> {
		try {
			const assignment = await this.getAssignment(assignmentId);
			if (!assignment) {
				throw new Error(`Assignment ${assignmentId} not found`);
			}

			if (assignment.assignedTranslator !== translatorId) {
				throw new Error("Only assigned translator can update progress");
			}

			const updateData: any = {};

			if (updates.completedFields) {
				updateData.completed_fields = updates.completedFields;
				// Auto-calculate progress if not provided
				if (!updates.progressPercentage) {
					const totalFields = assignment.fieldsToTranslate.length;
					const completedCount = updates.completedFields.length;
					updateData.progress_percentage = Math.round(
						(completedCount / totalFields) * 100,
					);
				}
			}

			if (updates.progressPercentage !== undefined) {
				updateData.progress_percentage = updates.progressPercentage;
			}

			if (updates.notes) {
				updateData.notes = updates.notes;
			}

			if (updates.actualWords) {
				updateData.actual_words = updates.actualWords;
			}

			// Check if translation is completed
			if (updateData.progress_percentage === 100) {
				updateData.status = "completed";
				updateData.completed_at = new Date();
			}

			const updatedAssignment = await strapi.entityService.update(
				"api::translation-assignment.translation-assignment",
				assignmentId,
				{ data: updateData },
			);

			// If completed, trigger review workflow
			if (updateData.status === "completed") {
				await this.triggerReviewWorkflow(assignmentId);
			}

			strapi.log.info(
				`Updated progress for assignment ${assignmentId}: ${updateData.progress_percentage || assignment.progressPercentage}%`,
			);

			return this.formatAssignment(updatedAssignment);
		} catch (error) {
			strapi.log.error("Failed to update translation progress:", error);
			throw error;
		}
	}

	/**
	 * Submit translation for review
	 */
	async submitForReview(
		assignmentId: number,
		translatorId: number,
	): Promise<TranslationAssignment> {
		try {
			const assignment = await this.getAssignment(assignmentId);
			if (!assignment) {
				throw new Error(`Assignment ${assignmentId} not found`);
			}

			if (assignment.assignedTranslator !== translatorId) {
				throw new Error("Only assigned translator can submit for review");
			}

			if (assignment.status !== "completed") {
				throw new Error(
					"Assignment must be completed before submitting for review",
				);
			}

			// Auto-assign reviewer if not already assigned
			let reviewerId = assignment.assignedReviewer;
			if (!reviewerId) {
				reviewerId = await this.autoAssignReviewer(assignment);
			}

			const updatedAssignment = await strapi.entityService.update(
				"api::translation-assignment.translation-assignment",
				assignmentId,
				{
					data: {
						// status: "reviewed", // Not in schema
						// assigned_reviewer: reviewerId, // Not in schema
					},
				},
			);

			// Notify reviewer
			if (reviewerId) {
				await this.notifyReviewer(reviewerId, assignment, "review_requested");
			}

			strapi.log.info(`Submitted assignment ${assignmentId} for review`);

			return this.formatAssignment(updatedAssignment);
		} catch (error) {
			strapi.log.error("Failed to submit for review:", error);
			throw error;
		}
	}

	/**
	 * Review translation
	 */
	async reviewTranslation(
		assignmentId: number,
		reviewerId: number,
		review: {
			approved: boolean;
			qualityScore: number;
			reviewerNotes?: string;
			requiredChanges?: string[];
		},
	): Promise<TranslationAssignment> {
		try {
			const assignment = await this.getAssignment(assignmentId);
			if (!assignment) {
				throw new Error(`Assignment ${assignmentId} not found`);
			}

			if (assignment.assignedReviewer !== reviewerId) {
				throw new Error("Only assigned reviewer can review this translation");
			}

			const updateData: any = {
				quality_score: review.qualityScore,
				reviewer_notes: review.reviewerNotes,
				reviewed_at: new Date(),
			};

			if (review.approved) {
				updateData.status = "approved";
				updateData.approved_at = new Date();
			} else {
				updateData.status = "rejected";
				// Reset progress to allow re-translation
				updateData.progress_percentage = Math.max(
					0,
					assignment.progressPercentage - 25,
				);
			}

			const updatedAssignment = await strapi.entityService.update(
				"api::translation-assignment.translation-assignment",
				assignmentId,
				{ data: updateData },
			);

			// Notify translator of review result
			if (assignment.assignedTranslator) {
				await this.notifyTranslator(
					assignment.assignedTranslator,
					assignment,
					review.approved ? "approved" : "rejected",
				);
			}

			// Update translator performance metrics
			await this.updateTranslatorPerformance(
				assignment.assignedTranslator!,
				review.qualityScore,
				review.approved,
			);

			strapi.log.info(
				`Reviewed assignment ${assignmentId}: ${review.approved ? "approved" : "rejected"} (score: ${review.qualityScore})`,
			);

			return this.formatAssignment(updatedAssignment);
		} catch (error) {
			strapi.log.error("Failed to review translation:", error);
			throw error;
		}
	}

	/**
	 * Get assignments with filters
	 */
	async getAssignments(
		filters: AssignmentFilters = {},
		pagination = { page: 1, pageSize: 25 },
	): Promise<{
		data: TranslationAssignment[];
		meta: { pagination: any };
	}> {
		try {
			const where: any = {};

			if (filters.status) {
				where.status = { $in: filters.status };
			}

			if (filters.priority) {
				where.priority = { $in: filters.priority };
			}

			if (filters.assignedTranslator) {
				where.assigned_translator = filters.assignedTranslator;
			}

			if (filters.assignedReviewer) {
				where.assigned_reviewer = filters.assignedReviewer;
			}

			if (filters.sourceLocale) {
				where.source_locale = filters.sourceLocale;
			}

			if (filters.targetLocale) {
				where.target_locale = filters.targetLocale;
			}

			if (filters.contentType) {
				where.content_type = filters.contentType;
			}

			if (filters.deadline) {
				const deadlineFilter: any = {};
				if (filters.deadline.before) {
					deadlineFilter.$lt = filters.deadline.before;
				}
				if (filters.deadline.after) {
					deadlineFilter.$gt = filters.deadline.after;
				}
				where.deadline = deadlineFilter;
			}

			const result = await strapi.entityService.findPage(
				"api::translation-assignment.translation-assignment",
				{
					where,
					populate: ["assigned_translator", "assigned_reviewer"],
					sort: { createdAt: "desc" },
					...pagination,
				},
			);

			return {
				data: result.results.map((assignment) =>
					this.formatAssignment(assignment),
				),
				meta: {
					pagination: result.pagination || {},
				},
			};
		} catch (error) {
			strapi.log.error("Failed to get assignments:", error);
			throw error;
		}
	}

	/**
	 * Get assignment by ID
	 */
	async getAssignment(
		assignmentId: number,
	): Promise<TranslationAssignment | null> {
		try {
			const assignment = await strapi.entityService.findOne(
				"api::translation-assignment.translation-assignment",
				assignmentId,
				{
					populate: ["assigned_translator", "assigned_reviewer"],
				},
			);

			return assignment ? this.formatAssignment(assignment) : null;
		} catch (error) {
			strapi.log.error("Failed to get assignment:", error);
			return null;
		}
	}

	/**
	 * Auto-assign translator based on availability and expertise
	 */
	async autoAssignTranslator(
		assignment: TranslationAssignment,
	): Promise<number | null> {
		try {
			const availableTranslators =
				await translatorRoleManager.getAvailableTranslators(
					assignment.sourceLocale,
					assignment.targetLocale,
				);

			if (availableTranslators.length === 0) {
				return null;
			}

			// Score translators based on various factors
			const scoredTranslators = availableTranslators.map((translator) => {
				let score = 0;

				// Quality score weight (40%)
				score += translator.performance.averageQualityScore * 0.4;

				// On-time delivery weight (30%)
				score += translator.performance.onTimeDeliveryRate * 0.3;

				// Content type preference weight (20%)
				if (
					translator.preferences.preferredContentTypes.includes(
						assignment.contentType,
					)
				) {
					score += 2;
				}

				// Workload weight (10%) - prefer less busy translators
				const currentLoad = translator.performance.completedAssignments || 0;
				score += Math.max(0, (10 - currentLoad) * 0.1);

				return { translator, score };
			});

			// Sort by score and return the best translator
			scoredTranslators.sort((a, b) => b.score - a.score);
			return scoredTranslators[0].translator.userId;
		} catch (error) {
			strapi.log.error("Failed to auto-assign translator:", error);
			return null;
		}
	}

	/**
	 * Auto-assign reviewer
	 */
	private async autoAssignReviewer(
		assignment: TranslationAssignment,
	): Promise<number | null> {
		try {
			// Find senior translators or translation managers who can review
			const availableReviewers =
				await translatorRoleManager.getAvailableTranslators(
					assignment.sourceLocale,
					assignment.targetLocale,
				);

			const qualifiedReviewers = availableReviewers.filter(
				(translator) =>
					translator.roles.includes("senior-translator") ||
					translator.roles.includes("translation-manager"),
			);

			if (qualifiedReviewers.length === 0) {
				return null;
			}

			// Return the reviewer with the highest quality score
			qualifiedReviewers.sort(
				(a, b) =>
					b.performance.averageQualityScore - a.performance.averageQualityScore,
			);
			return qualifiedReviewers[0].userId;
		} catch (error) {
			strapi.log.error("Failed to auto-assign reviewer:", error);
			return null;
		}
	}

	/**
	 * Apply workflow rules to assignment
	 */
	private async applyWorkflowRules(assignment: any): Promise<void> {
		for (const rule of this.workflowRules) {
			if (!rule.isActive) continue;

			if (this.matchesRuleConditions(assignment, rule.conditions)) {
				await this.executeRuleActions(assignment, rule.actions);
			}
		}
	}

	/**
	 * Check if assignment matches rule conditions
	 */
	private matchesRuleConditions(
		assignment: any,
		conditions: WorkflowRule["conditions"],
	): boolean {
		if (
			conditions.contentType &&
			!conditions.contentType.includes(assignment.content_type)
		) {
			return false;
		}

		if (
			conditions.priority &&
			!conditions.priority.includes(assignment.priority)
		) {
			return false;
		}

		if (conditions.wordCount) {
			const wordCount = assignment.estimated_words || 0;
			if (conditions.wordCount.min && wordCount < conditions.wordCount.min) {
				return false;
			}
			if (conditions.wordCount.max && wordCount > conditions.wordCount.max) {
				return false;
			}
		}

		return true;
	}

	/**
	 * Execute rule actions
	 */
	private async executeRuleActions(
		assignment: any,
		actions: WorkflowRule["actions"],
	): Promise<void> {
		if (actions.autoAssign) {
			const translatorId = await this.autoAssignTranslator(
				this.formatAssignment(assignment),
			);
			if (translatorId) {
				await this.assignTranslator(assignment.id, translatorId);
			}
		}

		if (actions.notifyStakeholders) {
			await this.notifyStakeholders(assignment);
		}

		// Set up escalation if configured
		if (actions.escalateAfter) {
			await this.scheduleEscalation(assignment.id, actions.escalateAfter);
		}
	}

	/**
	 * Helper methods
	 */
	private getLocalizableFields(schema: any): string[] {
		const localizableFields: string[] = [];
		for (const [fieldName, fieldConfig] of Object.entries(schema.attributes)) {
			const config = fieldConfig as any;
			if (config.pluginOptions?.i18n?.localized === true) {
				localizableFields.push(fieldName);
			}
		}
		return localizableFields;
	}

	private estimateWordCount(entity: any, fields: string[]): number {
		let totalWords = 0;
		for (const field of fields) {
			if (entity[field] && typeof entity[field] === "string") {
				totalWords += entity[field].split(/\s+/).length;
			}
		}
		return totalWords;
	}

	private formatAssignment(assignment: any): TranslationAssignment {
		return {
			id: assignment.id,
			contentType: assignment.content_type,
			entityId: assignment.entity_id,
			sourceLocale: assignment.source_locale,
			targetLocale: assignment.target_locale,
			status: assignment.status,
			priority: assignment.priority,
			assignedTranslator:
				assignment.assigned_translator?.id || assignment.assigned_translator,
			assignedReviewer:
				assignment.assigned_reviewer?.id || assignment.assigned_reviewer,
			deadline: assignment.deadline ? new Date(assignment.deadline) : undefined,
			estimatedWords: assignment.estimated_words,
			actualWords: assignment.actual_words,
			progressPercentage: assignment.progress_percentage || 0,
			fieldsToTranslate: assignment.fields_to_translate || [],
			completedFields: assignment.completed_fields || [],
			notes: assignment.notes,
			reviewerNotes: assignment.reviewer_notes,
			qualityScore: assignment.quality_score,
			startedAt: assignment.started_at
				? new Date(assignment.started_at)
				: undefined,
			completedAt: assignment.completed_at
				? new Date(assignment.completed_at)
				: undefined,
			reviewedAt: assignment.reviewed_at
				? new Date(assignment.reviewed_at)
				: undefined,
			approvedAt: assignment.approved_at
				? new Date(assignment.approved_at)
				: undefined,
			translationMemoryMatches: assignment.translation_memory_matches,
			externalServiceData: assignment.external_service_data,
			createdAt: new Date(assignment.createdAt),
			updatedAt: new Date(assignment.updatedAt),
		};
	}

	private async canTranslatorHandleAssignment(
		translatorId: number,
		assignment: TranslationAssignment,
	): Promise<boolean> {
		const profile = translatorRoleManager.getTranslatorProfile(translatorId);
		if (!profile) return false;

		return (
			profile.availability.isAvailable &&
			profile.languages.source.includes(assignment.sourceLocale) &&
			profile.languages.target.includes(assignment.targetLocale)
		);
	}

	private async triggerReviewWorkflow(assignmentId: number): Promise<void> {
		// Implementation for triggering review workflow
		strapi.log.info(`Triggered review workflow for assignment ${assignmentId}`);
	}

	private async notifyTranslator(
		translatorId: number,
		assignment: TranslationAssignment,
		event: string,
	): Promise<void> {
		// Implementation for translator notifications
		strapi.log.info(
			`Notified translator ${translatorId} about ${event} for assignment ${assignment.id}`,
		);
	}

	private async notifyReviewer(
		reviewerId: number,
		assignment: TranslationAssignment,
		event: string,
	): Promise<void> {
		// Implementation for reviewer notifications
		strapi.log.info(
			`Notified reviewer ${reviewerId} about ${event} for assignment ${assignment.id}`,
		);
	}

	private async notifyStakeholders(assignment: any): Promise<void> {
		// Implementation for stakeholder notifications
		strapi.log.info(`Notified stakeholders about assignment ${assignment.id}`);
	}

	private async scheduleEscalation(
		assignmentId: number,
		hours: number,
	): Promise<void> {
		// Implementation for escalation scheduling
		strapi.log.info(
			`Scheduled escalation for assignment ${assignmentId} in ${hours} hours`,
		);
	}

	private async updateTranslatorPerformance(
		translatorId: number,
		qualityScore: number,
		approved: boolean,
	): Promise<void> {
		const profile = translatorRoleManager.getTranslatorProfile(translatorId);
		if (profile) {
			const currentAvg = profile.performance.averageQualityScore;
			const currentCount = profile.performance.completedAssignments;

			const newAvg =
				(currentAvg * currentCount + qualityScore) / (currentCount + 1);
			const newOnTimeRate = approved
				? (profile.performance.onTimeDeliveryRate * currentCount + 1) /
				(currentCount + 1)
				: profile.performance.onTimeDeliveryRate;

			translatorRoleManager.updateTranslatorProfile(translatorId, {
				performance: {
					...profile.performance,
					averageQualityScore: newAvg,
					completedAssignments: currentCount + 1,
					onTimeDeliveryRate: newOnTimeRate,
				},
			});
		}
	}
}

export const translationAssignmentService = new TranslationAssignmentService();
