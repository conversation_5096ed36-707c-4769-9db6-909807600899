/**
 * Translation Workflow Service
 * Handles translation status tracking, completion monitoring, and bulk operations
 */

interface TranslationJob {
	id: string;
	contentType: any;
	entityId: string | number;
	sourceLocale: string;
	targetLocales: string[];
	fields: string[];
	priority: "low" | "normal" | "high" | "urgent";
	status: "pending" | "in_progress" | "completed" | "failed" | "cancelled";
	assignedTo?: string;
	createdAt: Date;
	updatedAt: Date;
	completedAt?: Date;
	progress: {
		total: number;
		completed: number;
		percentage: number;
	};
	metadata?: {
		deadline?: Date;
		instructions?: string;
		context?: string;
	};
}

interface TranslationMemoryEntry {
	id: string;
	sourceText: string;
	targetText: string;
	sourceLocale: string;
	targetLocale: string;
	contentType: any;
	field: string;
	confidence: number;
	createdAt: Date;
	lastUsed: Date;
	usageCount: number;
}

interface BulkTranslationRequest {
	contentType: any;
	entityIds?: (string | number)[];
	sourceLocale: string;
	targetLocales: string[];
	fields?: string[];
	priority?: TranslationJob["priority"];
	assignTo?: string;
	deadline?: Date;
	instructions?: string;
}

class TranslationWorkflow {
	private jobs: Map<string, TranslationJob> = new Map();
	private translationMemory: Map<string, TranslationMemoryEntry> = new Map();
	private jobCounter: number = 0;

	/**
	 * Create translation job for content
	 */
	async createTranslationJob(request: {
		contentType: any;
		entityId: string | number;
		sourceLocale: string;
		targetLocales: string[];
		fields?: string[];
		priority?: TranslationJob["priority"];
		assignTo?: string;
		deadline?: Date;
		instructions?: string;
	}): Promise<{ success: boolean; jobId?: string; errors?: string[] }> {
		try {
			// Validate content exists
			const entity = await strapi.entityService.findOne(
				request.contentType,
				request.entityId,
				{
					locale: request.sourceLocale,
					populate: "*",
				},
			);

			if (!entity) {
				return { success: false, errors: ["Source content not found"] };
			}

			// Determine fields to translate
			const fields =
				request.fields || this.getTranslatableFields(request.contentType);

			// Calculate total work
			const totalTranslations = request.targetLocales.length * fields.length;

			const jobId = `job_${++this.jobCounter}_${Date.now()}`;
			const job: TranslationJob = {
				id: jobId,
				contentType: request.contentType,
				entityId: request.entityId,
				sourceLocale: request.sourceLocale,
				targetLocales: request.targetLocales,
				fields,
				priority: request.priority || "normal",
				status: "pending",
				assignedTo: request.assignTo,
				createdAt: new Date(),
				updatedAt: new Date(),
				progress: {
					total: totalTranslations,
					completed: 0,
					percentage: 0,
				},
				metadata: {
					deadline: request.deadline,
					instructions: request.instructions,
				},
			};

			this.jobs.set(jobId, job);

			// Log job creation
			await this.logTranslationActivity(jobId, "job_created", {
				contentType: request.contentType,
				entityId: request.entityId,
				targetLocales: request.targetLocales,
			});

			strapi.log.info(
				`Translation job created: ${jobId} for ${request.contentType}:${request.entityId}`,
			);

			return { success: true, jobId };
		} catch (error) {
			strapi.log.error("Failed to create translation job:", error);
			return {
				success: false,
				errors: [error.message || "Unknown error creating translation job"],
			};
		}
	}

	/**
	 * Create bulk translation jobs
	 */
	async createBulkTranslationJobs(request: BulkTranslationRequest): Promise<{
		success: boolean;
		jobIds: string[];
		errors: string[];
	}> {
		const jobIds: string[] = [];
		const errors: string[] = [];

		try {
			// Get entities to translate
			const entities = request.entityIds
				? await Promise.all(
						request.entityIds.map((id) =>
							strapi.entityService.findOne(request.contentType, id, {
								locale: request.sourceLocale,
								populate: "*",
							}),
						),
					)
				: await strapi.entityService.findMany(request.contentType, {
						locale: request.sourceLocale,
						populate: "*",
					});

			const entitiesArray = Array.isArray(entities) ? entities : [entities];

			// Create job for each entity
			for (const entity of entitiesArray) {
				if (!entity) continue;

				try {
					const result = await this.createTranslationJob({
						contentType: request.contentType,
						entityId: entity.id,
						sourceLocale: request.sourceLocale,
						targetLocales: request.targetLocales,
						fields: request.fields,
						priority: request.priority,
						assignTo: request.assignTo,
						deadline: request.deadline,
						instructions: request.instructions,
					});

					if (result.success && result.jobId) {
						jobIds.push(result.jobId);
					} else {
						errors.push(
							`Failed to create job for entity ${entity.id}: ${result.errors?.join(", ")}`,
						);
					}
				} catch (error) {
					errors.push(
						`Error creating job for entity ${entity.id}: ${error.message}`,
					);
				}
			}

			return { success: errors.length === 0, jobIds, errors };
		} catch (error) {
			strapi.log.error("Failed to create bulk translation jobs:", error);
			return {
				success: false,
				jobIds,
				errors: [error.message || "Unknown error creating bulk jobs"],
			};
		}
	}

	/**
	 * Update translation job progress
	 */
	async updateJobProgress(
		jobId: string,
		locale: string,
		field: string,
		translatedText: string,
	): Promise<{ success: boolean; errors?: string[] }> {
		try {
			const job = this.jobs.get(jobId);
			if (!job) {
				return { success: false, errors: ["Job not found"] };
			}

			// Update the actual content
			const updateData: any = {};
			updateData[field] = translatedText;

			await strapi.entityService.update(job.contentType, job.entityId, {
				data: updateData,
				locale,
			});

			// Update job progress
			job.progress.completed++;
			job.progress.percentage =
				(job.progress.completed / job.progress.total) * 100;
			job.updatedAt = new Date();

			// Check if job is complete
			if (job.progress.completed >= job.progress.total) {
				job.status = "completed";
				job.completedAt = new Date();
			} else if (job.status === "pending") {
				job.status = "in_progress";
			}

			// Add to translation memory
			const sourceEntity = await strapi.entityService.findOne(
				job.contentType,
				job.entityId,
				{
					locale: job.sourceLocale,
					populate: "*",
				},
			);

			if (sourceEntity?.[field]) {
				await this.addToTranslationMemory({
					sourceText: sourceEntity[field],
					targetText: translatedText,
					sourceLocale: job.sourceLocale,
					targetLocale: locale,
					contentType: job.contentType,
					field,
				});
			}

			// Log progress update
			await this.logTranslationActivity(jobId, "progress_updated", {
				locale,
				field,
				progress: job.progress.percentage,
			});

			return { success: true };
		} catch (error) {
			strapi.log.error("Failed to update job progress:", error);
			return {
				success: false,
				errors: [error.message || "Unknown error updating progress"],
			};
		}
	}

	/**
	 * Get translation job status
	 */
	getJobStatus(jobId: string): TranslationJob | null {
		return this.jobs.get(jobId) || null;
	}

	/**
	 * Get all translation jobs
	 */
	getAllJobs(filters?: {
		status?: TranslationJob["status"];
		assignedTo?: string;
		contentType?: string;
		priority?: TranslationJob["priority"];
	}): TranslationJob[] {
		let jobs = Array.from(this.jobs.values());

		if (filters) {
			if (filters.status) {
				jobs = jobs.filter((job) => job.status === filters.status);
			}
			if (filters.assignedTo) {
				jobs = jobs.filter((job) => job.assignedTo === filters.assignedTo);
			}
			if (filters.contentType) {
				jobs = jobs.filter((job) => job.contentType === filters.contentType);
			}
			if (filters.priority) {
				jobs = jobs.filter((job) => job.priority === filters.priority);
			}
		}

		return jobs.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
	}

	/**
	 * Cancel translation job
	 */
	async cancelJob(
		jobId: string,
		reason?: string,
	): Promise<{ success: boolean; errors?: string[] }> {
		try {
			const job = this.jobs.get(jobId);
			if (!job) {
				return { success: false, errors: ["Job not found"] };
			}

			if (job.status === "completed") {
				return { success: false, errors: ["Cannot cancel completed job"] };
			}

			job.status = "cancelled";
			job.updatedAt = new Date();

			// Log cancellation
			await this.logTranslationActivity(jobId, "job_cancelled", { reason });

			return { success: true };
		} catch (error) {
			strapi.log.error("Failed to cancel job:", error);
			return {
				success: false,
				errors: [error.message || "Unknown error cancelling job"],
			};
		}
	}

	/**
	 * Get translation suggestions from memory
	 */
	getTranslationSuggestions(
		sourceText: string,
		sourceLocale: string,
		targetLocale: string,
		contentType?: string,
		field?: string,
	): TranslationMemoryEntry[] {
		const suggestions: TranslationMemoryEntry[] = [];

		for (const entry of this.translationMemory.values()) {
			if (
				entry.sourceLocale === sourceLocale &&
				entry.targetLocale === targetLocale
			) {
				// Exact match
				if (entry.sourceText === sourceText) {
					suggestions.push({ ...entry, confidence: 1.0 });
				}
				// Partial match
				else if (
					entry.sourceText.includes(sourceText) ||
					sourceText.includes(entry.sourceText)
				) {
					const similarity = this.calculateTextSimilarity(
						sourceText,
						entry.sourceText,
					);
					if (similarity > 0.7) {
						suggestions.push({ ...entry, confidence: similarity });
					}
				}
				// Context match (same content type and field)
				else if (
					contentType &&
					field &&
					entry.contentType === contentType &&
					entry.field === field
				) {
					const similarity = this.calculateTextSimilarity(
						sourceText,
						entry.sourceText,
					);
					if (similarity > 0.5) {
						suggestions.push({ ...entry, confidence: similarity * 0.8 });
					}
				}
			}
		}

		return suggestions.sort((a, b) => b.confidence - a.confidence).slice(0, 5);
	}

	/**
	 * Export translation data for external tools
	 */
	async exportTranslationData(
		contentType: any,
		sourceLocale: string,
		targetLocales: string[],
		format: "json" | "csv" | "xliff" = "json",
	): Promise<{ success: boolean; data?: any; errors?: string[] }> {
		try {
			const entities = await strapi.entityService.findMany(contentType, {
				locale: sourceLocale,
				populate: "*",
			});

			const entitiesArray = Array.isArray(entities) ? entities : [entities];
			const translationData: any[] = [];
			const fields = this.getTranslatableFields(contentType);

			for (const entity of entitiesArray) {
				for (const field of fields) {
					if (entity[field]) {
						const item = {
							entityId: entity.id,
							field,
							sourceText: entity[field],
							sourceLocale,
							targetLocales,
							context: `${contentType}.${field}`,
							metadata: {
								entityName:
									entity.name || entity.title || `Entity ${entity.id}`,
								lastUpdated: entity.updatedAt,
							},
						};
						translationData.push(item);
					}
				}
			}

			let exportData: any;
			switch (format) {
				case "json":
					exportData = {
						metadata: {
							contentType,
							sourceLocale,
							targetLocales,
							exportedAt: new Date(),
							totalItems: translationData.length,
						},
						translations: translationData,
					};
					break;
				case "csv":
					exportData = this.convertToCSV(translationData);
					break;
				case "xliff":
					exportData = this.convertToXLIFF(
						translationData,
						sourceLocale,
						targetLocales[0],
					);
					break;
			}

			return { success: true, data: exportData };
		} catch (error) {
			strapi.log.error("Failed to export translation data:", error);
			return {
				success: false,
				errors: [error.message || "Unknown error exporting data"],
			};
		}
	}

	/**
	 * Import translation data from external tools
	 */
	async importTranslationData(
		data: any,
		format: "json" | "csv" | "xliff" = "json",
	): Promise<{ success: boolean; imported: number; errors: string[] }> {
		const errors: string[] = [];
		let imported = 0;

		try {
			let translationData: any[];

			switch (format) {
				case "json":
					translationData = data.translations || data;
					break;
				case "csv":
					translationData = this.parseCSV(data);
					break;
				case "xliff":
					translationData = this.parseXLIFF(data);
					break;
				default:
					return {
						success: false,
						imported: 0,
						errors: ["Unsupported format"],
					};
			}

			for (const item of translationData) {
				try {
					if (item.targetText && item.targetLocale) {
						await strapi.entityService.update(
							item.contentType || "api::paywall.paywall",
							item.entityId,
							{
								data: { [item.field]: item.targetText },
								locale: item.targetLocale,
							},
						);
						imported++;
					}
				} catch (error) {
					errors.push(
						`Failed to import item ${item.entityId}.${item.field}: ${error.message}`,
					);
				}
			}

			return { success: errors.length === 0, imported, errors };
		} catch (error) {
			strapi.log.error("Failed to import translation data:", error);
			return {
				success: false,
				imported,
				errors: [error.message || "Unknown error importing data"],
			};
		}
	}

	// Private helper methods

	private getTranslatableFields(contentType: any): string[] {
		const fieldMappings: Record<string, string[]> = {
			"api::paywall.paywall": [
				"title",
				"subtitle",
				"description_text",
				"cta_text",
				"cta_secondary_text",
			],
			"api::ab-test.ab-test": ["description", "hypothesis", "notes"],
			"api::paywall-variation.paywall-variation": ["name", "description"],
		};

		return fieldMappings[contentType] || ["title", "description"];
	}

	private async addToTranslationMemory(entry: {
		sourceText: string;
		targetText: string;
		sourceLocale: string;
		targetLocale: string;
		contentType: any;
		field: string;
	}): Promise<void> {
		const id = `${entry.sourceLocale}_${entry.targetLocale}_${this.hashText(entry.sourceText)}`;

		const existingEntry = this.translationMemory.get(id);
		if (existingEntry) {
			existingEntry.lastUsed = new Date();
			existingEntry.usageCount++;
			existingEntry.targetText = entry.targetText; // Update with latest translation
		} else {
			const memoryEntry: TranslationMemoryEntry = {
				id,
				sourceText: entry.sourceText,
				targetText: entry.targetText,
				sourceLocale: entry.sourceLocale,
				targetLocale: entry.targetLocale,
				contentType: entry.contentType,
				field: entry.field,
				confidence: 1.0,
				createdAt: new Date(),
				lastUsed: new Date(),
				usageCount: 1,
			};
			this.translationMemory.set(id, memoryEntry);
		}
	}

	private calculateTextSimilarity(text1: string, text2: string): number {
		// Simple similarity calculation based on common words
		const words1 = text1.toLowerCase().split(/\s+/);
		const words2 = text2.toLowerCase().split(/\s+/);

		const commonWords = words1.filter((word) => words2.includes(word));
		const totalWords = Math.max(words1.length, words2.length);

		return commonWords.length / totalWords;
	}

	private hashText(text: string): string {
		// Simple hash function for text
		let hash = 0;
		for (let i = 0; i < text.length; i++) {
			const char = text.charCodeAt(i);
			hash = (hash << 5) - hash + char;
			hash = hash & hash; // Convert to 32-bit integer
		}
		return Math.abs(hash).toString(36);
	}

	private convertToCSV(data: any[]): string {
		if (data.length === 0) return "";

		const headers = Object.keys(data[0]);
		const csvRows = [headers.join(",")];

		for (const item of data) {
			const values = headers.map((header) => {
				const value = item[header];
				return typeof value === "string"
					? `"${value.replace(/"/g, '""')}"`
					: value;
			});
			csvRows.push(values.join(","));
		}

		return csvRows.join("\n");
	}

	private convertToXLIFF(
		data: any[],
		sourceLocale: string,
		targetLocale: string,
	): string {
		// Basic XLIFF 2.1 format
		const xliffHeader = `<?xml version="1.0" encoding="UTF-8"?>
<xliff version="2.1" xmlns="urn:oasis:names:tc:xliff:document:2.1" srcLang="${sourceLocale}" trgLang="${targetLocale}">
  <file id="f1">
    <body>`;

		const xliffFooter = `    </body>
  </file>
</xliff>`;

		const units = data
			.map(
				(item, index) => `
      <unit id="u${index + 1}">
        <segment>
          <source>${this.escapeXML(item.sourceText)}</source>
          <target>${item.targetText ? this.escapeXML(item.targetText) : ""}</target>
        </segment>
      </unit>`,
			)
			.join("");

		return xliffHeader + units + xliffFooter;
	}

	private parseCSV(csvData: string): any[] {
		// Basic CSV parser
		const lines = csvData.split("\n");
		const headers = lines[0].split(",").map((h) => h.trim().replace(/"/g, ""));

		return lines.slice(1).map((line) => {
			const values = line.split(",").map((v) => v.trim().replace(/"/g, ""));
			const item: any = {};
			headers.forEach((header, index) => {
				item[header] = values[index];
			});
			return item;
		});
	}

	private parseXLIFF(xliffData: string): any[] {
		// Basic XLIFF parser (would need proper XML parsing in production)
		const units: any[] = [];
		const unitRegex =
			/<unit id="([^"]+)"[\s\S]*?<source>([\s\S]*?)<\/source>[\s\S]*?<target>([\s\S]*?)<\/target>/g;

		let match;
		while ((match = unitRegex.exec(xliffData)) !== null) {
			units.push({
				id: match[1],
				sourceText: this.unescapeXML(match[2]),
				targetText: this.unescapeXML(match[3]),
			});
		}

		return units;
	}

	private escapeXML(text: string): string {
		return text
			.replace(/&/g, "&amp;")
			.replace(/</g, "&lt;")
			.replace(/>/g, "&gt;")
			.replace(/"/g, "&quot;")
			.replace(/'/g, "&apos;");
	}

	private unescapeXML(text: string): string {
		return text
			.replace(/&amp;/g, "&")
			.replace(/&lt;/g, "<")
			.replace(/&gt;/g, ">")
			.replace(/&quot;/g, '"')
			.replace(/&apos;/g, "'");
	}

	private async logTranslationActivity(
		jobId: string,
		activity: string,
		data: any,
	): Promise<void> {
		try {
			await strapi.entityService.create("api::deployment-log.deployment-log", {
				data: {
					type: "translation_activity",
					job_id: jobId,
					activity,
					data,
					occurred_at: new Date(),
					status: "success",
				},
			});
		} catch (error) {
			strapi.log.error("Failed to log translation activity:", error);
		}
	}
}

// Export singleton instance
export const translationWorkflow = new TranslationWorkflow();
