/**
 * Translator Role Manager Service
 * Manages translator roles, permissions, and access controls
 */

export interface TranslatorRole {
	id: string;
	name: string;
	description: string;
	permissions: TranslatorPermission[];
	locales: string[];
	contentTypes: string[];
	maxAssignments: number;
	canReview: boolean;
	canApprove: boolean;
	isActive: boolean;
}

export interface TranslatorPermission {
	action: string;
	subject: string;
	conditions?: any;
	fields?: string[];
}

export interface TranslatorProfile {
	userId: number;
	roles: string[];
	specializations: string[];
	languages: {
		source: string[];
		target: string[];
	};
	qualifications: {
		certifications: string[];
		experience: number; // years
		domains: string[];
	};
	performance: {
		averageQualityScore: number;
		completedAssignments: number;
		onTimeDeliveryRate: number;
		averageWordsPerDay: number;
	};
	availability: {
		timezone: string;
		workingHours: {
			start: string;
			end: string;
		};
		workingDays: number[];
		isAvailable: boolean;
	};
	preferences: {
		preferredContentTypes: string[];
		maxWordsPerAssignment: number;
		notificationSettings: {
			email: boolean;
			inApp: boolean;
			deadlineReminders: boolean;
		};
	};
}

class TranslatorRoleManager {
	private defaultRoles: TranslatorRole[] = [
		{
			id: "translator",
			name: "Translator",
			description: "Can translate content and manage own assignments",
			permissions: [
				{
					action: "read",
					subject: "translation-assignment",
					conditions: { assigned_translator: { $eq: "$user.id" } },
				},
				{
					action: "update",
					subject: "translation-assignment",
					conditions: { assigned_translator: { $eq: "$user.id" } },
					fields: [
						"progress_percentage",
						"notes",
						"completed_fields",
						"status",
					],
				},
				{
					action: "read",
					subject: "translation-memory",
				},
				{
					action: "create",
					subject: "translation-memory",
				},
				{
					action: "read",
					subject: "paywall",
					conditions: { locale: { $in: "$user.locales" } },
				},
				{
					action: "update",
					subject: "paywall",
					conditions: { locale: { $in: "$user.locales" } },
					fields: [
						"title",
						"subtitle",
						"description_text",
						"cta_text",
						"cta_secondary_text",
					],
				},
			],
			locales: [],
			contentTypes: ["api::paywall.paywall"],
			maxAssignments: 10,
			canReview: false,
			canApprove: false,
			isActive: true,
		},
		{
			id: "senior-translator",
			name: "Senior Translator",
			description: "Experienced translator with review capabilities",
			permissions: [
				{
					action: "read",
					subject: "translation-assignment",
				},
				{
					action: "update",
					subject: "translation-assignment",
					conditions: {
						$or: [
							{ assigned_translator: { $eq: "$user.id" } },
							{ assigned_reviewer: { $eq: "$user.id" } },
						],
					},
				},
				{
					action: "read",
					subject: "translation-memory",
				},
				{
					action: "create",
					subject: "translation-memory",
				},
				{
					action: "update",
					subject: "translation-memory",
				},
				{
					action: "read",
					subject: "paywall",
				},
				{
					action: "update",
					subject: "paywall",
				},
			],
			locales: [],
			contentTypes: ["api::paywall.paywall", "api::ab-test.ab-test"],
			maxAssignments: 15,
			canReview: true,
			canApprove: false,
			isActive: true,
		},
		{
			id: "translation-manager",
			name: "Translation Manager",
			description:
				"Can manage all translation workflows and approve translations",
			permissions: [
				{
					action: "create",
					subject: "translation-assignment",
				},
				{
					action: "read",
					subject: "translation-assignment",
				},
				{
					action: "update",
					subject: "translation-assignment",
				},
				{
					action: "delete",
					subject: "translation-assignment",
				},
				{
					action: "create",
					subject: "translation-memory",
				},
				{
					action: "read",
					subject: "translation-memory",
				},
				{
					action: "update",
					subject: "translation-memory",
				},
				{
					action: "delete",
					subject: "translation-memory",
				},
				{
					action: "read",
					subject: "paywall",
				},
				{
					action: "update",
					subject: "paywall",
				},
				{
					action: "publish",
					subject: "paywall",
				},
			],
			locales: [],
			contentTypes: [
				"api::paywall.paywall",
				"api::ab-test.ab-test",
				"api::paywall-variation.paywall-variation",
			],
			maxAssignments: 50,
			canReview: true,
			canApprove: true,
			isActive: true,
		},
	];

	private translatorProfiles: Map<number, TranslatorProfile> = new Map();

	/**
	 * Initialize translator roles in Strapi
	 */
	async initializeRoles(): Promise<void> {
		try {
			for (const role of this.defaultRoles) {
				await this.createOrUpdateRole(role);
			}
			strapi.log.info("Translator roles initialized successfully");
		} catch (error) {
			strapi.log.error("Failed to initialize translator roles:", error);
			throw error;
		}
	}

	/**
	 * Create or update a translator role
	 */
	async createOrUpdateRole(roleData: TranslatorRole): Promise<void> {
		try {
			// Check if role exists
			const existingRole = await strapi.query("admin::role").findOne({
				where: { code: `translator-${roleData.id}` },
			});

			const rolePayload = {
				name: roleData.name,
				code: `translator-${roleData.id}`,
				description: roleData.description,
			};

			let role;
			if (existingRole) {
				role = await strapi.query("admin::role").update({
					where: { id: existingRole.id },
					data: rolePayload,
				});
			} else {
				role = await strapi.query("admin::role").create({
					data: rolePayload,
				});
			}

			// Create permissions for the role
			await this.createRolePermissions(role.id, roleData.permissions);

			strapi.log.info(`Role ${roleData.name} created/updated successfully`);
		} catch (error) {
			strapi.log.error(`Failed to create/update role ${roleData.name}:`, error);
			throw error;
		}
	}

	/**
	 * Create permissions for a role
	 */
	private async createRolePermissions(
		roleId: number,
		permissions: TranslatorPermission[],
	): Promise<void> {
		try {
			// Remove existing permissions
			await strapi.query("admin::permission").deleteMany({
				where: { role: roleId },
			});

			// Create new permissions
			for (const permission of permissions) {
				await strapi.query("admin::permission").create({
					data: {
						action: permission.action,
						subject: permission.subject,
						conditions: permission.conditions || {},
						properties: {
							fields: permission.fields || [],
						},
						role: roleId,
					},
				});
			}
		} catch (error) {
			strapi.log.error("Failed to create role permissions:", error);
			throw error;
		}
	}

	/**
	 * Assign translator role to user
	 */
	async assignTranslatorRole(
		userId: number,
		roleId: string,
		locales: string[],
	): Promise<void> {
		try {
			const role = await strapi.query("admin::role").findOne({
				where: { code: `translator-${roleId}` },
			});

			if (!role) {
				throw new Error(`Translator role ${roleId} not found`);
			}

			// Update user role
			await strapi.query("admin::user").update({
				where: { id: userId },
				data: {
					roles: [role.id],
				},
			});

			// Create or update translator profile
			const profile: TranslatorProfile = {
				userId,
				roles: [roleId],
				specializations: [],
				languages: {
					source: ["en"], // Default source language
					target: locales,
				},
				qualifications: {
					certifications: [],
					experience: 0,
					domains: [],
				},
				performance: {
					averageQualityScore: 0,
					completedAssignments: 0,
					onTimeDeliveryRate: 0,
					averageWordsPerDay: 0,
				},
				availability: {
					timezone: "UTC",
					workingHours: {
						start: "09:00",
						end: "17:00",
					},
					workingDays: [1, 2, 3, 4, 5], // Monday to Friday
					isAvailable: true,
				},
				preferences: {
					preferredContentTypes: ["api::paywall.paywall"],
					maxWordsPerAssignment: 1000,
					notificationSettings: {
						email: true,
						inApp: true,
						deadlineReminders: true,
					},
				},
			};

			this.translatorProfiles.set(userId, profile);

			strapi.log.info(
				`Assigned role ${roleId} to user ${userId} for locales: ${locales.join(", ")}`,
			);
		} catch (error) {
			strapi.log.error("Failed to assign translator role:", error);
			throw error;
		}
	}

	/**
	 * Get translator profile
	 */
	getTranslatorProfile(userId: number): TranslatorProfile | null {
		return this.translatorProfiles.get(userId) || null;
	}

	/**
	 * Update translator profile
	 */
	updateTranslatorProfile(
		userId: number,
		updates: Partial<TranslatorProfile>,
	): void {
		const existingProfile = this.translatorProfiles.get(userId);
		if (existingProfile) {
			this.translatorProfiles.set(userId, { ...existingProfile, ...updates });
		}
	}

	/**
	 * Get available translators for locale pair
	 */
	async getAvailableTranslators(
		sourceLocale: string,
		targetLocale: string,
	): Promise<TranslatorProfile[]> {
		const availableTranslators: TranslatorProfile[] = [];

		for (const [userId, profile] of this.translatorProfiles) {
			if (
				profile.availability.isAvailable &&
				profile.languages.source.includes(sourceLocale) &&
				profile.languages.target.includes(targetLocale)
			) {
				// Check current workload
				const currentAssignments = await this.getCurrentAssignmentCount(userId);
				const maxAssignments = this.getMaxAssignmentsForUser(userId);

				if (currentAssignments < maxAssignments) {
					availableTranslators.push(profile);
				}
			}
		}

		// Sort by performance and availability
		return availableTranslators.sort((a, b) => {
			const scoreA =
				a.performance.averageQualityScore * a.performance.onTimeDeliveryRate;
			const scoreB =
				b.performance.averageQualityScore * b.performance.onTimeDeliveryRate;
			return scoreB - scoreA;
		});
	}

	/**
	 * Get current assignment count for user
	 */
	private async getCurrentAssignmentCount(userId: number): Promise<number> {
		try {
			const count = await strapi
				.query("api::translation-assignment.translation-assignment")
				.count({
					where: {
						assigned_translator: userId,
						status: { $in: ["assigned", "in_progress"] },
					},
				});
			return count;
		} catch (error) {
			strapi.log.error("Failed to get current assignment count:", error);
			return 0;
		}
	}

	/**
	 * Get max assignments for user based on role
	 */
	private getMaxAssignmentsForUser(userId: number): number {
		const profile = this.translatorProfiles.get(userId);
		if (!profile) return 0;

		const role = this.defaultRoles.find((r) => profile.roles.includes(r.id));
		return role?.maxAssignments || 5;
	}

	/**
	 * Check if user can perform action on subject
	 */
	async canUserPerformAction(
		userId: number,
		action: string,
		subject: string,
		resource?: any,
	): Promise<boolean> {
		try {
			const user = await strapi.query("admin::user").findOne({
				where: { id: userId },
				populate: ["roles", "roles.permissions"],
			});

			if (!user || !user.roles) return false;

			for (const role of user.roles) {
				if (role.permissions) {
					for (const permission of role.permissions) {
						if (
							permission.action === action &&
							permission.subject === subject
						) {
							// Check conditions if they exist
							if (permission.conditions && resource) {
								return this.evaluateConditions(
									permission.conditions,
									resource,
									user,
								);
							}
							return true;
						}
					}
				}
			}

			return false;
		} catch (error) {
			strapi.log.error("Failed to check user permissions:", error);
			return false;
		}
	}

	/**
	 * Evaluate permission conditions
	 */
	private evaluateConditions(
		conditions: any,
		resource: any,
		user: any,
	): boolean {
		// Simple condition evaluation - in a real implementation, this would be more sophisticated
		for (const [key, value] of Object.entries(conditions)) {
			if (key === "assigned_translator" && value === "$user.id") {
				return resource.assigned_translator === user.id;
			}
			if (key === "assigned_reviewer" && value === "$user.id") {
				return resource.assigned_reviewer === user.id;
			}
			// Add more condition evaluations as needed
		}
		return true;
	}

	/**
	 * Get all translator roles
	 */
	getTranslatorRoles(): TranslatorRole[] {
		return this.defaultRoles;
	}

	/**
	 * Get role by ID
	 */
	getRole(roleId: string): TranslatorRole | null {
		return this.defaultRoles.find((role) => role.id === roleId) || null;
	}
}

export const translatorRoleManager = new TranslatorRoleManager();
