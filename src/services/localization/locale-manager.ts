/**
 * Locale Manager Service
 * Handles locale-specific content management workflows and language fallback mechanisms
 */

interface LocaleConfig {
	code: string;
	name: string;
	nativeName: string;
	isDefault: boolean;
	isActive: boolean;
	fallbackLocale?: string;
	rtl: boolean;
	dateFormat: string;
	numberFormat: string;
	currencyCode: string;
	region: string;
}

export interface TranslationStatus {
	locale: string;
	contentType: string | any;
	entityId: string | number;
	totalFields: number;
	translatedFields: number;
	completionPercentage: number;
	lastUpdated: Date;
	status: "not_started" | "in_progress" | "completed" | "needs_review";
}

export interface LocalizationMetrics {
	totalContent: number;
	translatedContent: number;
	localesSupported: number;
	averageCompletionRate: number;
	contentByStatus: Record<string, number>;
}

class LocaleManager {
	private supportedLocales: LocaleConfig[] = [
		{
			code: "en",
			name: "English",
			nativeName: "English",
			isDefault: true,
			isActive: true,
			rtl: false,
			dateFormat: "MM/DD/YYYY",
			numberFormat: "en-US",
			currencyCode: "USD",
			region: "US",
		},
		{
			code: "es",
			name: "Spanish",
			nativeName: "Español",
			isDefault: false,
			isActive: true,
			fallbackLocale: "en",
			rtl: false,
			dateFormat: "DD/MM/YYYY",
			numberFormat: "es-ES",
			currencyCode: "EUR",
			region: "ES",
		},
		{
			code: "fr",
			name: "French",
			nativeName: "Français",
			isDefault: false,
			isActive: true,
			fallbackLocale: "en",
			rtl: false,
			dateFormat: "DD/MM/YYYY",
			numberFormat: "fr-FR",
			currencyCode: "EUR",
			region: "FR",
		},
		{
			code: "de",
			name: "German",
			nativeName: "Deutsch",
			isDefault: false,
			isActive: true,
			fallbackLocale: "en",
			rtl: false,
			dateFormat: "DD.MM.YYYY",
			numberFormat: "de-DE",
			currencyCode: "EUR",
			region: "DE",
		},
		{
			code: "ja",
			name: "Japanese",
			nativeName: "日本語",
			isDefault: false,
			isActive: true,
			fallbackLocale: "en",
			rtl: false,
			dateFormat: "YYYY/MM/DD",
			numberFormat: "ja-JP",
			currencyCode: "JPY",
			region: "JP",
		},
		{
			code: "ko",
			name: "Korean",
			nativeName: "한국어",
			isDefault: false,
			isActive: true,
			fallbackLocale: "en",
			rtl: false,
			dateFormat: "YYYY.MM.DD",
			numberFormat: "ko-KR",
			currencyCode: "KRW",
			region: "KR",
		},
		{
			code: "zh",
			name: "Chinese (Simplified)",
			nativeName: "简体中文",
			isDefault: false,
			isActive: true,
			fallbackLocale: "en",
			rtl: false,
			dateFormat: "YYYY/MM/DD",
			numberFormat: "zh-CN",
			currencyCode: "CNY",
			region: "CN",
		},
	];

	/**
	 * Get all supported locales
	 */
	getSupportedLocales(): LocaleConfig[] {
		return this.supportedLocales.filter((locale) => locale.isActive);
	}

	/**
	 * Get locale configuration by code
	 */
	getLocaleConfig(localeCode: string): LocaleConfig | null {
		return (
			this.supportedLocales.find((locale) => locale.code === localeCode) || null
		);
	}

	/**
	 * Get default locale
	 */
	getDefaultLocale(): LocaleConfig {
		return (
			this.supportedLocales.find((locale) => locale.isDefault) ||
			this.supportedLocales[0]
		);
	}

	/**
	 * Get fallback locale for a given locale
	 */
	getFallbackLocale(localeCode: string): LocaleConfig | null {
		const locale = this.getLocaleConfig(localeCode);
		if (!locale || !locale.fallbackLocale) {
			return this.getDefaultLocale();
		}
		return this.getLocaleConfig(locale.fallbackLocale);
	}

	/**
	 * Get content with fallback mechanism
	 */
	async getLocalizedContent(
		contentType: string | any,
		entityId: number,
		locale: string,
		fields?: string[],
	): Promise<any> {
		try {
			// Try to get content in requested locale
			const content = await strapi.entityService.findOne(
				contentType,
				entityId,
				{
					locale,
					populate: "*",
				},
			);

			if (content) {
				// Check if all required fields are translated
				const missingFields = this.findMissingTranslations(content, fields);

				if (missingFields.length === 0) {
					return content;
				}

				// Fill missing fields with fallback locale
				const fallbackContent = await this.getFallbackContent(
					contentType,
					entityId,
					locale,
					missingFields,
				);

				return this.mergeContentWithFallback(
					content,
					fallbackContent,
					missingFields,
				);
			}

			// If no content in requested locale, try fallback
			const fallbackLocale = this.getFallbackLocale(locale);
			if (fallbackLocale && fallbackLocale.code !== locale) {
				return await this.getLocalizedContent(
					contentType,
					entityId,
					fallbackLocale.code,
					fields,
				);
			}

			return null;
		} catch (error) {
			strapi.log.error("Failed to get localized content:", error);
			throw error;
		}
	}

	/**
	 * Create localized content for all active locales
	 */
	async createLocalizedContent(
		contentType: string | any,
		data: any,
		sourceLocale: string = "en",
	): Promise<{ success: boolean; entities: any[]; errors: string[] }> {
		const entities: any[] = [];
		const errors: string[] = [];

		try {
			// Create content in source locale first
			const sourceEntity = await strapi.entityService.create(contentType, {
				data: { ...data, locale: sourceLocale },
			});
			entities.push(sourceEntity);

			// Create placeholder entries for other locales
			const otherLocales = this.getSupportedLocales().filter(
				(l) => l.code !== sourceLocale,
			);

			for (const locale of otherLocales) {
				try {
					const localizedData = await this.prepareLocalizedData(
						data,
						locale.code,
					);
					const localizedEntity = await strapi.entityService.create(
						contentType,
						{
							data: {
								...localizedData,
								locale: locale.code,
								localizations: [sourceEntity.id],
							},
						},
					);
					entities.push(localizedEntity);
				} catch (error) {
					errors.push(
						`Failed to create content for locale ${locale.code}: ${error.message}`,
					);
				}
			}

			return { success: errors.length === 0, entities, errors };
		} catch (error) {
			strapi.log.error("Failed to create localized content:", error);
			return {
				success: false,
				entities,
				errors: [error.message || "Unknown error during content creation"],
			};
		}
	}

	/**
	 * Get translation status for content
	 */
	async getTranslationStatus(
		contentType: string | any,
		entityId?: number,
	): Promise<TranslationStatus[]> {
		const statuses: TranslationStatus[] = [];

		try {
			const entities = entityId
				? [
					await strapi.entityService.findOne(contentType, entityId, {
						populate: "*",
					}),
				]
				: await strapi.entityService.findMany(contentType, { populate: "*" });

			const locales = this.getSupportedLocales();
			const entitiesArray = Array.isArray(entities) ? entities : [entities];

			for (const entity of entitiesArray) {
				if (!entity) continue;

				for (const locale of locales) {
					const localeEntity = await strapi.entityService.findOne(
						contentType,
						entity.id,
						{
							locale: locale.code,
							populate: "*",
						},
					);

					if (localeEntity) {
						const translationStatus = this.calculateTranslationStatus(
							localeEntity,
							contentType,
							locale.code,
						);
						statuses.push(translationStatus);
					} else {
						// No translation exists
						statuses.push({
							locale: locale.code,
							contentType,
							entityId: entity.id,
							totalFields: 0,
							translatedFields: 0,
							completionPercentage: 0,
							lastUpdated: new Date(),
							status: "not_started",
						});
					}
				}
			}

			return statuses;
		} catch (error) {
			strapi.log.error("Failed to get translation status:", error);
			return [];
		}
	}

	/**
	 * Get localization metrics
	 */
	async getLocalizationMetrics(): Promise<LocalizationMetrics> {
		try {
			const contentTypes = ["api::paywall.paywall", "api::ab-test.ab-test"];
			let totalContent = 0;
			let translatedContent = 0;
			const contentByStatus: Record<string, number> = {
				not_started: 0,
				in_progress: 0,
				completed: 0,
				needs_review: 0,
			};

			for (const contentType of contentTypes) {
				const statuses = await this.getTranslationStatus(contentType);

				totalContent += statuses.length;

				for (const status of statuses) {
					if (status.completionPercentage === 100) {
						translatedContent++;
					}
					contentByStatus[status.status]++;
				}
			}

			const localesSupported = this.getSupportedLocales().length;
			const averageCompletionRate =
				totalContent > 0 ? (translatedContent / totalContent) * 100 : 0;

			return {
				totalContent,
				translatedContent,
				localesSupported,
				averageCompletionRate,
				contentByStatus,
			};
		} catch (error) {
			strapi.log.error("Failed to get localization metrics:", error);
			return {
				totalContent: 0,
				translatedContent: 0,
				localesSupported: 0,
				averageCompletionRate: 0,
				contentByStatus: {
					not_started: 0,
					in_progress: 0,
					completed: 0,
					needs_review: 0,
				},
			};
		}
	}

	/**
	 * Bulk translate content using fallback or external service
	 */
	async bulkTranslateContent(
		contentType: string | any,
		sourceLocale: string,
		targetLocales: string[],
		entityIds?: number[],
	): Promise<{ success: boolean; translated: number; errors: string[] }> {
		const errors: string[] = [];
		let translated = 0;

		try {
			const entities = entityIds
				? await Promise.all(
					entityIds.map((id) =>
						strapi.entityService.findOne(contentType, id, {
							locale: sourceLocale,
							populate: "*",
						}),
					),
				)
				: await strapi.entityService.findMany(contentType, {
					locale: sourceLocale,
					populate: "*",
				});

			const entitiesArray = Array.isArray(entities) ? entities : [entities];

			for (const entity of entitiesArray) {
				if (!entity) continue;

				for (const targetLocale of targetLocales) {
					try {
						await this.translateEntity(
							entity,
							contentType,
							sourceLocale,
							targetLocale,
						);
						translated++;
					} catch (error) {
						errors.push(
							`Failed to translate entity ${entity.id} to ${targetLocale}: ${error.message}`,
						);
					}
				}
			}

			return { success: errors.length === 0, translated, errors };
		} catch (error) {
			strapi.log.error("Failed to bulk translate content:", error);
			return {
				success: false,
				translated,
				errors: [error.message || "Unknown error during bulk translation"],
			};
		}
	}

	// Private helper methods

	private findMissingTranslations(
		content: any,
		requiredFields?: string[],
	): string[] {
		const fieldsToCheck = requiredFields || [
			"title",
			"subtitle",
			"description",
			"cta_text",
		];
		const missingFields: string[] = [];

		for (const field of fieldsToCheck) {
			if (!content[field] || content[field].trim() === "") {
				missingFields.push(field);
			}
		}

		return missingFields;
	}

	private async getFallbackContent(
		contentType: string | any,
		entityId: number,
		locale: string,
		_missingFields: string[],
	): Promise<any> {
		const fallbackLocale = this.getFallbackLocale(locale);
		if (!fallbackLocale) return null;

		return await strapi.entityService.findOne(contentType, entityId, {
			locale: fallbackLocale.code,
			populate: "*",
		});
	}

	private mergeContentWithFallback(
		content: any,
		fallbackContent: any,
		missingFields: string[],
	): any {
		const merged = { ...content };

		if (fallbackContent) {
			for (const field of missingFields) {
				if (fallbackContent[field]) {
					merged[field] = fallbackContent[field];
					merged[`${field}_fallback`] = true; // Mark as fallback
				}
			}
		}

		return merged;
	}

	private async prepareLocalizedData(data: any, locale: string): Promise<any> {
		const localeConfig = this.getLocaleConfig(locale);
		const localizedData = { ...data };

		// Apply locale-specific formatting
		if (localeConfig) {
			// Format dates according to locale
			if (localizedData.start_date) {
				localizedData.start_date = this.formatDateForLocale(
					localizedData.start_date,
					localeConfig,
				);
			}

			// Apply RTL settings if needed
			if (localeConfig.rtl) {
				localizedData.text_direction = "rtl";
			}

			// Set placeholder text for translation
			const translatableFields = [
				"title",
				"subtitle",
				"description",
				"cta_text",
			];
			for (const field of translatableFields) {
				if (localizedData[field]) {
					localizedData[field] =
						`[${locale.toUpperCase()}] ${localizedData[field]}`;
				}
			}
		}

		return localizedData;
	}

	private calculateTranslationStatus(
		entity: any,
		contentType: string | any,
		locale: string,
	): TranslationStatus {
		const translatableFields = ["title", "subtitle", "description", "cta_text"];
		let translatedFields = 0;
		const totalFields = translatableFields.length;

		for (const field of translatableFields) {
			if (
				entity[field] &&
				entity[field].trim() !== "" &&
				!entity[field].startsWith(`[${locale.toUpperCase()}]`)
			) {
				translatedFields++;
			}
		}

		const completionPercentage = (translatedFields / totalFields) * 100;
		let status: TranslationStatus["status"] = "not_started";

		if (completionPercentage === 0) {
			status = "not_started";
		} else if (completionPercentage === 100) {
			status = "completed";
		} else {
			status = "in_progress";
		}

		return {
			locale,
			contentType,
			entityId: entity.id,
			totalFields,
			translatedFields,
			completionPercentage,
			lastUpdated: entity.updatedAt || new Date(),
			status,
		};
	}

	private async translateEntity(
		entity: any,
		contentType: string | any,
		_sourceLocale: string,
		targetLocale: string,
	): Promise<void> {
		// Check if translation already exists
		const existingTranslation = await strapi.entityService.findOne(
			contentType,
			entity.id,
			{
				// locale: targetLocale, // Not in query options
			},
		);

		const translatedData = await this.prepareLocalizedData(
			entity,
			targetLocale,
		);

		if (existingTranslation) {
			// Update existing translation
			await strapi.entityService.update(contentType, existingTranslation.id, {
				data: translatedData,
			});
		} else {
			// Create new translation
			await strapi.entityService.create(contentType, {
				data: {
					...translatedData,
					locale: targetLocale,
					localizations: [entity.id],
				},
			});
		}
	}

	private formatDateForLocale(
		date: Date | string,
		localeConfig: LocaleConfig,
	): string {
		const dateObj = typeof date === "string" ? new Date(date) : date;
		return dateObj.toLocaleDateString(localeConfig.numberFormat, {
			year: "numeric",
			month: "2-digit",
			day: "2-digit",
		});
	}

	/**
	 * Add new locale to the system
	 */
	async addLocale(
		localeConfig: Omit<LocaleConfig, "isActive">,
	): Promise<{ success: boolean; errors?: string[] }> {
		try {
			// Validate locale code
			if (this.supportedLocales.some((l) => l.code === localeConfig.code)) {
				return { success: false, errors: ["Locale already exists"] };
			}

			// Add to supported locales
			this.supportedLocales.push({ ...localeConfig, isActive: true });

			// Update Strapi i18n configuration
			await this.updateStrapiI18nConfig();

			strapi.log.info(
				`Added new locale: ${localeConfig.code} (${localeConfig.name})`,
			);
			return { success: true };
		} catch (error) {
			strapi.log.error("Failed to add locale:", error);
			return { success: false, errors: [error.message] };
		}
	}

	/**
	 * Remove locale from the system
	 */
	async removeLocale(
		localeCode: string,
	): Promise<{ success: boolean; errors?: string[] }> {
		try {
			const locale = this.getLocaleConfig(localeCode);
			if (!locale) {
				return { success: false, errors: ["Locale not found"] };
			}

			if (locale.isDefault) {
				return { success: false, errors: ["Cannot remove default locale"] };
			}

			// Mark as inactive
			locale.isActive = false;

			// Update Strapi i18n configuration
			await this.updateStrapiI18nConfig();

			strapi.log.info(`Removed locale: ${localeCode}`);
			return { success: true };
		} catch (error) {
			strapi.log.error("Failed to remove locale:", error);
			return { success: false, errors: [error.message] };
		}
	}

	private async updateStrapiI18nConfig(): Promise<void> {
		// This would update the Strapi i18n plugin configuration
		// In a real implementation, this might require restarting Strapi
		const activeLocales = this.getSupportedLocales().map((l) => l.code);

		// Log the configuration change
		strapi.log.info("Updated i18n configuration with locales:", activeLocales);
	}
}

// Export singleton instance
export const localeManager = new LocaleManager();
