/**
 * Regional Customization Service
 * Handles region-specific pricing, cultural adaptations, compliance, and scheduling
 */

import { localeConfigService } from "./locale-config";

export interface RegionalPricingConfig {
	region: string;
	currencyCode: string;
	currencySymbol: string;
	taxRate?: number;
	vatIncluded?: boolean;
	priceFormat: "before" | "after"; // Currency symbol position
	decimalPlaces: number;
	thousandsSeparator: string;
	decimalSeparator: string;
}

export interface CulturalAdaptation {
	region: string;
	colorScheme: {
		primary: string;
		secondary: string;
		accent: string;
		background: string;
		text: string;
	};
	typography: {
		fontFamily: string;
		fontSize: {
			small: string;
			medium: string;
			large: string;
		};
		lineHeight: number;
	};
	layout: {
		direction: "ltr" | "rtl";
		spacing: "compact" | "normal" | "spacious";
		borderRadius: string;
	};
	imagery: {
		preferredStyles: string[];
		culturalConsiderations: string[];
	};
}

export interface RegionalCompliance {
	region: string;
	gdprRequired: boolean;
	ccpaRequired: boolean;
	coppaRequired: boolean;
	customLegalText?: string;
	requiredDisclosures: string[];
	cookieConsentRequired: boolean;
	dataRetentionPeriod: number; // days
	rightToDeleteEnabled: boolean;
}

export interface TimezoneConfig {
	region: string;
	timezone: string;
	dstObserved: boolean;
	businessHours: {
		start: string; // HH:mm format
		end: string;
		days: number[]; // 0-6, Sunday = 0
	};
	preferredDeploymentTimes: string[]; // HH:mm format
}

export interface RegionalAnalytics {
	region: string;
	conversionRate: number;
	averageRevenuePerUser: number;
	churnRate: number;
	lifetimeValue: number;
	topPerformingVariations: string[];
	culturalInsights: {
		preferredColors: string[];
		preferredMessaging: string[];
		optimalTiming: string[];
	};
}

export interface RegionalABTestConfig {
	region: string;
	minSampleSize: number;
	confidenceLevel: number;
	testDuration: number; // days
	culturalVariations: {
		colorTests: boolean;
		messagingTests: boolean;
		layoutTests: boolean;
		pricingTests: boolean;
	};
	excludedFeatures: string[];
}

class RegionalCustomizationService {
	private regionalPricingConfigs: Map<string, RegionalPricingConfig> =
		new Map();
	private culturalAdaptations: Map<string, CulturalAdaptation> = new Map();
	private complianceConfigs: Map<string, RegionalCompliance> = new Map();
	private timezoneConfigs: Map<string, TimezoneConfig> = new Map();
	private analyticsData: Map<string, RegionalAnalytics> = new Map();
	private abTestConfigs: Map<string, RegionalABTestConfig> = new Map();

	constructor() {
		this.initializeDefaultConfigurations();
	}

	/**
	 * Initialize default regional configurations
	 */
	private initializeDefaultConfigurations(): void {
		// Initialize pricing configs
		this.regionalPricingConfigs.set("US", {
			region: "US",
			currencyCode: "USD",
			currencySymbol: "$",
			taxRate: 0.08,
			vatIncluded: false,
			priceFormat: "before",
			decimalPlaces: 2,
			thousandsSeparator: ",",
			decimalSeparator: ".",
		});

		this.regionalPricingConfigs.set("EU", {
			region: "EU",
			currencyCode: "EUR",
			currencySymbol: "€",
			taxRate: 0.2,
			vatIncluded: true,
			priceFormat: "after",
			decimalPlaces: 2,
			thousandsSeparator: ".",
			decimalSeparator: ",",
		});

		this.regionalPricingConfigs.set("JP", {
			region: "JP",
			currencyCode: "JPY",
			currencySymbol: "¥",
			priceFormat: "before",
			decimalPlaces: 0,
			thousandsSeparator: ",",
			decimalSeparator: ".",
		});

		// Initialize cultural adaptations
		this.culturalAdaptations.set("US", {
			region: "US",
			colorScheme: {
				primary: "#007AFF",
				secondary: "#34C759",
				accent: "#FF9500",
				background: "#FFFFFF",
				text: "#000000",
			},
			typography: {
				fontFamily: "system-ui, -apple-system, sans-serif",
				fontSize: { small: "14px", medium: "16px", large: "20px" },
				lineHeight: 1.5,
			},
			layout: {
				direction: "ltr",
				spacing: "normal",
				borderRadius: "8px",
			},
			imagery: {
				preferredStyles: ["modern", "clean", "professional"],
				culturalConsiderations: ["diverse-representation", "accessibility"],
			},
		});

		this.culturalAdaptations.set("JP", {
			region: "JP",
			colorScheme: {
				primary: "#BC002D",
				secondary: "#FFFFFF",
				accent: "#000000",
				background: "#F8F8F8",
				text: "#333333",
			},
			typography: {
				fontFamily: 'system-ui, "Hiragino Sans", sans-serif',
				fontSize: { small: "12px", medium: "14px", large: "18px" },
				lineHeight: 1.7,
			},
			layout: {
				direction: "ltr",
				spacing: "compact",
				borderRadius: "4px",
			},
			imagery: {
				preferredStyles: ["minimalist", "elegant", "traditional"],
				culturalConsiderations: ["respect-hierarchy", "group-harmony"],
			},
		});

		// Initialize compliance configs
		this.complianceConfigs.set("EU", {
			region: "EU",
			gdprRequired: true,
			ccpaRequired: false,
			coppaRequired: false,
			requiredDisclosures: [
				"data-processing",
				"cookie-usage",
				"third-party-sharing",
			],
			cookieConsentRequired: true,
			dataRetentionPeriod: 730, // 2 years
			rightToDeleteEnabled: true,
		});

		this.complianceConfigs.set("US", {
			region: "US",
			gdprRequired: false,
			ccpaRequired: true,
			coppaRequired: true,
			requiredDisclosures: ["privacy-policy", "terms-of-service"],
			cookieConsentRequired: false,
			dataRetentionPeriod: 1095, // 3 years
			rightToDeleteEnabled: true,
		});

		// Initialize timezone configs
		this.timezoneConfigs.set("US", {
			region: "US",
			timezone: "America/New_York",
			dstObserved: true,
			businessHours: {
				start: "09:00",
				end: "17:00",
				days: [1, 2, 3, 4, 5], // Monday to Friday
			},
			preferredDeploymentTimes: ["02:00", "14:00"],
		});

		this.timezoneConfigs.set("JP", {
			region: "JP",
			timezone: "Asia/Tokyo",
			dstObserved: false,
			businessHours: {
				start: "09:00",
				end: "18:00",
				days: [1, 2, 3, 4, 5],
			},
			preferredDeploymentTimes: ["01:00", "13:00"],
		});

		// Initialize A/B test configs
		this.abTestConfigs.set("US", {
			region: "US",
			minSampleSize: 1000,
			confidenceLevel: 0.95,
			testDuration: 14,
			culturalVariations: {
				colorTests: true,
				messagingTests: true,
				layoutTests: true,
				pricingTests: true,
			},
			excludedFeatures: [],
		});

		this.abTestConfigs.set("JP", {
			region: "JP",
			minSampleSize: 500,
			confidenceLevel: 0.9,
			testDuration: 21,
			culturalVariations: {
				colorTests: true,
				messagingTests: true,
				layoutTests: false, // More conservative with layout changes
				pricingTests: false, // Price sensitivity
			},
			excludedFeatures: ["aggressive-messaging"],
		});
	}

	/**
	 * Format price according to regional preferences
	 */
	formatRegionalPrice(amount: number, region: string): string {
		const config = this.regionalPricingConfigs.get(region);
		if (!config) {
			return amount.toString();
		}

		// Format number with regional separators
		const formattedNumber = this.formatNumber(amount, config);

		// Apply currency symbol position
		if (config.priceFormat === "before") {
			return `${config.currencySymbol}${formattedNumber}`;
		} else {
			return `${formattedNumber} ${config.currencySymbol}`;
		}
	}

	/**
	 * Format number with regional separators
	 */
	private formatNumber(amount: number, config: RegionalPricingConfig): string {
		const fixed = amount.toFixed(config.decimalPlaces);
		const [integer, decimal] = fixed.split(".");

		// Add thousands separators
		const formattedInteger = integer.replace(
			/\B(?=(\d{3})+(?!\d))/g,
			config.thousandsSeparator,
		);

		if (config.decimalPlaces > 0 && decimal) {
			return `${formattedInteger}${config.decimalSeparator}${decimal}`;
		}

		return formattedInteger;
	}

	/**
	 * Get cultural adaptation for region
	 */
	getCulturalAdaptation(region: string): CulturalAdaptation | null {
		return this.culturalAdaptations.get(region) || null;
	}

	/**
	 * Get compliance requirements for region
	 */
	getComplianceRequirements(region: string): RegionalCompliance | null {
		return this.complianceConfigs.get(region) || null;
	}

	/**
	 * Get timezone configuration for region
	 */
	getTimezoneConfig(region: string): TimezoneConfig | null {
		return this.timezoneConfigs.get(region) || null;
	}

	/**
	 * Check if current time is within business hours for region
	 */
	isBusinessHours(region: string, date: Date = new Date()): boolean {
		const config = this.getTimezoneConfig(region);
		if (!config) return true;

		const day = date.getDay();
		if (!config.businessHours.days.includes(day)) {
			return false;
		}

		const currentTime = date.toLocaleTimeString("en-US", {
			timeZone: config.timezone,
			hour12: false,
			hour: "2-digit",
			minute: "2-digit",
		});

		return (
			currentTime >= config.businessHours.start &&
			currentTime <= config.businessHours.end
		);
	}

	/**
	 * Get optimal deployment time for region
	 */
	getOptimalDeploymentTime(region: string, date: Date = new Date()): Date {
		const config = this.getTimezoneConfig(region);
		if (!config || config.preferredDeploymentTimes.length === 0) {
			return date;
		}

		// Find next preferred deployment time
		const currentTime = date.toLocaleTimeString("en-US", {
			timeZone: config.timezone,
			hour12: false,
			hour: "2-digit",
			minute: "2-digit",
		});

		for (const preferredTime of config.preferredDeploymentTimes) {
			if (preferredTime > currentTime) {
				const [hours, minutes] = preferredTime.split(":").map(Number);
				const deploymentDate = new Date(date);
				deploymentDate.setHours(hours, minutes, 0, 0);
				return deploymentDate;
			}
		}

		// If no time today, use first preferred time tomorrow
		const [hours, minutes] = config.preferredDeploymentTimes[0]
			.split(":")
			.map(Number);
		const deploymentDate = new Date(date);
		deploymentDate.setDate(deploymentDate.getDate() + 1);
		deploymentDate.setHours(hours, minutes, 0, 0);
		return deploymentDate;
	}

	/**
	 * Get A/B test configuration for region
	 */
	getRegionalABTestConfig(region: string): RegionalABTestConfig | null {
		return this.abTestConfigs.get(region) || null;
	}

	/**
	 * Update regional analytics data
	 */
	updateRegionalAnalytics(
		region: string,
		analytics: Partial<RegionalAnalytics>,
	): void {
		const existing = this.analyticsData.get(region) || {
			region,
			conversionRate: 0,
			averageRevenuePerUser: 0,
			churnRate: 0,
			lifetimeValue: 0,
			topPerformingVariations: [],
			culturalInsights: {
				preferredColors: [],
				preferredMessaging: [],
				optimalTiming: [],
			},
		};

		this.analyticsData.set(region, { ...existing, ...analytics });
	}

	/**
	 * Get regional analytics data
	 */
	getRegionalAnalytics(region: string): RegionalAnalytics | null {
		return this.analyticsData.get(region) || null;
	}

	/**
	 * Get all supported regions
	 */
	getSupportedRegions(): string[] {
		const regions = new Set<string>();

		// Collect regions from all configuration maps
		this.regionalPricingConfigs.forEach((_, region) => regions.add(region));
		this.culturalAdaptations.forEach((_, region) => regions.add(region));
		this.complianceConfigs.forEach((_, region) => regions.add(region));
		this.timezoneConfigs.forEach((_, region) => regions.add(region));

		return Array.from(regions);
	}

	/**
	 * Validate region code
	 */
	isValidRegion(region: string): boolean {
		return this.getSupportedRegions().includes(region);
	}

	/**
	 * Get region from locale code
	 */
	getRegionFromLocale(locale: string): string | null {
		const localeConfig = localeConfigService.getLocaleConfig(locale);
		return localeConfig?.region || null;
	}

	/**
	 * Apply cultural adaptations to theme
	 */
	applyCulturalAdaptations(baseTheme: any, region: string): any {
		const adaptation = this.getCulturalAdaptation(region);
		if (!adaptation) return baseTheme;

		return {
			...baseTheme,
			colors: {
				...baseTheme.colors,
				primary: adaptation.colorScheme.primary,
				secondary: adaptation.colorScheme.secondary,
				accent: adaptation.colorScheme.accent,
				background: adaptation.colorScheme.background,
				text: adaptation.colorScheme.text,
			},
			typography: {
				...baseTheme.typography,
				fontFamily: adaptation.typography.fontFamily,
				fontSize: adaptation.typography.fontSize,
				lineHeight: adaptation.typography.lineHeight,
			},
			layout: {
				...baseTheme.layout,
				direction: adaptation.layout.direction,
				spacing: adaptation.layout.spacing,
				borderRadius: adaptation.layout.borderRadius,
			},
		};
	}
}

export const regionalCustomizationService = new RegionalCustomizationService();
