/**
 * Translation Status Service
 * Tracks translation completion and provides monitoring capabilities
 */

import { localeConfigService } from "./locale-config";

export interface TranslationStatus {
	locale: string;
	contentType: any;
	entityId: string | number;
	totalFields: number;
	translatedFields: number;
	completionPercentage: number;
	lastUpdated: Date;
	status: "not_started" | "in_progress" | "completed" | "needs_review";
	missingFields: string[];
}

export interface BulkTranslationOperation {
	id: string;
	contentType: any;
	sourceLocale: string;
	targetLocales: string[];
	entityIds: (string | number)[];
	status: "pending" | "in_progress" | "completed" | "failed";
	progress: number;
	startedAt: Date;
	completedAt?: Date;
	errors: string[];
}

class TranslationStatusService {
	/**
	 * Get translation status for a specific entity
	 */
	async getTranslationStatus(
		contentType: any,
		entityId: string | number,
		locale: string,
	): Promise<TranslationStatus> {
		try {
			// Get the entity in the specified locale
			const entity = await strapi.entityService.findOne(contentType, entityId, {
				locale,
				populate: "*",
			});

			if (!entity) {
				return this.createEmptyTranslationStatus(contentType, entityId, locale);
			}

			// Get content type schema to identify localizable fields
			const schema = strapi.getModel(contentType);
			const localizableFields = this.getLocalizableFields(schema);

			// Count translated fields
			const translatedFields = this.countTranslatedFields(
				entity,
				localizableFields,
			);
			const totalFields = localizableFields.length;
			const completionPercentage =
				totalFields > 0 ? (translatedFields / totalFields) * 100 : 100;

			// Determine status
			let status: TranslationStatus["status"] = "not_started";
			if (completionPercentage === 100) {
				status = "completed";
			} else if (completionPercentage > 0) {
				status = "in_progress";
			}

			// Get missing fields
			const missingFields = this.getMissingFields(entity, localizableFields);

			return {
				locale,
				contentType,
				entityId,
				totalFields,
				translatedFields,
				completionPercentage,
				lastUpdated: entity.updatedAt || new Date(),
				status,
				missingFields,
			};
		} catch (error) {
			strapi.log.error("Failed to get translation status:", error);
			return this.createEmptyTranslationStatus(contentType, entityId, locale);
		}
	}

	/**
	 * Get translation status for all locales of an entity
	 */
	async getAllTranslationStatuses(
		contentType: any,
		entityId: string | number,
	): Promise<TranslationStatus[]> {
		const activeLocales = localeConfigService.getActiveLocales();
		const statuses: TranslationStatus[] = [];

		for (const locale of activeLocales) {
			const status = await this.getTranslationStatus(
				contentType,
				entityId,
				locale.code,
			);
			statuses.push(status);
		}

		return statuses;
	}

	/**
	 * Get translation completion overview for a content type
	 */
	async getContentTypeTranslationOverview(contentType: any): Promise<{
		totalEntities: number;
		localeCompletionRates: { [locale: string]: number };
		overallCompletionRate: number;
	}> {
		try {
			// Get all entities for this content type
			const entities = await strapi.entityService.findMany(contentType, {
				populate: "*",
			});

			const entitiesArray = Array.isArray(entities) ? entities : [entities];
			const activeLocales = localeConfigService.getActiveLocales();
			const localeCompletionRates: { [locale: string]: number } = {};
			let totalCompletionSum = 0;

			for (const locale of activeLocales) {
				let localeCompletionSum = 0;

				for (const entity of entitiesArray) {
					const status = await this.getTranslationStatus(
						contentType,
						entity.id,
						locale.code,
					);
					localeCompletionSum += status.completionPercentage;
				}

				const localeAverage =
					entitiesArray.length > 0 ? localeCompletionSum / entitiesArray.length : 0;
				localeCompletionRates[locale.code] = localeAverage;
				totalCompletionSum += localeAverage;
			}

			const overallCompletionRate =
				activeLocales.length > 0
					? totalCompletionSum / activeLocales.length
					: 0;

			return {
				totalEntities: entities.length,
				localeCompletionRates,
				overallCompletionRate,
			};
		} catch (error) {
			strapi.log.error(
				"Failed to get content type translation overview:",
				error,
			);
			return {
				totalEntities: 0,
				localeCompletionRates: {},
				overallCompletionRate: 0,
			};
		}
	}

	/**
	 * Start bulk translation operation
	 */
	async startBulkTranslation(
		contentType: any,
		sourceLocale: string,
		targetLocales: string[],
		entityIds: (string | number)[],
	): Promise<BulkTranslationOperation> {
		const operation: BulkTranslationOperation = {
			id: `bulk_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
			contentType,
			sourceLocale,
			targetLocales,
			entityIds,
			status: "pending",
			progress: 0,
			startedAt: new Date(),
			errors: [],
		};

		// Start the operation asynchronously
		this.processBulkTranslation(operation);

		return operation;
	}

	/**
	 * Process bulk translation operation
	 */
	private async processBulkTranslation(
		operation: BulkTranslationOperation,
	): Promise<void> {
		try {
			operation.status = "in_progress";
			const totalOperations =
				operation.entityIds.length * operation.targetLocales.length;
			let completedOperations = 0;

			for (const entityId of operation.entityIds) {
				for (const targetLocale of operation.targetLocales) {
					try {
						await this.translateEntity(
							operation.contentType,
							entityId,
							operation.sourceLocale,
							targetLocale,
						);
						completedOperations++;
						operation.progress = (completedOperations / totalOperations) * 100;
					} catch (error) {
						operation.errors.push(
							`Failed to translate entity ${entityId} to ${targetLocale}: ${error.message}`,
						);
					}
				}
			}

			operation.status = operation.errors.length > 0 ? "failed" : "completed";
			operation.completedAt = new Date();

			strapi.log.info(
				`Bulk translation operation ${operation.id} completed with ${operation.errors.length} errors`,
			);
		} catch (error) {
			operation.status = "failed";
			operation.errors.push(`Bulk operation failed: ${error.message}`);
			strapi.log.error("Bulk translation operation failed:", error);
		}
	}

	/**
	 * Translate a single entity
	 */
	private async translateEntity(
		contentType: any,
		entityId: string | number,
		sourceLocale: string,
		targetLocale: string,
	): Promise<void> {
		// Get source entity
		const sourceEntity = await strapi.entityService.findOne(
			contentType,
			entityId,
			{
				locale: sourceLocale,
				populate: "*",
			},
		);

		if (!sourceEntity) {
			throw new Error(
				`Source entity not found: ${entityId} in locale ${sourceLocale}`,
			);
		}

		// Check if target entity already exists
		const existingTarget = await strapi.entityService.findOne(
			contentType,
			entityId,
			{
				locale: targetLocale,
				populate: "*",
			},
		);

		const schema = strapi.getModel(contentType);
		const localizableFields = this.getLocalizableFields(schema);

		// Prepare translation data (placeholder for now)
		const translationData: any = {};

		for (const field of localizableFields) {
			if (sourceEntity[field]) {
				// For now, we'll just mark fields as needing translation
				// In a real implementation, this would integrate with translation services
				translationData[field] =
					`[${targetLocale.toUpperCase()}] ${sourceEntity[field]}`;
			}
		}

		if (existingTarget) {
			// Update existing translation
			await strapi.entityService.update(contentType, entityId, {
				data: translationData,
				locale: targetLocale,
			});
		} else {
			// Create new translation
			await strapi.entityService.create(contentType, {
				data: {
					...translationData,
					locale: targetLocale,
				},
			});
		}
	}

	/**
	 * Get localizable fields from content type schema
	 */
	private getLocalizableFields(schema: any): string[] {
		const localizableFields: string[] = [];

		for (const [fieldName, fieldConfig] of Object.entries(schema.attributes)) {
			const config = fieldConfig as any;
			if (config.pluginOptions?.i18n?.localized === true) {
				localizableFields.push(fieldName);
			}
		}

		return localizableFields;
	}

	/**
	 * Count translated fields in an entity
	 */
	private countTranslatedFields(
		entity: any,
		localizableFields: string[],
	): number {
		let count = 0;

		for (const field of localizableFields) {
			if (entity[field] && entity[field].toString().trim() !== "") {
				count++;
			}
		}

		return count;
	}

	/**
	 * Get missing fields for an entity
	 */
	private getMissingFields(entity: any, localizableFields: string[]): string[] {
		const missingFields: string[] = [];

		for (const field of localizableFields) {
			if (!entity[field] || entity[field].toString().trim() === "") {
				missingFields.push(field);
			}
		}

		return missingFields;
	}

	/**
	 * Create empty translation status
	 */
	private createEmptyTranslationStatus(
		contentType: any,
		entityId: string | number,
		locale: string,
	): TranslationStatus {
		return {
			locale,
			contentType,
			entityId,
			totalFields: 0,
			translatedFields: 0,
			completionPercentage: 0,
			lastUpdated: new Date(),
			status: "not_started",
			missingFields: [],
		};
	}
}

export const translationStatusService = new TranslationStatusService();
