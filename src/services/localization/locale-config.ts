/**
 * Locale Configuration Service
 * Manages locale-specific configurations and cultural adaptations
 */

export interface LocaleConfig {
	code: string;
	name: string;
	nativeName: string;
	isDefault: boolean;
	isActive: boolean;
	fallbackLocale?: string;
	rtl: boolean;
	dateFormat: string;
	numberFormat: string;
	currencyCode: string;
	region: string;
	culturalAdaptations?: {
		colorPreferences?: string[];
		layoutDirection?: "ltr" | "rtl";
		imageStyles?: string[];
		fontFamily?: string;
	};
}

export interface LocaleThemeCustomization {
	locale: string;
	primaryColor?: string;
	backgroundColor?: string;
	textColor?: string;
	fontFamily?: string;
	layoutDirection: "ltr" | "rtl";
	customStyles?: string;
}

class LocaleConfigService {
	private defaultLocales: LocaleConfig[] = [
		{
			code: "en",
			name: "English",
			nativeName: "English",
			isDefault: true,
			isActive: true,
			rtl: false,
			dateFormat: "MM/DD/YYYY",
			numberFormat: "en-US",
			currencyCode: "USD",
			region: "US",
			culturalAdaptations: {
				colorPreferences: ["#007AFF", "#34C759", "#FF9500"],
				layoutDirection: "ltr",
				fontFamily: "system-ui",
			},
		},
		{
			code: "es",
			name: "Spanish",
			nativeName: "Español",
			isDefault: false,
			isActive: true,
			fallbackLocale: "en",
			rtl: false,
			dateFormat: "DD/MM/YYYY",
			numberFormat: "es-ES",
			currencyCode: "EUR",
			region: "ES",
			culturalAdaptations: {
				colorPreferences: ["#FF6B35", "#F7931E", "#FFD23F"],
				layoutDirection: "ltr",
				fontFamily: "system-ui",
			},
		},
		{
			code: "fr",
			name: "French",
			nativeName: "Français",
			isDefault: false,
			isActive: true,
			fallbackLocale: "en",
			rtl: false,
			dateFormat: "DD/MM/YYYY",
			numberFormat: "fr-FR",
			currencyCode: "EUR",
			region: "FR",
			culturalAdaptations: {
				colorPreferences: ["#0055A4", "#EF4135", "#FFFFFF"],
				layoutDirection: "ltr",
				fontFamily: "system-ui",
			},
		},
		{
			code: "de",
			name: "German",
			nativeName: "Deutsch",
			isDefault: false,
			isActive: true,
			fallbackLocale: "en",
			rtl: false,
			dateFormat: "DD.MM.YYYY",
			numberFormat: "de-DE",
			currencyCode: "EUR",
			region: "DE",
			culturalAdaptations: {
				colorPreferences: ["#000000", "#DD0000", "#FFCE00"],
				layoutDirection: "ltr",
				fontFamily: "system-ui",
			},
		},
		{
			code: "ja",
			name: "Japanese",
			nativeName: "日本語",
			isDefault: false,
			isActive: true,
			fallbackLocale: "en",
			rtl: false,
			dateFormat: "YYYY/MM/DD",
			numberFormat: "ja-JP",
			currencyCode: "JPY",
			region: "JP",
			culturalAdaptations: {
				colorPreferences: ["#BC002D", "#FFFFFF", "#000000"],
				layoutDirection: "ltr",
				fontFamily: "system-ui",
			},
		},
		{
			code: "ko",
			name: "Korean",
			nativeName: "한국어",
			isDefault: false,
			isActive: true,
			fallbackLocale: "en",
			rtl: false,
			dateFormat: "YYYY.MM.DD",
			numberFormat: "ko-KR",
			currencyCode: "KRW",
			region: "KR",
			culturalAdaptations: {
				colorPreferences: ["#CD2E3A", "#0047A0", "#FFFFFF"],
				layoutDirection: "ltr",
				fontFamily: "system-ui",
			},
		},
		{
			code: "zh",
			name: "Chinese (Simplified)",
			nativeName: "简体中文",
			isDefault: false,
			isActive: true,
			fallbackLocale: "en",
			rtl: false,
			dateFormat: "YYYY-MM-DD",
			numberFormat: "zh-CN",
			currencyCode: "CNY",
			region: "CN",
			culturalAdaptations: {
				colorPreferences: ["#DE2910", "#FFDE00", "#000000"],
				layoutDirection: "ltr",
				fontFamily: "system-ui",
			},
		},
	];

	/**
	 * Get all available locale configurations
	 */
	getAllLocales(): LocaleConfig[] {
		return this.defaultLocales;
	}

	/**
	 * Get active locale configurations
	 */
	getActiveLocales(): LocaleConfig[] {
		return this.defaultLocales.filter((locale) => locale.isActive);
	}

	/**
	 * Get locale configuration by code
	 */
	getLocaleConfig(code: string): LocaleConfig | null {
		return this.defaultLocales.find((locale) => locale.code === code) || null;
	}

	/**
	 * Get default locale
	 */
	getDefaultLocale(): LocaleConfig {
		return (
			this.defaultLocales.find((locale) => locale.isDefault) ||
			this.defaultLocales[0]
		);
	}

	/**
	 * Get locale-specific theme customizations
	 */
	getLocaleThemeCustomization(locale: string): LocaleThemeCustomization {
		const localeConfig = this.getLocaleConfig(locale);
		if (!localeConfig || !localeConfig.culturalAdaptations) {
			return {
				locale,
				layoutDirection: "ltr",
			};
		}

		const adaptations = localeConfig.culturalAdaptations;
		return {
			locale,
			primaryColor: adaptations.colorPreferences?.[0],
			fontFamily: adaptations.fontFamily,
			layoutDirection: adaptations.layoutDirection || "ltr",
		};
	}

	/**
	 * Format currency for locale
	 */
	formatCurrency(amount: number, locale: string): string {
		const localeConfig = this.getLocaleConfig(locale);
		if (!localeConfig) return amount.toString();

		return new Intl.NumberFormat(localeConfig.numberFormat, {
			style: "currency",
			currency: localeConfig.currencyCode,
		}).format(amount);
	}

	/**
	 * Format date for locale
	 */
	formatDate(date: Date, locale: string): string {
		const localeConfig = this.getLocaleConfig(locale);
		if (!localeConfig) return date.toISOString();

		return new Intl.DateTimeFormat(localeConfig.numberFormat).format(date);
	}

	/**
	 * Get language fallback chain
	 */
	getFallbackChain(locale: string): string[] {
		const chain: string[] = [locale];
		let currentLocale = locale;

		while (currentLocale) {
			const config = this.getLocaleConfig(currentLocale);
			if (config?.fallbackLocale && !chain.includes(config.fallbackLocale)) {
				chain.push(config.fallbackLocale);
				currentLocale = config.fallbackLocale;
			} else {
				break;
			}
		}

		// Always include default locale as final fallback
		const defaultLocale = this.getDefaultLocale();
		if (!chain.includes(defaultLocale.code)) {
			chain.push(defaultLocale.code);
		}

		return chain;
	}

	/**
	 * Validate locale code
	 */
	isValidLocale(locale: string): boolean {
		return this.defaultLocales.some((l) => l.code === locale && l.isActive);
	}

	/**
	 * Get supported locale codes
	 */
	getSupportedLocaleCodes(): string[] {
		return this.getActiveLocales().map((l) => l.code);
	}
}

export const localeConfigService = new LocaleConfigService();
