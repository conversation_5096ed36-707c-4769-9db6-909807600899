/**
 * Adapty Analytics Integration Service
 * Handles analytics data synchronization and reporting between Strapi and Adapty
 */

import { factories } from "@strapi/strapi";
import { AdaptyApiClient } from "../adapty/client";

const adaptyClient = new AdaptyApiClient({
	apiKey: process.env.ADAPTY_API_KEY || "",
});

export default factories.createCoreService(
	"api::paywall-metrics.paywall-metrics", // Using existing content type instead of analytics
	({ strapi }) => ({
		/**
		 * Get comprehensive analytics data from Adapty
		 */
		async getAdaptyAnalytics(params: {
			start_date: string;
			end_date: string;
			metrics?: string[];
			cohort_period?: string;
		}) {
			try {
				const analytics = await adaptyClient.getAnalytics({
					start_date: params.start_date,
					end_date: params.end_date,
					// metrics: params.metrics || ['revenue', 'subscriptions', 'trials'], // Not in AnalyticsRequest
					// group_by: ['country'], // Not in AnalyticsRequest
					// cohort_period: params.cohort_period || 'monthly' // Not in AnalyticsRequest
				});

				const overall = (analytics as any).overall;
				const countryData = (analytics as any).by_country;
				const productData = (analytics as any).by_product;
				const variationData = (analytics as any).by_variation;

				return {
					overall: (this as any).transformMetrics(overall),
					by_country: (this as any).transformMetrics(countryData as any),
					by_product: (this as any).transformMetrics(productData as any),
					by_variation: (this as any).transformMetrics(variationData as any),
					cohort_analysis: await (this as any).getCohortAnalysis(params),
					anomaly_detection: await (this as any).detectAnomalies(params),
				};
			} catch (error: any) {
				strapi.log.error("Failed to get Adapty analytics:", error);
				throw error;
			}
		},

		/**
		 * Transform metrics data for consistent format
		 */
		transformMetrics(metrics: any) {
			if (!metrics) return null;

			const subscriptions = (metrics as any).subscriptions || 0;
			const trials = (metrics as any).trials || 0;

			return {
				revenue: metrics.revenue || 0,
				subscriptions: subscriptions,
				trials: trials,
				conversion_rate: metrics.conversion_rate || 0,
				churn_rate: metrics.churn_rate || 0,
				ltv: metrics.ltv || 0,
				arpu: metrics.arpu || 0,
				active_users: metrics.active_users || 0,
			};
		},

		/**
		 * Get cohort analysis data
		 */
		async getCohortAnalysis(params: any) {
			try {
				const cohortAnalysisResult = await strapi.entityService.findMany(
					"api::cohort-analysis.cohort-analysis",
					{
						filters: {
							createdAt: {
								$gte: params.start_date,
								$lte: params.end_date,
							},
						},
						sort: { createdAt: "desc" },
						limit: 100,
					},
				);

				const cohortAnalysis = Array.isArray(cohortAnalysisResult)
					? cohortAnalysisResult
					: [cohortAnalysisResult];
				return cohortAnalysis;
			} catch (error: any) {
				strapi.log.error("Failed to get cohort analysis:", error);
				return [];
			}
		},

		/**
		 * Detect anomalies in analytics data
		 */
		async detectAnomalies(params: any) {
			try {
				const anomaliesResult = await strapi.entityService.findMany(
					"api::performance-alert.performance-alert",
					{
						filters: {
							createdAt: {
								$gte: params.start_date,
								$lte: params.end_date,
							},
							type: "conversion_drop", // Using valid enum value
						},
						sort: { createdAt: "desc" },
						limit: 50,
					},
				);

				const anomalies = Array.isArray(anomaliesResult)
					? anomaliesResult
					: [anomaliesResult];
				return anomalies;
			} catch (error: any) {
				strapi.log.error("Failed to detect anomalies:", error);
				return [];
			}
		},

		/**
		 * Sync subscription data from Adapty
		 */
		async syncSubscriptionData() {
			try {
				// Implementation for subscription data sync
				strapi.log.info("Subscription data sync completed");
			} catch (error: any) {
				strapi.log.error("Failed to sync subscription data:", error);
			}
		},

		/**
		 * Generate insights from analytics data
		 */
		async generateInsights() {
			try {
				// Implementation for insights generation
				strapi.log.info("Insights generation completed");
			} catch (error: any) {
				strapi.log.error("Failed to generate insights:", error);
			}
		},

		/**
		 * Get unified reporting data
		 */
		async getUnifiedReporting(params: any): Promise<any> {
			try {
				const analytics = await (this as any).getAdaptyAnalytics(params);
				return {
					summary: analytics.overall,
					breakdowns: {
						country: analytics.by_country,
						product: analytics.by_product,
						variation: analytics.by_variation,
					},
					cohorts: analytics.cohort_analysis,
					anomalies: analytics.anomaly_detection,
				};
			} catch (error: any) {
				strapi.log.error("Failed to get unified reporting:", error);
				throw error;
			}
		},

		/**
		 * Detect significant drops in metrics
		 */
		async detectSignificantDrop(metric: string, threshold: number = 0.2) {
			try {
				// Implementation for significant drop detection
				return {
					detected: false,
					metric: metric,
					threshold: threshold,
					current_value: 0,
					previous_value: 0,
					drop_percentage: 0,
				};
			} catch (error: any) {
				strapi.log.error("Failed to detect significant drop:", error);
				return null;
			}
		},
	}),
);
