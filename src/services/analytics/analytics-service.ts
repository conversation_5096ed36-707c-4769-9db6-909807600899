/**
 * Analytics Service - Core analytics data collection and processing
 */

export interface PaywallMetrics {
	id: string;
	paywallId: string;
	timestamp: Date;
	impressions: number;
	conversions: number;
	conversionRate: number;
	revenue: number;
	averageRevenuePerUser: number;
	bounceRate: number;
	timeToConversion: number;
	userSegment: string;
	region: string;
	locale: string;
	deviceType: "mobile" | "tablet" | "desktop";
	platform: "ios" | "android" | "web";
}

export interface UserEngagementMetrics {
	id: string;
	paywallId: string;
	timestamp: Date;
	sessionDuration: number;
	pageViews: number;
	clickThroughRate: number;
	scrollDepth: number;
	interactionEvents: {
		type: string;
		element: string;
		timestamp: Date;
		value?: any;
	}[];
	heatmapData: {
		x: number;
		y: number;
		clicks: number;
	}[];
}

export interface RevenueMetrics {
	id: string;
	timestamp: Date;
	totalRevenue: number;
	recurringRevenue: number;
	oneTimeRevenue: number;
	averageOrderValue: number;
	customerLifetimeValue: number;
	churnRate: number;
	retentionRate: number;
	revenuePerPaywall: {
		paywallId: string;
		revenue: number;
		conversions: number;
	}[];
	revenueByRegion: {
		region: string;
		revenue: number;
		conversions: number;
	}[];
}

export interface AnalyticsFilter {
	dateRange: {
		start: Date;
		end: Date;
	};
	paywallIds?: string[];
	regions?: string[];
	locales?: string[];
	deviceTypes?: string[];
	platforms?: string[];
	userSegments?: string[];
}

export interface AnalyticsDashboardData {
	overview: {
		totalImpressions: number;
		totalConversions: number;
		overallConversionRate: number;
		totalRevenue: number;
		averageRevenuePerUser: number;
		activePaywalls: number;
		topPerformingPaywall: {
			id: string;
			name: string;
			conversionRate: number;
		};
	};
	trends: {
		impressionsTrend: { date: string; value: number }[];
		conversionsTrend: { date: string; value: number }[];
		revenueTrend: { date: string; value: number }[];
		conversionRateTrend: { date: string; value: number }[];
	};
	paywallPerformance: {
		paywallId: string;
		name: string;
		impressions: number;
		conversions: number;
		conversionRate: number;
		revenue: number;
		averageRevenuePerUser: number;
		trend: "up" | "down" | "stable";
		changePercentage: number;
	}[];
	regionalPerformance: {
		region: string;
		impressions: number;
		conversions: number;
		conversionRate: number;
		revenue: number;
		topPaywall: string;
	}[];
	userEngagement: {
		averageSessionDuration: number;
		averagePageViews: number;
		averageClickThroughRate: number;
		bounceRate: number;
		topInteractionElements: {
			element: string;
			interactions: number;
			conversionImpact: number;
		}[];
	};
}

export interface ExportOptions {
	format: "csv" | "json" | "pdf";
	includeCharts: boolean;
	dateRange: {
		start: Date;
		end: Date;
	};
	metrics: string[];
	groupBy?: "paywall" | "region" | "date" | "device";
}

class AnalyticsService {
	private metricsCache: Map<string, any> = new Map();
	private cacheExpiry: number = 5 * 60 * 1000; // 5 minutes

	/**
	 * Record paywall metrics
	 */
	async recordPaywallMetrics(
		metrics: Omit<PaywallMetrics, "id" | "timestamp">,
	): Promise<void> {
		const paywallMetrics: PaywallMetrics = {
			id: this.generateId(),
			timestamp: new Date(),
			...metrics,
		};

		try {
			// Store in database (Strapi entity)
			await strapi.entityService.create(
				"api::paywall-metrics.paywall-metrics",
				{
					data: paywallMetrics,
				},
			);

			// Invalidate relevant caches
			this.invalidateCache(`paywall_${metrics.paywallId}`);
			this.invalidateCache(`region_${metrics.region}`);
			this.invalidateCache("overview");
		} catch (error) {
			strapi.log.error("Failed to record paywall metrics:", error);
			throw error;
		}
	}

	/**
	 * Record user engagement metrics
	 */
	async recordUserEngagement(
		engagement: Omit<UserEngagementMetrics, "id" | "timestamp">,
	): Promise<void> {
		const engagementMetrics: UserEngagementMetrics = {
			id: this.generateId(),
			timestamp: new Date(),
			...engagement,
		};

		try {
			await strapi.entityService.create(
				"api::user-engagement.user-engagement",
				{
					data: {
						...engagementMetrics,
						interactionEvents: JSON.stringify(engagementMetrics.interactionEvents),
					},
				},
			);

			this.invalidateCache(`engagement_${engagement.paywallId}`);
			this.invalidateCache("user_engagement");
		} catch (error) {
			strapi.log.error("Failed to record user engagement:", error);
			throw error;
		}
	}

	/**
	 * Get dashboard data with caching
	 */
	async getDashboardData(
		filter: AnalyticsFilter,
	): Promise<AnalyticsDashboardData> {
		const cacheKey = this.generateCacheKey("dashboard", filter);

		if (this.isCacheValid(cacheKey)) {
			return this.metricsCache.get(cacheKey);
		}

		try {
			const dashboardData = await this.calculateDashboardData(filter);
			this.setCache(cacheKey, dashboardData);
			return dashboardData;
		} catch (error) {
			strapi.log.error("Failed to get dashboard data:", error);
			throw error;
		}
	}

	/**
	 * Calculate comprehensive dashboard data
	 */
	private async calculateDashboardData(
		filter: AnalyticsFilter,
	): Promise<AnalyticsDashboardData> {
		const [paywallMetrics, engagementMetrics, revenueData] = await Promise.all([
			this.getPaywallMetrics(filter),
			this.getUserEngagementMetrics(filter),
			this.getRevenueMetrics(filter),
		]);

		// Calculate overview metrics
		const overview = this.calculateOverviewMetrics(paywallMetrics, revenueData);

		// Calculate trends
		const trends = this.calculateTrends(paywallMetrics, filter.dateRange);

		// Calculate paywall performance
		const paywallPerformance = this.calculatePaywallPerformance(paywallMetrics);

		// Calculate regional performance
		const regionalPerformance =
			this.calculateRegionalPerformance(paywallMetrics);

		// Calculate user engagement
		const userEngagement =
			this.calculateUserEngagementSummary(engagementMetrics);

		return {
			overview,
			trends,
			paywallPerformance,
			regionalPerformance,
			userEngagement,
		};
	}

	/**
	 * Get paywall metrics with filters
	 */
	private async getPaywallMetrics(
		filter: AnalyticsFilter,
	): Promise<PaywallMetrics[]> {
		const whereClause: any = {
			timestamp: {
				$gte: filter.dateRange.start,
				$lte: filter.dateRange.end,
			},
		};

		if (filter.paywallIds?.length) {
			whereClause.paywallId = { $in: filter.paywallIds };
		}

		if (filter.regions?.length) {
			whereClause.region = { $in: filter.regions };
		}

		if (filter.locales?.length) {
			whereClause.locale = { $in: filter.locales };
		}

		if (filter.deviceTypes?.length) {
			whereClause.deviceType = { $in: filter.deviceTypes };
		}

		if (filter.platforms?.length) {
			whereClause.platform = { $in: filter.platforms };
		}

		const result = await strapi.entityService.findMany(
			"api::paywall-metrics.paywall-metrics",
			{
				filters: whereClause,
				sort: { timestamp: "desc" },
			},
		);
		return Array.isArray(result) ? result as any[] : [];
	}

	/**
	 * Get user engagement metrics with filters
	 */
	private async getUserEngagementMetrics(
		filter: AnalyticsFilter,
	): Promise<UserEngagementMetrics[]> {
		const whereClause: any = {
			timestamp: {
				$gte: filter.dateRange.start,
				$lte: filter.dateRange.end,
			},
		};

		if (filter.paywallIds?.length) {
			whereClause.paywallId = { $in: filter.paywallIds };
		}

		const result = await strapi.entityService.findMany(
			"api::user-engagement.user-engagement",
			{
				filters: whereClause,
				sort: { timestamp: "desc" },
			},
		);
		return Array.isArray(result) ? result as any[] : [];
	}

	/**
	 * Get revenue metrics
	 */
	private async getRevenueMetrics(
		filter: AnalyticsFilter,
	): Promise<RevenueMetrics[]> {
		// This would integrate with Adapty's revenue data
		// For now, we'll calculate from paywall metrics
		const paywallMetrics = await this.getPaywallMetrics(filter);

		const revenueByDate = new Map<string, number>();
		const revenueByPaywall = new Map<
			string,
			{ revenue: number; conversions: number }
		>();
		const revenueByRegion = new Map<
			string,
			{ revenue: number; conversions: number }
		>();

		paywallMetrics.forEach((metric) => {
			const dateKey = metric.timestamp.toISOString().split("T")[0];

			// Aggregate by date
			revenueByDate.set(
				dateKey,
				(revenueByDate.get(dateKey) || 0) + metric.revenue,
			);

			// Aggregate by paywall
			const paywallData = revenueByPaywall.get(metric.paywallId) || {
				revenue: 0,
				conversions: 0,
			};
			paywallData.revenue += metric.revenue;
			paywallData.conversions += metric.conversions;
			revenueByPaywall.set(metric.paywallId, paywallData);

			// Aggregate by region
			const regionData = revenueByRegion.get(metric.region) || {
				revenue: 0,
				conversions: 0,
			};
			regionData.revenue += metric.revenue;
			regionData.conversions += metric.conversions;
			revenueByRegion.set(metric.region, regionData);
		});

		const totalRevenue = Array.from(revenueByDate.values()).reduce(
			(sum, revenue) => sum + revenue,
			0,
		);
		const totalConversions = paywallMetrics.reduce(
			(sum, metric) => sum + metric.conversions,
			0,
		);

		return [
			{
				id: this.generateId(),
				timestamp: new Date(),
				totalRevenue,
				recurringRevenue: totalRevenue * 0.8, // Estimate
				oneTimeRevenue: totalRevenue * 0.2, // Estimate
				averageOrderValue:
					totalConversions > 0 ? totalRevenue / totalConversions : 0,
				customerLifetimeValue: totalRevenue * 2.5, // Estimate
				churnRate: 0.05, // Estimate
				retentionRate: 0.95, // Estimate
				revenuePerPaywall: Array.from(revenueByPaywall.entries()).map(
					([paywallId, data]) => ({
						paywallId,
						revenue: data.revenue,
						conversions: data.conversions,
					}),
				),
				revenueByRegion: Array.from(revenueByRegion.entries()).map(
					([region, data]) => ({
						region,
						revenue: data.revenue,
						conversions: data.conversions,
					}),
				),
			},
		];
	}

	/**
	 * Calculate overview metrics
	 */
	private calculateOverviewMetrics(
		paywallMetrics: PaywallMetrics[],
		revenueData: RevenueMetrics[],
	) {
		const totalImpressions = paywallMetrics.reduce(
			(sum, metric) => sum + metric.impressions,
			0,
		);
		const totalConversions = paywallMetrics.reduce(
			(sum, metric) => sum + metric.conversions,
			0,
		);
		const totalRevenue = revenueData.reduce(
			(sum, revenue) => sum + revenue.totalRevenue,
			0,
		);

		// Find top performing paywall
		const paywallPerformance = new Map<
			string,
			{ conversions: number; impressions: number }
		>();
		paywallMetrics.forEach((metric) => {
			const existing = paywallPerformance.get(metric.paywallId) || {
				conversions: 0,
				impressions: 0,
			};
			existing.conversions += metric.conversions;
			existing.impressions += metric.impressions;
			paywallPerformance.set(metric.paywallId, existing);
		});

		let topPerformingPaywall = { id: "", name: "", conversionRate: 0 };
		paywallPerformance.forEach((performance, paywallId) => {
			const conversionRate =
				performance.impressions > 0
					? performance.conversions / performance.impressions
					: 0;
			if (conversionRate > topPerformingPaywall.conversionRate) {
				topPerformingPaywall = {
					id: paywallId,
					name: `Paywall ${paywallId}`, // Would fetch actual name
					conversionRate,
				};
			}
		});

		return {
			totalImpressions,
			totalConversions,
			overallConversionRate:
				totalImpressions > 0 ? totalConversions / totalImpressions : 0,
			totalRevenue,
			averageRevenuePerUser:
				totalConversions > 0 ? totalRevenue / totalConversions : 0,
			activePaywalls: paywallPerformance.size,
			topPerformingPaywall,
		};
	}

	/**
	 * Calculate trends over time
	 */
	private calculateTrends(
		paywallMetrics: PaywallMetrics[],
		_dateRange: { start: Date; end: Date },
	) {
		const dailyMetrics = new Map<
			string,
			{
				impressions: number;
				conversions: number;
				revenue: number;
			}
		>();

		paywallMetrics.forEach((metric) => {
			const dateKey = metric.timestamp.toISOString().split("T")[0];
			const existing = dailyMetrics.get(dateKey) || {
				impressions: 0,
				conversions: 0,
				revenue: 0,
			};
			existing.impressions += metric.impressions;
			existing.conversions += metric.conversions;
			existing.revenue += metric.revenue;
			dailyMetrics.set(dateKey, existing);
		});

		const sortedDates = Array.from(dailyMetrics.keys()).sort();

		return {
			impressionsTrend: sortedDates.map((date) => ({
				date,
				value: dailyMetrics.get(date)?.impressions || 0,
			})),
			conversionsTrend: sortedDates.map((date) => ({
				date,
				value: dailyMetrics.get(date)?.conversions || 0,
			})),
			revenueTrend: sortedDates.map((date) => ({
				date,
				value: dailyMetrics.get(date)?.revenue || 0,
			})),
			conversionRateTrend: sortedDates.map((date) => {
				const metrics = dailyMetrics.get(date);
				const conversionRate =
					metrics && metrics.impressions > 0
						? metrics.conversions / metrics.impressions
						: 0;
				return { date, value: conversionRate };
			}),
		};
	}

	/**
	 * Calculate paywall performance
	 */
	private calculatePaywallPerformance(paywallMetrics: PaywallMetrics[]) {
		const paywallStats = new Map<
			string,
			{
				impressions: number;
				conversions: number;
				revenue: number;
				name: string;
			}
		>();

		paywallMetrics.forEach((metric) => {
			const existing = paywallStats.get(metric.paywallId) || {
				impressions: 0,
				conversions: 0,
				revenue: 0,
				name: `Paywall ${metric.paywallId}`,
			};
			existing.impressions += metric.impressions;
			existing.conversions += metric.conversions;
			existing.revenue += metric.revenue;
			paywallStats.set(metric.paywallId, existing);
		});

		return Array.from(paywallStats.entries()).map(([paywallId, stats]) => ({
			paywallId,
			name: stats.name,
			impressions: stats.impressions,
			conversions: stats.conversions,
			conversionRate:
				stats.impressions > 0 ? stats.conversions / stats.impressions : 0,
			revenue: stats.revenue,
			averageRevenuePerUser:
				stats.conversions > 0 ? stats.revenue / stats.conversions : 0,
			trend: "stable" as const, // Would calculate based on historical data
			changePercentage: 0, // Would calculate based on previous period
		}));
	}

	/**
	 * Calculate regional performance
	 */
	private calculateRegionalPerformance(paywallMetrics: PaywallMetrics[]) {
		const regionalStats = new Map<
			string,
			{
				impressions: number;
				conversions: number;
				revenue: number;
				paywallPerformance: Map<string, number>;
			}
		>();

		paywallMetrics.forEach((metric) => {
			const existing = regionalStats.get(metric.region) || {
				impressions: 0,
				conversions: 0,
				revenue: 0,
				paywallPerformance: new Map(),
			};
			existing.impressions += metric.impressions;
			existing.conversions += metric.conversions;
			existing.revenue += metric.revenue;

			const paywallConversions =
				existing.paywallPerformance.get(metric.paywallId) || 0;
			existing.paywallPerformance.set(
				metric.paywallId,
				paywallConversions + metric.conversions,
			);

			regionalStats.set(metric.region, existing);
		});

		return Array.from(regionalStats.entries()).map(([region, stats]) => {
			// Find top performing paywall in this region
			let topPaywall = "";
			let topConversions = 0;
			stats.paywallPerformance.forEach((conversions, paywallId) => {
				if (conversions > topConversions) {
					topConversions = conversions;
					topPaywall = paywallId;
				}
			});

			return {
				region,
				impressions: stats.impressions,
				conversions: stats.conversions,
				conversionRate:
					stats.impressions > 0 ? stats.conversions / stats.impressions : 0,
				revenue: stats.revenue,
				topPaywall,
			};
		});
	}

	/**
	 * Calculate user engagement summary
	 */
	private calculateUserEngagementSummary(
		engagementMetrics: UserEngagementMetrics[],
	) {
		if (engagementMetrics.length === 0) {
			return {
				averageSessionDuration: 0,
				averagePageViews: 0,
				averageClickThroughRate: 0,
				bounceRate: 0,
				topInteractionElements: [],
			};
		}

		const totalSessions = engagementMetrics.length;
		const averageSessionDuration =
			engagementMetrics.reduce(
				(sum, metric) => sum + metric.sessionDuration,
				0,
			) / totalSessions;
		const averagePageViews =
			engagementMetrics.reduce((sum, metric) => sum + metric.pageViews, 0) /
			totalSessions;
		const averageClickThroughRate =
			engagementMetrics.reduce(
				(sum, metric) => sum + metric.clickThroughRate,
				0,
			) / totalSessions;

		// Calculate bounce rate (sessions with only 1 page view)
		const bouncedSessions = engagementMetrics.filter(
			(metric) => metric.pageViews <= 1,
		).length;
		const bounceRate = bouncedSessions / totalSessions;

		// Calculate top interaction elements
		const elementInteractions = new Map<string, number>();
		engagementMetrics.forEach((metric) => {
			metric.interactionEvents.forEach((event) => {
				const current = elementInteractions.get(event.element) || 0;
				elementInteractions.set(event.element, current + 1);
			});
		});

		const topInteractionElements = Array.from(elementInteractions.entries())
			.sort(([, a], [, b]) => b - a)
			.slice(0, 10)
			.map(([element, interactions]) => ({
				element,
				interactions,
				conversionImpact: 0.1, // Would calculate based on correlation with conversions
			}));

		return {
			averageSessionDuration,
			averagePageViews,
			averageClickThroughRate,
			bounceRate,
			topInteractionElements,
		};
	}

	/**
	 * Export analytics data
	 */
	async exportData(options: ExportOptions): Promise<Buffer | string> {
		const filter: AnalyticsFilter = {
			dateRange: options.dateRange,
			// Add other filters as needed
		};

		const dashboardData = await this.getDashboardData(filter);

		switch (options.format) {
			case "csv":
				return this.exportToCSV(dashboardData, options);
			case "json":
				return JSON.stringify(dashboardData, null, 2);
			case "pdf":
				return this.exportToPDF(dashboardData, options);
			default:
				throw new Error(`Unsupported export format: ${options.format}`);
		}
	}

	/**
	 * Export to CSV format
	 */
	private exportToCSV(
		data: AnalyticsDashboardData,
		_options: ExportOptions,
	): string {
		const csvRows: string[] = [];

		// Add headers
		csvRows.push("Metric,Value,Date,Paywall,Region");

		// Add paywall performance data
		data.paywallPerformance.forEach((paywall) => {
			csvRows.push(`Impressions,${paywall.impressions},,${paywall.name},`);
			csvRows.push(`Conversions,${paywall.conversions},,${paywall.name},`);
			csvRows.push(
				`Conversion Rate,${paywall.conversionRate},,${paywall.name},`,
			);
			csvRows.push(`Revenue,${paywall.revenue},,${paywall.name},`);
		});

		// Add regional performance data
		data.regionalPerformance.forEach((region) => {
			csvRows.push(
				`Regional Impressions,${region.impressions},,,${region.region}`,
			);
			csvRows.push(
				`Regional Conversions,${region.conversions},,,${region.region}`,
			);
			csvRows.push(
				`Regional Conversion Rate,${region.conversionRate},,,${region.region}`,
			);
			csvRows.push(`Regional Revenue,${region.revenue},,,${region.region}`);
		});

		return csvRows.join("\n");
	}

	/**
	 * Export to PDF format
	 */
	private async exportToPDF(
		_data: AnalyticsDashboardData,
		_options: ExportOptions,
	): Promise<Buffer> {
		// This would use a PDF generation library like puppeteer or jsPDF
		// For now, return a placeholder
		return Buffer.from("PDF export not implemented yet");
	}

	/**
	 * Real-time metrics update
	 */
	async getRealtimeMetrics(paywallIds?: string[]): Promise<{
		activeUsers: number;
		currentImpressions: number;
		currentConversions: number;
		realtimeConversionRate: number;
	}> {
		const now = new Date();
		const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);

		const recentMetrics = await this.getPaywallMetrics({
			dateRange: { start: fiveMinutesAgo, end: now },
			paywallIds,
		});

		const currentImpressions = recentMetrics.reduce(
			(sum, metric) => sum + metric.impressions,
			0,
		);
		const currentConversions = recentMetrics.reduce(
			(sum, metric) => sum + metric.conversions,
			0,
		);

		return {
			activeUsers: recentMetrics.length, // Approximate
			currentImpressions,
			currentConversions,
			realtimeConversionRate:
				currentImpressions > 0 ? currentConversions / currentImpressions : 0,
		};
	}

	// Utility methods
	private generateId(): string {
		return `analytics_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
	}

	private generateCacheKey(prefix: string, filter: AnalyticsFilter): string {
		const filterStr = JSON.stringify(filter);
		return `${prefix}_${Buffer.from(filterStr).toString("base64")}`;
	}

	private isCacheValid(key: string): boolean {
		const cached = this.metricsCache.get(key);
		if (!cached) return false;

		return Date.now() - cached.timestamp < this.cacheExpiry;
	}

	private setCache(key: string, data: any): void {
		this.metricsCache.set(key, {
			data,
			timestamp: Date.now(),
		});
	}

	private invalidateCache(pattern: string): void {
		for (const key of this.metricsCache.keys()) {
			if (key.includes(pattern)) {
				this.metricsCache.delete(key);
			}
		}
	}
}

export const analyticsService = new AnalyticsService();
