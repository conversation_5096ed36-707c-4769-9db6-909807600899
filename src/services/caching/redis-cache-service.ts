/**
 * Redis Cache Service
 * Advanced caching service with Redis backend
 */

import Redis from 'ioredis';
import { factories } from '@strapi/strapi';

interface CacheOptions {
  ttl?: number; // Time to live in seconds
  tags?: string[]; // Cache tags for invalidation
  compress?: boolean; // Enable compression for large payloads
  serialize?: boolean; // Custom serialization
}

interface CacheStats {
  hits: number;
  misses: number;
  sets: number;
  deletes: number;
  hitRate: number;
}

export default factories.createCoreService('api::paywall.paywall', ({ strapi }): any => ({
  redis: null as Redis | null,
  stats: {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0,
    hitRate: 0
  } as CacheStats,

  /**
   * Initialize Redis connection
   */
  async initialize() {
    try {
      const redisConfig = {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
        password: process.env.REDIS_PASSWORD,
        db: parseInt(process.env.REDIS_DB || '0'),
        retryDelayOnFailover: 100,
        maxRetriesPerRequest: 3,
        lazyConnect: true,
        keepAlive: 30000,
        family: 4,
        keyPrefix: process.env.REDIS_KEY_PREFIX || 'strapi:cache:',
      };

      this.redis = new Redis(redisConfig);

      this.redis.on('connect', () => {
        strapi.log.info('Redis cache connected successfully');
      });

      this.redis.on('error', (error) => {
        strapi.log.error('Redis cache connection error:', error);
      });

      this.redis.on('close', () => {
        strapi.log.warn('Redis cache connection closed');
      });

      // Test connection
      await this.redis.ping();
      strapi.log.info('Redis cache service initialized');

    } catch (error) {
      strapi.log.error('Failed to initialize Redis cache:', error);
      throw error;
    }
  },

  /**
   * Get value from cache
   */
  async get(key: string): Promise<any> {
    try {
      if (!this.redis) {
        throw new Error('Redis not initialized');
      }

      const cached = await this.redis.get(key);
      
      if (cached === null) {
        this.stats.misses++;
        this.updateHitRate();
        return null;
      }

      this.stats.hits++;
      this.updateHitRate();

      // Decompress if needed
      let data = cached;
      if (cached.startsWith('gzip:')) {
        data = await this.decompress(cached.substring(5));
      }

      return JSON.parse(data);

    } catch (error) {
      strapi.log.error('Cache get error:', error);
      this.stats.misses++;
      this.updateHitRate();
      return null;
    }
  },

  /**
   * Set value in cache
   */
  async set(key: string, value: any, options: CacheOptions = {}): Promise<void> {
    try {
      if (!this.redis) {
        throw new Error('Redis not initialized');
      }

      const {
        ttl = 300, // 5 minutes default
        tags = [],
        compress = false,
        serialize = true
      } = options;

      let data = serialize ? JSON.stringify(value) : value;

      // Compress large payloads
      if (compress || data.length > 1024) {
        data = 'gzip:' + await this.compress(data);
      }

      // Set main cache entry
      if (ttl > 0) {
        await this.redis.setex(key, ttl, data);
      } else {
        await this.redis.set(key, data);
      }

      // Set cache tags for invalidation
      if (tags.length > 0) {
        await this.setTags(key, tags, ttl);
      }

      this.stats.sets++;

    } catch (error) {
      strapi.log.error('Cache set error:', error);
      throw error;
    }
  },

  /**
   * Delete value from cache
   */
  async delete(key: string): Promise<void> {
    try {
      if (!this.redis) {
        throw new Error('Redis not initialized');
      }

      await this.redis.del(key);
      this.stats.deletes++;

    } catch (error) {
      strapi.log.error('Cache delete error:', error);
      throw error;
    }
  },

  /**
   * Delete multiple keys
   */
  async deleteMany(keys: string[]): Promise<void> {
    try {
      if (!this.redis || keys.length === 0) {
        return;
      }

      await this.redis.del(...keys);
      this.stats.deletes += keys.length;

    } catch (error) {
      strapi.log.error('Cache delete many error:', error);
      throw error;
    }
  },

  /**
   * Invalidate cache by tags
   */
  async invalidateByTags(tags: string[]): Promise<void> {
    try {
      if (!this.redis || tags.length === 0) {
        return;
      }

      const keysToDelete: string[] = [];

      for (const tag of tags) {
        const tagKey = `tag:${tag}`;
        const keys = await this.redis.smembers(tagKey);
        keysToDelete.push(...keys);
        
        // Remove the tag set
        await this.redis.del(tagKey);
      }

      if (keysToDelete.length > 0) {
        await this.deleteMany([...new Set(keysToDelete)]);
        strapi.log.info(`Invalidated ${keysToDelete.length} cache entries by tags: ${tags.join(', ')}`);
      }

    } catch (error) {
      strapi.log.error('Cache invalidate by tags error:', error);
      throw error;
    }
  },

  /**
   * Clear all cache
   */
  async clear(): Promise<void> {
    try {
      if (!this.redis) {
        throw new Error('Redis not initialized');
      }

      await this.redis.flushdb();
      strapi.log.info('Cache cleared');

    } catch (error) {
      strapi.log.error('Cache clear error:', error);
      throw error;
    }
  },

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    return { ...this.stats };
  },

  /**
   * Reset cache statistics
   */
  resetStats(): void {
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      hitRate: 0
    };
  },

  /**
   * Check if key exists
   */
  async exists(key: string): Promise<boolean> {
    try {
      if (!this.redis) {
        return false;
      }

      const exists = await this.redis.exists(key);
      return exists === 1;

    } catch (error) {
      strapi.log.error('Cache exists error:', error);
      return false;
    }
  },

  /**
   * Get TTL for key
   */
  async getTTL(key: string): Promise<number> {
    try {
      if (!this.redis) {
        return -1;
      }

      return await this.redis.ttl(key);

    } catch (error) {
      strapi.log.error('Cache TTL error:', error);
      return -1;
    }
  },

  /**
   * Extend TTL for key
   */
  async extendTTL(key: string, ttl: number): Promise<void> {
    try {
      if (!this.redis) {
        return;
      }

      await this.redis.expire(key, ttl);

    } catch (error) {
      strapi.log.error('Cache extend TTL error:', error);
      throw error;
    }
  },

  /**
   * Get keys by pattern
   */
  async getKeysByPattern(pattern: string): Promise<string[]> {
    try {
      if (!this.redis) {
        return [];
      }

      return await this.redis.keys(pattern);

    } catch (error) {
      strapi.log.error('Cache get keys by pattern error:', error);
      return [];
    }
  },

  /**
   * Warm cache with paywall data
   */
  async warmPaywallCache(): Promise<void> {
    try {
      strapi.log.info('Starting cache warming for paywalls...');

      // Get all published paywalls
      const paywalls = await strapi.entityService.findMany('api::paywall.paywall', {
        filters: { publishedAt: { $notNull: true } },
        populate: {
          theme: { populate: ['background_image', 'logo'] },
          features: { populate: ['icon_image'] },
          testimonials: { populate: ['author_avatar'] },
          product_labels: true,
          localizations: true
        }
      });

      const locales = ['en', 'es', 'fr', 'de', 'ja', 'ko', 'zh'];
      const deviceTypes = ['mobile', 'tablet'];

      for (const paywall of paywalls) {
        for (const locale of locales) {
          for (const deviceType of deviceTypes) {
            const cacheKey = this.generatePaywallCacheKey(paywall.placement_id, {
              locale,
              deviceType,
              version: 'latest'
            });

            // Transform paywall for mobile API
            const transformedPaywall = await strapi.service('api::mobile.v1.paywall')
              .transformForMobile(paywall, { deviceType, locale });

            await this.set(cacheKey, transformedPaywall, {
              ttl: 1800, // 30 minutes
              tags: [`paywall:${paywall.id}`, `placement:${paywall.placement_id}`, `locale:${locale}`],
              compress: true
            });
          }
        }
      }

      strapi.log.info(`Cache warmed for ${paywalls.length} paywalls`);

    } catch (error) {
      strapi.log.error('Cache warming error:', error);
      throw error;
    }
  },

  /**
   * Generate paywall cache key
   */
  generatePaywallCacheKey(placementId: string, options: any): string {
    const { locale = 'en', deviceType = 'mobile', version = 'latest', userId } = options;
    const userPart = userId ? `:user:${userId}` : '';
    return `paywall:${placementId}:${locale}:${deviceType}:${version}${userPart}`;
  },

  /**
   * Private: Set cache tags
   */
  async setTags(key: string, tags: string[], ttl: number): Promise<void> {
    try {
      for (const tag of tags) {
        const tagKey = `tag:${tag}`;
        await this.redis.sadd(tagKey, key);
        
        // Set TTL for tag set (slightly longer than cache entry)
        if (ttl > 0) {
          await this.redis.expire(tagKey, ttl + 60);
        }
      }
    } catch (error) {
      strapi.log.error('Set cache tags error:', error);
    }
  },

  /**
   * Private: Update hit rate
   */
  updateHitRate(): void {
    const total = this.stats.hits + this.stats.misses;
    this.stats.hitRate = total > 0 ? (this.stats.hits / total) * 100 : 0;
  },

  /**
   * Private: Compress data
   */
  async compress(data: string): Promise<string> {
    const zlib = require('zlib');
    return new Promise((resolve, reject) => {
      zlib.gzip(data, (err, compressed) => {
        if (err) reject(err);
        else resolve(compressed.toString('base64'));
      });
    });
  },

  /**
   * Private: Decompress data
   */
  async decompress(data: string): Promise<string> {
    const zlib = require('zlib');
    return new Promise((resolve, reject) => {
      const buffer = Buffer.from(data, 'base64');
      zlib.gunzip(buffer, (err, decompressed) => {
        if (err) reject(err);
        else resolve(decompressed.toString());
      });
    });
  },

  /**
   * Cleanup on service destruction
   */
  async destroy(): Promise<void> {
    if (this.redis) {
      await this.redis.quit();
      this.redis = null;
    }
  }
}));