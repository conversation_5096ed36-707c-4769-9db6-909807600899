/**
 * Cache Invalidation Service
 * Intelligent cache invalidation based on content updates
 */

import { factories } from "@strapi/strapi";

interface InvalidationRule {
	contentType: string;
	events: string[]; // create, update, delete, publish, unpublish
	tags: (entity: any) => string[];
	keys?: (entity: any) => string[];
}

export default factories.createCoreService(
	"api::paywall.paywall", // Using existing content type
	({ strapi }) => ({
		/**
		 * Invalidation rules for different content types
		 */
		rules: [
			// Paywall invalidation rules
			{
				contentType: "api::paywall.paywall",
				events: [
					"afterCreate",
					"afterUpdate",
					"afterDelete",
					"afterPublish",
					"afterUnpublish",
				],
				tags: (paywall) => [
					`paywall:${paywall.id}`,
					`placement:${paywall.placement_id}`,
					"paywalls:all",
					...(paywall.localizations?.map((loc) => `locale:${loc.locale}`) ||
						[]),
				],
				keys: (paywall) => [
					// Invalidate all variations of this paywall
					`paywall:${paywall.placement_id}:*`,
					// Invalidate batch requests that might include this paywall
					"batch:*",
					// Invalidate prefetch caches
					"prefetch:*",
				],
			},

			// Product invalidation rules
			{
				contentType: "api::product.product",
				events: ["afterCreate", "afterUpdate", "afterDelete"],
				tags: (product) => {
					const paywallTags =
						product.paywalls?.map((p: any) => `paywall:${p.id}`) || [];
					return ["products:all", ...paywallTags];
				},
			},

			// Article invalidation rules
			{
				contentType: "api::article.article",
				events: ["afterCreate", "afterUpdate", "afterDelete", "afterPublish", "afterUnpublish"],
				tags: (article) => [
					`article:${article.id}`,
					"articles:all",
					...(article.categories?.map((cat: any) => `category:${cat.id}`) || []),
				],
			},

			// A/B test invalidation rules
			{
				contentType: "api::ab-test.ab-test",
				events: ["afterCreate", "afterUpdate", "afterDelete"],
				tags: (test) => [
					`ab-test:${test.id}`,
					`placement:${test.placement_id}`,
					"ab-tests:all",
				],
			},

			// Media invalidation rules
			{
				contentType: "plugin::upload.file",
				events: ["afterUpdate", "afterDelete"],
				tags: (file) => [
					`media:${file.id}`,
					"media:all",
					// Invalidate any content that might use this media
					"paywalls:all",
				],
			},
		] as InvalidationRule[],

		/**
		 * Initialize cache invalidation listeners
		 */
		initialize() {
			strapi.log.info("Initializing cache invalidation service...");

			// Register lifecycle hooks for each content type
			(this.rules as any[]).forEach((rule) => {
				rule.events.forEach((event) => {
					strapi.db.lifecycles.subscribe({
						models: [rule.contentType],
						[event]: async (data) => {
							await (this as any).handleInvalidation(
								rule,
								data.result || data.params?.data,
								event,
							);
						},
					});
				});
			});

			// Register custom invalidation events
			strapi.eventHub.on("cache:invalidate", async (data) => {
				await this.invalidateCustom(data);
			});

			strapi.log.info("Cache invalidation service initialized");
		},

		/**
		 * Handle cache invalidation for content changes
		 */
		async handleInvalidation(
			rule: InvalidationRule,
			entity: any,
			event: string,
		) {
			try {
				if (!entity) return;

				strapi.log.debug(
					`Cache invalidation triggered: ${rule.contentType} ${event}`,
					{
						entityId: entity.id,
						event,
					},
				);

				// Mock cache service for now - would need proper service registration
				const cacheService = {
					invalidateByTags: async (tags: string[]) => {
						strapi.log.info(`Mock cache invalidation for tags: ${tags.join(", ")}`);
					},
					getKeysByPattern: async (pattern: string) => [],
					deleteMany: async (keys: string[]) => {
						strapi.log.info(`Mock cache deletion for ${keys.length} keys`);
					},
					delete: async (key: string) => {
						strapi.log.info(`Mock cache deletion for key: ${key}`);
					}
				};

				// Get tags to invalidate
				const tags = rule.tags(entity);
				if (tags.length > 0) {
					await cacheService.invalidateByTags(tags);
					strapi.log.info(`Invalidated cache tags: ${tags.join(", ")}`);
				}

				// Get specific keys to invalidate
				if (rule.keys) {
					const keys = rule.keys(entity);
					for (const keyPattern of keys) {
						if (keyPattern.includes("*")) {
							// Handle wildcard patterns
							const matchingKeys =
								await cacheService.getKeysByPattern(keyPattern);
							if (matchingKeys.length > 0) {
								await cacheService.deleteMany(matchingKeys);
								strapi.log.info(
									`Invalidated ${matchingKeys.length} cache keys matching: ${keyPattern}`,
								);
							}
						} else {
							// Handle exact keys
							await cacheService.delete(keyPattern);
						}
					}
				}

				// Special handling for paywall updates
				if (rule.contentType === "api::paywall.paywall") {
					await this.handlePaywallInvalidation(entity, event);
				}

				// Record invalidation metrics
				await this.recordInvalidationMetrics(
					rule.contentType,
					event,
					tags.length,
				);
			} catch (error) {
				strapi.log.error("Cache invalidation error:", error);
			}
		},

		/**
		 * Handle paywall-specific cache invalidation
		 */
		async handlePaywallInvalidation(paywall: any, event: string) {
			try {
				// Mock cache service for now
				const cacheService = {
					generatePaywallCacheKey: (placementId: string, options: any) =>
						`paywall:${placementId}:${options.locale}:${options.deviceType}:${options.version}`,
					delete: async (key: string) => {
						strapi.log.info(`Mock cache deletion for key: ${key}`);
					}
				};

				// Invalidate all locale variations
				const locales = ["en", "es", "fr", "de", "ja", "ko", "zh"];
				const deviceTypes = ["mobile", "tablet"];

				for (const locale of locales) {
					for (const deviceType of deviceTypes) {
						const cacheKey = cacheService.generatePaywallCacheKey(
							paywall.placement_id,
							{
								locale,
								deviceType,
								version: "latest",
							},
						);
						await cacheService.delete(cacheKey);
					}
				}

				// If paywall was updated and published, warm the cache
				if (event === "afterUpdate" && paywall.publishedAt) {
					// Delay cache warming to ensure all related updates are complete
					setTimeout(async () => {
						await this.warmSpecificPaywall(paywall);
					}, 1000);
				}

				// Invalidate CDN cache if configured
				if (process.env.CDN_INVALIDATION_ENABLED === "true") {
					await this.invalidateCDNCache(paywall);
				}
			} catch (error) {
				strapi.log.error("Paywall cache invalidation error:", error);
			}
		},

		/**
		 * Warm cache for specific paywall
		 */
		async warmSpecificPaywall(paywall: any) {
			try {
				// Mock cache service for now
				const cacheService = {
					generatePaywallCacheKey: (placementId: string, options: any) =>
						`paywall:${placementId}:${options.locale}:${options.deviceType}:${options.version}`,
					set: async (key: string, value: any, options: any) => {
						strapi.log.info(`Mock cache set for key: ${key}`);
					}
				};
				const locales = ["en", "es", "fr", "de", "ja", "ko", "zh"];
				const deviceTypes = ["mobile", "tablet"];

				// Get full paywall data with relations
				const fullPaywall = await strapi.entityService.findOne(
					"api::paywall.paywall",
					paywall.id,
					{
						populate: {
							theme: { populate: ["background_image", "logo"] },
							features: { populate: ["icon_image"] },
							testimonials: { populate: ["author_avatar"] },
							product_labels: true,
							localizations: true,
						},
					},
				);

				if (!fullPaywall) return;

				for (const locale of locales) {
					for (const deviceType of deviceTypes) {
						const cacheKey = cacheService.generatePaywallCacheKey(
							paywall.placement_id,
							{
								locale,
								deviceType,
								version: "latest",
							},
						);

						const transformedPaywall = await strapi
							.service("api::mobile.v1.paywall")
							.transformForMobile(fullPaywall, { deviceType, locale });

						await cacheService.set(cacheKey, transformedPaywall, {
							ttl: 1800, // 30 minutes
							tags: [
								`paywall:${paywall.id}`,
								`placement:${paywall.placement_id}`,
								`locale:${locale}`,
							],
							compress: true,
						});
					}
				}

				strapi.log.info(`Cache warmed for paywall: ${paywall.placement_id}`);
			} catch (error) {
				strapi.log.error("Specific paywall cache warming error:", error);
			}
		},

		/**
		 * Invalidate CDN cache
		 */
		async invalidateCDNCache(paywall: any) {
			try {
				// Mock CDN service for now
				const cdnService = {
					invalidatePaywallCache: async (placementId: string) => {
						strapi.log.info(`Mock CDN invalidation for placement: ${placementId}`);
					}
				};
				if (cdnService) {
					await cdnService.invalidatePaywallCache(paywall.placement_id);
				}
			} catch (error) {
				strapi.log.error("CDN cache invalidation error:", error);
			}
		},

		/**
		 * Handle custom invalidation events
		 */
		async invalidateCustom(data: any) {
			try {
				const { tags, keys, reason } = data;
				// Mock cache service for now
				const cacheService = {
					invalidateByTags: async (tags: string[]) => {
						strapi.log.info(`Mock cache invalidation for tags: ${tags.join(", ")}`);
					},
					deleteMany: async (keys: string[]) => {
						strapi.log.info(`Mock cache deletion for ${keys.length} keys`);
					}
				};

				if (tags && tags.length > 0) {
					await cacheService.invalidateByTags(tags);
					strapi.log.info(
						`Custom cache invalidation by tags: ${tags.join(", ")} - Reason: ${reason}`,
					);
				}

				if (keys && keys.length > 0) {
					await cacheService.deleteMany(keys);
					strapi.log.info(
						`Custom cache invalidation by keys: ${keys.length} keys - Reason: ${reason}`,
					);
				}
			} catch (error) {
				strapi.log.error("Custom cache invalidation error:", error);
			}
		},

		/**
		 * Invalidate all paywall caches
		 */
		async invalidateAllPaywalls() {
			try {
				// Mock cache service for now
				const cacheService = {
					invalidateByTags: async (tags: string[]) => {
						strapi.log.info(`Mock cache invalidation for tags: ${tags.join(", ")}`);
					}
				};
				await cacheService.invalidateByTags(["paywalls:all"]);
				strapi.log.info("Invalidated all paywall caches");
			} catch (error) {
				strapi.log.error("Invalidate all paywalls error:", error);
			}
		},

		/**
		 * Invalidate cache by placement ID
		 */
		async invalidateByPlacementId(placementId: string) {
			try {
				// Mock cache service for now
				const cacheService = {
					invalidateByTags: async (tags: string[]) => {
						strapi.log.info(`Mock cache invalidation for tags: ${tags.join(", ")}`);
					}
				};
				await cacheService.invalidateByTags([`placement:${placementId}`]);
				strapi.log.info(`Invalidated cache for placement: ${placementId}`);
			} catch (error) {
				strapi.log.error("Invalidate by placement ID error:", error);
			}
		},

		/**
		 * Record invalidation metrics
		 */
		async recordInvalidationMetrics(
			contentType: string,
			event: string,
			tagsCount: number,
		) {
			try {
				// Record metrics for monitoring
				await strapi.entityService.create(
					"api::cache-invalidation.cache-invalidation",
					{
						data: {
							content_type: contentType,
							event,
							tags_invalidated: tagsCount,
							timestamp: new Date(),
						},
					},
				);
			} catch (error) {
				// Don't throw error for metrics recording failure
				strapi.log.warn("Failed to record invalidation metrics:", error);
			}
		},

		/**
		 * Get invalidation statistics
		 */
		async getInvalidationStats(days: number = 7) {
			try {
				const startDate = new Date();
				startDate.setDate(startDate.getDate() - days);

				const logs = await strapi.entityService.findMany(
					"api::cache-invalidation.cache-invalidation",
					{
						filters: {
							timestamp: { $gte: startDate },
						},
					},
				);

				const stats = {
					total_invalidations: logs.length,
					by_content_type: {},
					by_event: {},
					total_tags_invalidated: 0,
				};

				logs.forEach((log) => {
					stats.by_content_type[log.content_type] =
						(stats.by_content_type[log.content_type] || 0) + 1;
					stats.by_event[log.event] = (stats.by_event[log.event] || 0) + 1;
					stats.total_tags_invalidated += log.tags_invalidated || 0;
				});

				return stats;
			} catch (error) {
				strapi.log.error("Get invalidation stats error:", error);
				return null;
			}
		},
	}),
);
