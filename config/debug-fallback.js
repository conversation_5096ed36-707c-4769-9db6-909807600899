/**
 * Debug Mode Fallback Configuration
 * Provides graceful degradation when enhanced debug mode fails
 */

const fs = require("node:fs");
const path = require("node:path");

class DebugFallbackManager {
	constructor() {
		this.fallbackLevels = [
			"enhanced", // Full debug with all features
			"standard", // Standard debug without advanced features
			"minimal", // Basic debug with essential features only
			"safe", // Safest mode with minimal features
		];

		this.currentLevel = "enhanced";
		this.errors = [];
	}

	/**
	 * Get configuration for current fallback level
	 */
	getConfiguration(level = this.currentLevel) {
		const configs = {
			enhanced: this.getEnhancedConfig(),
			standard: this.getStandardConfig(),
			minimal: this.getMinimalConfig(),
			safe: this.getSafeConfig(),
		};

		return configs[level] || configs.safe;
	}

	/**
	 * Enhanced debug configuration (full features)
	 */
	getEnhancedConfig() {
		return {
			environment: {
				NODE_ENV: "development",
				GENERATE_SOURCEMAP: "true",
				DEV_SERVER_SOURCEMAP: "true",
				TS_NODE_OPTIONS: "--transpile-only --files --esm",
				DEBUG: "strapi:*",
				STRAPI_LOG_LEVEL: "debug",
				NODE_OPTIONS:
					"--trace-warnings --trace-uncaught --max-old-space-size=4096",
			},
			webpack: {
				devtool: "eval-cheap-module-source-map",
				mode: "development",
				optimization: { minimize: false },
				cache: { type: "filesystem" },
			},
			features: {
				sourceMap: true,
				hotReload: true,
				typeScriptDebugging: true,
				performanceMonitoring: true,
				errorTracking: true,
			},
		};
	}

	/**
	 * Standard debug configuration (reduced features)
	 */
	getStandardConfig() {
		return {
			environment: {
				NODE_ENV: "development",
				GENERATE_SOURCEMAP: "true",
				DEV_SERVER_SOURCEMAP: "false",
				TS_NODE_OPTIONS: "--transpile-only",
				DEBUG: "strapi:admin,strapi:api",
				STRAPI_LOG_LEVEL: "info",
				NODE_OPTIONS: "--max-old-space-size=2048",
			},
			webpack: {
				devtool: "eval-source-map",
				mode: "development",
				optimization: { minimize: false },
				cache: { type: "memory" },
			},
			features: {
				sourceMap: true,
				hotReload: true,
				typeScriptDebugging: true,
				performanceMonitoring: false,
				errorTracking: true,
			},
		};
	}

	/**
	 * Minimal debug configuration (essential features only)
	 */
	getMinimalConfig() {
		return {
			environment: {
				NODE_ENV: "development",
				GENERATE_SOURCEMAP: "false",
				DEV_SERVER_SOURCEMAP: "false",
				TS_NODE_OPTIONS: "--transpile-only",
				DEBUG: "strapi:admin",
				STRAPI_LOG_LEVEL: "info",
				NODE_OPTIONS: "--max-old-space-size=1024",
			},
			webpack: {
				devtool: "eval",
				mode: "development",
				optimization: { minimize: false },
				cache: false,
			},
			features: {
				sourceMap: false,
				hotReload: true,
				typeScriptDebugging: false,
				performanceMonitoring: false,
				errorTracking: true,
			},
		};
	}

	/**
	 * Safe debug configuration (maximum compatibility)
	 */
	getSafeConfig() {
		return {
			environment: {
				NODE_ENV: "development",
				GENERATE_SOURCEMAP: "false",
				DEV_SERVER_SOURCEMAP: "false",
				TS_NODE_OPTIONS: "",
				DEBUG: "",
				STRAPI_LOG_LEVEL: "warn",
				NODE_OPTIONS: "",
			},
			webpack: {
				devtool: false,
				mode: "development",
				optimization: { minimize: false },
				cache: false,
			},
			features: {
				sourceMap: false,
				hotReload: false,
				typeScriptDebugging: false,
				performanceMonitoring: false,
				errorTracking: false,
			},
		};
	}

	/**
	 * Apply configuration to environment
	 */
	applyConfiguration(config) {
		try {
			// Apply environment variables
			Object.entries(config.environment).forEach(([key, value]) => {
				if (value) {
					process.env[key] = value;
				}
			});

			console.log(`✅ Applied ${this.currentLevel} debug configuration`);
			return true;
		} catch (error) {
			console.error(
				`❌ Failed to apply ${this.currentLevel} configuration:`,
				error.message,
			);
			this.errors.push({ level: this.currentLevel, error: error.message });
			return false;
		}
	}

	/**
	 * Attempt to fallback to next level
	 */
	fallback(reason) {
		const currentIndex = this.fallbackLevels.indexOf(this.currentLevel);

		if (currentIndex < this.fallbackLevels.length - 1) {
			const previousLevel = this.currentLevel;
			this.currentLevel = this.fallbackLevels[currentIndex + 1];

			console.log(
				`⚠️  Falling back from ${previousLevel} to ${this.currentLevel} mode`,
			);
			console.log(`   Reason: ${reason}`);

			this.errors.push({
				level: previousLevel,
				reason,
				timestamp: new Date().toISOString(),
			});

			return true;
		}

		console.error(
			`❌ No more fallback levels available. Current: ${this.currentLevel}`,
		);
		return false;
	}

	/**
	 * Check if dependencies support current level
	 */
	checkDependencyCompatibility() {
		try {
			const packageJson = require("../package.json");
			const nodeVersion = process.version;
			const nodeMajor = parseInt(nodeVersion.slice(1).split(".")[0]);

			// Check Node.js version compatibility
			if (nodeMajor < 18) {
				return {
					compatible: false,
					reason: `Node.js ${nodeVersion} is not supported`,
				};
			}

			// Check ESBuild loader version
			const esbuildLoaderVersion =
				packageJson.devDependencies?.["esbuild-loader"];
			if (esbuildLoaderVersion && !esbuildLoaderVersion.includes("4.")) {
				return {
					compatible: false,
					reason: `esbuild-loader ${esbuildLoaderVersion} may not be compatible with Node.js ${nodeVersion}`,
				};
			}

			return { compatible: true };
		} catch (error) {
			return {
				compatible: false,
				reason: `Dependency check failed: ${error.message}`,
			};
		}
	}

	/**
	 * Get diagnostic information
	 */
	getDiagnostics() {
		return {
			currentLevel: this.currentLevel,
			availableLevels: this.fallbackLevels,
			errors: this.errors,
			nodeVersion: process.version,
			platform: process.platform,
			arch: process.arch,
			memoryUsage: process.memoryUsage(),
			uptime: process.uptime(),
		};
	}

	/**
	 * Reset to enhanced mode
	 */
	reset() {
		this.currentLevel = "enhanced";
		this.errors = [];
		console.log("🔄 Reset to enhanced debug mode");
	}

	/**
	 * Save current state for debugging
	 */
	saveState() {
		const state = {
			level: this.currentLevel,
			errors: this.errors,
			timestamp: new Date().toISOString(),
			diagnostics: this.getDiagnostics(),
		};

		try {
			const stateFile = path.join(__dirname, "../.cache/debug-state.json");
			fs.mkdirSync(path.dirname(stateFile), { recursive: true });
			fs.writeFileSync(stateFile, JSON.stringify(state, null, 2));
			console.log(`💾 Debug state saved to ${stateFile}`);
		} catch (error) {
			console.warn(`⚠️  Could not save debug state: ${error.message}`);
		}
	}
}

module.exports = DebugFallbackManager;
