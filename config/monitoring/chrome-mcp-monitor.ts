/**
 * Chrome MCP Monitoring Configuration
 * Configuration settings for Chrome MCP server monitoring and health checks
 */

export interface MonitoringDatabaseConfig {
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
  ssl: boolean;
  pool: {
    min: number;
    max: number;
    acquireTimeoutMillis: number;
    createTimeoutMillis: number;
    destroyTimeoutMillis: number;
    idleTimeoutMillis: number;
  };
}

export interface ChromeMCPMonitoringConfig {
  server: {
    url: string;
    healthEndpoint: string;
    metricsEndpoint: string;
    timeout: number;
    retryAttempts: number;
  };
  monitoring: {
    healthCheckInterval: number;
    metricsCollectionInterval: number;
    uptimeCalculationTime: string; // HH:MM format
    enableRealTimeAlerts: boolean;
  };
  storage: {
    type: 'memory' | 'database';
    retentionDays: number;
    batchSize: number;
    flushInterval: number;
    enableCompression: boolean;
  };
  alerts: {
    enabled: boolean;
    channels: ('console' | 'webhook' | 'email')[];
    webhookUrl?: string;
    emailRecipients?: string[];
  };
  dashboard: {
    enabled: boolean;
    port: number;
    refreshInterval: number;
    maxConcurrentConnections: number;
    enableAuthentication: boolean;
  };
}

// Environment-based configuration
const getMonitoringConfig = (): ChromeMCPMonitoringConfig => {
  const env = process.env.NODE_ENV || 'development';
  
  const baseConfig: ChromeMCPMonitoringConfig = {
    server: {
      url: process.env.CHROME_MCP_SERVER_URL || 'http://localhost:8080',
      healthEndpoint: '/health',
      metricsEndpoint: '/metrics',
      timeout: parseInt(process.env.CHROME_MCP_TIMEOUT || '5000'),
      retryAttempts: parseInt(process.env.CHROME_MCP_RETRY_ATTEMPTS || '3'),
    },
    monitoring: {
      healthCheckInterval: parseInt(process.env.HEALTH_CHECK_INTERVAL || '30000'), // 30 seconds
      metricsCollectionInterval: parseInt(process.env.METRICS_COLLECTION_INTERVAL || '60000'), // 1 minute
      uptimeCalculationTime: process.env.UPTIME_CALCULATION_TIME || '00:00', // midnight
      enableRealTimeAlerts: process.env.ENABLE_REAL_TIME_ALERTS === 'true',
    },
    storage: {
      type: (process.env.MONITORING_STORAGE_TYPE as 'memory' | 'database') || 'memory',
      retentionDays: parseInt(process.env.MONITORING_RETENTION_DAYS || '30'),
      batchSize: parseInt(process.env.MONITORING_BATCH_SIZE || '10'),
      flushInterval: parseInt(process.env.MONITORING_FLUSH_INTERVAL || '60000'), // 1 minute
      enableCompression: process.env.ENABLE_MONITORING_COMPRESSION === 'true',
    },
    alerts: {
      enabled: process.env.MONITORING_ALERTS_ENABLED !== 'false',
      channels: (process.env.ALERT_CHANNELS?.split(',') as ('console' | 'webhook' | 'email')[]) || ['console'],
      webhookUrl: process.env.ALERT_WEBHOOK_URL,
      emailRecipients: process.env.ALERT_EMAIL_RECIPIENTS?.split(','),
    },
    dashboard: {
      enabled: process.env.MONITORING_DASHBOARD_ENABLED !== 'false',
      port: parseInt(process.env.MONITORING_DASHBOARD_PORT || '3001'),
      refreshInterval: parseInt(process.env.DASHBOARD_REFRESH_INTERVAL || '5000'), // 5 seconds
      maxConcurrentConnections: parseInt(process.env.DASHBOARD_MAX_CONNECTIONS || '10'),
      enableAuthentication: process.env.DASHBOARD_AUTH_ENABLED === 'true',
    },
  };

  // Environment-specific overrides
  switch (env) {
    case 'production':
      return {
        ...baseConfig,
        monitoring: {
          ...baseConfig.monitoring,
          healthCheckInterval: 30000, // 30 seconds
          metricsCollectionInterval: 60000, // 1 minute
          enableRealTimeAlerts: true,
        },
        storage: {
          ...baseConfig.storage,
          type: 'database',
          retentionDays: 90, // 3 months in production
          enableCompression: true,
        },
        alerts: {
          ...baseConfig.alerts,
          enabled: true,
          channels: ['webhook', 'email'],
        },
        dashboard: {
          ...baseConfig.dashboard,
          enableAuthentication: true,
        },
      };

    case 'staging':
      return {
        ...baseConfig,
        monitoring: {
          ...baseConfig.monitoring,
          healthCheckInterval: 60000, // 1 minute
          metricsCollectionInterval: 120000, // 2 minutes
          enableRealTimeAlerts: true,
        },
        storage: {
          ...baseConfig.storage,
          type: 'database',
          retentionDays: 30,
        },
        alerts: {
          ...baseConfig.alerts,
          enabled: true,
          channels: ['console', 'webhook'],
        },
      };

    case 'test':
      return {
        ...baseConfig,
        monitoring: {
          ...baseConfig.monitoring,
          healthCheckInterval: 5000, // 5 seconds for faster testing
          metricsCollectionInterval: 10000, // 10 seconds
          enableRealTimeAlerts: false,
        },
        storage: {
          ...baseConfig.storage,
          type: 'memory',
          retentionDays: 1,
          flushInterval: 5000, // 5 seconds for faster testing
        },
        alerts: {
          ...baseConfig.alerts,
          enabled: false,
        },
        dashboard: {
          ...baseConfig.dashboard,
          enabled: false,
        },
      };

    default: // development
      return baseConfig;
  }
};

// Database schema for monitoring data
export const monitoringDatabaseSchema = {
  tables: {
    chrome_mcp_health_metrics: {
      id: 'VARCHAR(255) PRIMARY KEY',
      timestamp: 'TIMESTAMP NOT NULL',
      server_status: "ENUM('healthy', 'degraded', 'unhealthy') NOT NULL",
      uptime_seconds: 'BIGINT NOT NULL',
      response_time_ms: 'INTEGER NOT NULL',
      memory_usage_mb: 'INTEGER NOT NULL',
      cpu_usage_percent: 'DECIMAL(5,2) NOT NULL',
      active_connections: 'INTEGER NOT NULL',
      connection_success_rate: 'DECIMAL(5,2) NOT NULL',
      error_count: 'INTEGER NOT NULL',
      created_at: 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
      updated_at: 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
    },
    chrome_mcp_uptime_records: {
      id: 'VARCHAR(255) PRIMARY KEY',
      date: 'DATE NOT NULL UNIQUE',
      uptime_percentage: 'DECIMAL(5,2) NOT NULL',
      total_downtime_minutes: 'DECIMAL(8,2) NOT NULL',
      incident_count: 'INTEGER NOT NULL',
      created_at: 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
      updated_at: 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
    },
    chrome_mcp_alerts: {
      id: 'VARCHAR(255) PRIMARY KEY',
      type: "ENUM('performance', 'availability', 'error_rate') NOT NULL",
      severity: "ENUM('low', 'medium', 'high', 'critical') NOT NULL",
      message: 'TEXT NOT NULL',
      timestamp: 'TIMESTAMP NOT NULL',
      metrics_snapshot: 'JSON',
      acknowledged: 'BOOLEAN DEFAULT FALSE',
      acknowledged_at: 'TIMESTAMP NULL',
      acknowledged_by: 'VARCHAR(255) NULL',
      resolved: 'BOOLEAN DEFAULT FALSE',
      resolved_at: 'TIMESTAMP NULL',
      created_at: 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
      updated_at: 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
    },
  },
  indexes: {
    chrome_mcp_health_metrics: [
      'CREATE INDEX idx_health_metrics_timestamp ON chrome_mcp_health_metrics(timestamp)',
      'CREATE INDEX idx_health_metrics_status ON chrome_mcp_health_metrics(server_status)',
      'CREATE INDEX idx_health_metrics_timestamp_status ON chrome_mcp_health_metrics(timestamp, server_status)',
    ],
    chrome_mcp_uptime_records: [
      'CREATE INDEX idx_uptime_records_date ON chrome_mcp_uptime_records(date)',
    ],
    chrome_mcp_alerts: [
      'CREATE INDEX idx_alerts_timestamp ON chrome_mcp_alerts(timestamp)',
      'CREATE INDEX idx_alerts_type_severity ON chrome_mcp_alerts(type, severity)',
      'CREATE INDEX idx_alerts_acknowledged ON chrome_mcp_alerts(acknowledged)',
      'CREATE INDEX idx_alerts_resolved ON chrome_mcp_alerts(resolved)',
    ],
  },
};

// Database configuration for monitoring
export const getMonitoringDatabaseConfig = (): MonitoringDatabaseConfig => {
  return {
    host: process.env.MONITORING_DB_HOST || 'localhost',
    port: parseInt(process.env.MONITORING_DB_PORT || '5432'),
    database: process.env.MONITORING_DB_NAME || 'chrome_mcp_monitoring',
    username: process.env.MONITORING_DB_USER || 'monitoring_user',
    password: process.env.MONITORING_DB_PASSWORD || 'monitoring_password',
    ssl: process.env.MONITORING_DB_SSL === 'true',
    pool: {
      min: parseInt(process.env.MONITORING_DB_POOL_MIN || '2'),
      max: parseInt(process.env.MONITORING_DB_POOL_MAX || '10'),
      acquireTimeoutMillis: parseInt(process.env.MONITORING_DB_ACQUIRE_TIMEOUT || '30000'),
      createTimeoutMillis: parseInt(process.env.MONITORING_DB_CREATE_TIMEOUT || '30000'),
      destroyTimeoutMillis: parseInt(process.env.MONITORING_DB_DESTROY_TIMEOUT || '5000'),
      idleTimeoutMillis: parseInt(process.env.MONITORING_DB_IDLE_TIMEOUT || '30000'),
    },
  };
};

// Alert threshold configurations
export const alertThresholds = {
  development: {
    responseTime: { warning: 5000, critical: 10000 },
    memoryUsage: { warning: 512, critical: 1024 },
    cpuUsage: { warning: 80, critical: 95 },
    connectionSuccessRate: { warning: 90, critical: 80 },
    errorRate: { warning: 10, critical: 20 },
    uptimePercentage: { warning: 95, critical: 90 },
  },
  staging: {
    responseTime: { warning: 3000, critical: 6000 },
    memoryUsage: { warning: 1024, critical: 2048 },
    cpuUsage: { warning: 75, critical: 90 },
    connectionSuccessRate: { warning: 95, critical: 85 },
    errorRate: { warning: 5, critical: 10 },
    uptimePercentage: { warning: 98, critical: 95 },
  },
  production: {
    responseTime: { warning: 2000, critical: 5000 },
    memoryUsage: { warning: 1024, critical: 2048 },
    cpuUsage: { warning: 70, critical: 90 },
    connectionSuccessRate: { warning: 99, critical: 95 },
    errorRate: { warning: 3, critical: 5 },
    uptimePercentage: { warning: 99.5, critical: 99.0 },
  },
};

// Export configuration
export const chromeMCPMonitoringConfig = getMonitoringConfig();

// Validation function
export const validateMonitoringConfig = (config: ChromeMCPMonitoringConfig): string[] => {
  const errors: string[] = [];

  // Validate server configuration
  if (!config.server.url) {
    errors.push('Chrome MCP server URL is required');
  }

  if (config.server.timeout < 1000) {
    errors.push('Server timeout must be at least 1000ms');
  }

  // Validate monitoring intervals
  if (config.monitoring.healthCheckInterval < 5000) {
    errors.push('Health check interval must be at least 5000ms');
  }

  if (config.monitoring.metricsCollectionInterval < 10000) {
    errors.push('Metrics collection interval must be at least 10000ms');
  }

  // Validate storage configuration
  if (config.storage.retentionDays < 1) {
    errors.push('Retention days must be at least 1');
  }

  if (config.storage.batchSize < 1) {
    errors.push('Batch size must be at least 1');
  }

  // Validate dashboard configuration
  if (config.dashboard.enabled && (config.dashboard.port < 1000 || config.dashboard.port > 65535)) {
    errors.push('Dashboard port must be between 1000 and 65535');
  }

  // Validate alert configuration
  if (config.alerts.enabled && config.alerts.channels.length === 0) {
    errors.push('At least one alert channel must be configured when alerts are enabled');
  }

  if (config.alerts.channels.includes('webhook') && !config.alerts.webhookUrl) {
    errors.push('Webhook URL is required when webhook alert channel is enabled');
  }

  if (config.alerts.channels.includes('email') && (!config.alerts.emailRecipients || config.alerts.emailRecipients.length === 0)) {
    errors.push('Email recipients are required when email alert channel is enabled');
  }

  return errors;
};

// Configuration validation on module load
const configErrors = validateMonitoringConfig(chromeMCPMonitoringConfig);
if (configErrors.length > 0) {
  console.warn('Chrome MCP monitoring configuration warnings:');
  configErrors.forEach(error => console.warn(`  - ${error}`));
}