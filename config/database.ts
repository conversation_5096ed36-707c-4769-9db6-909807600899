import path from "node:path";
import { getRequiredEnv } from "./utils/env-validation";

// Ensure .env file is loaded before configuration
if (process.env.NODE_ENV !== "production") {
	require("dotenv").config();
}

export default ({ env }) => {
	// Validate that DATABASE_CLIENT is always specified
	const client = getRequiredEnv(
		env,
		"DATABASE_CLIENT",
		"Database client type",
		"sqlite, postgres, or mysql",
	);

	let connection;

	switch (client) {
		case "mysql":
			connection = {
				connection: {
					host: getRequiredEnv(
						env,
						"DATABASE_HOST",
						"MySQL database host",
						"localhost",
					),
					port:
						env.int("DATABASE_PORT") ||
						(() => {
							throw new Error("❌ DATABASE_PORT is required for MySQL");
						})(),
					database: getRequiredEnv(
						env,
						"DATABASE_NAME",
						"MySQL database name",
						"strapi_db",
					),
					user: getRequiredEnv(
						env,
						"DATABASE_USERNAME",
						"MySQL username",
						"strapi",
					),
					password: getRequiredEnv(
						env,
						"DATABASE_PASSWORD",
						"MySQL password",
						"your_password",
					),
					ssl: env.bool("DATABASE_SSL", false) && {
						key: env("DATABASE_SSL_KEY", undefined),
						cert: env("DATABASE_SSL_CERT", undefined),
						ca: env("DATABASE_SSL_CA", undefined),
						capath: env("DATABASE_SSL_CAPATH", undefined),
						cipher: env("DATABASE_SSL_CIPHER", undefined),
						rejectUnauthorized: env.bool(
							"DATABASE_SSL_REJECT_UNAUTHORIZED",
							true,
						),
					},
				},
				pool: {
					min: env.int("DATABASE_POOL_MIN", 2),
					max: env.int("DATABASE_POOL_MAX", 10),
				},
			};
			break;
		case "postgres":
			connection = {
				connection: {
					connectionString: env("DATABASE_URL"),
					host: getRequiredEnv(
						env,
						"DATABASE_HOST",
						"PostgreSQL database host",
						"localhost",
					),
					port:
						env.int("DATABASE_PORT") ||
						(() => {
							throw new Error("❌ DATABASE_PORT is required for PostgreSQL");
						})(),
					database: getRequiredEnv(
						env,
						"DATABASE_NAME",
						"PostgreSQL database name",
						"strapi_db",
					),
					user: getRequiredEnv(
						env,
						"DATABASE_USERNAME",
						"PostgreSQL username",
						"strapi",
					),
					password: getRequiredEnv(
						env,
						"DATABASE_PASSWORD",
						"PostgreSQL password",
						"your_password",
					),
					ssl: env.bool("DATABASE_SSL", false) && {
						key: env("DATABASE_SSL_KEY", undefined),
						cert: env("DATABASE_SSL_CERT", undefined),
						ca: env("DATABASE_SSL_CA", undefined),
						capath: env("DATABASE_SSL_CAPATH", undefined),
						cipher: env("DATABASE_SSL_CIPHER", undefined),
						rejectUnauthorized: env.bool(
							"DATABASE_SSL_REJECT_UNAUTHORIZED",
							true,
						),
					},
					schema: env("DATABASE_SCHEMA", "public"),
				},
				pool: {
					min: env.int("DATABASE_POOL_MIN", 2),
					max: env.int("DATABASE_POOL_MAX", 10),
				},
			};
			break;
		case "sqlite":
			connection = {
				connection: {
					filename: path.join(
						__dirname,
						"..",
						"..",
						getRequiredEnv(
							env,
							"DATABASE_FILENAME",
							"SQLite database file path",
							".tmp/data.db",
						),
					),
				},
				useNullAsDefault: true,
			};
			break;
		default:
			throw new Error(
				`❌ Unsupported DATABASE_CLIENT: ${client}. Supported clients: mysql, postgres, sqlite`,
			);
	}

	return {
		connection: {
			client,
			...connection,
			acquireConnectionTimeout: env.int("DATABASE_CONNECTION_TIMEOUT", 60000),
		},
	};
};
