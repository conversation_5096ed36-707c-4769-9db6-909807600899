/**
 * Enhanced Webpack Configuration for Debug Mode
 * Provides robust error handling and fallback mechanisms
 */

const path = require("node:path");

module.exports = {
	// Enhanced development configuration
	mode: "development",

	// Comprehensive source map configuration
	devtool: "eval-cheap-module-source-map", // Fast rebuild, good debugging

	// Enhanced resolve configuration
	resolve: {
		extensions: [".ts", ".tsx", ".js", ".jsx", ".json"],
		alias: {
			"@": path.resolve(__dirname, "../src"),
			"@admin": path.resolve(__dirname, "../src/admin"),
			"@api": path.resolve(__dirname, "../src/api"),
			"@services": path.resolve(__dirname, "../src/services"),
			"@components": path.resolve(__dirname, "../src/components"),
			"@config": path.resolve(__dirname, "../config"),
		},
		// Fallback for Node.js modules in browser environment
		fallback: {
			path: require.resolve("path-browserify"),
			os: require.resolve("os-browserify/browser"),
			crypto: require.resolve("crypto-browserify"),
			stream: require.resolve("stream-browserify"),
			util: require.resolve("util"),
			buffer: require.resolve("buffer"),
			process: require.resolve("process/browser"),
		},
	},

	// Enhanced module rules with error handling
	module: {
		rules: [
			{
				test: /\.(ts|tsx)$/,
				exclude: /node_modules/,
				use: [
					{
						loader: "esbuild-loader",
						options: {
							loader: "tsx",
							target: "es2019",
							sourcemap: true,
							// Enhanced error handling
							logLevel: "info",
							// Fallback for compilation errors
							keepNames: true,
							minify: false,
							// TypeScript configuration
							tsconfigRaw: {
								compilerOptions: {
									sourceMap: true,
									inlineSourceMap: false,
									inlineSources: false,
									declaration: false,
									declarationMap: false,
								},
							},
						},
					},
				],
			},
			{
				test: /\.(js|jsx)$/,
				exclude: /node_modules/,
				use: [
					{
						loader: "esbuild-loader",
						options: {
							loader: "jsx",
							target: "es2019",
							sourcemap: true,
							logLevel: "info",
							keepNames: true,
							minify: false,
						},
					},
				],
			},
			{
				test: /\.css$/,
				use: [
					"style-loader",
					{
						loader: "css-loader",
						options: {
							sourceMap: true,
							importLoaders: 1,
						},
					},
				],
			},
			{
				test: /\.(png|jpe?g|gif|svg|ico)$/,
				type: "asset/resource",
				generator: {
					filename: "assets/images/[name].[hash][ext]",
				},
			},
			{
				test: /\.(woff|woff2|eot|ttf|otf)$/,
				type: "asset/resource",
				generator: {
					filename: "assets/fonts/[name].[hash][ext]",
				},
			},
		],
	},

	// Enhanced optimization for debug mode
	optimization: {
		// Disable minimization in debug mode
		minimize: false,

		// Enhanced chunk splitting for better debugging
		splitChunks: {
			chunks: "all",
			cacheGroups: {
				vendor: {
					test: /[\\/]node_modules[\\/]/,
					name: "vendors",
					chunks: "all",
					priority: 10,
				},
				common: {
					name: "common",
					minChunks: 2,
					chunks: "all",
					priority: 5,
					reuseExistingChunk: true,
				},
			},
		},

		// Enhanced runtime chunk for better debugging
		runtimeChunk: {
			name: "runtime",
		},

		// Module IDs for consistent debugging
		moduleIds: "named",
		chunkIds: "named",
	},

	// Enhanced performance configuration
	performance: {
		hints: "warning",
		maxEntrypointSize: 512000,
		maxAssetSize: 512000,
	},

	// Enhanced stats configuration for better error reporting
	stats: {
		colors: true,
		modules: false,
		chunks: false,
		chunkModules: false,
		entrypoints: false,
		assets: false,
		version: false,
		builtAt: true,
		timings: true,
		errors: true,
		errorDetails: true,
		warnings: true,
		warningsFilter: [
			// Filter out known warnings that don't affect functionality
			/export .* was not found in/,
			/Critical dependency: the request of a dependency is an expression/,
		],
	},

	// Enhanced cache configuration for faster rebuilds
	cache: {
		type: "filesystem",
		buildDependencies: {
			config: [__filename],
		},
		cacheDirectory: path.resolve(__dirname, "../.cache/webpack"),
	},

	// Enhanced experiments for modern features
	experiments: {
		topLevelAwait: true,
		asyncWebAssembly: true,
	},

	// Enhanced externals configuration
	externals: {
		// Externalize Node.js built-ins that shouldn't be bundled
		fs: "commonjs fs",
		path: "commonjs path",
		os: "commonjs os",
	},

	// Enhanced plugins configuration
	plugins: [
		// Define plugin for environment variables
		new (require("webpack").DefinePlugin)({
			"process.env.NODE_ENV": JSON.stringify("development"),
			"process.env.DEBUG_MODE": JSON.stringify(true),
			__DEV__: JSON.stringify(true),
		}),

		// Provide plugin for polyfills
		new (require("webpack").ProvidePlugin)({
			process: "process/browser",
			Buffer: ["buffer", "Buffer"],
		}),

		// Progress plugin for build feedback
		new (require("webpack").ProgressPlugin)({
			activeModules: true,
			entries: true,
			modules: true,
			modulesCount: 5000,
			profile: false,
			dependencies: true,
			dependenciesCount: 10000,
			percentBy: null,
		}),
	],

	// Enhanced watch options
	watchOptions: {
		aggregateTimeout: 300,
		poll: false,
		ignored: ["**/node_modules", "**/.git", "**/dist", "**/.cache", "**/build"],
	},

	// Enhanced dev server configuration (if used)
	devServer: {
		hot: true,
		liveReload: true,
		compress: true,
		historyApiFallback: true,
		client: {
			logging: "info",
			overlay: {
				errors: true,
				warnings: false,
			},
			progress: true,
		},
		static: {
			directory: path.join(__dirname, "../public"),
			publicPath: "/",
		},
	},

	// Enhanced error handling
	ignoreWarnings: [
		// Ignore specific warnings that don't affect functionality
		/Critical dependency: the request of a dependency is an expression/,
		/export .* was not found in/,
	],
};

// Export configuration with error handling wrapper
module.exports = (env = {}, _argv = {}) => {
	try {
		const config = module.exports;

		// Apply environment-specific overrides
		if (env.analyze) {
			const BundleAnalyzerPlugin =
				require("webpack-bundle-analyzer").BundleAnalyzerPlugin;
			config.plugins.push(
				new BundleAnalyzerPlugin({
					analyzerMode: "server",
					openAnalyzer: true,
				}),
			);
		}

		if (env.profile) {
			config.profile = true;
			config.stats.timings = true;
			config.stats.builtAt = true;
		}

		return config;
	} catch (error) {
		console.error("❌ Error in webpack debug configuration:", error);

		// Return minimal fallback configuration
		return {
			mode: "development",
			devtool: "eval",
			entry: "./src/index.ts",
			output: {
				path: path.resolve(__dirname, "../dist"),
				filename: "bundle.js",
			},
			resolve: {
				extensions: [".ts", ".js"],
			},
			module: {
				rules: [
					{
						test: /\.ts$/,
						use: "ts-loader",
						exclude: /node_modules/,
					},
				],
			},
		};
	}
};
