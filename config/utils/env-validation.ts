/**
 * Environment variable validation utilities
 * Ensures mandatory configuration is present and throws clear errors when missing
 */

export interface RequiredEnvConfig {
	key: string;
	description: string;
	example?: string;
}

/**
 * Validates that required environment variables are present
 * Throws a detailed error if any are missing
 */
export function validateRequiredEnv(
	env: any,
	requiredConfigs: RequiredEnvConfig[],
): void {
	const missing: RequiredEnvConfig[] = [];

	for (const config of requiredConfigs) {
		const value = env(config.key);
		if (!value || value === "") {
			missing.push(config);
		}
	}

	if (missing.length > 0) {
		const errorMessage = [
			"❌ Missing required environment variables:",
			"",
			...missing.map(
				(config) =>
					`  • ${config.key}: ${config.description}${config.example ? ` (e.g., ${config.example})` : ""}`,
			),
			"",
			"💡 Please add these variables to your .env file",
			"📖 See .env.example for reference",
		].join("\n");

		throw new Error(errorMessage);
	}
}

/**
 * Gets a required environment variable or throws an error
 */
export function getRequiredEnv(
	env: any,
	key: string,
	description: string,
	example?: string,
): string {
	const value = env(key);
	if (!value || value === "") {
		const errorMessage = [
			`❌ Missing required environment variable: ${key}`,
			`   Description: ${description}`,
			example ? `   Example: ${example}` : "",
			"",
			"💡 Please add this variable to your .env file",
			"📖 See .env.example for reference",
		]
			.filter(Boolean)
			.join("\n");

		throw new Error(errorMessage);
	}
	return value;
}

/**
 * Gets a required integer environment variable or throws an error
 */
export function getRequiredEnvInt(
	env: any,
	key: string,
	description: string,
	example?: string,
): number {
	const value = getRequiredEnv(env, key, description, example);
	const parsed = parseInt(value, 10);

	if (Number.isNaN(parsed)) {
		throw new Error(
			`❌ Environment variable ${key} must be a valid integer, got: ${value}`,
		);
	}

	return parsed;
}

/**
 * Gets a required boolean environment variable or throws an error
 */
export function getRequiredEnvBool(
	env: any,
	key: string,
	description: string,
	example?: string,
): boolean {
	const value = getRequiredEnv(env, key, description, example);

	if (!["true", "false", "1", "0"].includes(value.toLowerCase())) {
		throw new Error(
			`❌ Environment variable ${key} must be a boolean (true/false), got: ${value}`,
		);
	}

	return value.toLowerCase() === "true" || value === "1";
}
