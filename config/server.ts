import { getRequiredEnv, validateRequiredEnv } from "./utils/env-validation";

// Ensure .env file is loaded before configuration
if (process.env.NODE_ENV !== "production") {
	require("dotenv").config();
}

export default ({ env }) => {
	// Validate required server configuration
	validateRequiredEnv(env, [
		{
			key: "HOST",
			description: "Server host address",
			example: "0.0.0.0",
		},
		{
			key: "PORT",
			description: "Server port number",
			example: "1337",
		},
		{
			key: "APP_KEYS",
			description: "Application encryption keys (comma-separated)",
			example: "key1,key2,key3,key4",
		},
	]);

	const appKeys = env.array("APP_KEYS");

	// Validate APP_KEYS format and security
	if (!appKeys || appKeys.length < 4) {
		throw new Error("❌ APP_KEYS must contain at least 4 keys for security");
	}

	// Check if using default/example keys (security risk)
	const defaultKeys = [
		"UCeqYICI8aB0PpNKdoh9GQ==",
		"IJmnG3xGv38WaJEH/R0JEQ==",
		"Nk3jylbUN7uaubqG7WadUw==",
		"Z7ThL2Pj65WvTvcblLdV0g==",
	];

	const hasDefaultKeys = appKeys.some((key) => defaultKeys.includes(key));
	if (hasDefaultKeys) {
		console.warn(
			"⚠️  WARNING: Using default APP_KEYS. Generate new keys for production!",
		);
	}

	return {
		host: getRequiredEnv(env, "HOST", "Server host address", "0.0.0.0"),
		port:
			env.int("PORT") ||
			(() => {
				throw new Error("❌ PORT must be a valid integer");
			})(),
		app: {
			keys: appKeys,
		},
	};
};
