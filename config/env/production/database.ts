export default ({ env }) => ({
	connection: {
		client: "postgres",
		connection: {
			connectionString: env("DATABASE_URL"),
			host: env("DATABASE_HOST", "localhost"),
			port: env.int("DATABASE_PORT", 5432),
			database: env("DATABASE_NAME", "strapi_adapty_cms"),
			user: env("DATABASE_USERNAME", "strapi"),
			password: env("DATABASE_PASSWORD", "strapi"),
			ssl: env.bool("DATABASE_SSL", true) && {
				rejectUnauthorized: env.bool("DATABASE_SSL_REJECT_UNAUTHORIZED", false),
			},
			schema: env("DATABASE_SCHEMA", "public"),
		},
		pool: {
			min: env.int("DATABASE_POOL_MIN", 2),
			max: env.int("DATABASE_POOL_MAX", 20),
		},
		acquireConnectionTimeout: env.int("DATABASE_CONNECTION_TIMEOUT", 60000),
	},
});
