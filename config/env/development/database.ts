import path from "node:path";
import { getRequiredEnv } from "../../utils/env-validation";

// Ensure .env file is loaded before configuration
if (process.env.NODE_ENV !== "production") {
	require("dotenv").config();
}

export default ({ env }) => {
	// In development, we still require DATABASE_CLIENT to be explicitly set
	const client = getRequiredEnv(
		env,
		"DATABASE_CLIENT",
		"Database client type for development",
		"sqlite, postgres, or mysql",
	);

	let connection;

	switch (client) {
		case "sqlite":
			connection = {
				connection: {
					filename: path.join(
						__dirname,
						"..",
						"..",
						"..",
						getRequiredEnv(
							env,
							"DATABASE_FILENAME",
							"SQLite database file path",
							".tmp/data.db",
						),
					),
				},
				useNullAsDefault: true,
			};
			break;
		case "postgres":
			connection = {
				connection: {
					host: getRequiredEnv(
						env,
						"DATABASE_HOST",
						"PostgreSQL database host",
						"localhost",
					),
					port:
						env.int("DATABASE_PORT") ||
						(() => {
							throw new Error(
								"❌ DATABASE_PORT is required for PostgreSQL in development",
							);
						})(),
					database: getRequiredEnv(
						env,
						"DATABASE_NAME",
						"PostgreSQL database name",
						"strapi_adapty_cms_dev",
					),
					user: getRequiredEnv(
						env,
						"DATABASE_USERNAME",
						"PostgreSQL username",
						"strapi",
					),
					password: getRequiredEnv(
						env,
						"DATABASE_PASSWORD",
						"PostgreSQL password",
						"your_password",
					),
					ssl: env.bool("DATABASE_SSL", false),
					schema: env("DATABASE_SCHEMA", "public"),
				},
				pool: {
					min: env.int("DATABASE_POOL_MIN", 2),
					max: env.int("DATABASE_POOL_MAX", 10),
				},
			};
			break;
		case "mysql":
			connection = {
				connection: {
					host: getRequiredEnv(
						env,
						"DATABASE_HOST",
						"MySQL database host",
						"localhost",
					),
					port:
						env.int("DATABASE_PORT") ||
						(() => {
							throw new Error(
								"❌ DATABASE_PORT is required for MySQL in development",
							);
						})(),
					database: getRequiredEnv(
						env,
						"DATABASE_NAME",
						"MySQL database name",
						"strapi_adapty_cms_dev",
					),
					user: getRequiredEnv(
						env,
						"DATABASE_USERNAME",
						"MySQL username",
						"strapi",
					),
					password: getRequiredEnv(
						env,
						"DATABASE_PASSWORD",
						"MySQL password",
						"your_password",
					),
					ssl: env.bool("DATABASE_SSL", false),
				},
				pool: {
					min: env.int("DATABASE_POOL_MIN", 2),
					max: env.int("DATABASE_POOL_MAX", 10),
				},
			};
			break;
		default:
			throw new Error(
				`❌ Unsupported DATABASE_CLIENT: ${client}. Supported clients: sqlite, postgres, mysql`,
			);
	}

	return {
		connection: {
			client,
			...connection,
			acquireConnectionTimeout: env.int("DATABASE_CONNECTION_TIMEOUT", 60000),
		},
	};
};
