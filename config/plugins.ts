({ env }) => ({
	// Internationalization plugin for multi-language support
	i18n: {
		enabled: true,
		config: {
			defaultLocale: "en",
			locales: ["en", "es", "fr", "de", "ja", "ko", "zh"],
		},
	},

	// Enhanced upload plugin for media management with CDN integration
	upload: {
		enabled: true,
		config: {
			sizeLimit: 50 * 1024 * 1024, // 50MB for paywall assets
			breakpoints: {
				xlarge: 1920,
				large: 1000,
				medium: 750,
				small: 500,
				xsmall: 64,
			},
			// Custom provider configuration for CDN
			provider: env("UPLOAD_PROVIDER", "local"),
			providerOptions: {
				// Local storage configuration
				...(env("UPLOAD_PROVIDER") === "local" && {
					sizeLimit: 50 * 1024 * 1024,
				}),
				// AWS S3 configuration for CDN
				...(env("UPLOAD_PROVIDER") === "aws-s3" && {
					accessKeyId: env("AWS_ACCESS_KEY_ID"),
					secretAccessKey: env("AWS_ACCESS_SECRET"),
					region: env("AWS_REGION", "us-east-1"),
					bucket: env("AWS_BUCKET"),
					cdn: env("AWS_CDN_URL"),
				}),
				// Cloudinary configuration
				...(env("UPLOAD_PROVIDER") === "cloudinary" && {
					cloud_name: env("CLOUDINARY_NAME"),
					api_key: env("CLOUDINARY_KEY"),
					api_secret: env("CLOUDINARY_SECRET"),
				}),
			},
			// Image optimization settings
			responsive: true,
			quality: 85,
			progressive: true,
		},
	},

	// Users and permissions plugin
	"users-permissions": {
		enabled: true,
		config: {
			jwt: {
				expiresIn: "7d",
			},
		},
	},

	// Cloud plugin for deployment
	cloud: {
		enabled: true,
	},
});
