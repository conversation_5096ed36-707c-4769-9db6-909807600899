/**
 * Tests for media management system
 */

import {
	afterAll,
	beforeAll,
	beforeEach,
	describe,
	expect,
	it,
	jest,
} from "@jest/globals";
import fs from "fs-extra";

// Mock Strapi instance
const _mockStrapi = {
	entityService: {
		findOne: jest.fn(),
		findMany: jest.fn(),
		create: jest.fn(),
		update: jest.fn(),
	},
	service: jest.fn(),
	log: {
		error: jest.fn(),
		info: jest.fn(),
	},
	dirs: {
		static: {
			public: "/tmp/test-uploads",
		},
	},
};

// Mock file data
const mockImageFile = {
	id: 1,
	name: "test-image.jpg",
	mime: "image/jpeg",
	size: 1024 * 1024, // 1MB
	url: "/uploads/test-image.jpg",
	buffer: Buffer.from("fake-image-data"),
};

const mockPaywallData = {
	id: 1,
	placement_id: "test_placement_001",
	name: "Test Paywall",
};

describe("Media Management System", () => {
	beforeAll(async () => {
		// Create test upload directory
		await fs.ensureDir("/tmp/test-uploads");
		await fs.ensureDir("/tmp/test-uploads/optimized");
	});

	afterAll(async () => {
		// Clean up test files
		await fs.remove("/tmp/test-uploads");
	});

	beforeEach(() => {
		jest.clearAllMocks();
	});

	describe("Media Validation", () => {
		it("should validate file size limits", async () => {
			const largeFile = {
				...mockImageFile,
				size: 60 * 1024 * 1024, // 60MB
			};

			const uploadService = {
				validatePaywallMedia: async (file: any, _field?: string) => {
					const errors: string[] = [];
					const maxSize = 50 * 1024 * 1024; // 50MB

					if (file.size > maxSize) {
						errors.push(
							`File size exceeds maximum allowed size of ${maxSize / (1024 * 1024)}MB`,
						);
					}

					return {
						valid: errors.length === 0,
						errors,
					};
				},
			};

			const result = await uploadService.validatePaywallMedia(
				largeFile,
				"background_image",
			);

			expect(result.valid).toBe(false);
			expect(result.errors).toContain(
				"File size exceeds maximum allowed size of 50MB",
			);
		});

		it("should validate image dimensions for different fields", async () => {
			const smallImage = {
				...mockImageFile,
				width: 50,
				height: 50,
			};

			const uploadService = {
				validatePaywallMedia: async (file: any, field?: string) => {
					const errors: string[] = [];

					const minDimensions: Record<
						string,
						{ width: number; height: number }
					> = {
						background_image: { width: 800, height: 600 },
						logo: { width: 100, height: 100 },
						icon_image: { width: 32, height: 32 },
						author_avatar: { width: 64, height: 64 },
					};

					const minDim = minDimensions[field];
					if (minDim && file.width && file.height) {
						if (file.width < minDim.width || file.height < minDim.height) {
							errors.push(
								`Image dimensions must be at least ${minDim.width}x${minDim.height}px`,
							);
						}
					}

					return {
						valid: errors.length === 0,
						errors,
					};
				},
			};

			const result = await uploadService.validatePaywallMedia(
				smallImage,
				"background_image",
			);

			expect(result.valid).toBe(false);
			expect(result.errors).toContain(
				"Image dimensions must be at least 800x600px",
			);
		});

		it("should validate allowed MIME types", async () => {
			const invalidFile = {
				...mockImageFile,
				mime: "application/x-executable",
			};

			const uploadService = {
				validatePaywallMedia: async (file: any, field?: string) => {
					const errors: string[] = [];

					const allowedFormats: Record<string, string[]> = {
						background_image: ["image/jpeg", "image/png", "image/webp"],
						logo: ["image/png", "image/svg+xml", "image/webp"],
						icon_image: ["image/png", "image/svg+xml", "image/webp"],
						author_avatar: ["image/jpeg", "image/png", "image/webp"],
					};

					const allowed = allowedFormats[field] || [
						"image/jpeg",
						"image/png",
						"image/webp",
						"image/svg+xml",
					];
					if (!allowed.includes(file.mime)) {
						errors.push(
							`File format not allowed. Supported formats: ${allowed.join(", ")}`,
						);
					}

					return {
						valid: errors.length === 0,
						errors,
					};
				},
			};

			const result = await uploadService.validatePaywallMedia(
				invalidFile,
				"background_image",
			);

			expect(result.valid).toBe(false);
			expect(result.errors[0]).toContain("File format not allowed");
		});
	});

	describe("Image Optimization", () => {
		it("should create optimized versions for different profiles", async () => {
			const uploadService = {
				getOptimizationProfiles: (field?: string) => {
					const baseProfiles = {
						thumbnail: {
							resize: { width: 150, height: 150, fit: "cover" },
							format: "webp",
							quality: 80,
						},
						mobile: {
							resize: { width: 400, height: 400, fit: "inside" },
							format: "webp",
							quality: 85,
						},
					};

					const fieldProfiles: Record<string, any> = {
						background_image: {
							...baseProfiles,
							hero: {
								resize: { width: 1920, height: 1080, fit: "cover" },
								format: "webp",
								quality: 85,
							},
						},
					};

					return fieldProfiles[field] || baseProfiles;
				},
			};

			const profiles =
				uploadService.getOptimizationProfiles("background_image");

			expect(profiles).toHaveProperty("thumbnail");
			expect(profiles).toHaveProperty("mobile");
			expect(profiles).toHaveProperty("hero");
			expect(profiles.hero.resize.width).toBe(1920);
			expect(profiles.hero.resize.height).toBe(1080);
		});

		it("should generate appropriate alt text for different fields", async () => {
			const uploadService = {
				generateAltText: (field?: string): string => {
					const altTexts: Record<string, string> = {
						background_image: "Paywall background image",
						logo: "Company logo",
						icon_image: "Feature icon",
						author_avatar: "Author profile picture",
					};

					return altTexts[field] || "Paywall asset";
				},
			};

			expect(uploadService.generateAltText("background_image")).toBe(
				"Paywall background image",
			);
			expect(uploadService.generateAltText("logo")).toBe("Company logo");
			expect(uploadService.generateAltText("icon_image")).toBe("Feature icon");
			expect(uploadService.generateAltText("author_avatar")).toBe(
				"Author profile picture",
			);
			expect(uploadService.generateAltText("unknown_field")).toBe(
				"Paywall asset",
			);
		});

		it("should generate ETag for caching", async () => {
			const uploadService = {
				generateETag: (file: any): string => {
					const content = JSON.stringify({
						id: file.id,
						updated_at: file.updated_at || new Date(),
						size: file.size,
					});

					let hash = 0;
					for (let i = 0; i < content.length; i++) {
						const char = content.charCodeAt(i);
						hash = (hash << 5) - hash + char;
						hash = hash & hash;
					}

					return Math.abs(hash).toString(16);
				},
			};

			const etag1 = uploadService.generateETag(mockImageFile);
			const etag2 = uploadService.generateETag({
				...mockImageFile,
				size: 2048,
			});

			expect(typeof etag1).toBe("string");
			expect(etag1.length).toBeGreaterThan(0);
			expect(etag1).not.toBe(etag2); // Different files should have different ETags
		});
	});

	describe("Folder Organization", () => {
		it("should organize files by field type", async () => {
			const uploadService = {
				getFieldFolder: (field: string) => {
					const folderMap: Record<string, string> = {
						background_image: "paywalls/backgrounds",
						logo: "paywalls/logos",
						icon_image: "paywalls/icons",
						author_avatar: "paywalls/testimonials",
					};

					return folderMap[field] || "paywalls";
				},
			};

			expect(uploadService.getFieldFolder("background_image")).toBe(
				"paywalls/backgrounds",
			);
			expect(uploadService.getFieldFolder("logo")).toBe("paywalls/logos");
			expect(uploadService.getFieldFolder("icon_image")).toBe("paywalls/icons");
			expect(uploadService.getFieldFolder("author_avatar")).toBe(
				"paywalls/testimonials",
			);
			expect(uploadService.getFieldFolder("unknown_field")).toBe("paywalls");
		});

		it("should create folder structure with paywall ID", async () => {
			const uploadService = {
				getFolderPath: (field: string, paywall?: any) => {
					let folderPath = "paywalls";

					if (field) {
						switch (field) {
							case "background_image":
								folderPath = "paywalls/backgrounds";
								break;
							case "logo":
								folderPath = "paywalls/logos";
								break;
							case "icon_image":
								folderPath = "paywalls/icons";
								break;
							case "author_avatar":
								folderPath = "paywalls/testimonials";
								break;
							default:
								folderPath = "paywalls";
						}

						if (paywall?.placement_id) {
							folderPath = `${folderPath}/${paywall.placement_id}`;
						}
					}

					return folderPath;
				},
			};

			const folderPath = uploadService.getFolderPath(
				"background_image",
				mockPaywallData,
			);
			expect(folderPath).toBe("paywalls/backgrounds/test_placement_001");
		});
	});

	describe("CDN Integration", () => {
		it("should support multiple CDN providers", () => {
			const cdnConfigs = {
				aws: {
					provider: "aws-s3",
					accessKeyId: "test-key",
					secretAccessKey: "test-secret",
					region: "us-east-1",
					bucket: "test-bucket",
					cdn: "https://cdn.example.com",
				},
				cloudinary: {
					provider: "cloudinary",
					cloud_name: "test-cloud",
					api_key: "test-key",
					api_secret: "test-secret",
				},
			};

			expect(cdnConfigs.aws.provider).toBe("aws-s3");
			expect(cdnConfigs.cloudinary.provider).toBe("cloudinary");
			expect(cdnConfigs.aws.cdn).toBe("https://cdn.example.com");
		});

		it("should generate CDN URLs for optimized images", () => {
			const uploadService = {
				getCDNUrl: (file: any, profile?: string, cdnBase?: string) => {
					const baseUrl = cdnBase || "https://cdn.example.com";
					let url = file.url;

					if (profile && file.paywall_optimizations?.[profile]) {
						url = file.paywall_optimizations[profile].url;
					}

					return `${baseUrl}${url}`;
				},
			};

			const fileWithOptimizations = {
				...mockImageFile,
				paywall_optimizations: {
					mobile: {
						url: "/uploads/optimized/test-image_mobile.webp",
					},
				},
			};

			const cdnUrl = uploadService.getCDNUrl(
				fileWithOptimizations,
				"mobile",
				"https://cdn.example.com",
			);
			expect(cdnUrl).toBe(
				"https://cdn.example.com/uploads/optimized/test-image_mobile.webp",
			);
		});
	});

	describe("Media Library Extensions", () => {
		it("should provide folder structure for admin interface", () => {
			const mediaLibrary = {
				getFolders: () => ({
					paywalls: {
						name: "Paywalls",
						description: "Media assets for paywall configurations",
						subfolders: {
							backgrounds: "Background images",
							logos: "Brand logos",
							icons: "Feature icons",
							avatars: "Author profile pictures",
						},
					},
					themes: {
						name: "Themes",
						description: "Theme-related media assets",
					},
					general: {
						name: "General",
						description: "General purpose media files",
					},
				}),
			};

			const folders = mediaLibrary.getFolders();

			expect(folders).toHaveProperty("paywalls");
			expect(folders).toHaveProperty("themes");
			expect(folders).toHaveProperty("general");
			expect(folders.paywalls.subfolders).toHaveProperty("backgrounds");
			expect(folders.paywalls.subfolders).toHaveProperty("logos");
		});

		it("should provide translations for folder names", () => {
			const translations = {
				en: {
					"upload.folders.title": "Media Folders",
					"upload.folders.paywalls": "Paywalls",
					"upload.folders.paywalls.description":
						"Media assets for paywall configurations",
					"upload.folders.backgrounds": "Backgrounds",
					"upload.folders.logos": "Logos",
					"upload.folders.icons": "Icons",
					"upload.folders.testimonials": "Testimonials",
				},
			};

			expect(translations.en["upload.folders.title"]).toBe("Media Folders");
			expect(translations.en["upload.folders.paywalls"]).toBe("Paywalls");
			expect(translations.en["upload.folders.backgrounds"]).toBe("Backgrounds");
		});
	});
});
