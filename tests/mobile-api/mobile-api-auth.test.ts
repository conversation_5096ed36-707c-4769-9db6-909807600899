/**
 * Mobile API v1 - Authentication Tests
 * Tests for mobile API authentication and authorization
 */

import jwt from "jsonwebtoken";
import { cleanupStrapi, setupStrapi } from "../setup";

describe("Mobile API v1 Authentication", () => {
	let strapi: any;
	let testApp: any;

	beforeAll(async () => {
		strapi = await setupStrapi();

		// Create test mobile app
		testApp = await strapi.entityService.create("api::mobile-app.mobile-app", {
			data: {
				app_id: "auth-test-app",
				name: "Auth Test App",
				platform: "ios",
				bundle_id: "com.test.authapp",
				api_key: "auth-test-key",
				secret_key: "auth-test-secret",
				min_version: "1.0.0",
				permissions: ["paywall:read"],
				is_active: true,
			},
		});
	});

	afterAll(async () => {
		await cleanupStrapi(strapi);
	});

	describe("Token Generation", () => {
		it("should generate valid JWT token", async () => {
			const authService = strapi.service("api::mobile.v1.auth");
			const token = await authService.generateToken(
				testApp.app_id,
				"test-user",
			);

			expect(token).toBeDefined();
			expect(typeof token).toBe("string");

			// Verify token structure
			const jwtSecret =
				process.env.MOBILE_API_JWT_SECRET ||
				strapi.config.get("server.app.keys")[0];
			const decoded = jwt.verify(token, jwtSecret) as any;

			expect(decoded.app_id).toBe(testApp.app_id);
			expect(decoded.permissions).toContain("paywall:read");
		});

		it("should reject invalid app ID", async () => {
			const authService = strapi.service("api::mobile.v1.auth");

			await expect(
				authService.generateToken("invalid-app-id", "test-user"),
			).rejects.toThrow("App not found or inactive");
		});
	});

	describe("API Credential Validation", () => {
		it("should validate correct API credentials", async () => {
			const authService = strapi.service("api::mobile.v1.auth");
			const app = await authService.validateApiCredentials(
				testApp.api_key,
				testApp.secret_key,
			);

			expect(app).toBeDefined();
			expect(app.app_id).toBe(testApp.app_id);
		});

		it("should reject invalid API credentials", async () => {
			const authService = strapi.service("api::mobile.v1.auth");
			const app = await authService.validateApiCredentials(
				"invalid-key",
				"invalid-secret",
			);

			expect(app).toBeNull();
		});
	});

	describe("Authentication Middleware", () => {
		let validToken: string;

		beforeEach(async () => {
			const authService = strapi.service("api::mobile.v1.auth");
			validToken = await authService.generateToken(testApp.app_id, "test-user");
		});

		it("should accept valid Bearer token", async () => {
			await strapi.request
				.get("/api/mobile/v1/health")
				.set("Authorization", `Bearer ${validToken}`)
				.expect(200);
		});

		it("should reject missing Authorization header", async () => {
			await strapi.request
				.get(`/api/mobile/v1/paywalls/test-placement`)
				.expect(401);
		});

		it("should reject invalid Bearer token format", async () => {
			await strapi.request
				.get(`/api/mobile/v1/paywalls/test-placement`)
				.set("Authorization", "Invalid token-format")
				.expect(401);
		});

		it("should reject expired token", async () => {
			// Create expired token
			const jwtSecret =
				process.env.MOBILE_API_JWT_SECRET ||
				strapi.config.get("server.app.keys")[0];
			const expiredToken = jwt.sign(
				{
					app_id: testApp.app_id,
					permissions: ["paywall:read"],
					exp: Math.floor(Date.now() / 1000) - 3600, // Expired 1 hour ago
				},
				jwtSecret,
			);

			await strapi.request
				.get(`/api/mobile/v1/paywalls/test-placement`)
				.set("Authorization", `Bearer ${expiredToken}`)
				.expect(401);
		});

		it("should reject token with invalid signature", async () => {
			const invalidToken = jwt.sign(
				{
					app_id: testApp.app_id,
					permissions: ["paywall:read"],
				},
				"wrong-secret",
			);

			await strapi.request
				.get(`/api/mobile/v1/paywalls/test-placement`)
				.set("Authorization", `Bearer ${invalidToken}`)
				.expect(401);
		});
	});

	describe("Permission Checking", () => {
		it("should allow access with correct permissions", async () => {
			const authService = strapi.service("api::mobile.v1.auth");
			const token = await authService.generateToken(
				testApp.app_id,
				"test-user",
				["paywall:read"],
			);

			// Create test paywall
			const paywall = await strapi.entityService.create(
				"api::paywall.paywall",
				{
					data: {
						name: "Permission Test Paywall",
						placement_id: "permission-test-placement",
						title: "Test",
						adapty_sync_status: "synced",
						publishedAt: new Date(),
					},
				},
			);

			try {
				await strapi.request
					.get(`/api/mobile/v1/paywalls/${paywall.placement_id}`)
					.set("Authorization", `Bearer ${token}`)
					.expect(200);
			} finally {
				await strapi.entityService.delete("api::paywall.paywall", paywall.id);
			}
		});

		it("should deny access without required permissions", async () => {
			const authService = strapi.service("api::mobile.v1.auth");
			const token = await authService.generateToken(
				testApp.app_id,
				"test-user",
				["analytics:write"], // Missing paywall:read permission
			);

			await strapi.request
				.get(`/api/mobile/v1/paywalls/test-placement`)
				.set("Authorization", `Bearer ${token}`)
				.expect(403);
		});
	});

	describe("App Version Compatibility", () => {
		let oldVersionApp: any;

		beforeEach(async () => {
			oldVersionApp = await strapi.entityService.create(
				"api::mobile-app.mobile-app",
				{
					data: {
						app_id: "old-version-app",
						name: "Old Version App",
						platform: "android",
						bundle_id: "com.test.oldapp",
						api_key: "old-version-key",
						secret_key: "old-version-secret",
						min_version: "2.0.0", // Require minimum version 2.0.0
						permissions: ["paywall:read"],
						is_active: true,
					},
				},
			);
		});

		afterEach(async () => {
			if (oldVersionApp) {
				await strapi.entityService.delete(
					"api::mobile-app.mobile-app",
					oldVersionApp.id,
				);
			}
		});

		it("should reject requests from unsupported app versions", async () => {
			const jwtSecret =
				process.env.MOBILE_API_JWT_SECRET ||
				strapi.config.get("server.app.keys")[0];
			const token = jwt.sign(
				{
					app_id: oldVersionApp.app_id,
					app_version: "1.5.0", // Below minimum version
					platform: "android",
					permissions: ["paywall:read"],
				},
				jwtSecret,
			);

			await strapi.request
				.get(`/api/mobile/v1/paywalls/test-placement`)
				.set("Authorization", `Bearer ${token}`)
				.expect(400);
		});

		it("should accept requests from supported app versions", async () => {
			const jwtSecret =
				process.env.MOBILE_API_JWT_SECRET ||
				strapi.config.get("server.app.keys")[0];
			const token = jwt.sign(
				{
					app_id: oldVersionApp.app_id,
					app_version: "2.1.0", // Above minimum version
					platform: "android",
					permissions: ["paywall:read"],
				},
				jwtSecret,
			);

			await strapi.request
				.get("/api/mobile/v1/health")
				.set("Authorization", `Bearer ${token}`)
				.expect(200);
		});
	});

	describe("Usage Tracking", () => {
		it("should record API usage", async () => {
			const authService = strapi.service("api::mobile.v1.auth");
			const token = await authService.generateToken(
				testApp.app_id,
				"usage-test-user",
			);

			// Make API request
			await strapi.request
				.get("/api/mobile/v1/health")
				.set("Authorization", `Bearer ${token}`)
				.expect(200);

			// Check if usage was recorded
			const usage = await strapi.entityService.findMany(
				"api::mobile-api-usage.mobile-api-usage",
				{
					filters: {
						app_id: testApp.app_id,
						endpoint: "/api/mobile/v1/health",
					},
					limit: 1,
				},
			);

			expect(usage.length).toBeGreaterThan(0);
			expect(usage[0].app_id).toBe(testApp.app_id);
			expect(usage[0].method).toBe("GET");
		});
	});
});
