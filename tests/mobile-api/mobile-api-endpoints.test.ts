/**
 * Mobile API v1 - Endpoint Tests
 * Comprehensive tests for mobile API endpoints
 */

import { cleanupStrapi, setupStrapi } from "../setup";

describe("Mobile API v1 Endpoints", () => {
	let strapi: any;
	let testApp: any;
	let authToken: string;

	beforeAll(async () => {
		strapi = await setupStrapi();

		// Create test mobile app
		testApp = await strapi.entityService.create("api::mobile-app.mobile-app", {
			data: {
				app_id: "test-app-001",
				name: "Test Mobile App",
				platform: "react_native",
				bundle_id: "com.test.app",
				api_key: "test-api-key",
				secret_key: "test-secret-key",
				min_version: "1.0.0",
				permissions: ["paywall:read", "analytics:write"],
				rate_limits: {
					requests_per_minute: 60,
					burst_limit: 100,
				},
				is_active: true,
			},
		});

		// Generate auth token
		authToken = await strapi
			.service("api::mobile.v1.auth")
			.generateToken(testApp.app_id, "test-user-123", [
				"paywall:read",
				"analytics:write",
			]);
	});

	afterAll(async () => {
		await cleanupStrapi(strapi);
	});

	describe("Health Check Endpoint", () => {
		it("should return healthy status", async () => {
			const response = await strapi.request
				.get("/api/mobile/v1/health")
				.expect(200);

			expect(response.body.status).toBe("healthy");
			expect(response.body.version).toBe("v1");
			expect(response.body.services).toBeDefined();
		});
	});

	describe("API Documentation Endpoint", () => {
		it("should return API documentation", async () => {
			const response = await strapi.request
				.get("/api/mobile/v1/docs")
				.expect(200);

			expect(response.body.data.version).toBe("v1");
			expect(response.body.data.title).toBe("Strapi-Adapty Mobile API");
			expect(response.body.data.endpoints).toBeDefined();
			expect(response.body.data.authentication).toBeDefined();
		});
	});

	describe("Version Info Endpoint", () => {
		it("should return version information", async () => {
			const response = await strapi.request
				.get("/api/mobile/v1/version")
				.expect(200);

			expect(response.body.data.version).toBe("v1");
			expect(response.body.data.supported_features).toBeInstanceOf(Array);
			expect(response.body.data.supported_features).toContain(
				"paywall_delivery",
			);
		});
	});

	describe("Paywall Endpoints", () => {
		let testPaywall: any;

		beforeEach(async () => {
			// Create test paywall
			testPaywall = await strapi.entityService.create("api::paywall.paywall", {
				data: {
					name: "Test Paywall",
					placement_id: "test-placement-001",
					title: "Premium Features",
					subtitle: "Unlock all features",
					description_text: "Get access to premium features",
					cta_text: "Subscribe Now",
					adapty_sync_status: "synced",
					publishedAt: new Date(),
				},
			});
		});

		afterEach(async () => {
			if (testPaywall) {
				await strapi.entityService.delete(
					"api::paywall.paywall",
					testPaywall.id,
				);
			}
		});

		it("should get paywall by placement ID", async () => {
			const response = await strapi.request
				.get(`/api/mobile/v1/paywalls/${testPaywall.placement_id}`)
				.set("Authorization", `Bearer ${authToken}`)
				.expect(200);

			expect(response.body.data.paywall.placement_id).toBe(
				testPaywall.placement_id,
			);
			expect(response.body.data.paywall.content.title).toBe("Premium Features");
			expect(response.body.data.cache_info).toBeDefined();
		});

		it("should return 404 for non-existent paywall", async () => {
			await strapi.request
				.get("/api/mobile/v1/paywalls/non-existent-placement")
				.set("Authorization", `Bearer ${authToken}`)
				.expect(404);
		});

		it("should require authentication", async () => {
			await strapi.request
				.get(`/api/mobile/v1/paywalls/${testPaywall.placement_id}`)
				.expect(401);
		});

		it("should get batch paywalls", async () => {
			const response = await strapi.request
				.post("/api/mobile/v1/paywalls/batch")
				.set("Authorization", `Bearer ${authToken}`)
				.send({
					placement_ids: [testPaywall.placement_id],
					locale: "en",
					device_type: "mobile",
				})
				.expect(200);

			expect(response.body.data).toBeInstanceOf(Array);
			expect(response.body.data.length).toBe(1);
			expect(response.body.meta.total).toBe(1);
		});

		it("should get multiple paywalls by placement IDs", async () => {
			const response = await strapi.request
				.get(`/api/mobile/v1/paywalls/placements/${testPaywall.placement_id}`)
				.set("Authorization", `Bearer ${authToken}`)
				.query({ locale: "en", device_type: "mobile" })
				.expect(200);

			expect(response.body.data).toBeInstanceOf(Array);
			expect(response.body.meta.total).toBe(1);
		});

		it("should record paywall interaction", async () => {
			const response = await strapi.request
				.post(
					`/api/mobile/v1/paywalls/${testPaywall.placement_id}/interactions`,
				)
				.set("Authorization", `Bearer ${authToken}`)
				.send({
					interaction_type: "view",
					user_id: "test-user-123",
					metadata: { source: "test" },
				})
				.expect(200);

			expect(response.body.data.success).toBe(true);
		});
	});

	describe("Bulk Operations", () => {
		let testPaywall: any;

		beforeEach(async () => {
			testPaywall = await strapi.entityService.create("api::paywall.paywall", {
				data: {
					name: "Bulk Test Paywall",
					placement_id: "bulk-test-placement",
					title: "Bulk Test",
					subtitle: "Testing bulk operations",
					description_text: "Bulk operation test paywall",
					cta_text: "Subscribe",
					adapty_sync_status: "synced",
					publishedAt: new Date(),
				},
			});
		});

		afterEach(async () => {
			if (testPaywall) {
				await strapi.entityService.delete(
					"api::paywall.paywall",
					testPaywall.id,
				);
			}
		});

		it("should process bulk operations", async () => {
			const operations = [
				{
					id: 1,
					type: "get_paywall",
					placement_id: testPaywall.placement_id,
					locale: "en",
				},
				{
					id: 2,
					type: "record_interaction",
					placement_id: testPaywall.placement_id,
					interaction_type: "view",
					user_id: "test-user-123",
				},
			];

			const response = await strapi.request
				.post("/api/mobile/v1/paywalls/bulk")
				.set("Authorization", `Bearer ${authToken}`)
				.send({ operations })
				.expect(200);

			expect(response.body.data).toBeInstanceOf(Array);
			expect(response.body.data.length).toBe(2);
			expect(response.body.meta.successful).toBe(2);
			expect(response.body.meta.failed).toBe(0);
		});

		it("should handle invalid operations gracefully", async () => {
			const operations = [
				{
					id: 1,
					type: "invalid_operation",
					placement_id: testPaywall.placement_id,
				},
			];

			const response = await strapi.request
				.post("/api/mobile/v1/paywalls/bulk")
				.set("Authorization", `Bearer ${authToken}`)
				.send({ operations })
				.expect(200);

			expect(response.body.data[0].success).toBe(false);
			expect(response.body.data[0].error).toBe("Unknown operation type");
		});

		it("should record batch interactions", async () => {
			const interactions = [
				{
					id: 1,
					placement_id: testPaywall.placement_id,
					interaction_type: "view",
					user_id: "test-user-123",
				},
				{
					id: 2,
					placement_id: testPaywall.placement_id,
					interaction_type: "click",
					user_id: "test-user-123",
				},
			];

			const response = await strapi.request
				.post("/api/mobile/v1/interactions/batch")
				.set("Authorization", `Bearer ${authToken}`)
				.send({ interactions })
				.expect(200);

			expect(response.body.data.processed).toBe(2);
			expect(response.body.data.successful).toBe(2);
			expect(response.body.data.failed).toBe(0);
		});
	});

	describe("Prefetch Operations", () => {
		let testPaywall: any;

		beforeEach(async () => {
			testPaywall = await strapi.entityService.create("api::paywall.paywall", {
				data: {
					name: "Prefetch Test Paywall",
					placement_id: "prefetch-test-placement",
					title: "Prefetch Test",
					subtitle: "Testing prefetch",
					description_text: "Prefetch test paywall",
					cta_text: "Subscribe",
					adapty_sync_status: "synced",
					publishedAt: new Date(),
				},
			});
		});

		afterEach(async () => {
			if (testPaywall) {
				await strapi.entityService.delete(
					"api::paywall.paywall",
					testPaywall.id,
				);
			}
		});

		it("should prefetch paywalls for offline use", async () => {
			const response = await strapi.request
				.post("/api/mobile/v1/paywalls/prefetch")
				.set("Authorization", `Bearer ${authToken}`)
				.send({
					placement_ids: [testPaywall.placement_id],
					locale: "en",
					device_type: "mobile",
				})
				.expect(200);

			expect(response.body.data).toBeInstanceOf(Array);
			expect(response.body.data[0].prefetch_info).toBeDefined();
			expect(response.body.data[0].prefetch_info.cached_at).toBeDefined();
			expect(response.body.data[0].prefetch_info.expires_at).toBeDefined();
			expect(response.body.meta.prefetch_expires).toBeDefined();
		});

		it("should limit prefetch requests", async () => {
			const placementIds = Array.from(
				{ length: 25 },
				(_, i) => `placement-${i}`,
			);

			await strapi.request
				.post("/api/mobile/v1/paywalls/prefetch")
				.set("Authorization", `Bearer ${authToken}`)
				.send({
					placement_ids: placementIds,
					locale: "en",
				})
				.expect(400);
		});
	});

	describe("Rate Limiting", () => {
		it("should enforce rate limits", async () => {
			// Make multiple rapid requests to trigger rate limiting
			const requests = Array.from({ length: 65 }, () =>
				strapi.request
					.get("/api/mobile/v1/health")
					.set("Authorization", `Bearer ${authToken}`),
			);

			const responses = await Promise.allSettled(requests);

			// Some requests should be rate limited (429 status)
			const rateLimitedResponses = responses.filter(
				(result: any) => result.value?.status === 429,
			);

			expect(rateLimitedResponses.length).toBeGreaterThan(0);
		});
	});

	describe("Error Handling", () => {
		it("should handle invalid JSON gracefully", async () => {
			await strapi.request
				.post("/api/mobile/v1/paywalls/batch")
				.set("Authorization", `Bearer ${authToken}`)
				.set("Content-Type", "application/json")
				.send("invalid json")
				.expect(400);
		});

		it("should handle missing required fields", async () => {
			await strapi.request
				.post("/api/mobile/v1/paywalls/batch")
				.set("Authorization", `Bearer ${authToken}`)
				.send({})
				.expect(400);
		});

		it("should handle invalid placement IDs", async () => {
			await strapi.request
				.get("/api/mobile/v1/paywalls/placements/")
				.set("Authorization", `Bearer ${authToken}`)
				.expect(400);
		});
	});
});
