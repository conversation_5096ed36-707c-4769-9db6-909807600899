/**
 * Jest test setup file
 */

// Global test setup
beforeEach(() => {
	// Reset console methods to avoid noise in tests
	jest.spyOn(console, "log").mockImplementation(() => {});
	jest.spyOn(console, "info").mockImplementation(() => {});
	jest.spyOn(console, "warn").mockImplementation(() => {});
	jest.spyOn(console, "error").mockImplementation(() => {});
});

afterEach(() => {
	// Restore console methods
	jest.restoreAllMocks();
});
