/**
 * Tests for A/B Test Manager
 */

import {
	afterEach,
	beforeEach,
	describe,
	expect,
	it,
	jest,
} from "@jest/globals";
import { ABTestManager } from "../../src/services/ab-testing/ab-test-manager";

// Mock Strapi
const mockStrapi = {
	entityService: {
		findOne: jest.fn(),
		findMany: jest.fn(),
		create: jest.fn(),
		update: jest.fn(),
		delete: jest.fn(),
	},
	log: {
		info: jest.fn(),
		error: jest.fn(),
		warn: jest.fn(),
	},
};

// Mock remote config service
const mockRemoteConfigService = {
	convertPaywallToRemoteConfig: jest.fn(),
	createVersion: jest.fn(),
	deployVersion: jest.fn(),
};

// Make strapi globally available
(global as any).strapi = mockStrapi;

// Mock the remote config service
jest.doMock("../../src/services/adapty/remote-config", () => ({
	remoteConfigService: mockRemoteConfigService,
}));

describe("A/B Test Manager", () => {
	let abTestManager: ABTestManager;

	beforeEach(() => {
		abTestManager = new ABTestManager();
		jest.clearAllMocks();
	});

	afterEach(() => {
		jest.resetAllMocks();
	});

	describe("createTest", () => {
		it("should create a new A/B test with variations", async () => {
			// Arrange
			const testData = {
				name: "Pricing Test",
				description: "Test different pricing strategies",
				hypothesis: "Higher prices will increase revenue per user",
				test_type: "pricing",
				primary_metric: "revenue_per_user",
				start_date: new Date("2024-02-01"),
				end_date: new Date("2024-02-15"),
				duration_days: 14,
				traffic_allocation: 100,
				minimum_sample_size: 1000,
				confidence_level: 95,
				statistical_power: 80,
				minimum_detectable_effect: 15,
				variations: [
					{
						name: "Control",
						description: "Current pricing",
						variation_type: "control" as const,
						traffic_percentage: 50,
						base_paywall_id: 1,
					},
					{
						name: "Higher Price",
						description: "20% price increase",
						variation_type: "variant" as const,
						traffic_percentage: 50,
						base_paywall_id: 1,
						overrides: {
							pricing_override: { multiplier: 1.2 },
						},
					},
				],
			};

			mockStrapi.entityService.create
				.mockResolvedValueOnce({ id: 1 }) // A/B test creation
				.mockResolvedValueOnce({ id: 1 }) // First variation
				.mockResolvedValueOnce({ id: 2 }); // Second variation

			// Act
			const result = await abTestManager.createTest(testData);

			// Assert
			expect(result.success).toBe(true);
			expect(result.test_id).toBe(1);
			expect(mockStrapi.entityService.create).toHaveBeenCalledTimes(3); // 1 test + 2 variations
			expect(mockStrapi.entityService.create).toHaveBeenCalledWith(
				"api::ab-test.ab-test",
				{
					data: expect.objectContaining({
						name: "Pricing Test",
						hypothesis: "Higher prices will increase revenue per user",
						status: "draft",
					}),
				},
			);
		});

		it("should validate test configuration and return errors", async () => {
			// Arrange
			const invalidTestData = {
				name: "", // Missing name
				hypothesis: "", // Missing hypothesis
				test_type: "pricing",
				primary_metric: "revenue_per_user",
				start_date: new Date("2024-02-01"),
				traffic_allocation: 100,
				minimum_sample_size: 1000,
				confidence_level: 95,
				statistical_power: 80,
				minimum_detectable_effect: 15,
				variations: [
					{
						name: "Control",
						variation_type: "control" as const,
						traffic_percentage: 60, // Invalid: doesn't sum to 100%
						base_paywall_id: 1,
					},
				], // Only 1 variation (need at least 2)
			};

			// Act
			const result = await abTestManager.createTest(invalidTestData);

			// Assert
			expect(result.success).toBe(false);
			expect(result.errors).toContain("Test name is required");
			expect(result.errors).toContain("Test hypothesis is required");
			expect(result.errors).toContain("At least 2 variations are required");
		});
	});

	describe("startTest", () => {
		it("should start a test successfully", async () => {
			// Arrange
			const mockTest = {
				id: 1,
				name: "Test A",
				status: "draft",
				variations: [
					{
						id: 1,
						name: "Control",
						traffic_percentage: 50,
						base_paywall: { id: 1 },
					},
					{
						id: 2,
						name: "Variant",
						traffic_percentage: 50,
						base_paywall: { id: 1 },
					},
				],
			};

			mockStrapi.entityService.findOne.mockResolvedValue(mockTest);
			mockStrapi.entityService.update.mockResolvedValue({ id: 1 });

			mockRemoteConfigService.convertPaywallToRemoteConfig.mockResolvedValue({
				success: true,
				config: { paywall: { id: "1", name: "Test" } },
			});
			mockRemoteConfigService.createVersion.mockResolvedValue({
				success: true,
				version: { id: "version_1" },
			});
			mockRemoteConfigService.deployVersion.mockResolvedValue({
				success: true,
			});

			// Act
			const result = await abTestManager.startTest(1);

			// Assert
			expect(result.success).toBe(true);
			expect(mockStrapi.entityService.update).toHaveBeenCalledWith(
				"api::ab-test.ab-test",
				1,
				{
					data: expect.objectContaining({
						status: "running",
						adapty_sync_status: "synced",
					}),
				},
			);
		});

		it("should fail to start test with invalid status", async () => {
			// Arrange
			const mockTest = {
				id: 1,
				name: "Test A",
				status: "running", // Already running
				variations: [],
			};

			mockStrapi.entityService.findOne.mockResolvedValue(mockTest);

			// Act
			const result = await abTestManager.startTest(1);

			// Assert
			expect(result.success).toBe(false);
			expect(result.errors).toContain(
				"Test can only be started from draft or scheduled status",
			);
		});

		it("should fail to start test that is not found", async () => {
			// Arrange
			mockStrapi.entityService.findOne.mockResolvedValue(null);

			// Act
			const result = await abTestManager.startTest(999);

			// Assert
			expect(result.success).toBe(false);
			expect(result.errors).toContain("Test not found");
		});
	});

	describe("updateTestMetrics", () => {
		it("should update test metrics and calculate significance", async () => {
			// Arrange
			const mockTest = {
				id: 1,
				name: "Test A",
				status: "running",
				confidence_level: 95,
				statistical_power: 80,
				minimum_detectable_effect: 10,
				minimum_sample_size: 1000,
				primary_metric: "conversion_rate",
				variations: [
					{
						id: 1,
						name: "Control",
						adapty_variation_id: "var_1",
					},
					{
						id: 2,
						name: "Variant",
						adapty_variation_id: "var_2",
					},
				],
			};

			mockStrapi.entityService.findOne.mockResolvedValue(mockTest);
			mockStrapi.entityService.update.mockResolvedValue({ id: 1 });

			// Act
			const result = await abTestManager.updateTestMetrics(1);

			// Assert
			expect(result.success).toBe(true);
			expect(result.results).toBeDefined();
			expect(mockStrapi.entityService.update).toHaveBeenCalledWith(
				"api::ab-test.ab-test",
				1,
				{
					data: expect.objectContaining({
						current_results: expect.any(Object),
						statistical_significance: expect.any(Number),
					}),
				},
			);
		});

		it("should fail to update metrics for non-running test", async () => {
			// Arrange
			const mockTest = {
				id: 1,
				status: "draft",
				variations: [],
			};

			mockStrapi.entityService.findOne.mockResolvedValue(mockTest);

			// Act
			const result = await abTestManager.updateTestMetrics(1);

			// Assert
			expect(result.success).toBe(false);
			expect(result.errors).toContain("Test must be running to update metrics");
		});
	});

	describe("selectWinner", () => {
		it("should select winner and optionally promote", async () => {
			// Arrange
			const mockTest = {
				id: 1,
				name: "Test A",
				variations: [
					{
						id: 1,
						name: "Control",
						base_paywall: { id: 1 },
					},
					{
						id: 2,
						name: "Variant",
						base_paywall: { id: 1 },
					},
				],
				current_results: {
					statistical_analysis: {
						winner: "2",
						statistical_significance: 96.5,
					},
				},
			};

			mockStrapi.entityService.findOne.mockResolvedValue(mockTest);
			mockStrapi.entityService.update.mockResolvedValue({ id: 1 });

			// Act
			const result = await abTestManager.selectWinner(1, "2", "manual", true);

			// Assert
			expect(result.success).toBe(true);
			expect(result.winner_variation_id).toBe("2");
			expect(result.promoted).toBe(true);
			expect(mockStrapi.entityService.update).toHaveBeenCalledWith(
				"api::ab-test.ab-test",
				1,
				{
					data: expect.objectContaining({
						winner_variation: 2,
						winner_selection_method: "manual",
						status: "completed",
					}),
				},
			);
		});

		it("should auto-determine winner from statistical analysis", async () => {
			// Arrange
			const mockTest = {
				id: 1,
				name: "Test A",
				variations: [
					{ id: 1, name: "Control", base_paywall: { id: 1 } },
					{ id: 2, name: "Variant", base_paywall: { id: 1 } },
				],
				current_results: {
					statistical_analysis: {
						winner: "2",
						statistical_significance: 97.2,
					},
				},
			};

			mockStrapi.entityService.findOne.mockResolvedValue(mockTest);
			mockStrapi.entityService.update.mockResolvedValue({ id: 1 });

			// Act - No winner specified, should use statistical analysis
			const result = await abTestManager.selectWinner(1);

			// Assert
			expect(result.success).toBe(true);
			expect(result.winner_variation_id).toBe("2");
		});
	});

	describe("stopTest", () => {
		it("should stop a running test", async () => {
			// Arrange
			const mockTest = {
				id: 1,
				name: "Test A",
				status: "running",
			};

			mockStrapi.entityService.findOne.mockResolvedValue(mockTest);
			mockStrapi.entityService.update.mockResolvedValue({ id: 1 });

			// Act
			const result = await abTestManager.stopTest(
				1,
				"Manual stop for analysis",
			);

			// Assert
			expect(result.success).toBe(true);
			expect(mockStrapi.entityService.update).toHaveBeenCalledWith(
				"api::ab-test.ab-test",
				1,
				{
					data: expect.objectContaining({
						status: "completed",
						end_date: expect.any(Date),
						notes: "Stopped: Manual stop for analysis",
					}),
				},
			);
		});

		it("should fail to stop non-running test", async () => {
			// Arrange
			const mockTest = {
				id: 1,
				status: "completed",
			};

			mockStrapi.entityService.findOne.mockResolvedValue(mockTest);

			// Act
			const result = await abTestManager.stopTest(1);

			// Assert
			expect(result.success).toBe(false);
			expect(result.errors).toContain("Test is not running or paused");
		});
	});

	describe("calculateSampleSize", () => {
		it("should calculate required sample size", () => {
			// Arrange
			const baselineConversionRate = 5.0; // 5%
			const minimumDetectableEffect = 20; // 20% relative improvement
			const confidenceLevel = 95;
			const statisticalPower = 80;

			// Act
			const result = abTestManager.calculateSampleSize(
				baselineConversionRate,
				minimumDetectableEffect,
				confidenceLevel,
				statisticalPower,
			);

			// Assert
			expect(result.sample_size_per_variation).toBeGreaterThan(0);
			expect(result.total_sample_size).toBe(
				result.sample_size_per_variation * 2,
			);
			expect(Number.isInteger(result.sample_size_per_variation)).toBe(true);
		});
	});

	describe("getTestSummary", () => {
		it("should return comprehensive test summary", async () => {
			// Arrange
			const mockTest = {
				id: 1,
				name: "Test A",
				status: "running",
				confidence_level: 95,
				variations: [
					{ id: 1, name: "Control" },
					{ id: 2, name: "Variant" },
				],
				winner_variation: null,
				current_results: {
					statistical_analysis: {
						statistical_significance: 85.2,
						sample_size_adequate: true,
						recommendation: "continue",
					},
				},
			};

			mockStrapi.entityService.findOne.mockResolvedValue(mockTest);

			// Act
			const result = await abTestManager.getTestSummary(1);

			// Assert
			expect(result.success).toBe(true);
			expect(result.summary).toBeDefined();
			expect(result.summary?.test).toEqual(mockTest);
			expect(result.summary?.variations).toHaveLength(2);
			expect(result.summary?.recommendations).toContain(
				"Recommendation: continue",
			);
			expect(result.summary?.recommendations).toContain(
				"Sample size is adequate for reliable results",
			);
		});

		it("should handle test not found", async () => {
			// Arrange
			mockStrapi.entityService.findOne.mockResolvedValue(null);

			// Act
			const result = await abTestManager.getTestSummary(999);

			// Assert
			expect(result.success).toBe(false);
			expect(result.errors).toContain("Test not found");
		});
	});
});
