/**
 * Tests for Statistical Engine
 */

import { beforeEach, describe, expect, it } from "@jest/globals";
import {
	StatisticalEngine,
	type TestConfiguration,
	type VariationMetrics,
} from "../../src/services/ab-testing/statistical-engine";

describe("Statistical Engine", () => {
	let statisticalEngine: StatisticalEngine;

	beforeEach(() => {
		statisticalEngine = new StatisticalEngine();
	});

	describe("calculateSignificance", () => {
		it("should calculate statistical significance for conversion rate metrics", () => {
			// Arrange
			const control: VariationMetrics = {
				id: "control",
				name: "Control",
				impressions: 10000,
				conversions: 500,
				revenue: 25000,
				unique_users: 8000,
				conversion_rate: 5.0,
				revenue_per_user: 3.125,
			};

			const variant: VariationMetrics = {
				id: "variant",
				name: "Variant",
				impressions: 10000,
				conversions: 600,
				revenue: 30000,
				unique_users: 8000,
				conversion_rate: 6.0,
				revenue_per_user: 3.75,
			};

			const config: TestConfiguration = {
				confidence_level: 95,
				statistical_power: 80,
				minimum_detectable_effect: 10,
				minimum_sample_size: 1000,
				primary_metric: "conversion_rate",
			};

			// Act
			const result = statisticalEngine.calculateSignificance(
				control,
				variant,
				config,
			);

			// Assert
			expect(result.p_value).toBeLessThan(0.05); // Should be significant
			expect(result.statistical_significance).toBeGreaterThan(95);
			expect(result.effect_size).toBeGreaterThan(0);
			expect(result.sample_size_adequate).toBe(true);
			expect(result.winner).toBe("variant");
			expect(result.recommendation).toBe("stop_winner");
		});

		it("should calculate significance for revenue metrics", () => {
			// Arrange
			const control: VariationMetrics = {
				id: "control",
				name: "Control",
				impressions: 5000,
				conversions: 250,
				revenue: 12500,
				unique_users: 4000,
				conversion_rate: 5.0,
				revenue_per_user: 3.125,
			};

			const variant: VariationMetrics = {
				id: "variant",
				name: "Variant",
				impressions: 5000,
				conversions: 275,
				revenue: 16500,
				unique_users: 4000,
				conversion_rate: 5.5,
				revenue_per_user: 4.125,
			};

			const config: TestConfiguration = {
				confidence_level: 95,
				statistical_power: 80,
				minimum_detectable_effect: 15,
				minimum_sample_size: 1000,
				primary_metric: "revenue_per_user",
			};

			// Act
			const result = statisticalEngine.calculateSignificance(
				control,
				variant,
				config,
			);

			// Assert
			expect(result.p_value).toBeDefined();
			expect(result.statistical_significance).toBeDefined();
			expect(result.confidence_interval).toBeDefined();
			expect(result.confidence_interval.level).toBe(95);
			expect(result.sample_size_adequate).toBe(true);
		});

		it("should recommend continuing test when sample size is inadequate", () => {
			// Arrange
			const control: VariationMetrics = {
				id: "control",
				name: "Control",
				impressions: 500, // Small sample
				conversions: 25,
				revenue: 1250,
				unique_users: 400,
				conversion_rate: 5.0,
				revenue_per_user: 3.125,
			};

			const variant: VariationMetrics = {
				id: "variant",
				name: "Variant",
				impressions: 500, // Small sample
				conversions: 30,
				revenue: 1500,
				unique_users: 400,
				conversion_rate: 6.0,
				revenue_per_user: 3.75,
			};

			const config: TestConfiguration = {
				confidence_level: 95,
				statistical_power: 80,
				minimum_detectable_effect: 10,
				minimum_sample_size: 1000,
				primary_metric: "conversion_rate",
			};

			// Act
			const result = statisticalEngine.calculateSignificance(
				control,
				variant,
				config,
			);

			// Assert
			expect(result.sample_size_adequate).toBe(false);
			expect(result.recommendation).toBe("continue");
		});
	});

	describe("calculateRequiredSampleSize", () => {
		it("should calculate required sample size correctly", () => {
			// Arrange
			const baselineRate = 0.05; // 5% conversion rate
			const minimumDetectableEffect = 20; // 20% relative improvement
			const confidenceLevel = 95;
			const statisticalPower = 80;

			// Act
			const sampleSize = statisticalEngine.calculateRequiredSampleSize(
				baselineRate,
				minimumDetectableEffect,
				confidenceLevel,
				statisticalPower,
			);

			// Assert
			expect(sampleSize).toBeGreaterThan(0);
			expect(sampleSize).toBeLessThan(100000); // Reasonable upper bound
			expect(Number.isInteger(sampleSize)).toBe(true);
		});

		it("should require larger sample size for smaller effects", () => {
			// Arrange
			const baselineRate = 0.05;
			const smallEffect = 5; // 5% relative improvement
			const largeEffect = 50; // 50% relative improvement

			// Act
			const smallEffectSample = statisticalEngine.calculateRequiredSampleSize(
				baselineRate,
				smallEffect,
			);
			const largeEffectSample = statisticalEngine.calculateRequiredSampleSize(
				baselineRate,
				largeEffect,
			);

			// Assert
			expect(smallEffectSample).toBeGreaterThan(largeEffectSample);
		});
	});

	describe("calculateTestDuration", () => {
		it("should calculate test duration based on traffic", () => {
			// Arrange
			const requiredSampleSize = 10000;
			const dailyTraffic = 1000;
			const trafficAllocation = 50; // 50% of traffic

			// Act
			const duration = statisticalEngine.calculateTestDuration(
				requiredSampleSize,
				dailyTraffic,
				trafficAllocation,
			);

			// Assert
			expect(duration).toBe(20); // 10000 / (1000 * 0.5) = 20 days
		});

		it("should enforce minimum 7-day duration", () => {
			// Arrange
			const requiredSampleSize = 1000;
			const dailyTraffic = 10000; // High traffic
			const trafficAllocation = 100;

			// Act
			const duration = statisticalEngine.calculateTestDuration(
				requiredSampleSize,
				dailyTraffic,
				trafficAllocation,
			);

			// Assert
			expect(duration).toBe(7); // Minimum 7 days
		});
	});

	describe("validateTrafficAllocation", () => {
		it("should validate correct traffic allocation", () => {
			// Arrange
			const variations = [
				{ traffic_percentage: 50 },
				{ traffic_percentage: 50 },
			];

			// Act
			const result = statisticalEngine.validateTrafficAllocation(variations);

			// Assert
			expect(result.valid).toBe(true);
			expect(result.total).toBe(100);
			expect(result.errors).toHaveLength(0);
		});

		it("should detect incorrect traffic allocation sum", () => {
			// Arrange
			const variations = [
				{ traffic_percentage: 60 },
				{ traffic_percentage: 50 },
			];

			// Act
			const result = statisticalEngine.validateTrafficAllocation(variations);

			// Assert
			expect(result.valid).toBe(false);
			expect(result.total).toBe(110);
			expect(result.errors).toContain(
				"Traffic allocation must sum to 100%, currently 110.00%",
			);
		});

		it("should detect insufficient traffic allocation", () => {
			// Arrange
			const variations = [
				{ traffic_percentage: 0.05 }, // Too low
				{ traffic_percentage: 99.95 },
			];

			// Act
			const result = statisticalEngine.validateTrafficAllocation(variations);

			// Assert
			expect(result.valid).toBe(false);
			expect(result.errors).toContain(
				"Variation 1 has insufficient traffic allocation (minimum 0.1%)",
			);
		});
	});

	describe("calculateConversionRateCI", () => {
		it("should calculate confidence interval for conversion rate", () => {
			// Arrange
			const conversions = 100;
			const impressions = 2000;
			const confidenceLevel = 95;

			// Act
			const ci = statisticalEngine.calculateConversionRateCI(
				conversions,
				impressions,
				confidenceLevel,
			);

			// Assert
			expect(ci.lower).toBeLessThan(ci.upper);
			expect(ci.lower).toBeGreaterThanOrEqual(0);
			expect(ci.upper).toBeLessThanOrEqual(1);
			expect(ci.level).toBe(95);

			// The true rate (5%) should be within the confidence interval
			const trueRate = conversions / impressions;
			expect(ci.lower).toBeLessThanOrEqual(trueRate);
			expect(ci.upper).toBeGreaterThanOrEqual(trueRate);
		});

		it("should have wider intervals for smaller samples", () => {
			// Arrange
			const conversions = 10;
			const smallSample = 200;
			const largeSample = 2000;

			// Act
			const smallCI = statisticalEngine.calculateConversionRateCI(
				conversions,
				smallSample,
			);
			const largeCI = statisticalEngine.calculateConversionRateCI(
				conversions * 10,
				largeSample,
			);

			// Assert
			const smallWidth = smallCI.upper - smallCI.lower;
			const largeWidth = largeCI.upper - largeCI.lower;
			expect(smallWidth).toBeGreaterThan(largeWidth);
		});
	});

	describe("edge cases", () => {
		it("should handle zero conversions", () => {
			// Arrange
			const control: VariationMetrics = {
				id: "control",
				name: "Control",
				impressions: 1000,
				conversions: 0,
				revenue: 0,
				unique_users: 800,
				conversion_rate: 0,
				revenue_per_user: 0,
			};

			const variant: VariationMetrics = {
				id: "variant",
				name: "Variant",
				impressions: 1000,
				conversions: 10,
				revenue: 500,
				unique_users: 800,
				conversion_rate: 1.0,
				revenue_per_user: 0.625,
			};

			const config: TestConfiguration = {
				confidence_level: 95,
				statistical_power: 80,
				minimum_detectable_effect: 10,
				minimum_sample_size: 500,
				primary_metric: "conversion_rate",
			};

			// Act
			const result = statisticalEngine.calculateSignificance(
				control,
				variant,
				config,
			);

			// Assert
			expect(result.p_value).toBeDefined();
			expect(result.statistical_significance).toBeDefined();
			expect(result.winner).toBe("variant");
		});

		it("should handle equal performance variations", () => {
			// Arrange
			const control: VariationMetrics = {
				id: "control",
				name: "Control",
				impressions: 5000,
				conversions: 250,
				revenue: 12500,
				unique_users: 4000,
				conversion_rate: 5.0,
				revenue_per_user: 3.125,
			};

			const variant: VariationMetrics = {
				id: "variant",
				name: "Variant",
				impressions: 5000,
				conversions: 250,
				revenue: 12500,
				unique_users: 4000,
				conversion_rate: 5.0,
				revenue_per_user: 3.125,
			};

			const config: TestConfiguration = {
				confidence_level: 95,
				statistical_power: 80,
				minimum_detectable_effect: 10,
				minimum_sample_size: 1000,
				primary_metric: "conversion_rate",
			};

			// Act
			const result = statisticalEngine.calculateSignificance(
				control,
				variant,
				config,
			);

			// Assert
			expect(result.p_value).toBeCloseTo(1.0, 1); // Should be close to 1 for no difference
			expect(result.statistical_significance).toBeLessThan(50);
			expect(result.effect_size).toBeCloseTo(0, 2);
		});
	});
});
