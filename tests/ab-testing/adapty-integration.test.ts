/**
 * Tests for Adapty A/B Test Integration
 */

import { abTestManager } from "../../src/services/ab-testing/ab-test-manager";
import { adaptySyncService } from "../../src/services/ab-testing/adapty-sync-service";
import { adaptyABTestIntegration } from "../../src/services/adapty/ab-test-integration";

// Mock Strapi
const mockStrapi = {
	entityService: {
		create: jest.fn(),
		findOne: jest.fn(),
		findMany: jest.fn(),
		update: jest.fn(),
		delete: jest.fn(),
	},
	log: {
		info: jest.fn(),
		warn: jest.fn(),
		error: jest.fn(),
		debug: jest.fn(),
	},
	server: {
		on: jest.fn(),
	},
};

global.strapi = mockStrapi as any;

// Mock Adapty client
jest.mock("../../src/services/adapty/client", () => ({
	adaptyClient: {
		createABTest: jest.fn(),
		getABTest: jest.fn(),
		startABTest: jest.fn(),
		stopABTest: jest.fn(),
		getABTestAnalytics: jest.fn(),
		createAudience: jest.fn(),
		createRemoteConfig: jest.fn(),
	},
}));

describe("Adapty A/B Test Integration", () => {
	beforeEach(() => {
		jest.clearAllMocks();
	});

	describe("A/B Test Manager Integration", () => {
		it("should create test with Adapty integration", async () => {
			// Mock test data
			const testData = {
				name: "Test Paywall Conversion",
				description: "Testing new paywall design",
				hypothesis: "New design will increase conversion by 15%",
				test_type: "paywall_optimization",
				primary_metric: "conversion_rate",
				start_date: new Date(),
				duration_days: 14,
				traffic_allocation: 80,
				minimum_sample_size: 1000,
				confidence_level: 95,
				statistical_power: 80,
				minimum_detectable_effect: 10,
				audience_targeting: { target_audience: "new_users" },
				variations: [
					{
						name: "Control",
						variation_type: "control",
						traffic_percentage: 50,
						base_paywall_id: 1,
					},
					{
						name: "Variant A",
						variation_type: "variant",
						traffic_percentage: 50,
						base_paywall_id: 1,
						overrides: { title_override: "New Title" },
					},
				],
			};

			// Mock Strapi responses
			mockStrapi.entityService.create
				.mockResolvedValueOnce({ id: 1, ...testData }) // A/B test creation
				.mockResolvedValueOnce({ id: 1, variation_type: "control" }) // Variation 1
				.mockResolvedValueOnce({ id: 2, variation_type: "variant" }); // Variation 2

			const result = await abTestManager.createTest(testData);

			expect(result.success).toBe(true);
			expect(result.test_id).toBe(1);
			expect(mockStrapi.entityService.create).toHaveBeenCalledTimes(3);
		});

		it("should start test and deploy to Adapty", async () => {
			const testId = 1;
			const mockTest = {
				id: testId,
				name: "Test Paywall",
				status: "draft",
				primary_metric: "conversion_rate",
				traffic_allocation: 80,
				variations: [
					{
						id: 1,
						name: "Control",
						variation_type: "control",
						traffic_percentage: 50,
						base_paywall: { id: 1 },
					},
					{
						id: 2,
						name: "Variant",
						variation_type: "variant",
						traffic_percentage: 50,
						base_paywall: { id: 1 },
					},
				],
			};

			mockStrapi.entityService.findOne.mockResolvedValue(mockTest);
			mockStrapi.entityService.update.mockResolvedValue({ id: testId });

			// Mock Adapty integration
			jest
				.spyOn(adaptyABTestIntegration, "createAdaptyABTest")
				.mockResolvedValue({
					success: true,
					data: { id: "adapty_test_123" },
				} as any);

			jest
				.spyOn(adaptyABTestIntegration, "startAdaptyTest")
				.mockResolvedValue();

			const result = await abTestManager.startTest(testId);

			expect(result.success).toBe(true);
			expect(adaptyABTestIntegration.createAdaptyABTest).toHaveBeenCalled();
			expect(adaptyABTestIntegration.startAdaptyTest).toHaveBeenCalledWith(
				testId,
			);
			expect(mockStrapi.entityService.update).toHaveBeenCalledWith(
				"api::ab-test.ab-test",
				testId,
				expect.objectContaining({
					data: expect.objectContaining({
						status: "running",
						adapty_test_id: "adapty_test_123",
					}),
				}),
			);
		});

		it("should sync test metrics from Adapty", async () => {
			const testId = 1;
			const mockTest = {
				id: testId,
				status: "running",
				adapty_test_id: "adapty_test_123",
				variations: [
					{
						id: 1,
						total_impressions: 1000,
						total_conversions: 50,
						conversion_rate: 5.0,
					},
					{
						id: 2,
						total_impressions: 1000,
						total_conversions: 75,
						conversion_rate: 7.5,
					},
				],
			};

			mockStrapi.entityService.findOne.mockResolvedValue(mockTest);
			mockStrapi.entityService.update.mockResolvedValue({ id: testId });

			// Mock sync service
			jest
				.spyOn(adaptyABTestIntegration, "syncTestResults")
				.mockResolvedValue();

			const result = await abTestManager.updateTestMetrics(testId);

			expect(result.success).toBe(true);
			expect(adaptyABTestIntegration.syncTestResults).toHaveBeenCalledWith(
				testId,
			);
		});

		it("should monitor test performance with Adapty data", async () => {
			const testId = 1;
			const mockTest = {
				id: testId,
				adapty_test_id: "adapty_test_123",
				confidence_level: 95,
				variations: [{ total_impressions: 1000 }, { total_impressions: 1000 }],
				current_results: {
					statistical_analysis: {
						statistical_significance: 96.5,
						recommendation: "stop_winner",
					},
				},
			};

			mockStrapi.entityService.findOne.mockResolvedValue(mockTest);

			// Mock Adapty performance monitoring
			jest
				.spyOn(adaptyABTestIntegration, "monitorTestPerformance")
				.mockResolvedValue({
					isSignificant: true,
					recommendedAction: "conclude_with_winner",
					confidence: 96.5,
					sampleSize: 2000,
				});

			const result = await abTestManager.monitorTestPerformance(testId);

			expect(result.success).toBe(true);
			expect(result.performance?.isSignificant).toBe(true);
			expect(result.performance?.confidence).toBe(96.5);
			expect(result.performance?.adaptyRecommendation).toBeDefined();
		});
	});

	describe("Sync Service", () => {
		it("should schedule sync jobs correctly", () => {
			const testId = 1;

			adaptySyncService.scheduleSync(testId, "metrics", "high");

			const status = adaptySyncService.getQueueStatus();
			expect(status.queueLength).toBe(1);
		});

		it("should handle immediate sync requests", () => {
			const testId = 2;

			adaptySyncService.scheduleImmediateSync(testId, "full");

			const status = adaptySyncService.getQueueStatus();
			expect(status.queueLength).toBeGreaterThan(0);
		});

		it("should clean up failed jobs", () => {
			adaptySyncService.cleanupFailedJobs();

			// Should not throw and should clean up old failed jobs
			expect(true).toBe(true);
		});
	});

	describe("Error Handling", () => {
		it("should handle Adapty API failures gracefully", async () => {
			const testId = 1;
			const mockTest = {
				id: testId,
				name: "Test",
				status: "draft",
				variations: [],
			};

			mockStrapi.entityService.findOne.mockResolvedValue(mockTest);

			// Mock Adapty failure
			jest
				.spyOn(adaptyABTestIntegration, "createAdaptyABTest")
				.mockRejectedValue(new Error("Adapty API unavailable"));

			const result = await abTestManager.startTest(testId);

			expect(result.success).toBe(false);
			expect(result.errors).toContain(
				"Adapty deployment failed: Adapty API unavailable",
			);
		});

		it("should continue local operations when Adapty sync fails", async () => {
			const testId = 1;
			const mockTest = {
				id: testId,
				status: "running",
				adapty_test_id: "adapty_test_123",
			};

			mockStrapi.entityService.findOne.mockResolvedValue(mockTest);
			mockStrapi.entityService.update.mockResolvedValue({ id: testId });

			// Mock Adapty sync failure
			jest
				.spyOn(adaptyABTestIntegration, "stopAdaptyTest")
				.mockRejectedValue(new Error("Adapty sync failed"));

			const result = await abTestManager.stopTest(testId, "Test completed");

			expect(result.success).toBe(true); // Should succeed locally even if Adapty fails
			expect(mockStrapi.log.warn).toHaveBeenCalledWith(
				"Failed to stop test in Adapty:",
				expect.any(Error),
			);
		});
	});
});
