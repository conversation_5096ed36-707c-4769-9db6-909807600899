/**
 * Integration tests for A/B test management workflows
 */

import {
	afterEach,
	beforeEach,
	describe,
	expect,
	it,
	jest,
} from "@jest/globals";

// Mock Strapi and helper functions
const mockToggleNotification = jest.fn();
const mockFetch = jest.fn();

// Mock @strapi/helper-plugin
jest.mock("@strapi/helper-plugin", () => ({
	useNotification: () => mockToggleNotification,
	useCMEditViewDataManager: () => ({
		modifiedData: {},
		onChange: jest.fn(),
	}),
}));

// Mock fetch globally
global.fetch = mockFetch as any;

describe("A/B Test Management Workflows", () => {
	beforeEach(() => {
		jest.clearAllMocks();
		mockFetch.mockClear();
	});

	afterEach(() => {
		jest.restoreAllMocks();
	});

	describe("Test Creation Workflow", () => {
		it("should create a new A/B test with proper validation", async () => {
			const testData = {
				name: "Paywall CTA Test",
				description: "Testing different call-to-action buttons",
				hypothesis: "Green button will increase conversions",
				successMetrics: ["conversion_rate", "revenue"],
				targetAudience: "new_users",
				trafficAllocation: 50,
				startDate: new Date().toISOString(),
				endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
			};

			mockFetch.mockResolvedValueOnce({
				ok: true,
				json: async () => ({
					data: { id: 1, attributes: testData },
				}),
			});

			const response = await fetch("/api/ab-tests", {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify({ data: testData }),
			});

			const result = await response.json();

			expect(mockFetch).toHaveBeenCalledWith("/api/ab-tests", {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify({ data: testData }),
			});

			expect(result.data.attributes.name).toBe(testData.name);
			expect(result.data.attributes.trafficAllocation).toBe(50);
		});

		it("should validate required fields before creating test", () => {
			const invalidTestData = {
				description: "Missing name field",
			};

			const validationErrors = validateTestData(invalidTestData);

			expect(validationErrors).toContain("Name is required");
			expect(validationErrors).toContain("Success metrics are required");
			expect(validationErrors).toContain("Traffic allocation is required");
		});

		it("should create test variations with proper traffic allocation", async () => {
			const variations = [
				{ name: "Control", isControl: true, trafficPercentage: 50 },
				{ name: "Variant A", isControl: false, trafficPercentage: 50 },
			];

			const totalAllocation = variations.reduce(
				(sum, v) => sum + v.trafficPercentage,
				0,
			);
			expect(totalAllocation).toBe(100);

			mockFetch.mockResolvedValueOnce({
				ok: true,
				json: async () => ({
					data: variations.map((v, i) => ({ id: i + 1, attributes: v })),
				}),
			});

			const response = await fetch("/api/paywall-variations", {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify({ data: variations }),
			});

			expect(response.ok).toBe(true);
		});
	});

	describe("Variation Management", () => {
		it("should allow side-by-side comparison of variations", async () => {
			const mockVariations = [
				{
					id: 1,
					name: "Control",
					isControl: true,
					conversionRate: 12.5,
					sampleSize: 1000,
					revenue: 5000,
				},
				{
					id: 2,
					name: "Variant A",
					isControl: false,
					conversionRate: 15.2,
					sampleSize: 1000,
					revenue: 6080,
				},
			];

			mockFetch.mockResolvedValueOnce({
				ok: true,
				json: async () => ({ data: mockVariations }),
			});

			const response = await fetch(
				"/api/paywall-variations?filters[abTest][id][$eq]=1",
			);
			const result = await response.json();

			expect(result.data).toHaveLength(2);
			expect(result.data[0].isControl).toBe(true);
			expect(result.data[1].conversionRate).toBeGreaterThan(
				result.data[0].conversionRate,
			);
		});

		it("should calculate statistical significance correctly", () => {
			const control = { conversionRate: 12.5, sampleSize: 1000 };
			const variant = { conversionRate: 15.2, sampleSize: 1000 };

			const significance = calculateStatisticalSignificance(control, variant);

			expect(significance.pValue).toBeLessThan(0.05);
			expect(significance.significant).toBe(true);
			expect(significance.improvement).toBeCloseTo(21.6, 1); // (15.2-12.5)/12.5 * 100
		});

		it("should handle insufficient sample sizes", () => {
			const control = { conversionRate: 12.5, sampleSize: 10 };
			const variant = { conversionRate: 15.2, sampleSize: 10 };

			const significance = calculateStatisticalSignificance(control, variant);

			expect(significance.significant).toBe(false);
			expect(significance.pValue).toBeGreaterThan(0.05);
		});
	});

	describe("Performance Monitoring", () => {
		it("should fetch real-time performance metrics", async () => {
			const mockMetrics = {
				testId: 1,
				totalImpressions: 5000,
				totalConversions: 650,
				overallConversionRate: 13.0,
				revenue: 26000,
				variations: [
					{ id: 1, impressions: 2500, conversions: 312, conversionRate: 12.5 },
					{ id: 2, impressions: 2500, conversions: 338, conversionRate: 13.5 },
				],
			};

			mockFetch.mockResolvedValueOnce({
				ok: true,
				json: async () => mockMetrics,
			});

			const response = await fetch("/api/ab-tests/1/metrics");
			const metrics = await response.json();

			expect(metrics.totalImpressions).toBe(5000);
			expect(metrics.variations).toHaveLength(2);
			expect(metrics.overallConversionRate).toBe(13.0);
		});

		it("should update metrics in real-time", async () => {
			const initialMetrics = { conversionRate: 12.5, sampleSize: 1000 };
			const updatedMetrics = { conversionRate: 13.2, sampleSize: 1200 };

			// Simulate real-time update
			mockFetch
				.mockResolvedValueOnce({
					ok: true,
					json: async () => initialMetrics,
				})
				.mockResolvedValueOnce({
					ok: true,
					json: async () => updatedMetrics,
				});

			const initialResponse = await fetch("/api/ab-tests/1/metrics");
			const initial = await initialResponse.json();

			// Simulate time passing and new data
			const updatedResponse = await fetch("/api/ab-tests/1/metrics");
			const updated = await updatedResponse.json();

			expect(initial.conversionRate).toBe(12.5);
			expect(updated.conversionRate).toBe(13.2);
			expect(updated.sampleSize).toBeGreaterThan(initial.sampleSize);
		});
	});

	describe("Test Conclusion Workflow", () => {
		it("should conclude test with winner selection", async () => {
			const conclusionData = {
				testId: 1,
				winnerId: 2,
				conclusionNotes: "Variant A showed significant improvement",
				deploymentOption: "immediate",
				conclusionDate: new Date().toISOString(),
			};

			mockFetch.mockResolvedValueOnce({
				ok: true,
				json: async () => ({
					data: {
						id: 1,
						attributes: {
							status: "completed",
							winner: { id: 2, name: "Variant A" },
							...conclusionData,
						},
					},
				}),
			});

			const response = await fetch("/api/ab-tests/1/conclude", {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify(conclusionData),
			});

			const result = await response.json();

			expect(result.data.attributes.status).toBe("completed");
			expect(result.data.attributes.winner.id).toBe(2);
		});

		it("should validate conclusion requirements", () => {
			const incompleteConclusion = {
				testId: 1,
				winnerId: 2,
				// Missing conclusionNotes and deploymentOption
			};

			const validationErrors = validateConclusionData(incompleteConclusion);

			expect(validationErrors).toContain("Conclusion notes are required");
			expect(validationErrors).toContain("Deployment option is required");
		});

		it("should handle winner promotion workflow", async () => {
			const promotionData = {
				testId: 1,
				winnerId: 2,
				deploymentStrategy: "gradual",
				rolloutPercentage: 25,
			};

			mockFetch.mockResolvedValueOnce({
				ok: true,
				json: async () => ({
					success: true,
					deploymentId: "deploy_123",
					status: "in_progress",
				}),
			});

			const response = await fetch("/api/ab-tests/1/promote-winner", {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify(promotionData),
			});

			const result = await response.json();

			expect(result.success).toBe(true);
			expect(result.deploymentId).toBeDefined();
			expect(result.status).toBe("in_progress");
		});
	});

	describe("Integration with Adapty", () => {
		it("should sync test data with Adapty remote config", async () => {
			const testConfig = {
				testId: 1,
				variations: [
					{ id: 1, name: "Control", config: { buttonColor: "blue" } },
					{ id: 2, name: "Variant A", config: { buttonColor: "green" } },
				],
				trafficAllocation: { 1: 50, 2: 50 },
			};

			mockFetch.mockResolvedValueOnce({
				ok: true,
				json: async () => ({
					success: true,
					remoteConfigId: "rc_abc123",
					syncedAt: new Date().toISOString(),
				}),
			});

			const response = await fetch("/api/adapty/sync-ab-test", {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify(testConfig),
			});

			const result = await response.json();

			expect(result.success).toBe(true);
			expect(result.remoteConfigId).toBeDefined();
		});
	});
});

// Helper functions for testing
function validateTestData(data: any): string[] {
	const errors: string[] = [];

	if (!data.name) errors.push("Name is required");
	if (!data.successMetrics || data.successMetrics.length === 0) {
		errors.push("Success metrics are required");
	}
	if (typeof data.trafficAllocation !== "number") {
		errors.push("Traffic allocation is required");
	}

	return errors;
}

function validateConclusionData(data: any): string[] {
	const errors: string[] = [];

	if (!data.conclusionNotes) errors.push("Conclusion notes are required");
	if (!data.deploymentOption) errors.push("Deployment option is required");

	return errors;
}

function calculateStatisticalSignificance(control: any, variant: any) {
	const p1 = control.conversionRate / 100;
	const p2 = variant.conversionRate / 100;
	const n1 = control.sampleSize;
	const n2 = variant.sampleSize;

	if (n1 < 30 || n2 < 30) {
		return { significant: false, pValue: 1, improvement: 0 };
	}

	const pooledP = (p1 * n1 + p2 * n2) / (n1 + n2);
	const se = Math.sqrt(pooledP * (1 - pooledP) * (1 / n1 + 1 / n2));
	const z = Math.abs(p2 - p1) / se;
	const pValue = 2 * (1 - normalCDF(Math.abs(z)));

	return {
		significant: pValue < 0.05,
		pValue,
		improvement:
			((variant.conversionRate - control.conversionRate) /
				control.conversionRate) *
			100,
	};
}

function normalCDF(x: number): number {
	return 0.5 * (1 + erf(x / Math.sqrt(2)));
}

function erf(x: number): number {
	const a1 = 0.254829592;
	const a2 = -0.284496736;
	const a3 = 1.421413741;
	const a4 = -1.453152027;
	const a5 = 1.061405429;
	const p = 0.3275911;

	const sign = x >= 0 ? 1 : -1;
	x = Math.abs(x);

	const t = 1.0 / (1.0 + p * x);
	const y =
		1.0 - ((((a5 * t + a4) * t + a3) * t + a2) * t + a1) * t * Math.exp(-x * x);

	return sign * y;
}
