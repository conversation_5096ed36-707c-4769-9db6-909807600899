/**
 * Tests for Remote Config Management Service
 */

import {
	afterEach,
	beforeEach,
	describe,
	expect,
	it,
	jest,
} from "@jest/globals";
import { RemoteConfigService } from "../../src/services/adapty/remote-config";

// Mock Strapi
const mockStrapi = {
	entityService: {
		findOne: jest.fn(),
		findMany: jest.fn(),
		create: jest.fn(),
		update: jest.fn(),
		delete: jest.fn(),
	},
	log: {
		info: jest.fn(),
		error: jest.fn(),
		warn: jest.fn(),
	},
};

// Mock Adapty client
const mockAdaptyClient = {
	getProductsByPlacement: jest.fn(),
	updateRemoteConfigByPlacement: jest.fn(),
	createRemoteConfig: jest.fn(),
	getPlacementAnalytics: jest.fn(),
};

// Make strapi globally available
(global as any).strapi = mockStrapi;
(global as any).process = { env: { STRAPI_URL: "http://localhost:1337" } };

// Mock the getAdaptyService function
jest.doMock("../../src/services/adapty", () => ({
	getAdaptyService: () => mockAdaptyClient,
}));

describe("Remote Config Management Service", () => {
	let remoteConfigService: RemoteConfigService;

	beforeEach(() => {
		remoteConfigService = new RemoteConfigService();
		jest.clearAllMocks();
	});

	afterEach(() => {
		jest.resetAllMocks();
	});

	describe("convertPaywallToRemoteConfig", () => {
		it("should convert a complete paywall to remote config format", async () => {
			// Arrange
			const mockPaywall = {
				id: 1,
				name: "Premium Paywall",
				title: "Unlock Premium Features",
				subtitle: "Get access to all premium content",
				description_text: "Join thousands of satisfied users",
				cta_text: "Subscribe Now",
				cta_secondary_text: "Cancel anytime",
				placement_id: "placement_123",
				is_fallback: false,
				createdAt: "2024-01-01T00:00:00Z",
				updatedAt: "2024-01-01T00:00:00Z",
				theme: {
					name: "Dark Theme",
					primary_color: "#007bff",
					background_color: "#1a1a1a",
					text_color: "#ffffff",
					button_style: "rounded",
					header_style: "hero",
					product_display_style: "grid",
					show_features: true,
					show_testimonials: true,
					background_image: { url: "https://example.com/bg.jpg" },
					logo: { url: "https://example.com/logo.png" },
				},
				features: [
					{
						icon: "star",
						title: "Premium Content",
						description: "Access to exclusive articles",
						order: 1,
						is_highlighted: true,
						icon_image: { url: "https://example.com/star.png" },
					},
				],
				testimonials: [
					{
						author_name: "John Doe",
						author_title: "CEO",
						content: "Amazing service!",
						rating: 5,
						author_avatar: { url: "https://example.com/avatar.jpg" },
						company: "Tech Corp",
						order: 1,
					},
				],
				product_labels: [
					{
						product_id: "premium_monthly",
						badge_text: "Most Popular",
						badge_color: "#ff6b6b",
						subtitle: "Best value",
						highlight: true,
						savings_percentage: 20,
						badge_position: "top-right",
					},
				],
			};

			const mockProducts = [
				{
					id: "premium_monthly",
					vendor_product_id: "com.app.premium.monthly",
					name: "Premium Monthly",
					type: "subscription",
					price: {
						amount: 999,
						currency_code: "USD",
						localized_string: "$9.99",
					},
					subscription_period: { unit: "month", number_of_units: 1 },
					store: "app_store",
				},
			];

			mockStrapi.entityService.findOne.mockResolvedValue(mockPaywall);
			mockAdaptyClient.getProductsByPlacement.mockResolvedValue({
				data: mockProducts,
			});

			// Act
			const result = await remoteConfigService.convertPaywallToRemoteConfig(
				1,
				"en",
				true,
			);

			// Assert
			expect(result.success).toBe(true);
			expect(result.config).toBeDefined();
			expect(result.config?.paywall.id).toBe("1");
			expect(result.config?.paywall.name).toBe("Premium Paywall");
			expect(result.config?.paywall.theme.name).toBe("Dark Theme");
			expect(result.config?.features).toHaveLength(1);
			expect(result.config?.testimonials).toHaveLength(1);
			expect(result.config?.product_labels).toHaveLength(1);
			expect(result.config?.products).toHaveLength(1);
			expect(result.config?.locale).toBe("en");
		});

		it("should handle paywall with minimal data", async () => {
			// Arrange
			const mockPaywall = {
				id: 2,
				name: "Basic Paywall",
				placement_id: "placement_456",
				createdAt: "2024-01-01T00:00:00Z",
				updatedAt: "2024-01-01T00:00:00Z",
			};

			mockStrapi.entityService.findOne.mockResolvedValue(mockPaywall);
			mockAdaptyClient.getProductsByPlacement.mockResolvedValue({ data: [] });

			// Act
			const result = await remoteConfigService.convertPaywallToRemoteConfig(
				2,
				"en",
				true,
			);

			// Assert
			expect(result.success).toBe(true);
			expect(result.config?.paywall.name).toBe("Basic Paywall");
			expect(result.config?.paywall.title).toBe("Basic Paywall");
			expect(result.config?.paywall.theme.name).toBe("Default");
			expect(result.config?.features).toHaveLength(0);
			expect(result.config?.products).toHaveLength(0);
		});

		it("should handle paywall not found", async () => {
			// Arrange
			mockStrapi.entityService.findOne.mockResolvedValue(null);

			// Act
			const result =
				await remoteConfigService.convertPaywallToRemoteConfig(999);

			// Assert
			expect(result.success).toBe(false);
			expect(result.errors).toContain("Paywall not found");
		});
	});

	describe("validateRemoteConfig", () => {
		it("should validate a complete and valid remote config", async () => {
			// Arrange
			const validConfig = {
				paywall: {
					id: "1",
					name: "Test Paywall",
					title: "Test Title",
					subtitle: "Test Subtitle",
					description: "Test Description",
					cta_text: "Subscribe",
					theme: {
						name: "Default",
						primary_color: "#007bff",
						background_color: "#ffffff",
						text_color: "#000000",
						button_style: "rounded",
						header_style: "hero",
						product_display_style: "list",
						show_features: true,
						show_testimonials: false,
					},
					features: [
						{
							icon: "check",
							title: "Feature 1",
							description: "Description 1",
							order: 1,
							is_highlighted: false,
						},
					],
					testimonials: [],
					product_labels: [],
				},
				products: [{ id: "product_1", name: "Premium", type: "subscription" }],
				version: "v1.0.0",
				locale: "en",
				generated_at: new Date().toISOString(),
			};

			// Act
			const result =
				await remoteConfigService.validateRemoteConfig(validConfig);

			// Assert
			expect(result.valid).toBe(true);
			expect(result.errors).toHaveLength(0);
			expect(result.mobile_compatibility.ios).toBe(true);
			expect(result.mobile_compatibility.android).toBe(true);
			expect(result.mobile_compatibility.react_native).toBe(true);
		});

		it("should detect validation errors in invalid config", async () => {
			// Arrange
			const invalidConfig = {
				paywall: {
					id: "", // Missing ID
					name: "", // Missing name
					title: "", // Missing title
					cta_text: "", // Missing CTA
					theme: {
						primary_color: "invalid-color", // Invalid color
						background_color: "rgb(300, 300, 300)", // Invalid RGB
						text_color: "#gggggg", // Invalid hex
					},
					features: [
						{ title: "", description: "No title" }, // Missing title
						{ title: "Valid Feature", description: "" }, // Missing description (warning)
					],
				},
				products: [], // No products (warning)
				version: "v1.0.0",
				locale: "en",
				generated_at: new Date().toISOString(),
			};

			// Act
			const result =
				await remoteConfigService.validateRemoteConfig(invalidConfig);

			// Assert
			expect(result.valid).toBe(false);
			expect(result.errors.length).toBeGreaterThan(0);
			expect(result.errors).toContain("Paywall ID is required");
			expect(result.errors).toContain("Paywall name is required");
			expect(result.errors).toContain("Paywall title is required");
			expect(result.errors).toContain("CTA text is required");
			expect(result.warnings.length).toBeGreaterThan(0);
		});

		it("should warn about long text content for mobile", async () => {
			// Arrange
			const configWithLongText = {
				paywall: {
					id: "1",
					name: "Test",
					title:
						"This is a very long title that exceeds the recommended length for mobile display and might cause issues",
					subtitle:
						"This is an extremely long subtitle that definitely exceeds the recommended character limit for mobile applications and will likely cause display issues on smaller screens",
					cta_text: "Subscribe",
					theme: {
						primary_color: "#007bff",
						background_color: "#ffffff",
						text_color: "#000000",
					},
				},
				products: [{ id: "product_1", name: "Premium" }],
				version: "v1.0.0",
				locale: "en",
				generated_at: new Date().toISOString(),
			};

			// Act
			const result =
				await remoteConfigService.validateRemoteConfig(configWithLongText);

			// Assert
			expect(result.valid).toBe(true);
			expect(result.warnings).toContain(
				"Title may be too long for mobile display",
			);
			expect(result.warnings).toContain(
				"Subtitle may be too long for mobile display",
			);
		});
	});

	describe("createVersion", () => {
		it("should create a new version successfully", async () => {
			// Arrange
			const mockPaywall = {
				id: 1,
				name: "Test Paywall",
				placement_id: "placement_123",
			};

			mockStrapi.entityService.findOne.mockResolvedValue(mockPaywall);
			mockAdaptyClient.getProductsByPlacement.mockResolvedValue({ data: [] });
			mockStrapi.entityService.create.mockResolvedValue({ id: 1 });

			// Act
			const result = await remoteConfigService.createVersion(
				1,
				"en",
				"Initial version",
				"admin",
			);

			// Assert
			expect(result.success).toBe(true);
			expect(result.version).toBeDefined();
			expect(result.version?.paywall_id).toBe(1);
			expect(result.version?.locale).toBe("en");
			expect(result.version?.deployment_notes).toBe("Initial version");
			expect(result.version?.created_by).toBe("admin");
			expect(result.version?.status).toBe("draft");
		});

		it("should fail when paywall is not found", async () => {
			// Arrange
			mockStrapi.entityService.findOne.mockResolvedValue(null);

			// Act
			const result = await remoteConfigService.createVersion(999);

			// Assert
			expect(result.success).toBe(false);
			expect(result.errors).toContain("Paywall not found");
		});
	});

	describe("deployVersion", () => {
		it("should deploy version successfully", async () => {
			// Arrange
			const mockVersionRecord = {
				id: 1,
				version_id: "version_123",
				config_data: JSON.stringify({
					paywall: {
						id: "1",
						name: "Test",
						title: "Test",
						cta_text: "Subscribe",
					},
					products: [],
					version: "v1.0.0",
				}),
				placement_id: "placement_123",
				locale: "en",
				paywall: { id: 1 },
			};

			mockStrapi.entityService.findMany
				.mockResolvedValueOnce([mockVersionRecord]) // Find version
				.mockResolvedValueOnce([]); // Find previous versions

			mockAdaptyClient.updateRemoteConfigByPlacement.mockResolvedValue({});
			mockStrapi.entityService.update.mockResolvedValue({});

			// Act
			const result = await remoteConfigService.deployVersion("version_123");

			// Assert
			expect(result.success).toBe(true);
			expect(result.version_id).toBe("version_123");
			expect(result.deployed_at).toBeInstanceOf(Date);
			expect(
				mockAdaptyClient.updateRemoteConfigByPlacement,
			).toHaveBeenCalledWith(
				"placement_123",
				expect.objectContaining({
					data: expect.any(Object),
					lang: "en",
				}),
			);
		});

		it("should create remote config if update fails", async () => {
			// Arrange
			const mockVersionRecord = {
				id: 1,
				version_id: "version_123",
				config_data: JSON.stringify({
					paywall: {
						id: "1",
						name: "Test",
						title: "Test",
						cta_text: "Subscribe",
					},
					products: [],
				}),
				placement_id: "placement_123",
				locale: "en",
				paywall: { id: 1 },
			};

			mockStrapi.entityService.findMany
				.mockResolvedValueOnce([mockVersionRecord])
				.mockResolvedValueOnce([]);

			mockAdaptyClient.updateRemoteConfigByPlacement.mockRejectedValue(
				new Error("Not found"),
			);
			mockAdaptyClient.createRemoteConfig.mockResolvedValue({});
			mockStrapi.entityService.update.mockResolvedValue({});

			// Act
			const result = await remoteConfigService.deployVersion("version_123");

			// Assert
			expect(result.success).toBe(true);
			expect(mockAdaptyClient.createRemoteConfig).toHaveBeenCalledWith({
				placement_id: "placement_123",
				lang: "en",
				data: expect.any(Object),
			});
		});

		it("should fail deployment for invalid version", async () => {
			// Arrange
			mockStrapi.entityService.findMany.mockResolvedValue([]);

			// Act
			const result = await remoteConfigService.deployVersion("invalid_version");

			// Assert
			expect(result.success).toBe(false);
			expect(result.errors).toContain("Version not found");
		});
	});

	describe("rollbackToVersion", () => {
		it("should rollback to previous version successfully", async () => {
			// Arrange
			const mockVersionRecord = {
				id: 1,
				version_id: "version_old",
				paywall: 1,
				locale: "en",
			};

			const mockCurrentVersion = {
				id: 2,
				version_id: "version_current",
			};

			mockStrapi.entityService.findMany
				.mockResolvedValueOnce([mockVersionRecord]) // Find rollback version
				.mockResolvedValueOnce([mockCurrentVersion]) // Find current version
				.mockResolvedValueOnce([mockVersionRecord]) // For deployment
				.mockResolvedValueOnce([]); // For marking previous versions

			mockAdaptyClient.updateRemoteConfigByPlacement.mockResolvedValue({});
			mockStrapi.entityService.update.mockResolvedValue({});
			mockStrapi.entityService.create.mockResolvedValue({});

			// Act
			const result = await remoteConfigService.rollbackToVersion(
				"version_old",
				"Performance issues",
			);

			// Assert
			expect(result.success).toBe(true);
			expect(result.rollback_version).toBe("version_current");
			expect(mockStrapi.entityService.create).toHaveBeenCalledWith(
				"api::deployment-log.deployment-log",
				expect.objectContaining({
					data: expect.objectContaining({
						action: "rollback",
						reason: "Performance issues",
					}),
				}),
			);
		});
	});

	describe("createPreview", () => {
		it("should create preview configuration successfully", async () => {
			// Arrange
			const mockPaywall = {
				id: 1,
				name: "Test Paywall",
				placement_id: "placement_123",
			};

			mockStrapi.entityService.findOne.mockResolvedValue(mockPaywall);
			mockAdaptyClient.getProductsByPlacement.mockResolvedValue({ data: [] });
			mockStrapi.entityService.create.mockResolvedValue({ id: 1 });

			// Act
			const result = await remoteConfigService.createPreview(1, "en", 60);

			// Assert
			expect(result.success).toBe(true);
			expect(result.preview).toBeDefined();
			expect(result.preview?.paywall_id).toBe(1);
			expect(result.preview?.locale).toBe("en");
			expect(result.preview?.preview_url).toContain(
				"http://localhost:1337/api/preview/",
			);
			expect(result.preview?.expires_at).toBeInstanceOf(Date);
		});
	});

	describe("getDeploymentMetrics", () => {
		it("should return deployment metrics successfully", async () => {
			// Arrange
			const startDate = new Date("2024-01-01");
			const endDate = new Date("2024-01-31");

			const mockDeploymentLogs = [
				{ action: "deploy", timestamp: new Date("2024-01-15") },
				{ action: "deploy", timestamp: new Date("2024-01-20") },
				{ action: "rollback", timestamp: new Date("2024-01-25") },
			];

			const mockAdaptyMetrics = {
				total_requests: 10000,
				successful_deliveries: 9950,
				error_rate: 0.005,
				average_response_time: 150,
				cache_hit_rate: 0.85,
			};

			mockStrapi.entityService.findMany.mockResolvedValue(mockDeploymentLogs);
			mockAdaptyClient.getPlacementAnalytics.mockResolvedValue({
				data: mockAdaptyMetrics,
			});

			// Act
			const result = await remoteConfigService.getDeploymentMetrics(
				"placement_123",
				startDate,
				endDate,
			);

			// Assert
			expect(result.success).toBe(true);
			expect(result.metrics).toBeDefined();
			expect(result.metrics?.versions_deployed).toBe(2);
			expect(result.metrics?.rollbacks).toBe(1);
			expect(result.metrics?.total_requests).toBe(10000);
			expect(result.metrics?.error_rate).toBe(0.005);
		});

		it("should handle missing Adapty analytics gracefully", async () => {
			// Arrange
			const startDate = new Date("2024-01-01");
			const endDate = new Date("2024-01-31");

			mockStrapi.entityService.findMany.mockResolvedValue([]);
			mockAdaptyClient.getPlacementAnalytics.mockRejectedValue(
				new Error("Analytics unavailable"),
			);

			// Act
			const result = await remoteConfigService.getDeploymentMetrics(
				"placement_123",
				startDate,
				endDate,
			);

			// Assert
			expect(result.success).toBe(true);
			expect(result.metrics?.total_requests).toBe(0);
			expect(result.metrics?.versions_deployed).toBe(0);
		});
	});
});
