/**
 * Unit tests for Adapty API client
 */

import {
	afterEach,
	beforeEach,
	describe,
	expect,
	it,
	jest,
} from "@jest/globals";
import axios, { type AxiosError } from "axios";
import {
	AdaptyApiClient,
	createAdaptyClient,
} from "../../src/services/adapty/client";
import {
	AdaptyApiError,
	AdaptyAuthenticationError,
	type AdaptyClientConfig,
	AdaptyNetworkError,
	type AdaptyPlacement,
	type AdaptyProduct,
	AdaptyRateLimitError,
	type AdaptyRemoteConfig,
} from "../../src/services/adapty/types";

// Mock axios
jest.mock("axios");
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe("Adapty API Client", () => {
	let client: AdaptyApiClient;
	let mockAxiosInstance: any;

	const defaultConfig: AdaptyClientConfig = {
		apiKey: "test-api-key",
		baseUrl: "https://api.adapty.io/api/v1",
		timeout: 30000,
		enableRetry: true,
		enableCircuitBreaker: true,
		enableLogging: false,
	};

	beforeEach(() => {
		// Reset all mocks
		jest.clearAllMocks();

		// Mock axios instance
		mockAxiosInstance = {
			request: jest.fn(),
			interceptors: {
				request: {
					use: jest.fn(),
				},
				response: {
					use: jest.fn(),
				},
			},
			defaults: {
				headers: {},
			},
		};

		mockedAxios.create.mockReturnValue(mockAxiosInstance);

		client = new AdaptyApiClient(defaultConfig);
	});

	afterEach(() => {
		jest.restoreAllMocks();
	});

	describe("Client Initialization", () => {
		it("should create client with default configuration", () => {
			expect(mockedAxios.create).toHaveBeenCalledWith({
				baseURL: "https://api.adapty.io/api/v1",
				timeout: 30000,
				headers: {
					Authorization: "Api-Key test-api-key",
					"Content-Type": "application/json",
					"User-Agent": "Strapi-Adapty-CMS/1.0.0",
				},
			});
		});

		it("should setup request and response interceptors", () => {
			expect(mockAxiosInstance.interceptors.request.use).toHaveBeenCalled();
			expect(mockAxiosInstance.interceptors.response.use).toHaveBeenCalled();
		});

		it("should create client with custom configuration", () => {
			const customConfig: AdaptyClientConfig = {
				apiKey: "custom-key",
				baseUrl: "https://custom.api.com",
				timeout: 15000,
				retryAttempts: 5,
				enableLogging: true,
				logLevel: "debug",
			};

			const _customClient = new AdaptyApiClient(customConfig);

			expect(mockedAxios.create).toHaveBeenCalledWith({
				baseURL: "https://custom.api.com",
				timeout: 15000,
				headers: {
					Authorization: "Api-Key custom-key",
					"Content-Type": "application/json",
					"User-Agent": "Strapi-Adapty-CMS/1.0.0",
				},
			});
		});
	});

	describe("Error Handling", () => {
		it("should transform network errors correctly", async () => {
			const networkError = new Error("Network Error");
			(networkError as any).code = "ECONNREFUSED";

			mockAxiosInstance.request.mockRejectedValue(networkError);

			await expect(client.testConnection()).rejects.toThrow(AdaptyNetworkError);
		});

		it("should transform 401 errors to authentication errors", async () => {
			const authError: AxiosError = {
				name: "AxiosError",
				message: "Request failed with status code 401",
				response: {
					status: 401,
					data: { message: "Invalid API key" },
					headers: {},
					config: {},
					statusText: "Unauthorized",
				},
				config: {},
				isAxiosError: true,
				toJSON: () => ({}),
			};

			mockAxiosInstance.request.mockRejectedValue(authError);

			await expect(client.testConnection()).rejects.toThrow(
				AdaptyAuthenticationError,
			);
		});

		it("should transform 429 errors to rate limit errors", async () => {
			const rateLimitError: AxiosError = {
				name: "AxiosError",
				message: "Request failed with status code 429",
				response: {
					status: 429,
					data: { message: "Rate limit exceeded" },
					headers: { "retry-after": "60" },
					config: {},
					statusText: "Too Many Requests",
				},
				config: {},
				isAxiosError: true,
				toJSON: () => ({}),
			};

			mockAxiosInstance.request.mockRejectedValue(rateLimitError);

			await expect(client.testConnection()).rejects.toThrow(
				AdaptyRateLimitError,
			);
		});

		it("should transform other HTTP errors to API errors", async () => {
			const apiError: AxiosError = {
				name: "AxiosError",
				message: "Request failed with status code 400",
				response: {
					status: 400,
					data: {
						message: "Invalid request",
						code: "INVALID_REQUEST",
						details: { field: "name" },
					},
					headers: {},
					config: {},
					statusText: "Bad Request",
				},
				config: {},
				isAxiosError: true,
				toJSON: () => ({}),
			};

			mockAxiosInstance.request.mockRejectedValue(apiError);

			try {
				await client.testConnection();
			} catch (error) {
				expect(error).toBeInstanceOf(AdaptyApiError);
				expect((error as AdaptyApiError).code).toBe("INVALID_REQUEST");
				expect((error as AdaptyApiError).statusCode).toBe(400);
				expect((error as AdaptyApiError).details).toEqual({ field: "name" });
			}
		});
	});

	describe("Circuit Breaker", () => {
		beforeEach(() => {
			client = new AdaptyApiClient({
				...defaultConfig,
				enableCircuitBreaker: true,
				circuitBreakerThreshold: 3,
				circuitBreakerTimeout: 5000,
			});
		});

		it("should open circuit breaker after threshold failures", async () => {
			const error = new Error("Service unavailable");
			mockAxiosInstance.request.mockRejectedValue(error);

			// Trigger failures to reach threshold
			for (let i = 0; i < 3; i++) {
				try {
					await client.testConnection();
				} catch (_e) {
					// Expected to fail
				}
			}

			// Circuit breaker should now be open
			const state = client.getCircuitBreakerState();
			expect(state.state).toBe("open");
			expect(state.failureCount).toBe(3);
		});

		it("should reject requests when circuit breaker is open", async () => {
			// Manually set circuit breaker to open state
			const error = new Error("Service unavailable");
			mockAxiosInstance.request.mockRejectedValue(error);

			// Trigger failures
			for (let i = 0; i < 3; i++) {
				try {
					await client.testConnection();
				} catch (_e) {
					// Expected to fail
				}
			}

			// Next request should be rejected by circuit breaker
			await expect(client.testConnection()).rejects.toThrow(
				"Circuit breaker is open",
			);
		});

		it("should reset circuit breaker on successful response", async () => {
			const error = new Error("Service unavailable");
			mockAxiosInstance.request
				.mockRejectedValueOnce(error)
				.mockRejectedValueOnce(error)
				.mockResolvedValueOnce({ data: { status: "ok" } });

			// Two failures
			try {
				await client.testConnection();
			} catch (_e) {}
			try {
				await client.testConnection();
			} catch (_e) {}

			// One success should reset the circuit breaker
			await client.testConnection();

			const state = client.getCircuitBreakerState();
			expect(state.state).toBe("closed");
			expect(state.failureCount).toBe(0);
		});
	});

	describe("Retry Logic", () => {
		beforeEach(() => {
			client = new AdaptyApiClient({
				...defaultConfig,
				enableRetry: true,
				retryAttempts: 3,
				retryDelay: 100,
			});
		});

		it("should retry on network errors", async () => {
			const networkError = new Error("Network Error");
			mockAxiosInstance.request
				.mockRejectedValueOnce(networkError)
				.mockRejectedValueOnce(networkError)
				.mockResolvedValueOnce({ data: { status: "ok" } });

			const result = await client.testConnection();

			expect(result).toBe(true);
			expect(mockAxiosInstance.request).toHaveBeenCalledTimes(3);
		});

		it("should retry on 5xx errors", async () => {
			const serverError: AxiosError = {
				name: "AxiosError",
				message: "Request failed with status code 500",
				response: {
					status: 500,
					data: { message: "Internal server error" },
					headers: {},
					config: {},
					statusText: "Internal Server Error",
				},
				config: {},
				isAxiosError: true,
				toJSON: () => ({}),
			};

			mockAxiosInstance.request
				.mockRejectedValueOnce(serverError)
				.mockResolvedValueOnce({ data: { status: "ok" } });

			const result = await client.testConnection();

			expect(result).toBe(true);
			expect(mockAxiosInstance.request).toHaveBeenCalledTimes(2);
		});

		it("should not retry on 4xx errors", async () => {
			const clientError: AxiosError = {
				name: "AxiosError",
				message: "Request failed with status code 400",
				response: {
					status: 400,
					data: { message: "Bad request" },
					headers: {},
					config: {},
					statusText: "Bad Request",
				},
				config: {},
				isAxiosError: true,
				toJSON: () => ({}),
			};

			mockAxiosInstance.request.mockRejectedValue(clientError);

			await expect(client.testConnection()).rejects.toThrow(AdaptyApiError);
			expect(mockAxiosInstance.request).toHaveBeenCalledTimes(1);
		});

		it("should respect maximum retry attempts", async () => {
			const networkError = new Error("Network Error");
			mockAxiosInstance.request.mockRejectedValue(networkError);

			await expect(client.testConnection()).rejects.toThrow();
			expect(mockAxiosInstance.request).toHaveBeenCalledTimes(4); // 1 initial + 3 retries
		});
	});

	describe("Placement Methods", () => {
		const mockPlacement: AdaptyPlacement = {
			id: "placement_123",
			name: "Test Placement",
			remote_config_id: "config_123",
			paywall_id: "paywall_123",
			audience_id: "audience_123",
			created_at: "2024-01-01T00:00:00Z",
			updated_at: "2024-01-01T00:00:00Z",
			is_fallback: false,
			variation_id: "variation_123",
		};

		it("should list placements", async () => {
			const mockResponse = {
				data: [mockPlacement],
				meta: {
					pagination: { page: 1, per_page: 25, total: 1, total_pages: 1 },
				},
			};

			mockAxiosInstance.request.mockResolvedValue({ data: mockResponse });

			const result = await client.listPlacements({ page: 1, per_page: 25 });

			expect(mockAxiosInstance.request).toHaveBeenCalledWith({
				method: "GET",
				url: "/placements",
				data: undefined,
				params: { page: 1, per_page: 25 },
				headers: undefined,
				timeout: undefined,
			});

			expect(result.data).toEqual([mockPlacement]);
		});

		it("should get placement by ID", async () => {
			const mockResponse = { data: mockPlacement };
			mockAxiosInstance.request.mockResolvedValue({ data: mockResponse });

			const result = await client.getPlacement("placement_123");

			expect(mockAxiosInstance.request).toHaveBeenCalledWith({
				method: "GET",
				url: "/placements/placement_123",
				data: undefined,
				params: undefined,
				headers: undefined,
				timeout: undefined,
			});

			expect(result.data).toEqual(mockPlacement);
		});

		it("should create placement", async () => {
			const createData = {
				name: "New Placement",
				paywall_id: "paywall_123",
				is_fallback: false,
			};

			const mockResponse = { data: { ...mockPlacement, ...createData } };
			mockAxiosInstance.request.mockResolvedValue({ data: mockResponse });

			const result = await client.createPlacement(createData);

			expect(mockAxiosInstance.request).toHaveBeenCalledWith({
				method: "POST",
				url: "/placements",
				data: createData,
				params: undefined,
				headers: undefined,
				timeout: undefined,
			});

			expect(result.data.name).toBe("New Placement");
		});

		it("should update placement", async () => {
			const updateData = { name: "Updated Placement" };
			const mockResponse = { data: { ...mockPlacement, ...updateData } };
			mockAxiosInstance.request.mockResolvedValue({ data: mockResponse });

			const result = await client.updatePlacement("placement_123", updateData);

			expect(mockAxiosInstance.request).toHaveBeenCalledWith({
				method: "PUT",
				url: "/placements/placement_123",
				data: updateData,
				params: undefined,
				headers: undefined,
				timeout: undefined,
			});

			expect(result.data.name).toBe("Updated Placement");
		});

		it("should delete placement", async () => {
			mockAxiosInstance.request.mockResolvedValue({ data: {} });

			await client.deletePlacement("placement_123");

			expect(mockAxiosInstance.request).toHaveBeenCalledWith({
				method: "DELETE",
				url: "/placements/placement_123",
				data: undefined,
				params: undefined,
				headers: undefined,
				timeout: undefined,
			});
		});
	});

	describe("Product Methods", () => {
		const mockProduct: AdaptyProduct = {
			id: "product_123",
			vendor_product_id: "com.app.premium",
			type: "subscription",
			name: "Premium Subscription",
			store: "app_store",
			price: {
				amount: 9.99,
				currency_code: "USD",
				currency_symbol: "$",
				localized_string: "$9.99",
			},
			subscription_period: {
				unit: "month",
				number_of_units: 1,
			},
			created_at: "2024-01-01T00:00:00Z",
			updated_at: "2024-01-01T00:00:00Z",
		};

		it("should list products", async () => {
			const mockResponse = { data: [mockProduct] };
			mockAxiosInstance.request.mockResolvedValue({ data: mockResponse });

			const result = await client.listProducts({
				store: "app_store",
				type: "subscription",
			});

			expect(mockAxiosInstance.request).toHaveBeenCalledWith({
				method: "GET",
				url: "/products",
				data: undefined,
				params: { store: "app_store", type: "subscription" },
				headers: undefined,
				timeout: undefined,
			});

			expect(result.data).toEqual([mockProduct]);
		});

		it("should get product by ID", async () => {
			const mockResponse = { data: mockProduct };
			mockAxiosInstance.request.mockResolvedValue({ data: mockResponse });

			const result = await client.getProduct("product_123");

			expect(result.data).toEqual(mockProduct);
		});

		it("should sync products", async () => {
			const mockResponse = { data: { synced_count: 5 } };
			mockAxiosInstance.request.mockResolvedValue({ data: mockResponse });

			const result = await client.syncProducts();

			expect(mockAxiosInstance.request).toHaveBeenCalledWith({
				method: "POST",
				url: "/products/sync",
				data: undefined,
				params: undefined,
				headers: undefined,
				timeout: undefined,
			});

			expect(result.data.synced_count).toBe(5);
		});
	});

	describe("Remote Config Methods", () => {
		const mockRemoteConfig: AdaptyRemoteConfig = {
			id: "config_123",
			placement_id: "placement_123",
			lang: "en",
			data: {
				paywall: {
					id: "paywall_123",
					name: "Test Paywall",
					title: "Premium Features",
					subtitle: "Unlock everything",
					description: "Get access to all features",
					cta_text: "Subscribe Now",
					cta_secondary_text: "Start Free Trial",
					theme: {
						name: "Default",
						primary_color: "#007bff",
						background_color: "#ffffff",
						text_color: "#000000",
						button_style: "rounded",
						header_style: "hero",
						product_display_style: "list",
						show_features: true,
						show_testimonials: false,
					},
					features: [],
					testimonials: [],
					product_labels: [],
				},
				products: [],
				ab_test: undefined,
			},
			created_at: "2024-01-01T00:00:00Z",
			updated_at: "2024-01-01T00:00:00Z",
			version: 1,
		};

		it("should list remote configs", async () => {
			const mockResponse = { data: [mockRemoteConfig] };
			mockAxiosInstance.request.mockResolvedValue({ data: mockResponse });

			const result = await client.listRemoteConfigs({
				placement_id: "placement_123",
			});

			expect(result.data).toEqual([mockRemoteConfig]);
		});

		it("should get remote config by placement", async () => {
			const mockResponse = { data: mockRemoteConfig };
			mockAxiosInstance.request.mockResolvedValue({ data: mockResponse });

			const result = await client.getRemoteConfigByPlacement(
				"placement_123",
				"en",
			);

			expect(mockAxiosInstance.request).toHaveBeenCalledWith({
				method: "GET",
				url: "/placements/placement_123/remote-config",
				data: undefined,
				params: { lang: "en" },
				headers: undefined,
				timeout: undefined,
			});

			expect(result.data).toEqual(mockRemoteConfig);
		});

		it("should create remote config", async () => {
			const createData = {
				placement_id: "placement_123",
				lang: "en",
				data: mockRemoteConfig.data,
			};

			const mockResponse = { data: mockRemoteConfig };
			mockAxiosInstance.request.mockResolvedValue({ data: mockResponse });

			const result = await client.createRemoteConfig(createData);

			expect(mockAxiosInstance.request).toHaveBeenCalledWith({
				method: "POST",
				url: "/remote-configs",
				data: createData,
				params: undefined,
				headers: undefined,
				timeout: undefined,
			});

			expect(result.data).toEqual(mockRemoteConfig);
		});

		it("should update remote config by placement", async () => {
			const updateData = { data: mockRemoteConfig.data, version: 2 };
			const mockResponse = { data: { ...mockRemoteConfig, version: 2 } };
			mockAxiosInstance.request.mockResolvedValue({ data: mockResponse });

			const result = await client.updateRemoteConfigByPlacement(
				"placement_123",
				updateData,
			);

			expect(mockAxiosInstance.request).toHaveBeenCalledWith({
				method: "PUT",
				url: "/placements/placement_123/remote-config",
				data: updateData,
				params: undefined,
				headers: undefined,
				timeout: undefined,
			});

			expect(result.data.version).toBe(2);
		});
	});

	describe("Utility Methods", () => {
		it("should test connection successfully", async () => {
			mockAxiosInstance.request.mockResolvedValue({ data: { status: "ok" } });

			const result = await client.testConnection();

			expect(result).toBe(true);
			expect(mockAxiosInstance.request).toHaveBeenCalledWith({
				method: "GET",
				url: "/health",
				data: undefined,
				params: undefined,
				headers: undefined,
				timeout: undefined,
			});
		});

		it("should handle connection test failure", async () => {
			mockAxiosInstance.request.mockRejectedValue(
				new Error("Connection failed"),
			);

			const result = await client.testConnection();

			expect(result).toBe(false);
		});

		it("should get API status", async () => {
			const mockStatus = { status: "operational", version: "1.0.0" };
			mockAxiosInstance.request.mockResolvedValue({ data: mockStatus });

			const result = await client.getApiStatus();

			expect(result.data).toEqual(mockStatus);
		});

		it("should update API key", () => {
			const newApiKey = "new-api-key";

			client.updateApiKey(newApiKey);

			expect(mockAxiosInstance.defaults.headers.Authorization).toBe(
				`Api-Key ${newApiKey}`,
			);
		});

		it("should get current configuration", () => {
			const config = client.getConfig();

			expect(config.apiKey).toBe("test-api-key");
			expect(config.baseUrl).toBe("https://api.adapty.io/api/v1");
		});

		it("should get circuit breaker state", () => {
			const state = client.getCircuitBreakerState();

			expect(state.state).toBe("closed");
			expect(state.failureCount).toBe(0);
		});
	});

	describe("Factory Function", () => {
		it("should create client using factory function", () => {
			const factoryClient = createAdaptyClient(defaultConfig);

			expect(factoryClient).toBeInstanceOf(AdaptyApiClient);
		});
	});

	describe("Request Configuration", () => {
		it("should handle custom headers", async () => {
			mockAxiosInstance.request.mockResolvedValue({ data: { status: "ok" } });

			// Access private request method through testConnection
			await client.testConnection();

			expect(mockAxiosInstance.request).toHaveBeenCalledWith(
				expect.objectContaining({
					method: "GET",
					url: "/health",
				}),
			);
		});

		it("should handle request timeout", async () => {
			const _timeoutClient = new AdaptyApiClient({
				...defaultConfig,
				timeout: 5000,
			});

			expect(mockedAxios.create).toHaveBeenCalledWith(
				expect.objectContaining({
					timeout: 5000,
				}),
			);
		});
	});

	describe("Logging", () => {
		it("should log requests when debug logging is enabled", async () => {
			const consoleSpy = jest.spyOn(console, "log").mockImplementation();

			const loggingClient = new AdaptyApiClient({
				...defaultConfig,
				enableLogging: true,
				logLevel: "debug",
			});

			mockAxiosInstance.request.mockResolvedValue({ data: { status: "ok" } });

			await loggingClient.testConnection();

			// Note: Actual logging happens in interceptors, which are mocked
			// This test verifies the client is created with logging enabled
			expect(loggingClient.getConfig().enableLogging).toBe(true);
			expect(loggingClient.getConfig().logLevel).toBe("debug");

			consoleSpy.mockRestore();
		});

		it("should not log when logging is disabled", () => {
			const consoleSpy = jest.spyOn(console, "log").mockImplementation();

			const nonLoggingClient = new AdaptyApiClient({
				...defaultConfig,
				enableLogging: false,
			});

			expect(nonLoggingClient.getConfig().enableLogging).toBe(false);

			consoleSpy.mockRestore();
		});
	});
});
