/**
 * Integration tests for Adapty synchronization service
 */

import {
	afterEach,
	beforeEach,
	describe,
	expect,
	it,
	jest,
} from "@jest/globals";
import { AdaptyIntegrationService } from "../../src/services/adapty/strapi-integration";

// Mock Strapi
const mockStrapi = {
	entityService: {
		findOne: jest.fn(),
		findMany: jest.fn(),
		create: jest.fn(),
		update: jest.fn(),
		delete: jest.fn(),
	},
	log: {
		info: jest.fn(),
		error: jest.fn(),
		warn: jest.fn(),
	},
};

// Make strapi globally available
(global as any).strapi = mockStrapi;

describe("Adapty Synchronization Service Integration Tests", () => {
	let integrationService: AdaptyIntegrationService;
	let mockAdaptyClient: any;

	beforeEach(() => {
		integrationService = new AdaptyIntegrationService();

		// Mock Adapty client
		mockAdaptyClient = {
			listPlacements: jest.fn(),
			listProducts: jest.fn(),
			getPlacement: jest.fn(),
			createPlacement: jest.fn(),
			updateRemoteConfigByPlacement: jest.fn(),
			createRemoteConfig: jest.fn(),
			testConnection: jest.fn(),
			getApiStatus: jest.fn(),
			getPlacementAnalytics: jest.fn(),
		};

		// Mock the getAdaptyService function
		jest.doMock("../../src/services/adapty", () => ({
			getAdaptyService: () => mockAdaptyClient,
		}));

		// Reset all mocks
		jest.clearAllMocks();
	});

	afterEach(() => {
		jest.resetAllMocks();
	});

	describe("syncPlacementsFromAdapty", () => {
		it("should sync new placements from Adapty to Strapi", async () => {
			// Arrange
			const mockPlacements = [
				{ id: "placement_1", name: "Test Placement 1", is_fallback: false },
				{ id: "placement_2", name: "Test Placement 2", is_fallback: true },
			];

			mockAdaptyClient.listPlacements.mockResolvedValue({
				data: mockPlacements,
			});

			mockStrapi.entityService.findMany
				.mockResolvedValueOnce([]) // No existing paywall for placement_1
				.mockResolvedValueOnce([]) // No existing paywall for placement_2
				.mockResolvedValue([]); // For sync status queries

			mockStrapi.entityService.create.mockResolvedValue({ id: 1 });

			// Act
			const result = await integrationService.syncPlacementsFromAdapty();

			// Assert
			expect(result.success).toBe(true);
			expect(result.synced_count).toBe(2);
			expect(mockStrapi.entityService.create).toHaveBeenCalledTimes(2);
			expect(mockStrapi.entityService.create).toHaveBeenCalledWith(
				"api::paywall.paywall",
				{
					data: expect.objectContaining({
						name: "Test Placement 1",
						placement_id: "placement_1",
						is_fallback: false,
					}),
				},
			);
		});

		it("should update existing paywalls when placement already exists", async () => {
			// Arrange
			const mockPlacements = [
				{ id: "placement_1", name: "Updated Placement", is_fallback: true },
			];

			mockAdaptyClient.listPlacements.mockResolvedValue({
				data: mockPlacements,
			});

			mockStrapi.entityService.findMany
				.mockResolvedValueOnce([{ id: 1, placement_id: "placement_1" }]) // Existing paywall
				.mockResolvedValue([]); // For sync status queries

			mockStrapi.entityService.update.mockResolvedValue({ id: 1 });

			// Act
			const result = await integrationService.syncPlacementsFromAdapty();

			// Assert
			expect(result.success).toBe(true);
			expect(result.synced_count).toBe(1);
			expect(mockStrapi.entityService.update).toHaveBeenCalledWith(
				"api::paywall.paywall",
				1,
				{
					data: expect.objectContaining({
						name: "Updated Placement",
						is_fallback: true,
					}),
				},
			);
		});

		it("should handle sync errors gracefully", async () => {
			// Arrange
			mockAdaptyClient.listPlacements.mockRejectedValue(new Error("API Error"));

			// Act
			const result = await integrationService.syncPlacementsFromAdapty();

			// Assert
			expect(result.success).toBe(false);
			expect(result.synced_count).toBe(0);
			expect(result.errors).toContain("API Error");
		});
	});

	describe("performBidirectionalSync", () => {
		it("should perform complete bidirectional sync without conflicts", async () => {
			// Arrange
			mockAdaptyClient.listProducts.mockResolvedValue({ data: [] });
			mockAdaptyClient.listPlacements.mockResolvedValue({ data: [] });

			mockStrapi.entityService.findMany
				.mockResolvedValueOnce([]) // Products sync
				.mockResolvedValueOnce([]) // Placements sync
				.mockResolvedValueOnce([
					{
						// Paywalls to sync
						id: 1,
						name: "Test Paywall",
						placement_id: "placement_1",
					},
				])
				.mockResolvedValue([]); // Sync status queries

			mockAdaptyClient.getPlacement.mockResolvedValue({
				data: { id: "placement_1", name: "Test Placement" },
			});
			mockAdaptyClient.updateRemoteConfigByPlacement.mockResolvedValue({});

			// Act
			const result =
				await integrationService.performBidirectionalSync("strapi_wins");

			// Assert
			expect(result.success).toBe(true);
			expect(result.conflicts).toHaveLength(0);
			expect(result.synced.paywalls).toBe(1);
		});

		it("should detect and handle conflicts with manual resolution", async () => {
			// Arrange
			mockAdaptyClient.listProducts.mockResolvedValue({ data: [] });
			mockAdaptyClient.listPlacements.mockResolvedValue({ data: [] });

			const mockPaywall = {
				id: 1,
				name: "Test Paywall",
				placement_id: "placement_1",
				updatedAt: new Date(),
			};

			mockStrapi.entityService.findMany
				.mockResolvedValueOnce([]) // Products sync
				.mockResolvedValueOnce([]) // Placements sync
				.mockResolvedValueOnce([mockPaywall]) // Paywalls to sync
				.mockResolvedValueOnce([
					{
						// Sync status with conflict
						entity_type: "paywall",
						entity_id: "1",
						last_sync: new Date(Date.now() - 10000),
						adapty_last_modified: new Date(),
					},
				]);

			mockStrapi.entityService.findOne.mockResolvedValue(mockPaywall);

			// Act
			const result =
				await integrationService.performBidirectionalSync("manual");

			// Assert
			expect(result.conflicts).toHaveLength(1);
			expect(result.conflicts[0].entity_type).toBe("paywall");
			expect(result.conflicts[0].conflict_reason).toContain(
				"Both Strapi and Adapty have been modified",
			);
		});
	});

	describe("handleWebhook", () => {
		it("should handle placement webhook events", async () => {
			// Arrange
			const webhookEvent = {
				type: "placement.updated" as const,
				data: {
					id: "placement_1",
					name: "Updated Placement",
					is_fallback: false,
				},
				timestamp: new Date().toISOString(),
				id: "webhook_1",
			};

			mockStrapi.entityService.findMany
				.mockResolvedValueOnce([{ id: 1, placement_id: "placement_1" }]) // Existing paywall
				.mockResolvedValue([]); // Sync status queries

			mockStrapi.entityService.update.mockResolvedValue({ id: 1 });

			// Act
			const result = await integrationService.handleWebhook(webhookEvent);

			// Assert
			expect(result.success).toBe(true);
			expect(mockStrapi.entityService.update).toHaveBeenCalledWith(
				"api::paywall.paywall",
				1,
				{
					data: expect.objectContaining({
						name: "Updated Placement",
						is_fallback: false,
					}),
				},
			);
		});

		it("should handle product webhook events", async () => {
			// Arrange
			const webhookEvent = {
				type: "product.created" as const,
				data: {
					id: "product_1",
					vendor_product_id: "com.app.premium",
					name: "Premium Subscription",
					type: "subscription",
					store: "app_store",
					price: {
						amount: 999,
						currency_code: "USD",
						localized_string: "$9.99",
					},
				},
				timestamp: new Date().toISOString(),
				id: "webhook_2",
			};

			mockStrapi.entityService.findMany
				.mockResolvedValueOnce([]) // No existing product
				.mockResolvedValue([]); // Sync status queries

			mockStrapi.entityService.create.mockResolvedValue({ id: 1 });

			// Act
			const result = await integrationService.handleWebhook(webhookEvent);

			// Assert
			expect(result.success).toBe(true);
			expect(mockStrapi.entityService.create).toHaveBeenCalledWith(
				"api::product.product",
				{
					data: expect.objectContaining({
						adapty_product_id: "product_1",
						name: "Premium Subscription",
						price_amount: 999,
					}),
				},
			);
		});
	});

	describe("retryFailedSyncs", () => {
		it("should retry failed sync operations with exponential backoff", async () => {
			// Arrange
			const failedSyncs = [
				{
					id: 1,
					entity_type: "paywall",
					entity_id: "1",
					sync_status: "error",
					retry_count: 0,
				},
			];

			mockStrapi.entityService.findMany.mockResolvedValue(failedSyncs);
			mockAdaptyClient.getPlacement.mockResolvedValue({
				data: { id: "placement_1", name: "Test Placement" },
			});
			mockAdaptyClient.updateRemoteConfigByPlacement.mockResolvedValue({});

			mockStrapi.entityService.findOne.mockResolvedValue({
				id: 1,
				placement_id: "placement_1",
				name: "Test Paywall",
			});

			// Act
			const result = await integrationService.retryFailedSyncs(3);

			// Assert
			expect(result.success).toBe(true);
			expect(result.retried_count).toBe(1);
		});
	});

	describe("getSyncStatistics", () => {
		it("should return comprehensive sync statistics", async () => {
			// Arrange
			const mockStatuses = [
				{ sync_status: "synced", last_sync: new Date() },
				{ sync_status: "error", last_sync: new Date() },
				{ sync_status: "pending", last_sync: new Date() },
				{ sync_status: "conflict", last_sync: new Date() },
			];

			mockStrapi.entityService.findMany.mockResolvedValue(mockStatuses);

			// Act
			const stats = await integrationService.getSyncStatistics();

			// Assert
			expect(stats.total_entities).toBe(4);
			expect(stats.synced).toBe(1);
			expect(stats.errors).toBe(1);
			expect(stats.pending).toBe(1);
			expect(stats.conflicts).toBe(1);
			expect(stats.last_sync).toBeInstanceOf(Date);
		});
	});

	describe("bulkSyncPaywalls", () => {
		it("should sync multiple paywalls and return summary", async () => {
			// Arrange
			const paywallIds = [1, 2, 3];

			mockAdaptyClient.getPlacement.mockResolvedValue({
				data: { id: "placement_1", name: "Test Placement" },
			});
			mockAdaptyClient.updateRemoteConfigByPlacement.mockResolvedValue({});

			mockStrapi.entityService.findOne
				.mockResolvedValueOnce({
					id: 1,
					placement_id: "placement_1",
					name: "Paywall 1",
				})
				.mockResolvedValueOnce({
					id: 2,
					placement_id: "placement_2",
					name: "Paywall 2",
				})
				.mockResolvedValueOnce(null); // Paywall 3 not found

			mockStrapi.entityService.update.mockResolvedValue({});

			// Act
			const result = await integrationService.bulkSyncPaywalls(paywallIds);

			// Assert
			expect(result.success).toBe(true);
			expect(result.summary.total).toBe(3);
			expect(result.summary.succeeded).toBe(2);
			expect(result.summary.failed).toBe(1);
			expect(result.results).toHaveLength(3);
		});
	});
});
