/**
 * Enhanced unit tests for Adapty API client - Task 3.1 completion
 */

import { beforeEach, describe, expect, it, jest } from "@jest/globals";
import axios, { type AxiosError } from "axios";
import {
	AdaptyApiClient,
	createAdaptyClient,
} from "../../src/services/adapty/client";
import {
	AdaptyAuthenticationError,
	type AdaptyClientConfig,
	AdaptyConflictError,
	AdaptyServiceUnavailableError,
	AdaptyValidationError,
} from "../../src/services/adapty/types";

// Mock axios
jest.mock("axios");
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe("Adapty API Client - Enhanced Features (Task 3.1)", () => {
	let client: AdaptyApiClient;
	let mockAxiosInstance: any;

	const defaultConfig: AdaptyClientConfig = {
		apiKey: "test-api-key",
		baseUrl: "https://api.adapty.io/api/v1",
		timeout: 30000,
		enableRetry: true,
		enableCircuitBreaker: true,
		enableLogging: false,
		enableTokenRefresh: true,
		refreshToken: "test-refresh-token",
		tokenRefreshThreshold: 300000,
	};

	beforeEach(() => {
		jest.clearAllMocks();

		mockAxiosInstance = {
			request: jest.fn(),
			defaults: {
				headers: {},
			},
			interceptors: {
				request: {
					use: jest.fn(),
				},
				response: {
					use: jest.fn(),
				},
			},
		};

		mockedAxios.create.mockReturnValue(mockAxiosInstance);
		client = new AdaptyApiClient(defaultConfig);
	});

	describe("Token Refresh Functionality", () => {
		it("should refresh token when approaching expiry", async () => {
			const mockRefreshResponse = {
				data: {
					access_token: "new-access-token",
					expires_in: 3600,
				},
			};

			mockedAxios.post.mockResolvedValueOnce(mockRefreshResponse);

			// Set token to expire soon
			client.setTokenExpiry(Date.now() + 200000); // 3.3 minutes from now

			// Mock the request interceptor to trigger token refresh
			const requestInterceptor =
				mockAxiosInstance.interceptors.request.use.mock.calls[0][0];

			const mockConfig = {
				method: "GET",
				url: "/test",
				headers: {},
			};

			await requestInterceptor(mockConfig);

			expect(mockedAxios.post).toHaveBeenCalledWith(
				"https://api.adapty.io/api/v1/auth/refresh",
				{ refresh_token: "test-refresh-token" },
				expect.objectContaining({
					timeout: 30000,
					headers: expect.objectContaining({
						"Content-Type": "application/json",
					}),
				}),
			);
		});

		it("should handle token refresh failure", async () => {
			mockedAxios.post.mockRejectedValueOnce(new Error("Refresh failed"));

			client.setTokenExpiry(Date.now() + 200000);

			const requestInterceptor =
				mockAxiosInstance.interceptors.request.use.mock.calls[0][0];

			const mockConfig = {
				method: "GET",
				url: "/test",
				headers: {},
			};

			await expect(requestInterceptor(mockConfig)).rejects.toThrow(
				AdaptyAuthenticationError,
			);
		});

		it("should call onTokenRefresh callback when token is refreshed", async () => {
			const onTokenRefresh = jest.fn();
			const configWithCallback = {
				...defaultConfig,
				onTokenRefresh,
			};

			client = new AdaptyApiClient(configWithCallback);

			const mockRefreshResponse = {
				data: {
					access_token: "new-access-token",
					expires_in: 3600,
				},
			};

			mockedAxios.post.mockResolvedValueOnce(mockRefreshResponse);
			client.setTokenExpiry(Date.now() + 200000);

			const requestInterceptor =
				mockAxiosInstance.interceptors.request.use.mock.calls[0][0];

			await requestInterceptor({
				method: "GET",
				url: "/test",
				headers: {},
			});

			expect(onTokenRefresh).toHaveBeenCalledWith("new-access-token");
		});
	});

	describe("Enhanced Error Handling", () => {
		it("should handle validation errors (422)", async () => {
			const validationError = {
				response: {
					status: 422,
					data: {
						message: "Validation failed",
						validation_errors: {
							name: ["Name is required"],
							email: ["Email format is invalid"],
						},
					},
				},
			} as AxiosError;

			mockAxiosInstance.request.mockRejectedValueOnce(validationError);

			try {
				await client.getPlacement("test-id");
			} catch (error) {
				expect(error).toBeInstanceOf(AdaptyValidationError);
				expect((error as AdaptyValidationError).validationErrors).toEqual({
					name: ["Name is required"],
					email: ["Email format is invalid"],
				});
			}
		});

		it("should handle conflict errors (409)", async () => {
			const conflictError = {
				response: {
					status: 409,
					data: {
						message: "Resource already exists",
						existing_resource_id: "existing-123",
					},
				},
			} as AxiosError;

			mockAxiosInstance.request.mockRejectedValueOnce(conflictError);

			try {
				await client.createPlacement({ name: "Test Placement" });
			} catch (error) {
				expect(error).toBeInstanceOf(AdaptyConflictError);
				expect((error as AdaptyConflictError).conflictDetails).toEqual({
					message: "Resource already exists",
					existing_resource_id: "existing-123",
				});
			}
		});

		it("should handle service unavailable errors (503)", async () => {
			const serviceError = {
				response: {
					status: 503,
					data: {
						message: "Service temporarily unavailable",
						estimated_recovery_time: 300000,
					},
				},
			} as AxiosError;

			mockAxiosInstance.request.mockRejectedValueOnce(serviceError);

			try {
				await client.getPlacement("test-id");
			} catch (error) {
				expect(error).toBeInstanceOf(AdaptyServiceUnavailableError);
				expect(
					(error as AdaptyServiceUnavailableError).estimatedRecoveryTime,
				).toBe(300000);
			}
		});
	});

	describe("Additional API Methods", () => {
		it("should get API health status", async () => {
			const healthResponse = {
				data: {
					status: "healthy",
					timestamp: "2024-01-01T00:00:00Z",
				},
			};

			mockAxiosInstance.request.mockResolvedValueOnce(healthResponse);

			const result = await client.getHealth();

			expect(mockAxiosInstance.request).toHaveBeenCalledWith({
				method: "GET",
				url: "/health",
				data: undefined,
				params: undefined,
				headers: undefined,
				timeout: undefined,
			});

			expect(result).toEqual(healthResponse);
		});

		it("should validate API key", async () => {
			const validationResponse = {
				data: {
					valid: true,
					permissions: ["read:placements", "write:placements"],
				},
			};

			mockAxiosInstance.request.mockResolvedValueOnce(validationResponse);

			const result = await client.validateApiKey();

			expect(mockAxiosInstance.request).toHaveBeenCalledWith({
				method: "GET",
				url: "/auth/validate",
				data: undefined,
				params: undefined,
				headers: undefined,
				timeout: undefined,
			});

			expect(result).toEqual(validationResponse);
		});

		it("should get account information", async () => {
			const accountResponse = {
				data: {
					id: "account-123",
					name: "Test Account",
					plan: "premium",
				},
			};

			mockAxiosInstance.request.mockResolvedValueOnce(accountResponse);

			const result = await client.getAccount();

			expect(mockAxiosInstance.request).toHaveBeenCalledWith({
				method: "GET",
				url: "/account",
				data: undefined,
				params: undefined,
				headers: undefined,
				timeout: undefined,
			});

			expect(result).toEqual(accountResponse);
		});

		it("should perform bulk placement operations", async () => {
			const bulkResponse = {
				data: {
					success: 2,
					failed: 1,
					errors: [{ id: "placement-3", error: "Not found" }],
				},
			};

			mockAxiosInstance.request.mockResolvedValueOnce(bulkResponse);

			const operations = [
				{
					id: "placement-1",
					operation: "update" as const,
					data: { name: "Updated Name" },
				},
				{ id: "placement-2", operation: "delete" as const },
				{
					id: "placement-3",
					operation: "update" as const,
					data: { name: "Another Update" },
				},
			];

			const result = await client.bulkUpdatePlacements(operations);

			expect(mockAxiosInstance.request).toHaveBeenCalledWith({
				method: "POST",
				url: "/placements/bulk",
				data: { operations },
				params: undefined,
				headers: undefined,
				timeout: undefined,
			});

			expect(result).toEqual(bulkResponse);
		});

		it("should export configuration", async () => {
			const exportResponse = {
				data: {
					config: { placements: [], paywalls: [] },
					timestamp: "2024-01-01T00:00:00Z",
				},
			};

			mockAxiosInstance.request.mockResolvedValueOnce(exportResponse);

			const result = await client.exportConfiguration();

			expect(mockAxiosInstance.request).toHaveBeenCalledWith({
				method: "GET",
				url: "/export",
				data: undefined,
				params: undefined,
				headers: undefined,
				timeout: undefined,
			});

			expect(result).toEqual(exportResponse);
		});

		it("should import configuration", async () => {
			const importResponse = {
				data: {
					imported: 5,
					skipped: 2,
				},
			};

			mockAxiosInstance.request.mockResolvedValueOnce(importResponse);

			const config = { placements: [], paywalls: [] };
			const result = await client.importConfiguration(config);

			expect(mockAxiosInstance.request).toHaveBeenCalledWith({
				method: "POST",
				url: "/import",
				data: { config },
				params: undefined,
				headers: undefined,
				timeout: undefined,
			});

			expect(result).toEqual(importResponse);
		});
	});

	describe("Configuration Management", () => {
		it("should update API key and headers", () => {
			const newApiKey = "new-api-key";

			client.updateApiKey(newApiKey);

			expect(client.getConfig().apiKey).toBe(newApiKey);
			expect(mockAxiosInstance.defaults.headers.Authorization).toBe(
				`Api-Key ${newApiKey}`,
			);
		});

		it("should return current configuration", () => {
			const config = client.getConfig();

			expect(config).toEqual(
				expect.objectContaining({
					apiKey: "test-api-key",
					baseUrl: "https://api.adapty.io/api/v1",
					enableTokenRefresh: true,
				}),
			);
		});

		it("should set token expiry time", () => {
			const expiryTime = Date.now() + 3600000;

			client.setTokenExpiry(expiryTime);

			// We can't directly test the private property, but we can test the behavior
			// by checking if token refresh is triggered when appropriate
			expect(() => client.setTokenExpiry(expiryTime)).not.toThrow();
		});
	});

	describe("Factory Function", () => {
		it("should create client instance with factory function", () => {
			const factoryClient = createAdaptyClient(defaultConfig);

			expect(factoryClient).toBeInstanceOf(AdaptyApiClient);
			expect(factoryClient.getConfig()).toEqual(
				expect.objectContaining(defaultConfig),
			);
		});
	});
});
