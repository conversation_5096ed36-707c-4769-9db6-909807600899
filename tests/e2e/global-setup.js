/**
 * Global setup for E2E tests
 * Handles admin user creation and authentication
 */

const { chromium } = require('@playwright/test');

async function globalSetup(config) {
  console.log('🚀 Starting global E2E test setup...');
  
  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    // Wait for Strapi to be ready
    console.log('⏳ Waiting for Strapi server to be ready...');
    await page.goto('http://localhost:1337/admin', { waitUntil: 'networkidle' });
    
    // Check if admin user exists, if not create one
    const currentUrl = page.url();
    
    if (currentUrl.includes('/auth/register-admin')) {
      console.log('👤 Creating admin user...');
      
      // Fill admin registration form
      await page.fill('input[name="firstname"]', 'E2E');
      await page.fill('input[name="lastname"]', 'Admin');
      await page.fill('input[name="email"]', '<EMAIL>');
      await page.fill('input[name="password"]', 'E2ETestPassword123!');
      await page.fill('input[name="confirmPassword"]', 'E2ETestPassword123!');
      
      // Accept terms
      await page.check('input[name="news"]');
      
      // Submit registration
      await page.click('button[type="submit"]');
      
      // Wait for redirect to admin dashboard
      await page.waitForURL('**/admin', { timeout: 30000 });
      
      console.log('✅ Admin user created successfully');
    } else if (currentUrl.includes('/auth/login')) {
      console.log('🔐 Logging in with existing admin user...');
      
      // Login with existing admin
      await page.fill('input[name="email"]', '<EMAIL>');
      await page.fill('input[name="password"]', 'E2ETestPassword123!');
      await page.click('button[type="submit"]');
      
      // Wait for dashboard
      await page.waitForURL('**/admin', { timeout: 30000 });
      
      console.log('✅ Admin login successful');
    } else {
      console.log('✅ Admin already authenticated');
    }
    
    // Save authentication state
    await context.storageState({ path: 'tests/e2e/auth-state.json' });
    
    console.log('✅ Global setup completed successfully');
    
  } catch (error) {
    console.error('❌ Global setup failed:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

module.exports = globalSetup;