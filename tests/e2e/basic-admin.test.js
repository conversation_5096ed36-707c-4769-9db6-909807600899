/**
 * Basic E2E Test for Admin Interface
 * Simple test to verify admin interface loads and basic functionality
 */

const { test, expect } = require('@playwright/test');

const ADMIN_URL = 'http://localhost:1337/admin';

test.describe('Basic Admin Interface Tests', () => {
  
  test('should load admin interface successfully', async ({ page }) => {
    // Navigate to admin
    await page.goto(ADMIN_URL);
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
    
    // Check if we're on admin page (either login or dashboard)
    const currentUrl = page.url();
    expect(currentUrl).toContain('/admin');
    
    // Should see either login form or admin interface
    const hasLoginForm = await page.locator('input[name="email"]').isVisible();
    const hasAdminInterface = await page.locator('[data-testid="main-nav"]').isVisible();
    const hasRegisterForm = await page.locator('input[name="firstname"]').isVisible();
    
    expect(hasLoginForm || hasAdminInterface || hasRegisterForm).toBeTruthy();
  });
  
  test('should handle admin registration if needed', async ({ page }) => {
    await page.goto(ADMIN_URL);
    await page.waitForLoadState('networkidle');
    
    const currentUrl = page.url();
    
    if (currentUrl.includes('/auth/register-admin')) {
      console.log('Creating admin user...');
      
      // Fill registration form
      await page.fill('input[name="firstname"]', 'E2E');
      await page.fill('input[name="lastname"]', 'Admin');
      await page.fill('input[name="email"]', '<EMAIL>');
      await page.fill('input[name="password"]', 'E2ETestPassword123!');
      await page.fill('input[name="confirmPassword"]', 'E2ETestPassword123!');
      
      // Accept terms if checkbox exists
      const termsCheckbox = page.locator('input[type="checkbox"]');
      if (await termsCheckbox.isVisible()) {
        await termsCheckbox.check();
      }
      
      // Submit registration
      await page.click('button[type="submit"]');
      
      // Wait for redirect
      await page.waitForURL('**/admin/**', { timeout: 30000 });
      
      console.log('Admin user created successfully');
    } else if (currentUrl.includes('/auth/login')) {
      console.log('Logging in with existing admin...');
      
      // Try to login
      await page.fill('input[name="email"]', '<EMAIL>');
      await page.fill('input[name="password"]', 'E2ETestPassword123!');
      await page.click('button[type="submit"]');
      
      // Wait for dashboard
      await page.waitForURL('**/admin/**', { timeout: 30000 });
    }
    
    // Should now be on admin dashboard
    expect(page.url()).toContain('/admin');
  });
  
  test('should navigate to content manager', async ({ page }) => {
    await page.goto(ADMIN_URL);
    await page.waitForLoadState('networkidle');
    
    // Handle auth if needed
    const currentUrl = page.url();
    if (currentUrl.includes('/auth/')) {
      // Skip this test if not authenticated
      test.skip();
    }
    
    // Look for Content Manager link
    const contentManagerLink = page.locator('text=Content Manager').or(
      page.locator('[href*="content-manager"]')
    );
    
    if (await contentManagerLink.isVisible()) {
      await contentManagerLink.click();
      await page.waitForLoadState('networkidle');
      
      // Should be on content manager page
      expect(page.url()).toContain('content-manager');
    } else {
      console.log('Content Manager not visible, checking navigation structure...');
      
      // Log available navigation items for debugging
      const navItems = await page.locator('nav a, nav button').allTextContents();
      console.log('Available navigation items:', navItems);
    }
  });
  
  test('should check for custom components', async ({ page }) => {
    await page.goto(ADMIN_URL);
    await page.waitForLoadState('networkidle');
    
    // Skip if not authenticated
    const currentUrl = page.url();
    if (currentUrl.includes('/auth/')) {
      test.skip();
    }
    
    // Check if page loads without JavaScript errors
    const errors = [];
    page.on('pageerror', error => errors.push(error.message));
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    // Navigate around the interface
    await page.waitForTimeout(3000);
    
    // Log any errors found
    if (errors.length > 0) {
      console.log('JavaScript errors found:', errors);
    }
    
    // Check for basic admin interface elements
    const hasNavigation = await page.locator('nav').isVisible();
    const hasMainContent = await page.locator('main').or(page.locator('[role="main"]')).isVisible();
    
    expect(hasNavigation || hasMainContent).toBeTruthy();
  });
  
  test('should verify admin interface responsiveness', async ({ page }) => {
    await page.goto(ADMIN_URL);
    await page.waitForLoadState('networkidle');
    
    // Skip if not authenticated
    if (page.url().includes('/auth/')) {
      test.skip();
    }
    
    // Test different viewport sizes
    const viewports = [
      { width: 1920, height: 1080 }, // Desktop
      { width: 1024, height: 768 },  // Tablet
      { width: 375, height: 667 }    // Mobile
    ];
    
    for (const viewport of viewports) {
      await page.setViewportSize(viewport);
      await page.waitForTimeout(1000);
      
      // Check if page is still functional
      const isVisible = await page.locator('body').isVisible();
      expect(isVisible).toBeTruthy();
      
      console.log(`Viewport ${viewport.width}x${viewport.height}: OK`);
    }
  });
});