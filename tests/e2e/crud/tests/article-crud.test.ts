import { test, expect } from '@playwright/test';
import { ArticlePage } from '../core/page-objects/ArticlePage';
import { EntityDataFactory } from '../core/factories/EntityDataFactory';
import { CRUDTestRunner } from '../core/utilities/CRUDTestRunner';

test.describe('Article CRUD Operations', () => {
  let articlePage: ArticlePage;
  let crudRunner: CRUDTestRunner;

  test.beforeEach(async ({ page }) => {
    articlePage = new ArticlePage(page);
    crudRunner = new CRUDTestRunner(page, articlePage);
    await page.goto('http://localhost:1337/admin');
    await page.waitForLoadState('networkidle');
  });

  test.afterEach(async () => {
    await crudRunner.cleanup();
  });

  test('should perform complete CRUD operations on article', async () => {
    const testData = EntityDataFactory.createTestData('article');
    await crudRunner.runFullCRUDTest(testData, 'article');
  });

  test('should test performance within acceptable limits', async () => {
    const testData = EntityDataFactory.createTestData('article');
    await crudRunner.testPerformance(testData, 'article', 6000);
  });
});