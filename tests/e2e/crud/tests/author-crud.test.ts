import { test, expect } from '@playwright/test';
import { AuthorPage } from '../core/page-objects/AuthorPage';
import { EntityDataFactory } from '../core/factories/EntityDataFactory';
import { CRUDTestRunner } from '../core/utilities/CRUDTestRunner';

test.describe('Author CRUD Operations', () => {
  let authorPage: AuthorPage;
  let crudRunner: CRUDTestRunner;

  test.beforeEach(async ({ page }) => {
    authorPage = new AuthorPage(page);
    crudRunner = new CRUDTestRunner(page, authorPage);
    await page.goto('http://localhost:1337/admin');
    await page.waitForLoadState('networkidle');
  });

  test.afterEach(async () => {
    await crudRunner.cleanup();
  });

  test('should perform complete CRUD operations on author', async () => {
    const testData = EntityDataFactory.createTestData('author');
    await crudRunner.runFullCRUDTest(testData, 'author');
  });

  test('should test performance within acceptable limits', async () => {
    const testData = EntityDataFactory.createTestData('author');
    await crudRunner.testPerformance(testData, 'author', 6000);
  });
});