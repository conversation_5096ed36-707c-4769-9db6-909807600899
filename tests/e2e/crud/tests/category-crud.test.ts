import { test, expect } from '@playwright/test';
import { CategoryPage } from '../core/page-objects/CategoryPage';
import { EntityDataFactory } from '../core/factories/EntityDataFactory';
import { CRUDTestRunner } from '../core/utilities/CRUDTestRunner';

test.describe('Category CRUD Operations', () => {
  let categoryPage: CategoryPage;
  let crudRunner: CRUDTestRunner;

  test.beforeEach(async ({ page }) => {
    categoryPage = new CategoryPage(page);
    crudRunner = new CRUDTestRunner(page, categoryPage);
    await page.goto('http://localhost:1337/admin');
    await page.waitForLoadState('networkidle');
  });

  test.afterEach(async () => {
    await crudRunner.cleanup();
  });

  test('should perform complete CRUD operations on category', async () => {
    const testData = EntityDataFactory.createTestData('category');
    await crudRunner.runFullCRUDTest(testData, 'category');
  });

  test('should test performance within acceptable limits', async () => {
    const testData = EntityDataFactory.createTestData('category');
    await crudRunner.testPerformance(testData, 'category', 6000);
  });
});