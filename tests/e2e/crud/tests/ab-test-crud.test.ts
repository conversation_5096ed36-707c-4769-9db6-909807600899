import { test, expect } from '@playwright/test';
import { ABTestPage } from '../core/page-objects/ABTestPage';
import { EntityDataFactory } from '../core/factories/EntityDataFactory';
import { CRUDTestRunner } from '../core/utilities/CRUDTestRunner';
import { DatabaseCleaner } from '../core/utilities/DatabaseCleaner';

test.describe('AB Test CRUD Operations', () => {
  let abTestPage: ABTestPage;
  let crudRunner: CRUDTestRunner;
  let cleaner: DatabaseCleaner;

  test.beforeEach(async ({ page }) => {
    abTestPage = new ABTestPage(page);
    crudRunner = new CRUDTestRunner(page, abTestPage);
    cleaner = new DatabaseCleaner(page);

    await page.goto('http://localhost:1337/admin');
    await page.waitForLoadState('networkidle');
  });

  test.afterEach(async () => {
    await crudRunner.cleanup();
  });

  test('should perform complete CRUD operations on ab-test', async () => {
    const testData = EntityDataFactory.createTestData('ab-test', {
      name: 'E2E Test AB Test CRUD',
      hypothesis: 'E2E testing hypothesis'
    });

    await crudRunner.runFullCRUDTest(testData, 'ab-test');
  });

  test('should validate required fields', async () => {
    const requiredFields = ['name', 'hypothesis', 'start_date', 'end_date'];
    const fieldConstraints = [
      {
        field: 'traffic_allocation',
        invalidValue: 150,
        expectedError: 'Must be between 0 and 100'
      },
      {
        field: 'confidence_level',
        invalidValue: 50,
        expectedError: 'Must be at least 90'
      }
    ];

    await crudRunner.testValidation(requiredFields, fieldConstraints);
  });

  test('should handle date range validation', async () => {
    const testData = EntityDataFactory.createMinimalData('ab-test');

    const id = await abTestPage.create(testData);

    // Test valid date range
    await abTestPage.navigateToEdit(id);
    await abTestPage.setDateRange('2024-12-20T00:00:00.000Z', '2024-12-27T00:00:00.000Z');
    await abTestPage['save']();

    const isValid = await abTestPage.validateDateRange();
    expect(isValid).toBe(true);

    await abTestPage.delete(id);
  });

  test('should handle traffic allocation settings', async () => {
    const testData = EntityDataFactory.createMinimalData('ab-test');

    const id = await abTestPage.create(testData);

    // Test traffic allocation
    await abTestPage.navigateToEdit(id);
    await abTestPage.setTrafficAllocation(75);
    await abTestPage['save']();

    const readData = await abTestPage.read(id);
    expect(readData.traffic_allocation).toBe(75);

    await abTestPage.delete(id);
  });

  test('should handle confidence level settings', async () => {
    const testData = EntityDataFactory.createMinimalData('ab-test');

    const id = await abTestPage.create(testData);

    // Test confidence level
    await abTestPage.navigateToEdit(id);
    await abTestPage.setConfidenceLevel(95);
    await abTestPage['save']();

    const readData = await abTestPage.read(id);
    expect(readData.confidence_level).toBe(95);

    await abTestPage.delete(id);
  });

  test('should handle test type selection', async () => {
    const testData = EntityDataFactory.createMinimalData('ab-test');

    const id = await abTestPage.create(testData);

    // Test type selection
    await abTestPage.navigateToEdit(id);
    await abTestPage.setTestType('paywall');
    await abTestPage['save']();

    const readData = await abTestPage.read(id);
    expect(readData.test_type).toBe('paywall');

    await abTestPage.delete(id);
  });

  test('should handle success metrics management', async () => {
    const testData = EntityDataFactory.createMinimalData('ab-test');

    const id = await abTestPage.create(testData);

    // Add success metrics
    await abTestPage.navigateToEdit(id);
    await abTestPage.addSuccessMetric('conversion_rate');
    await abTestPage.addSuccessMetric('revenue');
    await abTestPage['save']();

    const readData = await abTestPage.read(id);
    expect(readData.success_metrics).toContain('conversion_rate');
    expect(readData.success_metrics).toContain('revenue');

    await abTestPage.delete(id);
  });

  test('should calculate test duration correctly', async () => {
    const testData = EntityDataFactory.createTestData('ab-test', {
      start_date: '2024-12-20T00:00:00.000Z',
      end_date: '2024-12-27T00:00:00.000Z'
    });

    const id = await abTestPage.create(testData);
    await abTestPage.navigateToEdit(id);

    const duration = await abTestPage.getTestDuration();
    expect(duration).toBe(7); // 7 days

    await abTestPage.delete(id);
  });

  test('should handle test status workflow', async () => {
    const testData = EntityDataFactory.createTestData('ab-test', {
      status: 'draft'
    });

    const id = await abTestPage.create(testData);

    // Update status to active
    await abTestPage.update(id, { status: 'active' });

    const updatedData = await abTestPage.read(id);
    expect(updatedData.status).toBe('active');

    await abTestPage.delete(id);
  });

  test('should perform bulk operations', async () => {
    const testDataArray = [
      EntityDataFactory.createTestData('ab-test', { name: 'Bulk AB Test 1' }),
      EntityDataFactory.createTestData('ab-test', { name: 'Bulk AB Test 2' }),
      EntityDataFactory.createTestData('ab-test', { name: 'Bulk AB Test 3' })
    ];

    await crudRunner.testBulkOperations(testDataArray, 'ab-test');
  });

  test('should test performance within acceptable limits', async () => {
    const testData = EntityDataFactory.createTestData('ab-test');
    await crudRunner.testPerformance(testData, 'ab-test', 8000); // 8 second max
  });
});