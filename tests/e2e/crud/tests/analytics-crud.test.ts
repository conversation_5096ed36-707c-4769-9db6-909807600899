import { test, expect } from '@playwright/test';
import { AnalyticsPage } from '../core/page-objects/AnalyticsPage';
import { EntityDataFactory } from '../core/factories/EntityDataFactory';
import { CRUDTestRunner } from '../core/utilities/CRUDTestRunner';

test.describe('Analytics CRUD Operations', () => {
  let analyticsPage: AnalyticsPage;
  let crudRunner: CRUDTestRunner;

  test.beforeEach(async ({ page }) => {
    analyticsPage = new AnalyticsPage(page);
    crudRunner = new CRUDTestRunner(page, analyticsPage);
    await page.goto('http://localhost:1337/admin');
    await page.waitForLoadState('networkidle');
  });

  test.afterEach(async () => {
    await crudRunner.cleanup();
  });

  test('should perform complete CRUD operations on analytics', async () => {
    const testData = EntityDataFactory.createTestData('analytics');
    await crudRunner.runFullCRUDTest(testData, 'analytics');
  });

  test('should handle metric type selection', async () => {
    const testData = EntityDataFactory.createMinimalData('analytics');
    const id = await analyticsPage.create(testData);

    await analyticsPage.navigateToEdit(id);
    await analyticsPage.setMetricType('revenue');
    await analyticsPage['save']();

    const readData = await analyticsPage.read(id);
    expect(readData.metric_type).toBe('revenue');

    await analyticsPage.delete(id);
  });

  test('should handle aggregation period settings', async () => {
    const testData = EntityDataFactory.createMinimalData('analytics');
    const id = await analyticsPage.create(testData);

    await analyticsPage.navigateToEdit(id);
    await analyticsPage.setAggregationPeriod('weekly');
    await analyticsPage['save']();

    const readData = await analyticsPage.read(id);
    expect(readData.aggregation_period).toBe('weekly');

    await analyticsPage.delete(id);
  });

  test('should test performance within acceptable limits', async () => {
    const testData = EntityDataFactory.createTestData('analytics');
    await crudRunner.testPerformance(testData, 'analytics', 6000);
  });
});