import { test, expect } from '@playwright/test';
import { MobileAppPage } from '../core/page-objects/MobileAppPage';
import { EntityDataFactory } from '../core/factories/EntityDataFactory';
import { CRUDTestRunner } from '../core/utilities/CRUDTestRunner';

test.describe('Mobile App CRUD Operations', () => {
  let mobileAppPage: MobileAppPage;
  let crudRunner: CRUDTestRunner;

  test.beforeEach(async ({ page }) => {
    mobileAppPage = new MobileAppPage(page);
    crudRunner = new CRUDTestRunner(page, mobileAppPage);
    await page.goto('http://localhost:1337/admin');
    await page.waitForLoadState('networkidle');
  });

  test.afterEach(async () => {
    await crudRunner.cleanup();
  });

  test('should perform complete CRUD operations on mobile-app', async () => {
    const testData = EntityDataFactory.createTestData('mobile-app');
    await crudRunner.runFullCRUDTest(testData, 'mobile-app');
  });

  test('should handle platform selection', async () => {
    const testData = EntityDataFactory.createMinimalData('mobile-app');
    const id = await mobileAppPage.create(testData);

    await mobileAppPage.navigateToEdit(id);
    await mobileAppPage.setPlatform('android');
    await mobileAppPage['save']();

    const readData = await mobileAppPage.read(id);
    expect(readData.platform).toBe('android');

    await mobileAppPage.delete(id);
  });

  test('should test performance within acceptable limits', async () => {
    const testData = EntityDataFactory.createTestData('mobile-app');
    await crudRunner.testPerformance(testData, 'mobile-app', 6000);
  });
});