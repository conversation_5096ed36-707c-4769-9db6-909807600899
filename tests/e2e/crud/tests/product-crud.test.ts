import { test, expect } from '@playwright/test';
import { ProductPage } from '../core/page-objects/ProductPage';
import { EntityDataFactory } from '../core/factories/EntityDataFactory';
import { CRUDTestRunner } from '../core/utilities/CRUDTestRunner';

test.describe('Product CRUD Operations', () => {
  let productPage: ProductPage;
  let crudRunner: CRUDTestRunner;

  test.beforeEach(async ({ page }) => {
    productPage = new ProductPage(page);
    crudRunner = new CRUDTestRunner(page, productPage);
    await page.goto('http://localhost:1337/admin');
    await page.waitForLoadState('networkidle');
  });

  test.afterEach(async () => {
    await crudRunner.cleanup();
  });

  test('should perform complete CRUD operations on product', async () => {
    const testData = EntityDataFactory.createTestData('product');
    await crudRunner.runFullCRUDTest(testData, 'product');
  });

  test('should test performance within acceptable limits', async () => {
    const testData = EntityDataFactory.createTestData('product');
    await crudRunner.testPerformance(testData, 'product', 6000);
  });
});