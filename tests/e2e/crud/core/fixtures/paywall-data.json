{
  "minimal": {
    "name": "E2E Test Paywall Minimal",
    "placement_id": "e2e-minimal-test",
    "title": "Test Paywall",
    "cta_text": "Subscribe Now",
    "theme": {
      "primary_color": "#007bff",
      "background_color": "#ffffff",
      "text_color": "#000000"
    }
  },
  "complete": {
    "name": "E2E Test Paywall Complete",
    "placement_id": "e2e-complete-test",
    "title": "Premium Subscription",
    "subtitle": "Unlock all premium features",
    "cta_text": "Start Free Trial",
    "description_text": "Get access to exclusive content, advanced features, and priority support.",
    "status": "draft",
    "theme": {
      "primary_color": "#007bff",
      "background_color": "#ffffff",
      "text_color": "#333333"
    },
    "features": [
      {
        "title": "Unlimited Access",
        "description": "Access to all premium content and features",
        "icon": "star"
      },
      {
        "title": "Priority Support",
        "description": "Get help when you need it most",
        "icon": "support"
      },
      {
        "title": "Advanced Analytics",
        "description": "Detailed insights and reporting",
        "icon": "chart"
      }
    ],
    "testimonials": [
      {
        "content": "This service has transformed how we work. Highly recommended!",
        "author": "John Smith",
        "title": "CEO, Tech Corp"
      },
      {
        "content": "The premium features are worth every penny. Great value!",
        "author": "Sarah Johnson",
        "title": "Product Manager"
      }
    ]
  },
  "validation": {
    "requiredFields": ["name", "placement_id", "title", "cta_text", "theme"],
    "constraints": [
      {
        "field": "placement_id",
        "invalidValue": "a".repeat(101),
        "expectedError": "Must be 100 characters or less"
      },
      {
        "field": "name",
        "invalidValue": "",
        "expectedError": "This field is required"
      }
    ]
  }
}