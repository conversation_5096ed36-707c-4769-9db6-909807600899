import { Page, expect } from '@playwright/test';
import { BaseEntityPage } from '../page-objects/BaseEntityPage';
import { DatabaseCleaner } from './DatabaseCleaner';

export class CRUDTestRunner {
  private page: Page;
  private entityPage: BaseEntityPage;
  private cleaner: DatabaseCleaner;
  private createdIds: string[] = [];

  constructor(page: Page, entityPage: BaseEntityPage) {
    this.page = page;
    this.entityPage = entityPage;
    this.cleaner = new DatabaseCleaner(page);
  }

  async runFullCRUDTest(testData: any, entityName: string): Promise<void> {
    console.log(`🧪 Running full CRUD test for ${entityName}`);

    // Test CREATE
    const createdId = await this.testCreate(testData);
    this.createdIds.push(createdId);

    // Test READ
    await this.testRead(createdId, testData);

    // Test UPDATE
    const updateData = { ...testData, name: `${testData.name} - Updated` };
    await this.testUpdate(createdId, updateData);

    // Test DELETE
    await this.testDelete(createdId);
  }

  async testCreate(data: any): Promise<string> {
    console.log('  ✅ Testing CREATE operation');
    
    const id = await this.entityPage.create(data);
    expect(id).toBeTruthy();
    expect(id).toMatch(/^\d+$/); // Should be a numeric ID
    
    // Verify entity appears in list
    await this.entityPage.navigateToList();
    const isInList = await this.entityPage.isEntityInList(data.name);
    expect(isInList).toBe(true);
    
    return id;
  }

  async testRead(id: string, expectedData: any): Promise<void> {
    console.log('  📖 Testing READ operation');
    
    const actualData = await this.entityPage.read(id);
    
    // Verify core fields match
    expect(actualData.name).toBe(expectedData.name);
    
    // Verify other important fields if they exist
    if (expectedData.description && actualData.description) {
      expect(actualData.description).toBe(expectedData.description);
    }
    
    if (expectedData.title && actualData.title) {
      expect(actualData.title).toBe(expectedData.title);
    }
    
    if (expectedData.status && actualData.status) {
      expect(actualData.status).toBe(expectedData.status);
    }
  }

  async testUpdate(id: string, updateData: any): Promise<void> {
    console.log('  ✏️ Testing UPDATE operation');
    
    await this.entityPage.update(id, updateData);
    
    // Verify update was successful
    const updatedData = await this.entityPage.read(id);
    expect(updatedData.name).toBe(updateData.name);
    
    // Verify the entity still exists in the list with updated name
    const isInList = await this.entityPage.isEntityInList(updateData.name);
    expect(isInList).toBe(true);
  }

  async testDelete(id: string): Promise<void> {
    console.log('  🗑️ Testing DELETE operation');
    
    await this.entityPage.delete(id);
    
    // Verify entity is no longer accessible
    try {
      await this.page.goto(`${this.entityPage['baseUrl']}/${id}`);
      
      // Should either get a 404 page or be redirected to list
      const currentUrl = this.page.url();
      const isNotFoundOrRedirected = 
        currentUrl.includes('404') || 
        currentUrl === this.entityPage['baseUrl'] ||
        await this.page.locator('text=Not Found, text=404, text=Entry not found').isVisible();
      
      expect(isNotFoundOrRedirected).toBe(true);
    } catch (error) {
      // If navigation fails, that's also acceptable (404 response)
      console.log('  ✅ Entity deletion confirmed (navigation failed as expected)');
    }
    
    // Remove from cleanup list since it's already deleted
    this.createdIds = this.createdIds.filter(createdId => createdId !== id);
  }

  async testValidation(requiredFields: string[], fieldConstraints: any[] = []): Promise<void> {
    console.log('  🔍 Testing validation rules');
    
    // Test required fields
    if (requiredFields.length > 0) {
      await this.entityPage.validateRequiredFields(requiredFields);
    }
    
    // Test field constraints
    for (const constraint of fieldConstraints) {
      await this.entityPage.validateFieldConstraints(
        constraint.field,
        constraint.invalidValue,
        constraint.expectedError
      );
    }
  }

  async testBulkOperations(testDataArray: any[], entityName: string): Promise<void> {
    console.log(`  📦 Testing bulk operations for ${entityName}`);
    
    const createdIds: string[] = [];
    
    try {
      // Bulk create
      for (const data of testDataArray) {
        const id = await this.entityPage.create(data);
        createdIds.push(id);
        this.createdIds.push(id);
      }
      
      // Verify all entities were created
      expect(createdIds).toHaveLength(testDataArray.length);
      
      // Verify all entities appear in list
      await this.entityPage.navigateToList();
      for (const data of testDataArray) {
        const isInList = await this.entityPage.isEntityInList(data.name);
        expect(isInList).toBe(true);
      }
      
      console.log(`  ✅ Bulk create successful: ${createdIds.length} entities created`);
      
    } catch (error) {
      console.error(`  ❌ Bulk operations failed:`, error);
      throw error;
    }
  }

  async testWorkflow(workflowSteps: any[], entityName: string): Promise<void> {
    console.log(`  🔄 Testing workflow for ${entityName}`);
    
    let entityId: string = '';
    
    for (const step of workflowSteps) {
      switch (step.action) {
        case 'create':
          entityId = await this.testCreate(step.data);
          break;
        case 'update':
          await this.testUpdate(entityId, step.data);
          break;
        case 'read':
          await this.testRead(entityId, step.expectedData);
          break;
        case 'delete':
          await this.testDelete(entityId);
          entityId = ''; // Reset since entity is deleted
          break;
        default:
          throw new Error(`Unknown workflow action: ${step.action}`);
      }
    }
  }

  async testPerformance(data: any, entityName: string, maxTimeMs: number = 5000): Promise<void> {
    console.log(`  ⚡ Testing performance for ${entityName} (max: ${maxTimeMs}ms)`);
    
    const startTime = Date.now();
    
    // Test create performance
    const createStart = Date.now();
    const id = await this.entityPage.create(data);
    const createTime = Date.now() - createStart;
    this.createdIds.push(id);
    
    // Test read performance
    const readStart = Date.now();
    await this.entityPage.read(id);
    const readTime = Date.now() - readStart;
    
    // Test update performance
    const updateStart = Date.now();
    await this.entityPage.update(id, { ...data, name: `${data.name} - Updated` });
    const updateTime = Date.now() - updateStart;
    
    // Test delete performance
    const deleteStart = Date.now();
    await this.entityPage.delete(id);
    const deleteTime = Date.now() - deleteStart;
    
    const totalTime = Date.now() - startTime;
    
    console.log(`    Create: ${createTime}ms, Read: ${readTime}ms, Update: ${updateTime}ms, Delete: ${deleteTime}ms`);
    console.log(`    Total: ${totalTime}ms`);
    
    // Performance assertions
    expect(createTime).toBeLessThan(maxTimeMs);
    expect(readTime).toBeLessThan(maxTimeMs);
    expect(updateTime).toBeLessThan(maxTimeMs);
    expect(deleteTime).toBeLessThan(maxTimeMs);
    expect(totalTime).toBeLessThan(maxTimeMs * 2); // Allow double time for full workflow
    
    // Remove from cleanup since already deleted
    this.createdIds = this.createdIds.filter(createdId => createdId !== id);
  }

  async cleanup(): Promise<void> {
    console.log('  🧹 Cleaning up test data');
    
    for (const id of this.createdIds) {
      try {
        await this.entityPage.delete(id);
        console.log(`    ✅ Cleaned up entity ${id}`);
      } catch (error) {
        console.warn(`    ⚠️ Failed to cleanup entity ${id}:`, error);
      }
    }
    
    this.createdIds = [];
  }

  getCreatedIds(): string[] {
    return [...this.createdIds];
  }

  async verifyNoRegressions(): Promise<void> {
    console.log('  🔍 Verifying no regressions in existing functionality');
    
    // Navigate to entity list and verify it loads
    await this.entityPage.navigateToList();
    await this.entityPage.waitForPageLoad();
    
    // Verify create form loads
    await this.entityPage.navigateToCreate();
    await this.entityPage.waitForPageLoad();
    
    // Navigate back to list
    await this.entityPage.navigateToList();
    
    console.log('  ✅ No regressions detected');
  }
}