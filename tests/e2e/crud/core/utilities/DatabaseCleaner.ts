import { Page } from '@playwright/test';

export class DatabaseCleaner {
  private page: Page;

  constructor(page: Page) {
    this.page = page;
  }

  async cleanupTestData(entityType: string, testPrefix: string = 'E2E-TEST-'): Promise<void> {
    console.log(`🧹 Cleaning up ${entityType} test data with prefix: ${testPrefix}`);

    try {
      // Navigate to entity list
      const baseUrl = `http://localhost:1337/admin/content-manager/collection-types/api::${entityType}.${entityType}`;
      await this.page.goto(baseUrl);
      await this.page.waitForLoadState('networkidle');

      // Look for test entries with various selectors
      const testEntrySelectors = [
        `[data-testid="entity-row"]:has-text("${testPrefix}")`,
        `tr:has-text("${testPrefix}")`,
        `[role="row"]:has-text("${testPrefix}")`,
        `.row:has-text("${testPrefix}")`,
        `*:has-text("${testPrefix}")`
      ];

      let testEntries = null;
      for (const selector of testEntrySelectors) {
        testEntries = this.page.locator(selector);
        const count = await testEntries.count();
        if (count > 0) {
          console.log(`  Found ${count} test entries using selector: ${selector}`);
          break;
        }
      }

      if (!testEntries) {
        console.log(`  No test entries found for ${entityType}`);
        return;
      }

      const entryCount = await testEntries.count();
      console.log(`  Found ${entryCount} test entries to clean up`);

      // Delete entries one by one
      for (let i = 0; i < entryCount; i++) {
        try {
          // Always click the first entry as the list updates after deletion
          const firstEntry = testEntries.first();
          
          if (await firstEntry.isVisible()) {
            await firstEntry.click();
            await this.page.waitForSelector('form, [data-testid="entity-form"]', { timeout: 5000 });
            
            // Find and click delete button
            const deleteSelectors = [
              '[data-testid="delete-button"]',
              'button:has-text("Delete")',
              '[aria-label="Delete"]',
              'button[type="button"]:has-text("Delete")'
            ];
            
            let deleteClicked = false;
            for (const selector of deleteSelectors) {
              const deleteButton = this.page.locator(selector).first();
              if (await deleteButton.isVisible()) {
                await deleteButton.click();
                deleteClicked = true;
                break;
              }
            }
            
            if (deleteClicked) {
              // Confirm deletion
              const confirmSelectors = [
                '[data-testid="confirm-delete"]',
                'button:has-text("Confirm")',
                'button:has-text("Delete")',
                'button:has-text("Yes")'
              ];
              
              for (const selector of confirmSelectors) {
                const confirmButton = this.page.locator(selector);
                if (await confirmButton.isVisible()) {
                  await confirmButton.click();
                  break;
                }
              }
              
              // Wait for redirect back to list
              await this.page.waitForURL(baseUrl, { timeout: 10000 });
              console.log(`    ✅ Deleted test ${entityType} entry ${i + 1}/${entryCount}`);
            } else {
              console.warn(`    ⚠️ Delete button not found for ${entityType} entry`);
            }
          }
        } catch (error) {
          console.warn(`    ⚠️ Failed to delete test ${entityType} entry ${i + 1}:`, error);
        }
      }

      console.log(`  ✅ Cleanup completed for ${entityType}`);
      
    } catch (error) {
      console.error(`  ❌ Cleanup failed for ${entityType}:`, error);
    }
  }

  async cleanupAllTestData(testPrefix: string = 'E2E-TEST-'): Promise<void> {
    console.log(`🧹 Starting cleanup of all test data with prefix: ${testPrefix}`);
    
    const entities = [
      'paywall', 'ab-test', 'analytics', 'mobile-app', 
      'product', 'author', 'article', 'category'
    ];

    for (const entity of entities) {
      await this.cleanupTestData(entity, testPrefix);
    }
    
    console.log('✅ All test data cleanup completed');
  }

  async cleanupByIds(entityType: string, ids: string[]): Promise<void> {
    console.log(`🧹 Cleaning up ${entityType} by IDs: ${ids.join(', ')}`);
    
    const baseUrl = `http://localhost:1337/admin/content-manager/collection-types/api::${entityType}.${entityType}`;
    
    for (const id of ids) {
      try {
        await this.page.goto(`${baseUrl}/${id}`);
        
        // Check if the page loads successfully (entity exists)
        try {
          await this.page.waitForSelector('form, [data-testid="entity-form"]', { timeout: 5000 });
        } catch {
          console.log(`    ⚠️ Entity ${id} not found, skipping`);
          continue;
        }
        
        // Find and click delete button
        const deleteSelectors = [
          '[data-testid="delete-button"]',
          'button:has-text("Delete")',
          '[aria-label="Delete"]',
          'button[type="button"]:has-text("Delete")'
        ];
        
        let deleteClicked = false;
        for (const selector of deleteSelectors) {
          const deleteButton = this.page.locator(selector).first();
          if (await deleteButton.isVisible()) {
            await deleteButton.click();
            deleteClicked = true;
            break;
          }
        }
        
        if (deleteClicked) {
          // Confirm deletion
          const confirmSelectors = [
            '[data-testid="confirm-delete"]',
            'button:has-text("Confirm")',
            'button:has-text("Delete")',
            'button:has-text("Yes")'
          ];
          
          for (const selector of confirmSelectors) {
            const confirmButton = this.page.locator(selector);
            if (await confirmButton.isVisible()) {
              await confirmButton.click();
              break;
            }
          }
          
          // Wait for redirect
          await this.page.waitForURL(baseUrl, { timeout: 10000 });
          console.log(`    ✅ Deleted ${entityType} with ID: ${id}`);
        } else {
          console.warn(`    ⚠️ Delete button not found for ${entityType} ID: ${id}`);
        }
        
      } catch (error) {
        console.warn(`    ⚠️ Failed to delete ${entityType} ID ${id}:`, error);
      }
    }
  }

  async verifyCleanup(entityType: string, testPrefix: string = 'E2E-TEST-'): Promise<boolean> {
    console.log(`🔍 Verifying cleanup for ${entityType} with prefix: ${testPrefix}`);
    
    try {
      const baseUrl = `http://localhost:1337/admin/content-manager/collection-types/api::${entityType}.${entityType}`;
      await this.page.goto(baseUrl);
      await this.page.waitForLoadState('networkidle');

      // Check if any test entries remain
      const testEntrySelectors = [
        `[data-testid="entity-row"]:has-text("${testPrefix}")`,
        `tr:has-text("${testPrefix}")`,
        `[role="row"]:has-text("${testPrefix}")`,
        `*:has-text("${testPrefix}")`
      ];

      for (const selector of testEntrySelectors) {
        const testEntries = this.page.locator(selector);
        const count = await testEntries.count();
        if (count > 0) {
          console.warn(`  ⚠️ Found ${count} remaining test entries for ${entityType}`);
          return false;
        }
      }

      console.log(`  ✅ No test entries remaining for ${entityType}`);
      return true;
      
    } catch (error) {
      console.error(`  ❌ Verification failed for ${entityType}:`, error);
      return false;
    }
  }

  async emergencyCleanup(): Promise<void> {
    console.log('🚨 Running emergency cleanup for all test data');
    
    const prefixes = ['E2E-TEST-', 'E2E-MIN-', 'test-', 'TEST-'];
    
    for (const prefix of prefixes) {
      await this.cleanupAllTestData(prefix);
    }
    
    console.log('🚨 Emergency cleanup completed');
  }

  async getTestDataCount(entityType: string, testPrefix: string = 'E2E-TEST-'): Promise<number> {
    try {
      const baseUrl = `http://localhost:1337/admin/content-manager/collection-types/api::${entityType}.${entityType}`;
      await this.page.goto(baseUrl);
      await this.page.waitForLoadState('networkidle');

      const testEntrySelectors = [
        `[data-testid="entity-row"]:has-text("${testPrefix}")`,
        `tr:has-text("${testPrefix}")`,
        `[role="row"]:has-text("${testPrefix}")`,
        `*:has-text("${testPrefix}")`
      ];

      for (const selector of testEntrySelectors) {
        const testEntries = this.page.locator(selector);
        const count = await testEntries.count();
        if (count > 0) {
          return count;
        }
      }

      return 0;
      
    } catch (error) {
      console.error(`Failed to get test data count for ${entityType}:`, error);
      return 0;
    }
  }
}