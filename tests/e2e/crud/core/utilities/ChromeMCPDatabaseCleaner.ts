// Chrome MCP-based Database Cleaner
// Replaces Playwright-based DatabaseCleaner with Chrome MCP integration

export class ChromeMCPDatabaseCleaner {

  constructor() {
    // No page dependency needed for Chrome MCP
  }

  async cleanupTestData(entityType: string, testPrefix: string = 'E2E-TEST-'): Promise<void> {
    console.log(`🧹 Cleaning up ${entityType} test data with prefix: ${testPrefix} using Chrome MCP`);

    try {
      // Navigate to entity list
      const baseUrl = `http://localhost:1337/admin/content-manager/collection-types/api::${entityType}.${entityType}`;
      await this.chromeNavigate(baseUrl);
      await this.waitForPageLoad();

      // Get page content to find test entries
      const pageContent = await this.chromeGetWebContent();
      
      // Count test entries by searching for the prefix in content
      const testEntryMatches = pageContent.match(new RegExp(testPrefix, 'g'));
      const entryCount = testEntryMatches ? testEntryMatches.length : 0;
      
      if (entryCount === 0) {
        console.log(`  No test entries found for ${entityType}`);
        return;
      }

      console.log(`  Found ${entryCount} test entries to clean up`);

      // Delete entries one by one
      for (let i = 0; i < entryCount; i++) {
        try {
          // Look for test entries with various selectors
          const testEntrySelectors = [
            `[data-testid="entity-row"]:has-text("${testPrefix}")`,
            `tr:has-text("${testPrefix}")`,
            `[role="row"]:has-text("${testPrefix}")`,
            `.row:has-text("${testPrefix}")`
          ];

          let entryClicked = false;
          for (const selector of testEntrySelectors) {
            try {
              await this.chromeClick(selector);
              entryClicked = true;
              break;
            } catch {
              continue;
            }
          }

          if (entryClicked) {
            await this.waitForTimeout(1000); // Wait for form to load
            
            // Find and click delete button
            const deleteSelectors = [
              '[data-testid="delete-button"]',
              'button:has-text("Delete")',
              '[aria-label="Delete"]',
              'button[type="button"]:has-text("Delete")'
            ];
            
            let deleteClicked = false;
            for (const selector of deleteSelectors) {
              try {
                await this.chromeClick(selector);
                deleteClicked = true;
                break;
              } catch {
                continue;
              }
            }
            
            if (deleteClicked) {
              // Confirm deletion
              const confirmSelectors = [
                '[data-testid="confirm-delete"]',
                'button:has-text("Confirm")',
                'button:has-text("Delete")',
                'button:has-text("Yes")'
              ];
              
              for (const selector of confirmSelectors) {
                try {
                  await this.chromeClick(selector);
                  break;
                } catch {
                  continue;
                }
              }
              
              // Wait for redirect back to list
              await this.waitForNavigation(baseUrl);
              console.log(`    ✅ Deleted test ${entityType} entry ${i + 1}/${entryCount}`);
            } else {
              console.warn(`    ⚠️ Delete button not found for ${entityType} entry`);
            }
          } else {
            console.warn(`    ⚠️ Could not find test entry ${i + 1} for ${entityType}`);
          }
        } catch (error) {
          console.warn(`    ⚠️ Failed to delete test ${entityType} entry ${i + 1}:`, error);
        }
      }

      console.log(`  ✅ Cleanup completed for ${entityType}`);
      
    } catch (error) {
      console.error(`  ❌ Cleanup failed for ${entityType}:`, error);
    }
  }

  async cleanupAllTestData(testPrefix: string = 'E2E-TEST-'): Promise<void> {
    console.log(`🧹 Starting cleanup of all test data with prefix: ${testPrefix} using Chrome MCP`);
    
    const entities = [
      'paywall', 'ab-test', 'analytics', 'mobile-app', 
      'product', 'author', 'article', 'category'
    ];

    for (const entity of entities) {
      await this.cleanupTestData(entity, testPrefix);
    }
    
    console.log('✅ All test data cleanup completed');
  }

  async cleanupByIds(entityType: string, ids: string[]): Promise<void> {
    console.log(`🧹 Cleaning up ${entityType} by IDs: ${ids.join(', ')} using Chrome MCP`);
    
    const baseUrl = `http://localhost:1337/admin/content-manager/collection-types/api::${entityType}.${entityType}`;
    
    for (const id of ids) {
      try {
        await this.chromeNavigate(`${baseUrl}/${id}`);
        
        // Check if the page loads successfully (entity exists)
        await this.waitForTimeout(1000);
        const content = await this.chromeGetWebContent();
        
        if (content.includes('Not Found') || content.includes('404')) {
          console.log(`    ⚠️ Entity ${id} not found, skipping`);
          continue;
        }
        
        // Find and click delete button
        const deleteSelectors = [
          '[data-testid="delete-button"]',
          'button:has-text("Delete")',
          '[aria-label="Delete"]',
          'button[type="button"]:has-text("Delete")'
        ];
        
        let deleteClicked = false;
        for (const selector of deleteSelectors) {
          try {
            await this.chromeClick(selector);
            deleteClicked = true;
            break;
          } catch {
            continue;
          }
        }
        
        if (deleteClicked) {
          // Confirm deletion
          const confirmSelectors = [
            '[data-testid="confirm-delete"]',
            'button:has-text("Confirm")',
            'button:has-text("Delete")',
            'button:has-text("Yes")'
          ];
          
          for (const selector of confirmSelectors) {
            try {
              await this.chromeClick(selector);
              break;
            } catch {
              continue;
            }
          }
          
          // Wait for redirect
          await this.waitForNavigation(baseUrl);
          console.log(`    ✅ Deleted ${entityType} with ID: ${id}`);
        } else {
          console.warn(`    ⚠️ Delete button not found for ${entityType} ID: ${id}`);
        }
        
      } catch (error) {
        console.warn(`    ⚠️ Failed to delete ${entityType} ID ${id}:`, error);
      }
    }
  }

  async verifyCleanup(entityType: string, testPrefix: string = 'E2E-TEST-'): Promise<boolean> {
    console.log(`🔍 Verifying cleanup for ${entityType} with prefix: ${testPrefix} using Chrome MCP`);
    
    try {
      const baseUrl = `http://localhost:1337/admin/content-manager/collection-types/api::${entityType}.${entityType}`;
      await this.chromeNavigate(baseUrl);
      await this.waitForPageLoad();

      // Get page content and check for remaining test entries
      const pageContent = await this.chromeGetWebContent();
      const testEntryMatches = pageContent.match(new RegExp(testPrefix, 'g'));
      const remainingCount = testEntryMatches ? testEntryMatches.length : 0;

      if (remainingCount > 0) {
        console.warn(`  ⚠️ Found ${remainingCount} remaining test entries for ${entityType}`);
        return false;
      }

      console.log(`  ✅ No test entries remaining for ${entityType}`);
      return true;
      
    } catch (error) {
      console.error(`  ❌ Verification failed for ${entityType}:`, error);
      return false;
    }
  }

  async emergencyCleanup(): Promise<void> {
    console.log('🚨 Running emergency cleanup for all test data using Chrome MCP');
    
    const prefixes = ['E2E-TEST-', 'E2E-MIN-', 'test-', 'TEST-'];
    
    for (const prefix of prefixes) {
      await this.cleanupAllTestData(prefix);
    }
    
    console.log('🚨 Emergency cleanup completed');
  }

  async getTestDataCount(entityType: string, testPrefix: string = 'E2E-TEST-'): Promise<number> {
    try {
      const baseUrl = `http://localhost:1337/admin/content-manager/collection-types/api::${entityType}.${entityType}`;
      await this.chromeNavigate(baseUrl);
      await this.waitForPageLoad();

      // Get page content and count test entries
      const pageContent = await this.chromeGetWebContent();
      const testEntryMatches = pageContent.match(new RegExp(testPrefix, 'g'));
      return testEntryMatches ? testEntryMatches.length : 0;
      
    } catch (error) {
      console.error(`Failed to get test data count for ${entityType}:`, error);
      return 0;
    }
  }

  // Chrome MCP wrapper methods (these would be implemented to call actual Chrome MCP functions)
  private async chromeNavigate(url: string): Promise<void> {
    console.log(`[Chrome MCP] Navigating to: ${url}`);
    // Implementation would call Chrome MCP chrome_navigate function
  }

  private async chromeClick(selector: string): Promise<void> {
    console.log(`[Chrome MCP] Clicking: ${selector}`);
    // Implementation would call Chrome MCP chrome_click_element function
  }

  private async chromeGetWebContent(selector?: string): Promise<string> {
    console.log(`[Chrome MCP] Getting content for: ${selector || 'page'}`);
    // Implementation would call Chrome MCP chrome_get_web_content function
    return '';
  }

  private async waitForPageLoad(): Promise<void> {
    console.log(`[Chrome MCP] Waiting for page load`);
    // Implementation would wait for page to be ready
    await this.waitForTimeout(2000);
  }

  private async waitForNavigation(expectedUrl: string): Promise<void> {
    console.log(`[Chrome MCP] Waiting for navigation to: ${expectedUrl}`);
    // Implementation would wait for URL change
    await this.waitForTimeout(2000);
  }

  private async waitForTimeout(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}