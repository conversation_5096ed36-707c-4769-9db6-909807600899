// Using Chrome MCP server instead of <PERSON>wright
// This will be replaced with Chrome MCP integration

export abstract class BaseEntityPage {
  protected page: Page;
  protected entityName: string;
  protected baseUrl: string;

  constructor(page: Page, entityName: string) {
    this.page = page;
    this.entityName = entityName;
    this.baseUrl = `http://localhost:1337/admin/content-manager/collection-types/api::${entityName}.${entityName}`;
  }

  // Navigation methods
  async navigateToList(): Promise<void> {
    await this.page.goto(this.baseUrl);
    await this.page.waitForLoadState('networkidle');
  }

  async navigateToCreate(): Promise<void> {
    await this.navigateToList();
    await this.page.click('[data-testid="create-entry-button"], button:has-text("Create new entry")');
    await this.page.waitForSelector('form, [data-testid="entity-form"]', { timeout: 10000 });
  }

  async navigateToEdit(id: string): Promise<void> {
    await this.page.goto(`${this.baseUrl}/${id}`);
    await this.page.waitForSelector('form, [data-testid="entity-form"]', { timeout: 10000 });
  }

  // CRUD operations
  async create(data: Record<string, any>): Promise<string> {
    await this.navigateToCreate();
    await this.fillForm(data);
    await this.save();
    return await this.getEntityId();
  }

  async read(id: string): Promise<Record<string, any>> {
    await this.navigateToEdit(id);
    return await this.extractFormData();
  }

  async update(id: string, data: Record<string, any>): Promise<void> {
    await this.navigateToEdit(id);
    await this.fillForm(data);
    await this.save();
  }

  async delete(id: string): Promise<void> {
    await this.navigateToEdit(id);
    
    // Look for delete button with various possible selectors
    const deleteSelectors = [
      '[data-testid="delete-button"]',
      'button:has-text("Delete")',
      '[aria-label="Delete"]',
      'button[type="button"]:has-text("Delete")'
    ];
    
    let deleteButton = null;
    for (const selector of deleteSelectors) {
      deleteButton = this.page.locator(selector).first();
      if (await deleteButton.isVisible()) {
        break;
      }
    }
    
    if (deleteButton && await deleteButton.isVisible()) {
      await deleteButton.click();
      
      // Confirm deletion
      const confirmSelectors = [
        '[data-testid="confirm-delete"]',
        'button:has-text("Confirm")',
        'button:has-text("Delete")',
        'button:has-text("Yes")'
      ];
      
      for (const selector of confirmSelectors) {
        const confirmButton = this.page.locator(selector);
        if (await confirmButton.isVisible()) {
          await confirmButton.click();
          break;
        }
      }
      
      await this.page.waitForURL(this.baseUrl, { timeout: 10000 });
    } else {
      throw new Error(`Delete button not found for entity ${this.entityName}`);
    }
  }

  // Form interaction methods
  protected abstract fillForm(data: Record<string, any>): Promise<void>;
  protected abstract extractFormData(): Promise<Record<string, any>>;

  protected async save(): Promise<void> {
    const saveSelectors = [
      'button[type="submit"]',
      'button:has-text("Save")',
      'button:has-text("Create")',
      'button:has-text("Update")'
    ];
    
    let saveButton = null;
    for (const selector of saveSelectors) {
      saveButton = this.page.locator(selector).first();
      if (await saveButton.isVisible()) {
        break;
      }
    }
    
    if (saveButton && await saveButton.isVisible()) {
      await saveButton.click();
      
      // Wait for success indication
      try {
        await this.page.waitForSelector('text=Saved, text=Created, text=Updated', { timeout: 10000 });
      } catch {
        // If no success message, wait for URL change or form to be enabled again
        await this.page.waitForTimeout(2000);
      }
    } else {
      throw new Error(`Save button not found for entity ${this.entityName}`);
    }
  }

  protected async getEntityId(): Promise<string> {
    // Wait for URL to contain ID
    await this.page.waitForFunction(() => {
      return (window as any).location.pathname.match(/\/\d+$/);
    }, { timeout: 10000 });
    
    const url = this.page.url();
    const match = url.match(/\/(\d+)$/);
    return match ? match[1] : '';
  }

  // Validation methods
  async validateRequiredFields(requiredFields: string[]): Promise<void> {
    await this.navigateToCreate();
    
    // Try to submit without filling required fields
    await this.save();
    
    for (const field of requiredFields) {
      const fieldSelectors = [
        `[data-field="${field}"] .error-message`,
        `[name="${field}"] + .error`,
        `input[name="${field}"] ~ .error`,
        `.field-${field} .error`
      ];
      
      let errorFound = false;
      for (const selector of fieldSelectors) {
        const errorElement = this.page.locator(selector);
        if (await errorElement.isVisible()) {
          await expect(errorElement).toContainText(/required|mandatory|obligatoire/i);
          errorFound = true;
          break;
        }
      }
      
      if (!errorFound) {
        console.warn(`No validation error found for required field: ${field}`);
      }
    }
  }

  async validateFieldConstraints(field: string, invalidValue: any, expectedError: string): Promise<void> {
    await this.navigateToCreate();
    await this.fillField(field, invalidValue);
    await this.save();
    
    const fieldSelectors = [
      `[data-field="${field}"] .error-message`,
      `[name="${field}"] + .error`,
      `input[name="${field}"] ~ .error`,
      `.field-${field} .error`
    ];
    
    let errorFound = false;
    for (const selector of fieldSelectors) {
      const errorElement = this.page.locator(selector);
      if (await errorElement.isVisible()) {
        await expect(errorElement).toContainText(expectedError);
        errorFound = true;
        break;
      }
    }
    
    if (!errorFound) {
      console.warn(`No validation error found for field constraint: ${field}`);
    }
  }

  protected async fillField(fieldName: string, value: any): Promise<void> {
    const fieldSelectors = [
      `[data-field="${fieldName}"] input`,
      `[data-field="${fieldName}"] textarea`,
      `[data-field="${fieldName}"] select`,
      `input[name="${fieldName}"]`,
      `textarea[name="${fieldName}"]`,
      `select[name="${fieldName}"]`,
      `[name="${fieldName}"]`
    ];
    
    let field = null;
    for (const selector of fieldSelectors) {
      field = this.page.locator(selector).first();
      if (await field.isVisible()) {
        break;
      }
    }
    
    if (!field || !await field.isVisible()) {
      throw new Error(`Field ${fieldName} not found or not visible`);
    }
    
    const tagName = await field.evaluate(el => el.tagName.toLowerCase());
    const inputType = await field.evaluate(el => el.type || '');
    
    if (typeof value === 'string') {
      if (tagName === 'select') {
        await field.selectOption(value);
      } else {
        await field.fill(value);
      }
    } else if (typeof value === 'boolean') {
      if (inputType === 'checkbox' || inputType === 'radio') {
        if (value) {
          await field.check();
        } else {
          await field.uncheck();
        }
      }
    } else if (typeof value === 'number') {
      await field.fill(value.toString());
    }
  }

  protected async getFieldValue(fieldName: string): Promise<string> {
    const fieldSelectors = [
      `[data-field="${fieldName}"] input`,
      `[data-field="${fieldName}"] textarea`,
      `[data-field="${fieldName}"] select`,
      `input[name="${fieldName}"]`,
      `textarea[name="${fieldName}"]`,
      `select[name="${fieldName}"]`,
      `[name="${fieldName}"]`
    ];
    
    for (const selector of fieldSelectors) {
      const field = this.page.locator(selector).first();
      if (await field.isVisible()) {
        return await field.inputValue();
      }
    }
    
    return '';
  }

  // Utility methods
  async waitForPageLoad(): Promise<void> {
    await this.page.waitForLoadState('networkidle');
  }

  async isEntityInList(entityName: string): Promise<boolean> {
    await this.navigateToList();
    const entityRow = this.page.locator(`text=${entityName}`);
    return await entityRow.isVisible();
  }
}