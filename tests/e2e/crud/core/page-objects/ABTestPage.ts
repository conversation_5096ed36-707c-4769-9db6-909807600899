import { Page } from '@playwright/test';
import { BaseEntityPage } from './BaseEntityPage';

export class ABTestPage extends BaseEntityPage {
  constructor(page: Page) {
    super(page, 'ab-test');
  }

  protected async fillForm(data: Record<string, any>): Promise<void> {
    // Basic fields
    if (data.name) await this.fillField('name', data.name);
    if (data.description) await this.fillField('description', data.description);
    if (data.hypothesis) await this.fillField('hypothesis', data.hypothesis);
    
    // Status field
    if (data.status) {
      await this.fillSelectField('status', data.status);
    }
    
    // Date fields
    if (data.start_date) {
      await this.fillDateField('start_date', data.start_date);
    }
    if (data.end_date) {
      await this.fillDateField('end_date', data.end_date);
    }
    
    // Numeric fields
    if (data.traffic_allocation !== undefined) {
      await this.fillField('traffic_allocation', data.traffic_allocation.toString());
    }
    if (data.confidence_level !== undefined) {
      await this.fillField('confidence_level', data.confidence_level.toString());
    }
    
    // Test type
    if (data.test_type) {
      await this.fillSelectField('test_type', data.test_type);
    }
    
    // Success metrics (array)
    if (data.success_metrics) {
      await this.fillSuccessMetrics(data.success_metrics);
    }
  }

  protected async extractFormData(): Promise<Record<string, any>> {
    return {
      name: await this.getFieldValue('name'),
      description: await this.getFieldValue('description'),
      hypothesis: await this.getFieldValue('hypothesis'),
      status: await this.getFieldValue('status'),
      start_date: await this.getFieldValue('start_date'),
      end_date: await this.getFieldValue('end_date'),
      traffic_allocation: await this.getNumericFieldValue('traffic_allocation'),
      confidence_level: await this.getNumericFieldValue('confidence_level'),
      test_type: await this.getFieldValue('test_type'),
      success_metrics: await this.extractSuccessMetrics()
    };
  }

  private async fillSelectField(fieldName: string, value: string): Promise<void> {
    const selectSelectors = [
      `[data-field="${fieldName}"] select`,
      `select[name="${fieldName}"]`,
      `[name="${fieldName}"]`
    ];
    
    for (const selector of selectSelectors) {
      const field = this.page.locator(selector).first();
      if (await field.isVisible()) {
        await field.selectOption(value);
        return;
      }
    }
    
    // Try dropdown/combobox pattern
    const dropdownSelectors = [
      `[data-field="${fieldName}"] [role="combobox"]`,
      `[data-field="${fieldName}"] .dropdown`,
      `[data-field="${fieldName}"] button`
    ];
    
    for (const selector of dropdownSelectors) {
      const dropdown = this.page.locator(selector).first();
      if (await dropdown.isVisible()) {
        await dropdown.click();
        await this.page.click(`text=${value}`);
        return;
      }
    }
    
    console.warn(`Select field ${fieldName} not found, trying as text field`);
    await this.fillField(fieldName, value);
  }

  private async fillDateField(fieldName: string, dateValue: string): Promise<void> {
    const dateSelectors = [
      `[data-field="${fieldName}"] input[type="date"]`,
      `[data-field="${fieldName}"] input[type="datetime-local"]`,
      `input[name="${fieldName}"][type="date"]`,
      `input[name="${fieldName}"][type="datetime-local"]`,
      `[data-field="${fieldName}"] input`,
      `input[name="${fieldName}"]`
    ];
    
    // Convert ISO string to appropriate format
    let formattedDate = dateValue;
    if (dateValue.includes('T')) {
      const date = new Date(dateValue);
      formattedDate = date.toISOString().split('T')[0]; // YYYY-MM-DD format
    }
    
    for (const selector of dateSelectors) {
      const field = this.page.locator(selector).first();
      if (await field.isVisible()) {
        await field.fill(formattedDate);
        return;
      }
    }
    
    throw new Error(`Date field ${fieldName} not found`);
  }

  private async getNumericFieldValue(fieldName: string): Promise<number> {
    const value = await this.getFieldValue(fieldName);
    return value ? parseFloat(value) : 0;
  }

  private async fillSuccessMetrics(metrics: string[]): Promise<void> {
    // Try different patterns for multi-select or checkbox groups
    const metricsSelectors = [
      '[data-field="success_metrics"]',
      '[data-testid="success-metrics"]',
      '.success-metrics',
      '[name*="success_metrics"]'
    ];
    
    let metricsContainer = null;
    for (const selector of metricsSelectors) {
      metricsContainer = this.page.locator(selector).first();
      if (await metricsContainer.isVisible()) {
        break;
      }
    }
    
    if (!metricsContainer || !await metricsContainer.isVisible()) {
      console.warn('Success metrics field not found, skipping');
      return;
    }
    
    // Try checkbox pattern
    for (const metric of metrics) {
      const checkboxSelectors = [
        `input[type="checkbox"][value="${metric}"]`,
        `input[type="checkbox"][name*="${metric}"]`,
        `[data-value="${metric}"] input[type="checkbox"]`,
        `label:has-text("${metric}") input[type="checkbox"]`
      ];
      
      for (const selector of checkboxSelectors) {
        const checkbox = this.page.locator(selector).first();
        if (await checkbox.isVisible()) {
          await checkbox.check();
          break;
        }
      }
    }
    
    // Try multi-select pattern
    const multiSelect = metricsContainer.locator('select[multiple]').first();
    if (await multiSelect.isVisible()) {
      for (const metric of metrics) {
        await multiSelect.selectOption(metric);
      }
    }
  }

  private async extractSuccessMetrics(): Promise<string[]> {
    const metrics: string[] = [];
    
    // Try checkbox pattern
    const checkboxes = this.page.locator('input[type="checkbox"][name*="success_metrics"], input[type="checkbox"][name*="metric"]');
    const checkboxCount = await checkboxes.count();
    
    for (let i = 0; i < checkboxCount; i++) {
      const checkbox = checkboxes.nth(i);
      if (await checkbox.isChecked()) {
        const value = await checkbox.getAttribute('value');
        if (value) {
          metrics.push(value);
        }
      }
    }
    
    // Try multi-select pattern
    if (metrics.length === 0) {
      const multiSelect = this.page.locator('select[multiple][name*="success_metrics"], select[multiple][name*="metric"]').first();
      if (await multiSelect.isVisible()) {
        const selectedOptions = await multiSelect.locator('option:checked').all();
        for (const option of selectedOptions) {
          const value = await option.getAttribute('value');
          if (value) {
            metrics.push(value);
          }
        }
      }
    }
    
    return metrics;
  }

  // AB Test specific methods
  async setTestType(testType: 'paywall' | 'feature' | 'ui' | 'content'): Promise<void> {
    await this.fillSelectField('test_type', testType);
  }

  async setTrafficAllocation(percentage: number): Promise<void> {
    if (percentage < 0 || percentage > 100) {
      throw new Error('Traffic allocation must be between 0 and 100');
    }
    await this.fillField('traffic_allocation', percentage.toString());
  }

  async setConfidenceLevel(level: number): Promise<void> {
    if (level < 90 || level > 99) {
      throw new Error('Confidence level must be between 90 and 99');
    }
    await this.fillField('confidence_level', level.toString());
  }

  async setDateRange(startDate: string, endDate: string): Promise<void> {
    await this.fillDateField('start_date', startDate);
    await this.fillDateField('end_date', endDate);
  }

  async addSuccessMetric(metric: string): Promise<void> {
    const checkboxSelectors = [
      `input[type="checkbox"][value="${metric}"]`,
      `input[type="checkbox"][name*="${metric}"]`,
      `[data-value="${metric}"] input[type="checkbox"]`,
      `label:has-text("${metric}") input[type="checkbox"]`
    ];
    
    for (const selector of checkboxSelectors) {
      const checkbox = this.page.locator(selector).first();
      if (await checkbox.isVisible()) {
        await checkbox.check();
        return;
      }
    }
    
    console.warn(`Success metric checkbox for ${metric} not found`);
  }

  async removeSuccessMetric(metric: string): Promise<void> {
    const checkboxSelectors = [
      `input[type="checkbox"][value="${metric}"]`,
      `input[type="checkbox"][name*="${metric}"]`,
      `[data-value="${metric}"] input[type="checkbox"]`,
      `label:has-text("${metric}") input[type="checkbox"]`
    ];
    
    for (const selector of checkboxSelectors) {
      const checkbox = this.page.locator(selector).first();
      if (await checkbox.isVisible()) {
        await checkbox.uncheck();
        return;
      }
    }
    
    console.warn(`Success metric checkbox for ${metric} not found`);
  }

  async getTestDuration(): Promise<number> {
    const startDate = await this.getFieldValue('start_date');
    const endDate = await this.getFieldValue('end_date');
    
    if (!startDate || !endDate) {
      return 0;
    }
    
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays;
  }

  async validateDateRange(): Promise<boolean> {
    const startDate = await this.getFieldValue('start_date');
    const endDate = await this.getFieldValue('end_date');
    
    if (!startDate || !endDate) {
      return false;
    }
    
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    return end > start;
  }

  async isTestActive(): Promise<boolean> {
    const status = await this.getFieldValue('status');
    const startDate = await this.getFieldValue('start_date');
    const endDate = await this.getFieldValue('end_date');
    
    if (status !== 'active' && status !== 'running') {
      return false;
    }
    
    if (!startDate || !endDate) {
      return false;
    }
    
    const now = new Date();
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    return now >= start && now <= end;
  }
}