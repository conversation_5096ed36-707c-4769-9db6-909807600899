import { Page } from '@playwright/test';
import { BaseEntityPage } from './BaseEntityPage';

export class MobileAppPage extends BaseEntityPage {
  constructor(page: Page) {
    super(page, 'mobile-app');
  }

  protected async fillForm(data: Record<string, any>): Promise<void> {
    if (data.name) await this.fillField('name', data.name);
    if (data.bundle_id) await this.fillField('bundle_id', data.bundle_id);
    if (data.platform) await this.fillSelectField('platform', data.platform);
    if (data.version) await this.fillField('version', data.version);
    if (data.description) await this.fillField('description', data.description);
  }

  protected async extractFormData(): Promise<Record<string, any>> {
    return {
      name: await this.getFieldValue('name'),
      bundle_id: await this.getFieldValue('bundle_id'),
      platform: await this.getFieldValue('platform'),
      version: await this.getFieldValue('version'),
      description: await this.getFieldValue('description')
    };
  }

  private async fillSelectField(fieldName: string, value: string): Promise<void> {
    const selectSelectors = [
      `[data-field="${fieldName}"] select`,
      `select[name="${fieldName}"]`
    ];
    
    for (const selector of selectSelectors) {
      const field = this.page.locator(selector).first();
      if (await field.isVisible()) {
        await field.selectOption(value);
        return;
      }
    }
    
    await this.fillField(fieldName, value);
  }

  async setPlatform(platform: 'ios' | 'android' | 'web'): Promise<void> {
    await this.fillSelectField('platform', platform);
  }
}