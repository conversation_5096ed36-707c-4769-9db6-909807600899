import { Page } from '@playwright/test';
import { BaseEntityPage } from './BaseEntityPage';

export class CategoryPage extends BaseEntityPage {
  constructor(page: Page) {
    super(page, 'category');
  }

  protected async fillForm(data: Record<string, any>): Promise<void> {
    if (data.name) await this.fillField('name', data.name);
    if (data.description) await this.fillField('description', data.description);
    if (data.slug) await this.fillField('slug', data.slug);
  }

  protected async extractFormData(): Promise<Record<string, any>> {
    return {
      name: await this.getFieldValue('name'),
      description: await this.getFieldValue('description'),
      slug: await this.getFieldValue('slug')
    };
  }
}