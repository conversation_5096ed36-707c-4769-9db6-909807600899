import { Page } from '@playwright/test';
import { BaseEntityPage } from './BaseEntityPage';

export class PaywallPage extends BaseEntityPage {
  constructor(page: Page) {
    super(page, 'paywall');
  }

  protected async fillForm(data: Record<string, any>): Promise<void> {
    // Basic fields
    if (data.name) await this.fillField('name', data.name);
    if (data.placement_id) await this.fillField('placement_id', data.placement_id);
    if (data.title) await this.fillField('title', data.title);
    if (data.subtitle) await this.fillField('subtitle', data.subtitle);
    if (data.cta_text) await this.fillField('cta_text', data.cta_text);
    if (data.description_text) await this.fillField('description_text', data.description_text);

    // Status field
    if (data.status) {
      await this.fillSelectField('status', data.status);
    }

    // Theme component
    if (data.theme) {
      await this.fillThemeComponent(data.theme);
    }

    // Features component (repeatable)
    if (data.features) {
      await this.fillFeaturesComponent(data.features);
    }

    // Testimonials component (repeatable)
    if (data.testimonials) {
      await this.fillTestimonialsComponent(data.testimonials);
    }
  }

  protected async extractFormData(): Promise<Record<string, any>> {
    return {
      name: await this.getFieldValue('name'),
      placement_id: await this.getFieldValue('placement_id'),
      title: await this.getFieldValue('title'),
      subtitle: await this.getFieldValue('subtitle'),
      cta_text: await this.getFieldValue('cta_text'),
      description_text: await this.getFieldValue('description_text'),
      status: await this.getFieldValue('status'),
      theme: await this.extractThemeComponent(),
      features: await this.extractFeaturesComponent(),
      testimonials: await this.extractTestimonialsComponent()
    };
  }

  private async fillSelectField(fieldName: string, value: string): Promise<void> {
    const selectSelectors = [
      `[data-field="${fieldName}"] select`,
      `select[name="${fieldName}"]`,
      `[name="${fieldName}"]`
    ];
    
    for (const selector of selectSelectors) {
      const field = this.page.locator(selector).first();
      if (await field.isVisible()) {
        await field.selectOption(value);
        return;
      }
    }
    
    // Try dropdown/combobox pattern
    const dropdownSelectors = [
      `[data-field="${fieldName}"] [role="combobox"]`,
      `[data-field="${fieldName}"] .dropdown`,
      `[data-field="${fieldName}"] button`
    ];
    
    for (const selector of dropdownSelectors) {
      const dropdown = this.page.locator(selector).first();
      if (await dropdown.isVisible()) {
        await dropdown.click();
        await this.page.click(`text=${value}`);
        return;
      }
    }
    
    throw new Error(`Select field ${fieldName} not found`);
  }

  private async fillThemeComponent(theme: any): Promise<void> {
    const themeSelectors = [
      '[data-component="shared.theme"]',
      '[data-testid="theme-component"]',
      '.theme-component',
      '[data-field*="theme"]'
    ];
    
    let themeSection = null;
    for (const selector of themeSelectors) {
      themeSection = this.page.locator(selector).first();
      if (await themeSection.isVisible()) {
        break;
      }
    }
    
    if (!themeSection || !await themeSection.isVisible()) {
      console.warn('Theme component not found, trying individual fields');
      // Try direct field access
      if (theme.primary_color) {
        await this.fillField('primary_color', theme.primary_color);
      }
      if (theme.background_color) {
        await this.fillField('background_color', theme.background_color);
      }
      if (theme.text_color) {
        await this.fillField('text_color', theme.text_color);
      }
      return;
    }
    
    // Fill theme fields within the component
    if (theme.primary_color) {
      const primaryColorField = themeSection.locator('input[name*="primary"], input[data-field*="primary"]').first();
      if (await primaryColorField.isVisible()) {
        await primaryColorField.fill(theme.primary_color);
      }
    }
    
    if (theme.background_color) {
      const backgroundColorField = themeSection.locator('input[name*="background"], input[data-field*="background"]').first();
      if (await backgroundColorField.isVisible()) {
        await backgroundColorField.fill(theme.background_color);
      }
    }
    
    if (theme.text_color) {
      const textColorField = themeSection.locator('input[name*="text"], input[data-field*="text"]').first();
      if (await textColorField.isVisible()) {
        await textColorField.fill(theme.text_color);
      }
    }
  }

  private async extractThemeComponent(): Promise<any> {
    const themeSelectors = [
      '[data-component="shared.theme"]',
      '[data-testid="theme-component"]',
      '.theme-component',
      '[data-field*="theme"]'
    ];
    
    let themeSection = null;
    for (const selector of themeSelectors) {
      themeSection = this.page.locator(selector).first();
      if (await themeSection.isVisible()) {
        break;
      }
    }
    
    if (!themeSection || !await themeSection.isVisible()) {
      // Try direct field access
      return {
        primary_color: await this.getFieldValue('primary_color'),
        background_color: await this.getFieldValue('background_color'),
        text_color: await this.getFieldValue('text_color')
      };
    }
    
    const primaryColorField = themeSection.locator('input[name*="primary"], input[data-field*="primary"]').first();
    const backgroundColorField = themeSection.locator('input[name*="background"], input[data-field*="background"]').first();
    const textColorField = themeSection.locator('input[name*="text"], input[data-field*="text"]').first();
    
    return {
      primary_color: await primaryColorField.isVisible() ? await primaryColorField.inputValue() : '',
      background_color: await backgroundColorField.isVisible() ? await backgroundColorField.inputValue() : '',
      text_color: await textColorField.isVisible() ? await textColorField.inputValue() : ''
    };
  }

  private async fillFeaturesComponent(features: any[]): Promise<void> {
    const featuresSelectors = [
      '[data-component="shared.feature"]',
      '[data-testid="features-component"]',
      '.features-component',
      '[data-field*="feature"]'
    ];
    
    // First, try to find existing features and clear them
    await this.clearRepeatableComponent('feature');
    
    // Add new features
    for (const feature of features) {
      await this.addRepeatableComponent('feature');
      
      // Fill the last added feature
      const featureComponents = this.page.locator('[data-component="shared.feature"], [data-testid="feature-item"], .feature-item');
      const lastFeature = featureComponents.last();
      
      if (feature.title) {
        const titleField = lastFeature.locator('input[name*="title"], input[data-field*="title"]').first();
        if (await titleField.isVisible()) {
          await titleField.fill(feature.title);
        }
      }
      
      if (feature.description) {
        const descField = lastFeature.locator('textarea[name*="description"], textarea[data-field*="description"], input[name*="description"]').first();
        if (await descField.isVisible()) {
          await descField.fill(feature.description);
        }
      }
      
      if (feature.icon) {
        const iconField = lastFeature.locator('input[name*="icon"], input[data-field*="icon"]').first();
        if (await iconField.isVisible()) {
          await iconField.fill(feature.icon);
        }
      }
    }
  }

  private async extractFeaturesComponent(): Promise<any[]> {
    const featureComponents = this.page.locator('[data-component="shared.feature"], [data-testid="feature-item"], .feature-item');
    const featureCount = await featureComponents.count();
    const features = [];

    for (let i = 0; i < featureCount; i++) {
      const feature = featureComponents.nth(i);
      
      const titleField = feature.locator('input[name*="title"], input[data-field*="title"]').first();
      const descField = feature.locator('textarea[name*="description"], textarea[data-field*="description"], input[name*="description"]').first();
      const iconField = feature.locator('input[name*="icon"], input[data-field*="icon"]').first();
      
      features.push({
        title: await titleField.isVisible() ? await titleField.inputValue() : '',
        description: await descField.isVisible() ? await descField.inputValue() : '',
        icon: await iconField.isVisible() ? await iconField.inputValue() : ''
      });
    }

    return features;
  }

  private async fillTestimonialsComponent(testimonials: any[]): Promise<void> {
    // Clear existing testimonials
    await this.clearRepeatableComponent('testimonial');
    
    // Add new testimonials
    for (const testimonial of testimonials) {
      await this.addRepeatableComponent('testimonial');
      
      // Fill the last added testimonial
      const testimonialComponents = this.page.locator('[data-component="shared.testimonial"], [data-testid="testimonial-item"], .testimonial-item');
      const lastTestimonial = testimonialComponents.last();
      
      if (testimonial.content) {
        const contentField = lastTestimonial.locator('textarea[name*="content"], textarea[data-field*="content"], input[name*="content"]').first();
        if (await contentField.isVisible()) {
          await contentField.fill(testimonial.content);
        }
      }
      
      if (testimonial.author) {
        const authorField = lastTestimonial.locator('input[name*="author"], input[data-field*="author"]').first();
        if (await authorField.isVisible()) {
          await authorField.fill(testimonial.author);
        }
      }
      
      if (testimonial.title) {
        const titleField = lastTestimonial.locator('input[name*="title"], input[data-field*="title"]').first();
        if (await titleField.isVisible()) {
          await titleField.fill(testimonial.title);
        }
      }
    }
  }

  private async extractTestimonialsComponent(): Promise<any[]> {
    const testimonialComponents = this.page.locator('[data-component="shared.testimonial"], [data-testid="testimonial-item"], .testimonial-item');
    const testimonialCount = await testimonialComponents.count();
    const testimonials = [];

    for (let i = 0; i < testimonialCount; i++) {
      const testimonial = testimonialComponents.nth(i);
      
      const contentField = testimonial.locator('textarea[name*="content"], textarea[data-field*="content"], input[name*="content"]').first();
      const authorField = testimonial.locator('input[name*="author"], input[data-field*="author"]').first();
      const titleField = testimonial.locator('input[name*="title"], input[data-field*="title"]').first();
      
      testimonials.push({
        content: await contentField.isVisible() ? await contentField.inputValue() : '',
        author: await authorField.isVisible() ? await authorField.inputValue() : '',
        title: await titleField.isVisible() ? await titleField.inputValue() : ''
      });
    }

    return testimonials;
  }

  private async clearRepeatableComponent(componentType: string): Promise<void> {
    const removeSelectors = [
      `[data-testid="remove-${componentType}"]`,
      `[aria-label="Remove ${componentType}"]`,
      `button:has-text("Remove")`,
      `button:has-text("Delete")`,
      `.remove-${componentType}`,
      '[data-testid="remove-component"]'
    ];
    
    // Keep trying to remove components until none are left
    let attempts = 0;
    const maxAttempts = 10;
    
    while (attempts < maxAttempts) {
      let removed = false;
      
      for (const selector of removeSelectors) {
        const removeButtons = this.page.locator(selector);
        const count = await removeButtons.count();
        
        if (count > 0) {
          // Remove from the end to avoid index issues
          await removeButtons.last().click();
          removed = true;
          break;
        }
      }
      
      if (!removed) {
        break;
      }
      
      attempts++;
      await this.page.waitForTimeout(500); // Wait for DOM update
    }
  }

  private async addRepeatableComponent(componentType: string): Promise<void> {
    const addSelectors = [
      `[data-testid="add-${componentType}"]`,
      `button:has-text("Add ${componentType}")`,
      `button:has-text("Add")`,
      `.add-${componentType}`,
      '[data-testid="add-component"]'
    ];
    
    for (const selector of addSelectors) {
      const addButton = this.page.locator(selector).first();
      if (await addButton.isVisible()) {
        await addButton.click();
        await this.page.waitForTimeout(500); // Wait for component to be added
        return;
      }
    }
    
    console.warn(`Add button for ${componentType} not found`);
  }

  // Paywall-specific methods
  async setStatus(status: 'draft' | 'published' | 'archived'): Promise<void> {
    await this.fillSelectField('status', status);
  }

  async addFeature(title: string, description: string, icon: string = 'star'): Promise<void> {
    await this.addRepeatableComponent('feature');
    
    const featureComponents = this.page.locator('[data-component="shared.feature"], [data-testid="feature-item"], .feature-item');
    const lastFeature = featureComponents.last();
    
    const titleField = lastFeature.locator('input[name*="title"], input[data-field*="title"]').first();
    const descField = lastFeature.locator('textarea[name*="description"], textarea[data-field*="description"], input[name*="description"]').first();
    const iconField = lastFeature.locator('input[name*="icon"], input[data-field*="icon"]').first();
    
    if (await titleField.isVisible()) await titleField.fill(title);
    if (await descField.isVisible()) await descField.fill(description);
    if (await iconField.isVisible()) await iconField.fill(icon);
  }

  async addTestimonial(content: string, author: string, title: string = ''): Promise<void> {
    await this.addRepeatableComponent('testimonial');
    
    const testimonialComponents = this.page.locator('[data-component="shared.testimonial"], [data-testid="testimonial-item"], .testimonial-item');
    const lastTestimonial = testimonialComponents.last();
    
    const contentField = lastTestimonial.locator('textarea[name*="content"], textarea[data-field*="content"], input[name*="content"]').first();
    const authorField = lastTestimonial.locator('input[name*="author"], input[data-field*="author"]').first();
    const titleField = lastTestimonial.locator('input[name*="title"], input[data-field*="title"]').first();
    
    if (await contentField.isVisible()) await contentField.fill(content);
    if (await authorField.isVisible()) await authorField.fill(author);
    if (await titleField.isVisible()) await titleField.fill(title);
  }

  async getFeatureCount(): Promise<number> {
    const featureComponents = this.page.locator('[data-component="shared.feature"], [data-testid="feature-item"], .feature-item');
    return await featureComponents.count();
  }

  async getTestimonialCount(): Promise<number> {
    const testimonialComponents = this.page.locator('[data-component="shared.testimonial"], [data-testid="testimonial-item"], .testimonial-item');
    return await testimonialComponents.count();
  }
}