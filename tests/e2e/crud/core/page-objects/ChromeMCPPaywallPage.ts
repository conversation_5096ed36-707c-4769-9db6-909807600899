// Chrome MCP-based Paywall Page Object
// Extends ChromeMCPBaseEntityPage for consistent Chrome MCP integration

import { ChromeMCPBaseEntityPage } from './ChromeMCPBaseEntityPage';

export class ChromeMCPPaywallPage extends ChromeMCPBaseEntityPage {
  constructor() {
    super('paywall');
  }

  protected async fillForm(data: Record<string, any>): Promise<void> {
    // Basic fields using Chrome MCP
    if (data.name) await this.fillField('name', data.name);
    if (data.placement_id) await this.fillField('placement_id', data.placement_id);
    if (data.title) await this.fillField('title', data.title);
    if (data.subtitle) await this.fillField('subtitle', data.subtitle);
    if (data.cta_text) await this.fillField('cta_text', data.cta_text);
    if (data.description_text) await this.fillField('description_text', data.description_text);

    // Status field
    if (data.status) {
      await this.fillSelectField('status', data.status);
    }

    // Theme component
    if (data.theme) {
      await this.fillThemeComponent(data.theme);
    }

    // Features component (repeatable)
    if (data.features) {
      await this.fillFeaturesComponent(data.features);
    }

    // Testimonials component (repeatable)
    if (data.testimonials) {
      await this.fillTestimonialsComponent(data.testimonials);
    }
  }

  protected async extractFormData(): Promise<Record<string, any>> {
    return {
      name: await this.getFieldValue('name'),
      placement_id: await this.getFieldValue('placement_id'),
      title: await this.getFieldValue('title'),
      subtitle: await this.getFieldValue('subtitle'),
      cta_text: await this.getFieldValue('cta_text'),
      description_text: await this.getFieldValue('description_text'),
      status: await this.getFieldValue('status'),
      theme: await this.extractThemeComponent(),
      features: await this.extractFeaturesComponent(),
      testimonials: await this.extractTestimonialsComponent()
    };
  }

  private async fillSelectField(fieldName: string, value: string): Promise<void> {
    const selectSelectors = [
      `[data-field="${fieldName}"] select`,
      `select[name="${fieldName}"]`,
      `[name="${fieldName}"]`
    ];
    
    for (const selector of selectSelectors) {
      try {
        await this.chromeFillOrSelect(selector, value);
        return;
      } catch {
        continue;
      }
    }
    
    // Try dropdown/combobox pattern with Chrome MCP click + select
    const dropdownSelectors = [
      `[data-field="${fieldName}"] [role="combobox"]`,
      `[data-field="${fieldName}"] .dropdown`,
      `[data-field="${fieldName}"] button`
    ];
    
    for (const selector of dropdownSelectors) {
      try {
        await this.chromeClick(selector);
        await this.chromeClick(`text=${value}`);
        return;
      } catch {
        continue;
      }
    }
    
    throw new Error(`Select field ${fieldName} not found`);
  }

  private async fillThemeComponent(theme: any): Promise<void> {
    const themeSelectors = [
      '[data-component="shared.theme"]',
      '[data-testid="theme-component"]',
      '.theme-component',
      '[data-field*="theme"]'
    ];
    
    // Check if theme component exists using Chrome MCP
    let themeFound = false;
    for (const selector of themeSelectors) {
      try {
        const content = await this.chromeGetWebContent(selector);
        if (content) {
          themeFound = true;
          break;
        }
      } catch {
        continue;
      }
    }
    
    if (!themeFound) {
      console.warn('Theme component not found, trying individual fields');
    }
    
    // Fill theme fields
    if (theme.primary_color) {
      await this.fillThemeField('primary_color', theme.primary_color);
    }
    if (theme.background_color) {
      await this.fillThemeField('background_color', theme.background_color);
    }
    if (theme.text_color) {
      await this.fillThemeField('text_color', theme.text_color);
    }
  }

  private async fillThemeField(fieldType: string, value: string): Promise<void> {
    const fieldSelectors = [
      `[data-component="shared.theme"] input[name*="${fieldType}"]`,
      `[data-component="shared.theme"] input[data-field*="${fieldType}"]`,
      `input[name*="${fieldType}"]`,
      `input[data-field*="${fieldType}"]`
    ];
    
    for (const selector of fieldSelectors) {
      try {
        await this.chromeFillOrSelect(selector, value);
        return;
      } catch {
        continue;
      }
    }
    
    console.warn(`Theme field ${fieldType} not found`);
  }

  private async extractThemeComponent(): Promise<any> {
    return {
      primary_color: await this.extractThemeField('primary_color'),
      background_color: await this.extractThemeField('background_color'),
      text_color: await this.extractThemeField('text_color')
    };
  }

  private async extractThemeField(fieldType: string): Promise<string> {
    const fieldSelectors = [
      `[data-component="shared.theme"] input[name*="${fieldType}"]`,
      `[data-component="shared.theme"] input[data-field*="${fieldType}"]`,
      `input[name*="${fieldType}"]`,
      `input[data-field*="${fieldType}"]`
    ];
    
    for (const selector of fieldSelectors) {
      try {
        const content = await this.chromeGetWebContent(selector);
        if (content) {
          // Extract value from content - would need proper parsing in real implementation
          return content;
        }
      } catch {
        continue;
      }
    }
    
    return '';
  }

  private async fillFeaturesComponent(features: any[]): Promise<void> {
    // Clear existing features using Chrome MCP
    await this.clearRepeatableComponent('feature');
    
    // Add new features
    for (const feature of features) {
      await this.addRepeatableComponent('feature');
      
      // Fill the last added feature using Chrome MCP
      if (feature.title) {
        await this.fillComponentField('feature', 'title', feature.title);
      }
      if (feature.description) {
        await this.fillComponentField('feature', 'description', feature.description);
      }
      if (feature.icon) {
        await this.fillComponentField('feature', 'icon', feature.icon);
      }
    }
  }

  private async extractFeaturesComponent(): Promise<any[]> {
    // This would need Chrome MCP implementation to count and extract feature components
    // For now, return empty array as placeholder
    console.log('[Chrome MCP] Extracting features component - implementation needed');
    return [];
  }

  private async fillTestimonialsComponent(testimonials: any[]): Promise<void> {
    // Clear existing testimonials
    await this.clearRepeatableComponent('testimonial');
    
    // Add new testimonials
    for (const testimonial of testimonials) {
      await this.addRepeatableComponent('testimonial');
      
      if (testimonial.content) {
        await this.fillComponentField('testimonial', 'content', testimonial.content);
      }
      if (testimonial.author) {
        await this.fillComponentField('testimonial', 'author', testimonial.author);
      }
      if (testimonial.title) {
        await this.fillComponentField('testimonial', 'title', testimonial.title);
      }
    }
  }

  private async extractTestimonialsComponent(): Promise<any[]> {
    // This would need Chrome MCP implementation to count and extract testimonial components
    console.log('[Chrome MCP] Extracting testimonials component - implementation needed');
    return [];
  }

  private async clearRepeatableComponent(componentType: string): Promise<void> {
    const removeSelectors = [
      `[data-testid="remove-${componentType}"]`,
      `[aria-label="Remove ${componentType}"]`,
      `button:has-text("Remove")`,
      `button:has-text("Delete")`,
      `.remove-${componentType}`,
      '[data-testid="remove-component"]'
    ];
    
    // Keep trying to remove components until none are left
    let attempts = 0;
    const maxAttempts = 10;
    
    while (attempts < maxAttempts) {
      let removed = false;
      
      for (const selector of removeSelectors) {
        try {
          await this.chromeClick(selector);
          removed = true;
          break;
        } catch {
          continue;
        }
      }
      
      if (!removed) {
        break;
      }
      
      attempts++;
      await this.waitForTimeout(500); // Wait for DOM update
    }
  }

  private async addRepeatableComponent(componentType: string): Promise<void> {
    const addSelectors = [
      `[data-testid="add-${componentType}"]`,
      `button:has-text("Add ${componentType}")`,
      `button:has-text("Add")`,
      `.add-${componentType}`,
      '[data-testid="add-component"]'
    ];
    
    for (const selector of addSelectors) {
      try {
        await this.chromeClick(selector);
        await this.waitForTimeout(500); // Wait for component to be added
        return;
      } catch {
        continue;
      }
    }
    
    console.warn(`Add button for ${componentType} not found`);
  }

  private async fillComponentField(componentType: string, fieldName: string, value: string): Promise<void> {
    const fieldSelectors = [
      `[data-component="shared.${componentType}"]:last-child input[name*="${fieldName}"]`,
      `[data-component="shared.${componentType}"]:last-child textarea[name*="${fieldName}"]`,
      `[data-testid="${componentType}-item"]:last-child input[name*="${fieldName}"]`,
      `[data-testid="${componentType}-item"]:last-child textarea[name*="${fieldName}"]`,
      `.${componentType}-item:last-child input[name*="${fieldName}"]`,
      `.${componentType}-item:last-child textarea[name*="${fieldName}"]`
    ];
    
    for (const selector of fieldSelectors) {
      try {
        await this.chromeFillOrSelect(selector, value);
        return;
      } catch {
        continue;
      }
    }
    
    console.warn(`Component field ${componentType}.${fieldName} not found`);
  }

  // Paywall-specific Chrome MCP methods
  async setStatus(status: 'draft' | 'published' | 'archived'): Promise<void> {
    await this.fillSelectField('status', status);
  }

  async addFeature(title: string, description: string, icon: string = 'star'): Promise<void> {
    await this.addRepeatableComponent('feature');
    await this.fillComponentField('feature', 'title', title);
    await this.fillComponentField('feature', 'description', description);
    await this.fillComponentField('feature', 'icon', icon);
  }

  async addTestimonial(content: string, author: string, title: string = ''): Promise<void> {
    await this.addRepeatableComponent('testimonial');
    await this.fillComponentField('testimonial', 'content', content);
    await this.fillComponentField('testimonial', 'author', author);
    if (title) {
      await this.fillComponentField('testimonial', 'title', title);
    }
  }

  async getFeatureCount(): Promise<number> {
    // This would need Chrome MCP implementation to count feature components
    console.log('[Chrome MCP] Getting feature count - implementation needed');
    return 0;
  }

  async getTestimonialCount(): Promise<number> {
    // This would need Chrome MCP implementation to count testimonial components
    console.log('[Chrome MCP] Getting testimonial count - implementation needed');
    return 0;
  }

  // Chrome MCP wrapper methods (inherited from base class but used here)
  private async chromeClick(selector: string): Promise<void> {
    console.log(`[Chrome MCP] Clicking: ${selector}`);
    // Implementation would call Chrome MCP chrome_click_element function
  }

  private async chromeFillOrSelect(selector: string, value: string): Promise<void> {
    console.log(`[Chrome MCP] Filling ${selector} with: ${value}`);
    // Implementation would call Chrome MCP chrome_fill_or_select function
  }

  private async chromeGetWebContent(selector?: string): Promise<string> {
    console.log(`[Chrome MCP] Getting content for: ${selector || 'page'}`);
    // Implementation would call Chrome MCP chrome_get_web_content function
    return '';
  }

  private async waitForTimeout(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}