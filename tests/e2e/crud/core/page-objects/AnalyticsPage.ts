import { Page } from '@playwright/test';
import { BaseEntityPage } from './BaseEntityPage';

export class AnalyticsPage extends BaseEntityPage {
  constructor(page: Page) {
    super(page, 'analytics');
  }

  protected async fillForm(data: Record<string, any>): Promise<void> {
    // Basic fields
    if (data.name) await this.fillField('name', data.name);
    if (data.description) await this.fillField('description', data.description);
    
    // Metric type
    if (data.metric_type) {
      await this.fillSelectField('metric_type', data.metric_type);
    }
    
    // Aggregation period
    if (data.aggregation_period) {
      await this.fillSelectField('aggregation_period', data.aggregation_period);
    }
    
    // Numeric fields
    if (data.retention_days !== undefined) {
      await this.fillField('retention_days', data.retention_days.toString());
    }
    
    // Configuration fields
    if (data.is_active !== undefined) {
      await this.fillCheckboxField('is_active', data.is_active);
    }
    
    if (data.auto_refresh !== undefined) {
      await this.fillCheckboxField('auto_refresh', data.auto_refresh);
    }
  }

  protected async extractFormData(): Promise<Record<string, any>> {
    return {
      name: await this.getFieldValue('name'),
      description: await this.getFieldValue('description'),
      metric_type: await this.getFieldValue('metric_type'),
      aggregation_period: await this.getFieldValue('aggregation_period'),
      retention_days: await this.getNumericFieldValue('retention_days'),
      is_active: await this.getCheckboxValue('is_active'),
      auto_refresh: await this.getCheckboxValue('auto_refresh')
    };
  }

  private async fillSelectField(fieldName: string, value: string): Promise<void> {
    const selectSelectors = [
      `[data-field="${fieldName}"] select`,
      `select[name="${fieldName}"]`,
      `[name="${fieldName}"]`
    ];
    
    for (const selector of selectSelectors) {
      const field = this.page.locator(selector).first();
      if (await field.isVisible()) {
        await field.selectOption(value);
        return;
      }
    }
    
    // Try dropdown/combobox pattern
    const dropdownSelectors = [
      `[data-field="${fieldName}"] [role="combobox"]`,
      `[data-field="${fieldName}"] .dropdown`,
      `[data-field="${fieldName}"] button`
    ];
    
    for (const selector of dropdownSelectors) {
      const dropdown = this.page.locator(selector).first();
      if (await dropdown.isVisible()) {
        await dropdown.click();
        await this.page.click(`text=${value}`);
        return;
      }
    }
    
    console.warn(`Select field ${fieldName} not found, trying as text field`);
    await this.fillField(fieldName, value);
  }

  private async fillCheckboxField(fieldName: string, value: boolean): Promise<void> {
    const checkboxSelectors = [
      `[data-field="${fieldName}"] input[type="checkbox"]`,
      `input[name="${fieldName}"][type="checkbox"]`,
      `[name="${fieldName}"]`
    ];
    
    for (const selector of checkboxSelectors) {
      const field = this.page.locator(selector).first();
      if (await field.isVisible()) {
        if (value) {
          await field.check();
        } else {
          await field.uncheck();
        }
        return;
      }
    }
    
    console.warn(`Checkbox field ${fieldName} not found`);
  }

  private async getNumericFieldValue(fieldName: string): Promise<number> {
    const value = await this.getFieldValue(fieldName);
    return value ? parseFloat(value) : 0;
  }

  private async getCheckboxValue(fieldName: string): Promise<boolean> {
    const checkboxSelectors = [
      `[data-field="${fieldName}"] input[type="checkbox"]`,
      `input[name="${fieldName}"][type="checkbox"]`,
      `[name="${fieldName}"]`
    ];
    
    for (const selector of checkboxSelectors) {
      const field = this.page.locator(selector).first();
      if (await field.isVisible()) {
        return await field.isChecked();
      }
    }
    
    return false;
  }

  // Analytics-specific methods
  async setMetricType(type: 'conversion' | 'revenue' | 'engagement' | 'retention'): Promise<void> {
    await this.fillSelectField('metric_type', type);
  }

  async setAggregationPeriod(period: 'hourly' | 'daily' | 'weekly' | 'monthly'): Promise<void> {
    await this.fillSelectField('aggregation_period', period);
  }

  async setRetentionDays(days: number): Promise<void> {
    if (days < 1 || days > 365) {
      throw new Error('Retention days must be between 1 and 365');
    }
    await this.fillField('retention_days', days.toString());
  }

  async enableAutoRefresh(): Promise<void> {
    await this.fillCheckboxField('auto_refresh', true);
  }

  async disableAutoRefresh(): Promise<void> {
    await this.fillCheckboxField('auto_refresh', false);
  }

  async activate(): Promise<void> {
    await this.fillCheckboxField('is_active', true);
  }

  async deactivate(): Promise<void> {
    await this.fillCheckboxField('is_active', false);
  }

  async navigateToDashboard(): Promise<void> {
    // Navigate to analytics dashboard if available
    const dashboardSelectors = [
      '[data-testid="analytics-dashboard"]',
      'a:has-text("Dashboard")',
      'button:has-text("View Dashboard")',
      '.dashboard-link'
    ];
    
    for (const selector of dashboardSelectors) {
      const dashboardLink = this.page.locator(selector).first();
      if (await dashboardLink.isVisible()) {
        await dashboardLink.click();
        await this.page.waitForLoadState('networkidle');
        return;
      }
    }
    
    console.warn('Analytics dashboard link not found');
  }

  async waitForDataLoad(): Promise<void> {
    // Wait for analytics data to load
    const loadingSelectors = [
      '.loading',
      '[data-testid="loading"]',
      '.spinner',
      'text=Loading'
    ];
    
    // Wait for loading indicators to disappear
    for (const selector of loadingSelectors) {
      try {
        await this.page.waitForSelector(selector, { state: 'hidden', timeout: 5000 });
      } catch {
        // Loading indicator might not be present
      }
    }
    
    // Wait for data containers to appear
    const dataSelectors = [
      '.analytics-data',
      '[data-testid="analytics-data"]',
      '.chart-container',
      '.metrics-container'
    ];
    
    for (const selector of dataSelectors) {
      try {
        await this.page.waitForSelector(selector, { timeout: 5000 });
        break;
      } catch {
        // Data container might not be present
      }
    }
  }

  async hasData(): Promise<boolean> {
    const dataSelectors = [
      '.analytics-data:not(:empty)',
      '[data-testid="analytics-data"]:not(:empty)',
      '.chart-container:not(:empty)',
      '.metrics-container:not(:empty)'
    ];
    
    for (const selector of dataSelectors) {
      const dataContainer = this.page.locator(selector).first();
      if (await dataContainer.isVisible()) {
        return true;
      }
    }
    
    return false;
  }

  async getMetricValue(metricName: string): Promise<number> {
    const metricSelectors = [
      `[data-metric="${metricName}"] .value`,
      `[data-testid="${metricName}-value"]`,
      `.metric-${metricName} .value`,
      `text=${metricName}`,
    ];
    
    for (const selector of metricSelectors) {
      const metricElement = this.page.locator(selector).first();
      if (await metricElement.isVisible()) {
        const text = await metricElement.textContent();
        const numericValue = text?.match(/[\d.]+/)?.[0];
        return numericValue ? parseFloat(numericValue) : 0;
      }
    }
    
    return 0;
  }

  async exportData(format: 'csv' | 'json' | 'xlsx' = 'csv'): Promise<void> {
    const exportSelectors = [
      '[data-testid="export-button"]',
      'button:has-text("Export")',
      '.export-button',
      '[aria-label="Export data"]'
    ];
    
    for (const selector of exportSelectors) {
      const exportButton = this.page.locator(selector).first();
      if (await exportButton.isVisible()) {
        await exportButton.click();
        
        // Select format if dropdown appears
        const formatSelector = `text=${format.toUpperCase()}`;
        try {
          await this.page.click(formatSelector, { timeout: 2000 });
        } catch {
          // Format selection might not be available
        }
        
        return;
      }
    }
    
    console.warn('Export button not found');
  }
}