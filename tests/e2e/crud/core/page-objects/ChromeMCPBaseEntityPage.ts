// Chrome MCP-based Entity Page Object
// Uses Chrome MCP server for browser automation instead of Playwright

export abstract class ChromeMCPBaseEntityPage {
  protected entityName: string;
  protected baseUrl: string;

  constructor(entityName: string) {
    this.entityName = entityName;
    this.baseUrl = `http://localhost:1337/admin/content-manager/collection-types/api::${entityName}.${entityName}`;
  }

  // Navigation methods using Chrome MCP
  async navigateToList(): Promise<void> {
    // Use chrome_navigate from Chrome MCP
    await this.chromeNavigate(this.baseUrl);
    await this.waitForPageLoad();
  }

  async navigateToCreate(): Promise<void> {
    await this.navigateToList();
    await this.chromeClick('[data-testid="create-entry-button"], button:has-text("Create new entry")');
    await this.waitForSelector('form, [data-testid="entity-form"]');
  }

  async navigateToEdit(id: string): Promise<void> {
    await this.chromeNavigate(`${this.baseUrl}/${id}`);
    await this.waitForSelector('form, [data-testid="entity-form"]');
  }

  // CRUD operations using Chrome MCP
  async create(data: Record<string, any>): Promise<string> {
    await this.navigateToCreate();
    await this.fillForm(data);
    await this.save();
    return await this.getEntityId();
  }

  async read(id: string): Promise<Record<string, any>> {
    await this.navigateToEdit(id);
    return await this.extractFormData();
  }

  async update(id: string, data: Record<string, any>): Promise<void> {
    await this.navigateToEdit(id);
    await this.fillForm(data);
    await this.save();
  }

  async delete(id: string): Promise<void> {
    await this.navigateToEdit(id);
    
    const deleteSelectors = [
      '[data-testid="delete-button"]',
      'button:has-text("Delete")',
      '[aria-label="Delete"]',
      'button[type="button"]:has-text("Delete")'
    ];
    
    let deleteClicked = false;
    for (const selector of deleteSelectors) {
      try {
        await this.chromeClick(selector);
        deleteClicked = true;
        break;
      } catch {
        continue;
      }
    }
    
    if (deleteClicked) {
      // Confirm deletion
      const confirmSelectors = [
        '[data-testid="confirm-delete"]',
        'button:has-text("Confirm")',
        'button:has-text("Delete")',
        'button:has-text("Yes")'
      ];
      
      for (const selector of confirmSelectors) {
        try {
          await this.chromeClick(selector);
          break;
        } catch {
          continue;
        }
      }
      
      await this.waitForNavigation(this.baseUrl);
    } else {
      throw new Error(`Delete button not found for entity ${this.entityName}`);
    }
  }

  // Form interaction methods - to be implemented by subclasses
  protected abstract fillForm(data: Record<string, any>): Promise<void>;
  protected abstract extractFormData(): Promise<Record<string, any>>;

  protected async save(): Promise<void> {
    const saveSelectors = [
      'button[type="submit"]',
      'button:has-text("Save")',
      'button:has-text("Create")',
      'button:has-text("Update")'
    ];
    
    let saveClicked = false;
    for (const selector of saveSelectors) {
      try {
        await this.chromeClick(selector);
        saveClicked = true;
        break;
      } catch {
        continue;
      }
    }
    
    if (!saveClicked) {
      throw new Error(`Save button not found for entity ${this.entityName}`);
    }
    
    // Wait for success indication
    await this.waitForTimeout(2000);
  }

  protected async getEntityId(): Promise<string> {
    // Wait for URL to contain ID
    await this.waitForTimeout(1000);
    const currentUrl = await this.getCurrentUrl();
    const match = currentUrl.match(/\/(\d+)$/);
    return match ? match[1] : '';
  }

  // Chrome MCP wrapper methods
  private async chromeNavigate(url: string): Promise<void> {
    // This would use the chrome_navigate function from Chrome MCP
    console.log(`[Chrome MCP] Navigating to: ${url}`);
    // Implementation would call Chrome MCP chrome_navigate function
  }

  private async chromeClick(selector: string): Promise<void> {
    // This would use the chrome_click_element function from Chrome MCP
    console.log(`[Chrome MCP] Clicking: ${selector}`);
    // Implementation would call Chrome MCP chrome_click_element function
  }

  private async chromeFillOrSelect(selector: string, value: string): Promise<void> {
    // This would use the chrome_fill_or_select function from Chrome MCP
    console.log(`[Chrome MCP] Filling ${selector} with: ${value}`);
    // Implementation would call Chrome MCP chrome_fill_or_select function
  }

  private async chromeGetWebContent(selector?: string): Promise<string> {
    // This would use the chrome_get_web_content function from Chrome MCP
    console.log(`[Chrome MCP] Getting content for: ${selector || 'page'}`);
    // Implementation would call Chrome MCP chrome_get_web_content function
    return '';
  }

  private async getCurrentUrl(): Promise<string> {
    // This would get current URL from Chrome MCP
    console.log(`[Chrome MCP] Getting current URL`);
    // Implementation would get URL from Chrome MCP
    return '';
  }

  private async waitForSelector(selector: string, timeout: number = 10000): Promise<void> {
    // This would wait for selector using Chrome MCP
    console.log(`[Chrome MCP] Waiting for selector: ${selector}`);
    // Implementation would use Chrome MCP to wait for element
  }

  private async waitForNavigation(expectedUrl: string): Promise<void> {
    // This would wait for navigation using Chrome MCP
    console.log(`[Chrome MCP] Waiting for navigation to: ${expectedUrl}`);
    // Implementation would wait for URL change
  }

  private async waitForPageLoad(): Promise<void> {
    // This would wait for page load using Chrome MCP
    console.log(`[Chrome MCP] Waiting for page load`);
    // Implementation would wait for page to be ready
  }

  private async waitForTimeout(ms: number): Promise<void> {
    // Simple timeout
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Utility methods for form interaction
  protected async fillField(fieldName: string, value: any): Promise<void> {
    const fieldSelectors = [
      `[data-field="${fieldName}"] input`,
      `[data-field="${fieldName}"] textarea`,
      `[data-field="${fieldName}"] select`,
      `input[name="${fieldName}"]`,
      `textarea[name="${fieldName}"]`,
      `select[name="${fieldName}"]`,
      `[name="${fieldName}"]`
    ];
    
    let fieldFound = false;
    for (const selector of fieldSelectors) {
      try {
        if (typeof value === 'string' || typeof value === 'number') {
          await this.chromeFillOrSelect(selector, value.toString());
          fieldFound = true;
          break;
        }
      } catch {
        continue;
      }
    }
    
    if (!fieldFound) {
      throw new Error(`Field ${fieldName} not found or not fillable`);
    }
  }

  protected async getFieldValue(fieldName: string): Promise<string> {
    const fieldSelectors = [
      `[data-field="${fieldName}"] input`,
      `[data-field="${fieldName}"] textarea`,
      `[data-field="${fieldName}"] select`,
      `input[name="${fieldName}"]`,
      `textarea[name="${fieldName}"]`,
      `select[name="${fieldName}"]`,
      `[name="${fieldName}"]`
    ];
    
    for (const selector of fieldSelectors) {
      try {
        const content = await this.chromeGetWebContent(selector);
        if (content) {
          // Extract value from content - this would need proper parsing
          return content;
        }
      } catch {
        continue;
      }
    }
    
    return '';
  }

  // Validation methods
  async validateRequiredFields(requiredFields: string[]): Promise<void> {
    await this.navigateToCreate();
    
    // Try to submit without filling required fields
    await this.save();
    
    for (const field of requiredFields) {
      const fieldSelectors = [
        `[data-field="${field}"] .error-message`,
        `[name="${field}"] + .error`,
        `input[name="${field}"] ~ .error`,
        `.field-${field} .error`
      ];
      
      let errorFound = false;
      for (const selector of fieldSelectors) {
        try {
          const content = await this.chromeGetWebContent(selector);
          if (content && /required|mandatory|obligatoire/i.test(content)) {
            errorFound = true;
            break;
          }
        } catch {
          continue;
        }
      }
      
      if (!errorFound) {
        console.warn(`No validation error found for required field: ${field}`);
      }
    }
  }

  async validateFieldConstraints(field: string, invalidValue: any, expectedError: string): Promise<void> {
    await this.navigateToCreate();
    await this.fillField(field, invalidValue);
    await this.save();
    
    const fieldSelectors = [
      `[data-field="${field}"] .error-message`,
      `[name="${field}"] + .error`,
      `input[name="${field}"] ~ .error`,
      `.field-${field} .error`
    ];
    
    let errorFound = false;
    for (const selector of fieldSelectors) {
      try {
        const content = await this.chromeGetWebContent(selector);
        if (content && content.includes(expectedError)) {
          errorFound = true;
          break;
        }
      } catch {
        continue;
      }
    }
    
    if (!errorFound) {
      console.warn(`No validation error found for field constraint: ${field}`);
    }
  }

  // Utility methods
  async isEntityInList(entityName: string): Promise<boolean> {
    await this.navigateToList();
    try {
      const content = await this.chromeGetWebContent();
      return content.includes(entityName);
    } catch {
      return false;
    }
  }
}