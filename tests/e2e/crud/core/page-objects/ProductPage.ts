import { Page } from '@playwright/test';
import { BaseEntityPage } from './BaseEntityPage';

export class ProductPage extends BaseEntityPage {
  constructor(page: Page) {
    super(page, 'product');
  }

  protected async fillForm(data: Record<string, any>): Promise<void> {
    if (data.name) await this.fillField('name', data.name);
    if (data.description) await this.fillField('description', data.description);
    if (data.price !== undefined) await this.fillField('price', data.price.toString());
    if (data.currency) await this.fillField('currency', data.currency);
    if (data.product_id) await this.fillField('product_id', data.product_id);
  }

  protected async extractFormData(): Promise<Record<string, any>> {
    return {
      name: await this.getFieldValue('name'),
      description: await this.getFieldValue('description'),
      price: parseFloat(await this.getFieldValue('price')) || 0,
      currency: await this.getFieldValue('currency'),
      product_id: await this.getFieldValue('product_id')
    };
  }
}