import { Page } from '@playwright/test';
import { BaseEntityPage } from './BaseEntityPage';

export class AuthorPage extends BaseEntityPage {
  constructor(page: Page) {
    super(page, 'author');
  }

  protected async fillForm(data: Record<string, any>): Promise<void> {
    if (data.name) await this.fillField('name', data.name);
    if (data.email) await this.fillField('email', data.email);
    if (data.bio) await this.fillField('bio', data.bio);
    if (data.avatar) await this.fillField('avatar', data.avatar);
  }

  protected async extractFormData(): Promise<Record<string, any>> {
    return {
      name: await this.getFieldValue('name'),
      email: await this.getFieldValue('email'),
      bio: await this.getFieldValue('bio'),
      avatar: await this.getFieldValue('avatar')
    };
  }
}