import { Page } from '@playwright/test';
import { BaseEntityPage } from './BaseEntityPage';

export class ArticlePage extends BaseEntityPage {
  constructor(page: Page) {
    super(page, 'article');
  }

  protected async fillForm(data: Record<string, any>): Promise<void> {
    if (data.title) await this.fillField('title', data.title);
    if (data.content) await this.fillField('content', data.content);
    if (data.excerpt) await this.fillField('excerpt', data.excerpt);
    if (data.slug) await this.fillField('slug', data.slug);
    if (data.status) await this.fillSelectField('status', data.status);
  }

  protected async extractFormData(): Promise<Record<string, any>> {
    return {
      title: await this.getFieldValue('title'),
      content: await this.getFieldValue('content'),
      excerpt: await this.getFieldValue('excerpt'),
      slug: await this.getFieldValue('slug'),
      status: await this.getFieldValue('status')
    };
  }

  private async fillSelectField(fieldName: string, value: string): Promise<void> {
    const selectSelectors = [
      `[data-field="${fieldName}"] select`,
      `select[name="${fieldName}"]`
    ];
    
    for (const selector of selectSelectors) {
      const field = this.page.locator(selector).first();
      if (await field.isVisible()) {
        await field.selectOption(value);
        return;
      }
    }
    
    await this.fillField(fieldName, value);
  }
}