import { faker } from '@faker-js/faker';

export class EntityDataFactory {
  static createPaywallData(overrides: Partial<any> = {}): any {
    return {
      name: faker.commerce.productName(),
      placement_id: `test-${faker.string.alphanumeric(8)}`,
      title: faker.commerce.productName(),
      subtitle: faker.commerce.productDescription(),
      cta_text: 'Subscribe Now',
      description_text: faker.lorem.paragraph(),
      status: 'draft',
      theme: {
        primary_color: faker.color.rgb(),
        background_color: '#FFFFFF',
        text_color: '#000000'
      },
      features: [
        {
          title: 'Premium Feature 1',
          description: faker.lorem.sentence(),
          icon: 'star'
        },
        {
          title: 'Premium Feature 2',
          description: faker.lorem.sentence(),
          icon: 'check'
        }
      ],
      testimonials: [
        {
          content: faker.lorem.paragraph(),
          author: faker.person.fullName(),
          title: faker.person.jobTitle()
        }
      ],
      ...overrides
    };
  }

  static createABTestData(overrides: Partial<any> = {}): any {
    const startDate = faker.date.future();
    const endDate = new Date(startDate.getTime() + 7 * 24 * 60 * 60 * 1000); // 7 days later
    
    return {
      name: `AB Test ${faker.string.alphanumeric(6)}`,
      description: faker.lorem.paragraph(),
      hypothesis: faker.lorem.sentence(),
      status: 'draft',
      start_date: startDate.toISOString(),
      end_date: endDate.toISOString(),
      traffic_allocation: 50,
      success_metrics: ['conversion_rate', 'revenue'],
      ...overrides
    };
  }

  static createAnalyticsData(overrides: Partial<any> = {}): any {
    return {
      name: `Analytics ${faker.string.alphanumeric(6)}`,
      description: faker.lorem.paragraph(),
      metric_type: 'conversion',
      aggregation_period: 'daily',
      retention_days: 30,
      ...overrides
    };
  }

  static createMobileAppData(overrides: Partial<any> = {}): any {
    return {
      name: faker.company.name(),
      bundle_id: `com.test.${faker.string.alphanumeric(8)}`,
      platform: 'ios',
      version: faker.system.semver(),
      description: faker.lorem.paragraph(),
      ...overrides
    };
  }

  static createProductData(overrides: Partial<any> = {}): any {
    return {
      name: faker.commerce.productName(),
      description: faker.commerce.productDescription(),
      price: parseFloat(faker.commerce.price()),
      currency: 'USD',
      product_id: `test_${faker.string.alphanumeric(8)}`,
      ...overrides
    };
  }

  static createAuthorData(overrides: Partial<any> = {}): any {
    return {
      name: faker.person.fullName(),
      email: faker.internet.email(),
      bio: faker.lorem.paragraph(),
      avatar: faker.image.avatar(),
      ...overrides
    };
  }

  static createArticleData(overrides: Partial<any> = {}): any {
    return {
      title: faker.lorem.sentence(),
      content: faker.lorem.paragraphs(3),
      excerpt: faker.lorem.paragraph(),
      slug: faker.lorem.slug(),
      status: 'draft',
      ...overrides
    };
  }

  static createCategoryData(overrides: Partial<any> = {}): any {
    return {
      name: faker.commerce.department(),
      description: faker.lorem.paragraph(),
      slug: faker.lorem.slug(),
      ...overrides
    };
  }

  // Utility method to create test data with consistent prefix for easy cleanup
  static createTestData(entityType: string, overrides: Partial<any> = {}): any {
    const testPrefix = 'E2E-TEST-';
    const timestamp = Date.now();
    
    const baseOverrides = {
      name: `${testPrefix}${entityType}-${timestamp}`,
      ...overrides
    };

    switch (entityType.toLowerCase()) {
      case 'paywall':
        return this.createPaywallData(baseOverrides);
      case 'ab-test':
        return this.createABTestData(baseOverrides);
      case 'analytics':
        return this.createAnalyticsData(baseOverrides);
      case 'mobile-app':
        return this.createMobileAppData(baseOverrides);
      case 'product':
        return this.createProductData(baseOverrides);
      case 'author':
        return this.createAuthorData(baseOverrides);
      case 'article':
        return this.createArticleData(baseOverrides);
      case 'category':
        return this.createCategoryData(baseOverrides);
      default:
        throw new Error(`Unknown entity type: ${entityType}`);
    }
  }

  // Method to create minimal valid data for required fields only
  static createMinimalData(entityType: string, overrides: Partial<any> = {}): any {
    const testPrefix = 'E2E-MIN-';
    const timestamp = Date.now();
    
    const baseData = {
      name: `${testPrefix}${entityType}-${timestamp}`,
      ...overrides
    };

    switch (entityType.toLowerCase()) {
      case 'paywall':
        return {
          name: baseData.name,
          placement_id: `test-${faker.string.alphanumeric(8)}`,
          title: 'Test Title',
          cta_text: 'Subscribe',
          theme: {
            primary_color: '#000000',
            background_color: '#FFFFFF',
            text_color: '#000000'
          },
          ...overrides
        };
      case 'ab-test':
        return {
          name: baseData.name,
          hypothesis: 'Test hypothesis',
          start_date: new Date().toISOString(),
          end_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          ...overrides
        };
      case 'analytics':
        return {
          name: baseData.name,
          metric_type: 'conversion',
          ...overrides
        };
      case 'mobile-app':
        return {
          name: baseData.name,
          bundle_id: `com.test.${faker.string.alphanumeric(8)}`,
          platform: 'ios',
          version: '1.0.0',
          ...overrides
        };
      case 'product':
        return {
          name: baseData.name,
          product_id: `test_${faker.string.alphanumeric(8)}`,
          price: 9.99,
          currency: 'USD',
          ...overrides
        };
      case 'author':
        return {
          name: baseData.name,
          email: faker.internet.email(),
          ...overrides
        };
      case 'article':
        return {
          title: baseData.name,
          content: 'Test content',
          slug: faker.lorem.slug(),
          ...overrides
        };
      case 'category':
        return {
          name: baseData.name,
          slug: faker.lorem.slug(),
          ...overrides
        };
      default:
        return baseData;
    }
  }
}