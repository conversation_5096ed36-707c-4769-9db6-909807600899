/**
 * Global teardown for E2E tests
 * Cleanup test data and resources
 */

const fs = require('fs');
const path = require('path');

async function globalTeardown(config) {
  console.log('🧹 Starting global E2E test teardown...');
  
  try {
    // Clean up authentication state file
    const authStatePath = path.join(__dirname, 'auth-state.json');
    if (fs.existsSync(authStatePath)) {
      fs.unlinkSync(authStatePath);
      console.log('✅ Authentication state cleaned up');
    }
    
    // Clean up test artifacts
    const testResultsDir = path.join(process.cwd(), 'test-results');
    if (fs.existsSync(testResultsDir)) {
      console.log('📁 Test results saved in:', testResultsDir);
    }
    
    console.log('✅ Global teardown completed successfully');
    
  } catch (error) {
    console.error('❌ Global teardown failed:', error);
  }
}

module.exports = globalTeardown;