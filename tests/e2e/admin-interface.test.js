/**
 * E2E Test Scenarios for Strapi Adapty CMS Admin Interface
 * Based on comprehensive architecture analysis
 */

const { test, expect } = require('@playwright/test');

// Test configuration
const ADMIN_URL = 'http://localhost:1337/admin';
const TEST_TIMEOUT = 30000;

// Test data
const TEST_PAYWALL = {
  name: 'E2E Test Paywall',
  placement_id: 'e2e-test-placement',
  title: 'Premium Features',
  subtitle: 'Unlock all premium features',
  cta_text: 'Subscribe Now'
};

const TEST_AB_TEST = {
  name: 'E2E A/B Test',
  description: 'Testing conversion optimization',
  hypothesis: 'New design will improve conversion by 15%'
};

test.describe('Admin Interface E2E Tests', () => {
  
  test.beforeEach(async ({ page }) => {
    // Set longer timeout for admin interface
    test.setTimeout(TEST_TIMEOUT);
    
    // Navigate to admin and handle potential login
    await page.goto(ADMIN_URL);
    
    // Wait for admin interface to load
    await page.waitForLoadState('networkidle');
  });

  test.describe('Critical User Journey 1: Paywall Creation Workflow', () => {
    
    test('should complete full paywall creation workflow', async ({ page }) => {
      // Step 1: Navigate to paywall creation
      await page.click('text=Content Manager');
      await page.click('text=Paywall');
      await page.click('text=Create new entry');
      
      // Wait for form to load
      await page.waitForSelector('[data-testid="paywall-form"]', { timeout: 10000 });
      
      // Step 2: Fill basic paywall information
      await page.fill('input[name="name"]', TEST_PAYWALL.name);
      await page.fill('input[name="placement_id"]', TEST_PAYWALL.placement_id);
      await page.fill('input[name="title"]', TEST_PAYWALL.title);
      await page.fill('input[name="subtitle"]', TEST_PAYWALL.subtitle);
      await page.fill('input[name="cta_text"]', TEST_PAYWALL.cta_text);
      
      // Step 3: Configure theme using ThemeColorPicker component
      const themeSection = page.locator('[data-testid="theme-color-picker"]');
      if (await themeSection.isVisible()) {
        await themeSection.click();
        
        // Test primary color picker
        await page.click('[data-testid="primary-color-picker"]');
        await page.click('[data-testid="color-option-blue"]');
        
        // Test background color picker
        await page.click('[data-testid="background-color-picker"]');
        await page.click('[data-testid="color-option-white"]');
      }
      
      // Step 4: Add features using FeatureManager component
      const featureManager = page.locator('[data-testid="feature-manager"]');
      if (await featureManager.isVisible()) {
        await page.click('[data-testid="add-feature-button"]');
        await page.fill('[data-testid="feature-title-0"]', 'Unlimited Access');
        await page.fill('[data-testid="feature-description-0"]', 'Access to all premium content');
        
        // Test drag and drop reordering
        const feature1 = page.locator('[data-testid="feature-item-0"]');
        const feature2 = page.locator('[data-testid="feature-item-1"]');
        if (await feature2.isVisible()) {
          await feature1.dragTo(feature2);
        }
      }
      
      // Step 5: Add testimonials using TestimonialManager
      const testimonialManager = page.locator('[data-testid="testimonial-manager"]');
      if (await testimonialManager.isVisible()) {
        await page.click('[data-testid="add-testimonial-button"]');
        await page.fill('[data-testid="testimonial-content-0"]', 'Amazing app! Highly recommended.');
        await page.fill('[data-testid="testimonial-author-0"]', 'John Doe');
        await page.fill('[data-testid="testimonial-title-0"]', 'Happy Customer');
      }
      
      // Step 6: Verify PaywallPreview component updates
      const previewComponent = page.locator('[data-testid="paywall-preview"]');
      if (await previewComponent.isVisible()) {
        // Check if preview shows the entered title
        await expect(previewComponent.locator('text=' + TEST_PAYWALL.title)).toBeVisible();
        
        // Test device frame rendering
        await expect(previewComponent.locator('[data-testid="phone-frame"]')).toBeVisible();
        
        // Test refresh preview functionality
        await page.click('[data-testid="refresh-preview-button"]');
        await page.waitForLoadState('networkidle');
      }
      
      // Step 7: Validate form using PaywallFormValidation
      const validationComponent = page.locator('[data-testid="paywall-form-validation"]');
      if (await validationComponent.isVisible()) {
        // Check for validation status
        await expect(validationComponent.locator('[data-testid="validation-status"]')).toContainText('Valid');
        
        // Test inline validation messages
        const validationMessages = page.locator('[data-testid="validation-message"]');
        const messageCount = await validationMessages.count();
        expect(messageCount).toBeGreaterThanOrEqual(0);
      }
      
      // Step 8: Save the paywall
      await page.click('button[type="submit"]');
      await page.waitForSelector('text=Saved successfully', { timeout: 10000 });
      
      // Verify creation success
      await expect(page.locator('text=Saved successfully')).toBeVisible();
    });
    
    test('should handle paywall creation errors gracefully', async ({ page }) => {
      // Navigate to paywall creation
      await page.click('text=Content Manager');
      await page.click('text=Paywall');
      await page.click('text=Create new entry');
      
      // Try to save without required fields
      await page.click('button[type="submit"]');
      
      // Check for validation errors
      await expect(page.locator('text=This field is required')).toBeVisible();
      
      // Test duplicate placement_id error
      await page.fill('input[name="placement_id"]', 'existing-placement');
      await page.click('button[type="submit"]');
      
      // Should show unique constraint error
      await page.waitForSelector('text=already exists', { timeout: 5000 });
    });
  });

  test.describe('Critical User Journey 2: A/B Testing Workflow', () => {
    
    test('should complete A/B test creation and management', async ({ page }) => {
      // Step 1: Navigate to A/B test creation
      await page.click('text=Content Manager');
      await page.click('text=A/B Test');
      await page.click('text=Create new entry');
      
      // Step 2: Fill A/B test basic information
      await page.fill('input[name="name"]', TEST_AB_TEST.name);
      await page.fill('textarea[name="description"]', TEST_AB_TEST.description);
      await page.fill('textarea[name="hypothesis"]', TEST_AB_TEST.hypothesis);
      
      // Step 3: Configure test using ABTestInterface
      const abTestInterface = page.locator('[data-testid="ab-test-interface"]');
      if (await abTestInterface.isVisible()) {
        // Set test duration
        await page.selectOption('[data-testid="test-duration"]', '14');
        
        // Set traffic allocation
        await page.fill('[data-testid="traffic-allocation"]', '50');
        
        // Set success metrics
        await page.check('[data-testid="metric-conversion-rate"]');
        await page.check('[data-testid="metric-revenue"]');
      }
      
      // Step 4: Create variations using VariationComparison
      const variationComponent = page.locator('[data-testid="variation-comparison"]');
      if (await variationComponent.isVisible()) {
        // Add control variation
        await page.click('[data-testid="add-variation-button"]');
        await page.fill('[data-testid="variation-name-0"]', 'Control');
        
        // Add test variation
        await page.click('[data-testid="add-variation-button"]');
        await page.fill('[data-testid="variation-name-1"]', 'Test Variation');
        
        // Test side-by-side comparison view
        await page.click('[data-testid="side-by-side-view"]');
        await expect(page.locator('[data-testid="variation-preview-0"]')).toBeVisible();
        await expect(page.locator('[data-testid="variation-preview-1"]')).toBeVisible();
      }
      
      // Step 5: Save A/B test
      await page.click('button[type="submit"]');
      await page.waitForSelector('text=Saved successfully', { timeout: 10000 });
    });
    
    test('should monitor A/B test performance', async ({ page }) => {
      // Navigate to existing A/B test
      await page.click('text=Content Manager');
      await page.click('text=A/B Test');
      await page.click('text=' + TEST_AB_TEST.name);
      
      // Step 1: Check ABTestDashboard component
      const dashboard = page.locator('[data-testid="ab-test-dashboard"]');
      if (await dashboard.isVisible()) {
        // Verify performance metrics display
        await expect(dashboard.locator('[data-testid="conversion-rate"]')).toBeVisible();
        await expect(dashboard.locator('[data-testid="statistical-significance"]')).toBeVisible();
        
        // Test real-time updates
        await page.click('[data-testid="enable-realtime"]');
        await page.waitForTimeout(2000);
        
        // Check for live indicator
        await expect(dashboard.locator('[data-testid="live-indicator"]')).toBeVisible();
      }
      
      // Step 2: Check TestResultsVisualization
      const resultsViz = page.locator('[data-testid="test-results-visualization"]');
      if (await resultsViz.isVisible()) {
        // Verify chart rendering
        await expect(resultsViz.locator('[data-testid="performance-chart"]')).toBeVisible();
        
        // Test time range selection
        await page.selectOption('[data-testid="time-range-selector"]', '7d');
        await page.waitForLoadState('networkidle');
      }
      
      // Step 3: Test conclusion workflow
      const conclusionWorkflow = page.locator('[data-testid="test-conclusion-workflow"]');
      if (await conclusionWorkflow.isVisible()) {
        // Check if test is ready for conclusion
        const conclusionButton = page.locator('[data-testid="conclude-test-button"]');
        if (await conclusionButton.isVisible()) {
          await conclusionButton.click();
          
          // Verify conclusion options
          await expect(page.locator('[data-testid="winner-selection"]')).toBeVisible();
          await expect(page.locator('[data-testid="conclusion-reason"]')).toBeVisible();
        }
      }
    });
  });

  test.describe('Critical User Journey 3: Analytics Monitoring', () => {
    
    test('should display comprehensive analytics dashboard', async ({ page }) => {
      // Navigate to analytics (assuming custom route or component)
      await page.goto(ADMIN_URL + '/analytics');
      
      // Step 1: Verify AnalyticsDashboard loads
      const analyticsDashboard = page.locator('[data-testid="analytics-dashboard"]');
      await expect(analyticsDashboard).toBeVisible({ timeout: 15000 });
      
      // Step 2: Check key metrics display
      await expect(page.locator('[data-testid="total-impressions"]')).toBeVisible();
      await expect(page.locator('[data-testid="total-conversions"]')).toBeVisible();
      await expect(page.locator('[data-testid="conversion-rate"]')).toBeVisible();
      await expect(page.locator('[data-testid="total-revenue"]')).toBeVisible();
      
      // Step 3: Test real-time mode toggle
      const realtimeToggle = page.locator('[data-testid="realtime-toggle"]');
      if (await realtimeToggle.isVisible()) {
        await realtimeToggle.click();
        
        // Verify live indicator appears
        await expect(page.locator('[data-testid="live-indicator"]')).toBeVisible();
        
        // Wait for real-time update
        await page.waitForTimeout(3000);
        
        // Toggle off
        await realtimeToggle.click();
      }
      
      // Step 4: Test date range filtering
      const dateRangePicker = page.locator('[data-testid="date-range-picker"]');
      if (await dateRangePicker.isVisible()) {
        await dateRangePicker.click();
        await page.click('[data-testid="date-range-7d"]');
        await page.waitForLoadState('networkidle');
        
        // Verify data updates
        await expect(page.locator('[data-testid="date-range-label"]')).toContainText('7 days');
      }
      
      // Step 5: Test export functionality
      const exportButton = page.locator('[data-testid="export-button"]');
      if (await exportButton.isVisible()) {
        await exportButton.click();
        
        // Test CSV export
        const csvExport = page.locator('[data-testid="export-csv"]');
        if (await csvExport.isVisible()) {
          const downloadPromise = page.waitForEvent('download');
          await csvExport.click();
          const download = await downloadPromise;
          expect(download.suggestedFilename()).toContain('.csv');
        }
      }
    });
    
    test('should handle analytics data loading states', async ({ page }) => {
      // Navigate to analytics
      await page.goto(ADMIN_URL + '/analytics');
      
      // Check loading state
      const loadingIndicator = page.locator('[data-testid="analytics-loading"]');
      if (await loadingIndicator.isVisible()) {
        await expect(loadingIndicator).toContainText('Loading analytics data');
      }
      
      // Wait for data to load
      await page.waitForSelector('[data-testid="analytics-dashboard"]', { timeout: 15000 });
      
      // Test error state by simulating network failure
      await page.route('**/api/analytics/**', route => route.abort());
      await page.reload();
      
      // Should show error message
      await expect(page.locator('text=Failed to load analytics data')).toBeVisible();
    });
  });

  test.describe('Critical User Journey 4: Bulk Operations', () => {
    
    test('should perform bulk paywall operations', async ({ page }) => {
      // Navigate to paywall list
      await page.click('text=Content Manager');
      await page.click('text=Paywall');
      
      // Step 1: Select multiple paywalls
      const checkboxes = page.locator('[data-testid="paywall-checkbox"]');
      const checkboxCount = await checkboxes.count();
      
      if (checkboxCount > 0) {
        // Select first two paywalls
        await checkboxes.nth(0).check();
        if (checkboxCount > 1) {
          await checkboxes.nth(1).check();
        }
        
        // Step 2: Access bulk operations
        const bulkOperations = page.locator('[data-testid="bulk-paywall-operations"]');
        if (await bulkOperations.isVisible()) {
          await bulkOperations.click();
          
          // Step 3: Test bulk status change
          await page.selectOption('[data-testid="bulk-action-select"]', 'publish');
          await page.click('[data-testid="execute-bulk-action"]');
          
          // Confirm action
          await page.click('[data-testid="confirm-bulk-action"]');
          
          // Wait for completion
          await page.waitForSelector('text=Bulk operation completed', { timeout: 10000 });
          
          // Step 4: Verify status tracking
          const statusTracker = page.locator('[data-testid="bulk-operation-status"]');
          if (await statusTracker.isVisible()) {
            await expect(statusTracker).toContainText('Completed');
          }
        }
      }
    });
  });

  test.describe('Component-Specific Tests', () => {
    
    test('PaywallPreview component functionality', async ({ page }) => {
      // Navigate to paywall edit
      await page.click('text=Content Manager');
      await page.click('text=Paywall');
      await page.click('text=Create new entry');
      
      const preview = page.locator('[data-testid="paywall-preview"]');
      if (await preview.isVisible()) {
        // Test device frame rendering
        await expect(preview.locator('[data-testid="phone-frame"]')).toBeVisible();
        
        // Test status bar simulation
        await expect(preview.locator('[data-testid="status-bar"]')).toBeVisible();
        
        // Test real-time updates
        await page.fill('input[name="title"]', 'Test Title');
        await page.waitForTimeout(1000);
        await expect(preview.locator('text=Test Title')).toBeVisible();
        
        // Test refresh functionality
        await page.click('[data-testid="refresh-preview-button"]');
        await page.waitForLoadState('networkidle');
      }
    });
    
    test('ThemeColorPicker component functionality', async ({ page }) => {
      await page.click('text=Content Manager');
      await page.click('text=Paywall');
      await page.click('text=Create new entry');
      
      const colorPicker = page.locator('[data-testid="theme-color-picker"]');
      if (await colorPicker.isVisible()) {
        // Test primary color selection
        await page.click('[data-testid="primary-color-picker"]');
        await page.click('[data-testid="color-blue"]');
        
        // Verify color application
        const preview = page.locator('[data-testid="paywall-preview"]');
        if (await preview.isVisible()) {
          const ctaButton = preview.locator('[data-testid="cta-button"]');
          await expect(ctaButton).toHaveCSS('background-color', /blue/);
        }
        
        // Test gradient backgrounds
        await page.click('[data-testid="gradient-toggle"]');
        await page.click('[data-testid="gradient-preset-1"]');
      }
    });
    
    test('FeatureManager drag and drop functionality', async ({ page }) => {
      await page.click('text=Content Manager');
      await page.click('text=Paywall');
      await page.click('text=Create new entry');
      
      const featureManager = page.locator('[data-testid="feature-manager"]');
      if (await featureManager.isVisible()) {
        // Add multiple features
        await page.click('[data-testid="add-feature-button"]');
        await page.fill('[data-testid="feature-title-0"]', 'Feature 1');
        
        await page.click('[data-testid="add-feature-button"]');
        await page.fill('[data-testid="feature-title-1"]', 'Feature 2');
        
        // Test drag and drop reordering
        const feature1 = page.locator('[data-testid="feature-item-0"]');
        const feature2 = page.locator('[data-testid="feature-item-1"]');
        
        await feature1.dragTo(feature2);
        
        // Verify order changed
        const firstFeature = page.locator('[data-testid="feature-item-0"] [data-testid="feature-title"]');
        await expect(firstFeature).toHaveValue('Feature 2');
      }
    });
  });

  test.describe('Performance and Accessibility Tests', () => {
    
    test('should load admin interface within performance thresholds', async ({ page }) => {
      const startTime = Date.now();
      
      await page.goto(ADMIN_URL);
      await page.waitForLoadState('networkidle');
      
      const loadTime = Date.now() - startTime;
      
      // Should load within 5 seconds
      expect(loadTime).toBeLessThan(5000);
      
      // Check for performance metrics
      const performanceMetrics = await page.evaluate(() => {
        return JSON.stringify(performance.getEntriesByType('navigation'));
      });
      
      console.log('Performance metrics:', performanceMetrics);
    });
    
    test('should support keyboard navigation', async ({ page }) => {
      await page.goto(ADMIN_URL);
      
      // Test tab navigation
      await page.keyboard.press('Tab');
      await page.keyboard.press('Tab');
      await page.keyboard.press('Tab');
      
      // Verify focus is visible
      const focusedElement = page.locator(':focus');
      await expect(focusedElement).toBeVisible();
      
      // Test Enter key activation
      await page.keyboard.press('Enter');
    });
    
    test('should have proper ARIA labels', async ({ page }) => {
      await page.goto(ADMIN_URL);
      
      // Check for ARIA landmarks
      await expect(page.locator('[role="main"]')).toBeVisible();
      await expect(page.locator('[role="navigation"]')).toBeVisible();
      
      // Check for ARIA labels on interactive elements
      const buttons = page.locator('button');
      const buttonCount = await buttons.count();
      
      for (let i = 0; i < Math.min(buttonCount, 5); i++) {
        const button = buttons.nth(i);
        const ariaLabel = await button.getAttribute('aria-label');
        const textContent = await button.textContent();
        
        // Button should have either aria-label or text content
        expect(ariaLabel || textContent).toBeTruthy();
      }
    });
  });

  test.describe('Error Handling and Edge Cases', () => {
    
    test('should handle network failures gracefully', async ({ page }) => {
      await page.goto(ADMIN_URL);
      
      // Simulate network failure
      await page.route('**/api/**', route => route.abort());
      
      // Try to load data
      await page.click('text=Content Manager');
      await page.click('text=Paywall');
      
      // Should show error message
      await expect(page.locator('text=Failed to load')).toBeVisible();
      
      // Should provide retry option
      const retryButton = page.locator('[data-testid="retry-button"]');
      if (await retryButton.isVisible()) {
        await retryButton.click();
      }
    });
    
    test('should handle large datasets without performance degradation', async ({ page }) => {
      await page.goto(ADMIN_URL);
      
      // Navigate to analytics with large dataset
      await page.goto(ADMIN_URL + '/analytics');
      
      // Simulate large dataset load
      await page.route('**/api/analytics/**', route => {
        const largeDataset = {
          data: Array.from({ length: 10000 }, (_, i) => ({
            id: i,
            date: new Date().toISOString(),
            impressions: Math.floor(Math.random() * 1000),
            conversions: Math.floor(Math.random() * 100)
          }))
        };
        
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify(largeDataset)
        });
      });
      
      const startTime = Date.now();
      await page.reload();
      await page.waitForLoadState('networkidle');
      const loadTime = Date.now() - startTime;
      
      // Should handle large dataset within reasonable time
      expect(loadTime).toBeLessThan(10000);
    });
  });
});