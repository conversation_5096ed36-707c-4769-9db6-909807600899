/**
 * Analytics Dashboard Tests
 */

import { analyticsService } from "../../src/services/analytics";

describe("Analytics Service", () => {
	beforeEach(() => {
		// Reset any state if needed
	});

	describe("Dashboard Data", () => {
		it("should get dashboard data with valid filter", async () => {
			const filter = {
				dateRange: {
					start: new Date("2024-01-01"),
					end: new Date("2024-01-31"),
				},
				paywallIds: ["paywall_1", "paywall_2"],
				regions: ["US", "EU"],
			};

			// Mock the database calls
			const mockPaywallMetrics = [
				{
					id: "1",
					paywallId: "paywall_1",
					timestamp: new Date("2024-01-15"),
					impressions: 1000,
					conversions: 50,
					conversionRate: 0.05,
					revenue: 500,
					averageRevenuePerUser: 10,
					bounceRate: 0.3,
					timeToConversion: 120,
					userSegment: "premium",
					region: "US",
					locale: "en",
					deviceType: "mobile" as const,
					platform: "ios" as const,
				},
			];

			// Mock strapi.entityService.findMany
			global.strapi = {
				entityService: {
					findMany: jest.fn().mockResolvedValue(mockPaywallMetrics),
					create: jest.fn(),
				},
				log: {
					error: jest.fn(),
				},
			} as any;

			const dashboardData = await analyticsService.getDashboardData(filter);

			expect(dashboardData).toBeDefined();
			expect(dashboardData.overview).toBeDefined();
			expect(dashboardData.trends).toBeDefined();
			expect(dashboardData.paywallPerformance).toBeDefined();
			expect(dashboardData.regionalPerformance).toBeDefined();
			expect(dashboardData.userEngagement).toBeDefined();
		});

		it("should handle empty metrics gracefully", async () => {
			const filter = {
				dateRange: {
					start: new Date("2024-01-01"),
					end: new Date("2024-01-31"),
				},
			};

			// Mock empty response
			global.strapi = {
				entityService: {
					findMany: jest.fn().mockResolvedValue([]),
					create: jest.fn(),
				},
				log: {
					error: jest.fn(),
				},
			} as any;

			const dashboardData = await analyticsService.getDashboardData(filter);

			expect(dashboardData.overview.totalImpressions).toBe(0);
			expect(dashboardData.overview.totalConversions).toBe(0);
			expect(dashboardData.overview.overallConversionRate).toBe(0);
		});
	});

	describe("Metrics Recording", () => {
		it("should record paywall metrics successfully", async () => {
			const metrics = {
				paywallId: "paywall_1",
				impressions: 100,
				conversions: 5,
				revenue: 50,
				bounceRate: 0.2,
				timeToConversion: 90,
				userSegment: "free",
				region: "US",
				locale: "en",
				deviceType: "mobile" as const,
				platform: "ios" as const,
			};

			const mockCreatedMetric = {
				id: "metric_123",
				timestamp: new Date(),
				...metrics,
				conversionRate: 0.05,
				averageRevenuePerUser: 10,
			};

			global.strapi = {
				entityService: {
					create: jest.fn().mockResolvedValue(mockCreatedMetric),
					findMany: jest.fn(),
				},
				log: {
					error: jest.fn(),
				},
			} as any;

			await expect(
				analyticsService.recordPaywallMetrics(metrics),
			).resolves.not.toThrow();
			expect(strapi.entityService.create).toHaveBeenCalledWith(
				"api::paywall-metrics.paywall-metrics",
				expect.objectContaining({
					data: expect.objectContaining({
						paywallId: "paywall_1",
						impressions: 100,
						conversions: 5,
					}),
				}),
			);
		});

		it("should record user engagement successfully", async () => {
			const engagement = {
				paywallId: "paywall_1",
				sessionDuration: 300,
				pageViews: 3,
				clickThroughRate: 0.15,
				scrollDepth: 0.8,
				interactionEvents: [
					{
						type: "click",
						element: "cta-button",
						timestamp: new Date(),
						value: "subscribe",
					},
				],
				heatmapData: [{ x: 100, y: 200, clicks: 5 }],
			};

			const mockCreatedEngagement = {
				id: "engagement_123",
				timestamp: new Date(),
				...engagement,
			};

			global.strapi = {
				entityService: {
					create: jest.fn().mockResolvedValue(mockCreatedEngagement),
					findMany: jest.fn(),
				},
				log: {
					error: jest.fn(),
				},
			} as any;

			await expect(
				analyticsService.recordUserEngagement(engagement),
			).resolves.not.toThrow();
			expect(strapi.entityService.create).toHaveBeenCalledWith(
				"api::user-engagement.user-engagement",
				expect.objectContaining({
					data: expect.objectContaining({
						paywallId: "paywall_1",
						sessionDuration: 300,
						pageViews: 3,
					}),
				}),
			);
		});
	});

	describe("Real-time Metrics", () => {
		it("should get real-time metrics", async () => {
			const paywallIds = ["paywall_1", "paywall_2"];

			const mockRecentMetrics = [
				{
					id: "1",
					paywallId: "paywall_1",
					timestamp: new Date(),
					impressions: 10,
					conversions: 1,
					conversionRate: 0.1,
					revenue: 10,
					averageRevenuePerUser: 10,
					bounceRate: 0.2,
					timeToConversion: 60,
					userSegment: "premium",
					region: "US",
					locale: "en",
					deviceType: "mobile" as const,
					platform: "ios" as const,
				},
			];

			global.strapi = {
				entityService: {
					findMany: jest.fn().mockResolvedValue(mockRecentMetrics),
					create: jest.fn(),
				},
				log: {
					error: jest.fn(),
				},
			} as any;

			const realtimeMetrics =
				await analyticsService.getRealtimeMetrics(paywallIds);

			expect(realtimeMetrics).toBeDefined();
			expect(realtimeMetrics.activeUsers).toBe(1);
			expect(realtimeMetrics.currentImpressions).toBe(10);
			expect(realtimeMetrics.currentConversions).toBe(1);
			expect(realtimeMetrics.realtimeConversionRate).toBe(0.1);
		});
	});

	describe("Data Export", () => {
		it("should export data as CSV", async () => {
			const exportOptions = {
				format: "csv" as const,
				includeCharts: false,
				dateRange: {
					start: new Date("2024-01-01"),
					end: new Date("2024-01-31"),
				},
				metrics: ["impressions", "conversions", "revenue"],
				groupBy: "paywall",
			};

			const mockDashboardData = {
				overview: {
					totalImpressions: 1000,
					totalConversions: 50,
					overallConversionRate: 0.05,
					totalRevenue: 500,
					averageRevenuePerUser: 10,
					activePaywalls: 2,
					topPerformingPaywall: {
						id: "paywall_1",
						name: "Premium Paywall",
						conversionRate: 0.06,
					},
				},
				trends: {
					impressionsTrend: [],
					conversionsTrend: [],
					revenueTrend: [],
					conversionRateTrend: [],
				},
				paywallPerformance: [
					{
						paywallId: "paywall_1",
						name: "Premium Paywall",
						impressions: 600,
						conversions: 30,
						conversionRate: 0.05,
						revenue: 300,
						averageRevenuePerUser: 10,
						trend: "up" as const,
						changePercentage: 5.2,
					},
				],
				regionalPerformance: [],
				userEngagement: {
					averageSessionDuration: 180,
					averagePageViews: 2.5,
					averageClickThroughRate: 0.12,
					bounceRate: 0.35,
					topInteractionElements: [],
				},
			};

			// Mock the getDashboardData method
			jest
				.spyOn(analyticsService, "getDashboardData")
				.mockResolvedValue(mockDashboardData);

			const exportData = await analyticsService.exportData(exportOptions);

			expect(typeof exportData).toBe("string");
			expect(exportData).toContain("Metric,Value,Date,Paywall,Region");
			expect(exportData).toContain("Premium Paywall");
		});

		it("should export data as JSON", async () => {
			const exportOptions = {
				format: "json" as const,
				includeCharts: false,
				dateRange: {
					start: new Date("2024-01-01"),
					end: new Date("2024-01-31"),
				},
				metrics: ["impressions", "conversions"],
				groupBy: "region",
			};

			const mockDashboardData = {
				overview: {
					totalImpressions: 1000,
					totalConversions: 50,
					overallConversionRate: 0.05,
					totalRevenue: 500,
					averageRevenuePerUser: 10,
					activePaywalls: 2,
					topPerformingPaywall: {
						id: "paywall_1",
						name: "Premium Paywall",
						conversionRate: 0.06,
					},
				},
				trends: {
					impressionsTrend: [],
					conversionsTrend: [],
					revenueTrend: [],
					conversionRateTrend: [],
				},
				paywallPerformance: [],
				regionalPerformance: [],
				userEngagement: {
					averageSessionDuration: 180,
					averagePageViews: 2.5,
					averageClickThroughRate: 0.12,
					bounceRate: 0.35,
					topInteractionElements: [],
				},
			};

			jest
				.spyOn(analyticsService, "getDashboardData")
				.mockResolvedValue(mockDashboardData);

			const exportData = await analyticsService.exportData(exportOptions);

			expect(typeof exportData).toBe("string");
			const parsedData = JSON.parse(exportData);
			expect(parsedData.overview).toBeDefined();
			expect(parsedData.overview.totalImpressions).toBe(1000);
		});
	});

	describe("Error Handling", () => {
		it("should handle database errors gracefully", async () => {
			const filter = {
				dateRange: {
					start: new Date("2024-01-01"),
					end: new Date("2024-01-31"),
				},
			};

			global.strapi = {
				entityService: {
					findMany: jest.fn().mockRejectedValue(new Error("Database error")),
					create: jest.fn(),
				},
				log: {
					error: jest.fn(),
				},
			} as any;

			await expect(analyticsService.getDashboardData(filter)).rejects.toThrow(
				"Database error",
			);
			expect(strapi.log.error).toHaveBeenCalled();
		});

		it("should handle invalid export format", async () => {
			const exportOptions = {
				format: "invalid" as any,
				includeCharts: false,
				dateRange: {
					start: new Date("2024-01-01"),
					end: new Date("2024-01-31"),
				},
				metrics: ["impressions"],
				groupBy: "paywall",
			};

			await expect(analyticsService.exportData(exportOptions)).rejects.toThrow(
				"Unsupported export format: invalid",
			);
		});
	});

	describe("Caching", () => {
		it("should cache dashboard data", async () => {
			const filter = {
				dateRange: {
					start: new Date("2024-01-01"),
					end: new Date("2024-01-31"),
				},
			};

			const mockMetrics = [
				{
					id: "1",
					paywallId: "paywall_1",
					timestamp: new Date("2024-01-15"),
					impressions: 100,
					conversions: 5,
					conversionRate: 0.05,
					revenue: 50,
					averageRevenuePerUser: 10,
					bounceRate: 0.3,
					timeToConversion: 120,
					userSegment: "premium",
					region: "US",
					locale: "en",
					deviceType: "mobile" as const,
					platform: "ios" as const,
				},
			];

			global.strapi = {
				entityService: {
					findMany: jest.fn().mockResolvedValue(mockMetrics),
					create: jest.fn(),
				},
				log: {
					error: jest.fn(),
				},
			} as any;

			// First call should hit the database
			await analyticsService.getDashboardData(filter);
			expect(strapi.entityService.findMany).toHaveBeenCalledTimes(3); // Called for metrics, engagement, and revenue

			// Second call within cache expiry should use cache
			await analyticsService.getDashboardData(filter);
			expect(strapi.entityService.findMany).toHaveBeenCalledTimes(3); // Should not increase
		});
	});
});
