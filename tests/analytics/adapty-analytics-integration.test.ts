/**
 * Integration tests for Adapty Analytics Integration
 * Tests analytics data synchronization functionality
 */

import { adaptyAnalyticsIntegration } from "../../src/services/analytics/adapty-analytics-integration";
import { cleanupStrapi, setupStrapi } from "../setup";

describe("Adapty Analytics Integration", () => {
	let strapi: any;

	beforeAll(async () => {
		strapi = await setupStrapi();
	});

	afterAll(async () => {
		await cleanupStrapi();
	});

	beforeEach(async () => {
		// Clean up test data
		await strapi.db
			.query("api::subscription-metrics.subscription-metrics")
			.deleteMany({});
		await strapi.db
			.query("api::cohort-analysis.cohort-analysis")
			.deleteMany({});
		await strapi.db
			.query("api::performance-alert.performance-alert")
			.deleteMany({});
	});

	describe("Subscription Data Synchronization", () => {
		it("should sync subscription lifecycle data from Adapty", async () => {
			// Mock Adapty client response
			const mockSubscriptionEvents = [
				{
					userId: "user_123",
					profileId: "profile_123",
					subscriptionId: "sub_123",
					productId: "premium_monthly",
					placementId: "main_paywall",
					paywallId: "paywall_v1",
					event: "subscription_started",
					timestamp: new Date(),
					revenue: 9.99,
					currency: "USD",
					subscriptionPeriod: "monthly",
					isTrialPeriod: false,
					isIntroductoryPeriod: false,
					region: "US",
					locale: "en-US",
					deviceType: "mobile",
					platform: "ios",
				},
			];

			// Mock the Adapty client
			jest
				.spyOn(adaptyAnalyticsIntegration as any, "fetchSubscriptionEvents")
				.mockResolvedValue(mockSubscriptionEvents);

			// Perform sync
			await adaptyAnalyticsIntegration.syncSubscriptionData();

			// Verify data was stored
			const storedMetrics = await strapi.entityService.findMany(
				"api::subscription-metrics.subscription-metrics",
			);
			expect(storedMetrics).toHaveLength(1);
			expect(storedMetrics[0].type).toBe("overall");
			expect(storedMetrics[0].subscriptions).toBe(1);
			expect(storedMetrics[0].revenue).toBe(9.99);
		});

		it("should handle multiple subscription events correctly", async () => {
			const mockEvents = [
				{
					userId: "user_1",
					event: "subscription_started",
					revenue: 9.99,
					timestamp: new Date(),
				},
				{
					userId: "user_2",
					event: "trial_started",
					revenue: 0,
					timestamp: new Date(),
				},
				{
					userId: "user_2",
					event: "trial_converted",
					revenue: 9.99,
					timestamp: new Date(),
				},
			];

			jest
				.spyOn(adaptyAnalyticsIntegration as any, "fetchSubscriptionEvents")
				.mockResolvedValue(mockEvents);

			await adaptyAnalyticsIntegration.syncSubscriptionData();

			const metrics = await strapi.entityService.findMany(
				"api::subscription-metrics.subscription-metrics",
			);
			expect(metrics).toHaveLength(1);
			expect(metrics[0].subscriptions).toBe(2); // 2 conversions
			expect(metrics[0].trials).toBe(1); // 1 trial start
			expect(metrics[0].conversions).toBe(1); // 1 trial conversion
		});
	});

	describe("Cohort Analysis", () => {
		it("should generate cohort analysis data", async () => {
			// Create test subscription data
			await strapi.entityService.create(
				"api::subscription-metrics.subscription-metrics",
				{
					data: {
						type: "overall",
						identifier: "cohort_2024_01",
						timestamp: new Date("2024-01-01"),
						revenue: 100,
						subscriptions: 10,
						trials: 5,
						conversions: 8,
						publishedAt: new Date(),
					},
				},
			);

			// Generate cohort analysis
			await adaptyAnalyticsIntegration.generateCohortAnalysis();

			// Verify cohort data was created
			const cohorts = await strapi.entityService.findMany(
				"api::cohort-analysis.cohort-analysis",
			);
			expect(cohorts).toHaveLength(1);
			expect(cohorts[0].cohortName).toContain("2024");
			expect(cohorts[0].totalUsers).toBe(10);
			expect(cohorts[0].revenueMetrics.totalRevenue).toBe(100);
		});

		it("should calculate retention rates correctly", async () => {
			// Mock cohort data with retention information
			const mockCohortData = {
				cohortName: "January 2024",
				cohortStartDate: new Date("2024-01-01"),
				totalUsers: 100,
				retentionRates: {
					day1: 0.85,
					day7: 0.7,
					day30: 0.45,
					day90: 0.3,
					day180: 0.2,
					day365: 0.15,
				},
				revenueMetrics: {
					totalRevenue: 1000,
					averageRevenuePerUser: 10,
					averageRevenuePerPaidUser: 25,
					lifetimeValue: 50,
				},
				churnAnalysis: {
					churnRate: 0.55,
					churnReasons: {
						price_too_high: 0.3,
						lack_of_features: 0.15,
						technical_issues: 0.1,
					},
				},
			};

			jest
				.spyOn(adaptyAnalyticsIntegration as any, "calculateCohortMetrics")
				.mockResolvedValue(mockCohortData);

			await adaptyAnalyticsIntegration.generateCohortAnalysis();

			const cohorts = await strapi.entityService.findMany(
				"api::cohort-analysis.cohort-analysis",
			);
			expect(cohorts[0].retentionRates.day30).toBe(0.45);
			expect(cohorts[0].churnAnalysis.churnRate).toBe(0.55);
		});
	});

	describe("Unified Reporting", () => {
		beforeEach(async () => {
			// Create test data
			await strapi.entityService.create(
				"api::subscription-metrics.subscription-metrics",
				{
					data: {
						type: "overall",
						identifier: "test_metrics",
						timestamp: new Date(),
						revenue: 500,
						subscriptions: 50,
						trials: 25,
						conversions: 40,
						conversionRate: 0.8,
						trialConversionRate: 0.6,
						averageRevenuePerUser: 10,
						publishedAt: new Date(),
					},
				},
			);

			await strapi.entityService.create(
				"api::cohort-analysis.cohort-analysis",
				{
					data: {
						cohortName: "Test Cohort",
						cohortStartDate: new Date(),
						totalUsers: 50,
						retentionRates: {
							day1: 0.9,
							day7: 0.7,
							day30: 0.5,
							day90: 0.3,
							day180: 0.2,
							day365: 0.1,
						},
						revenueMetrics: {
							totalRevenue: 500,
							averageRevenuePerUser: 10,
							averageRevenuePerPaidUser: 12.5,
							lifetimeValue: 25,
						},
						churnAnalysis: { churnRate: 0.5, churnReasons: {} },
						conversionMetrics: {
							trialConversions: 30,
							overallConversionRate: 0.6,
						},
						timestamp: new Date(),
						publishedAt: new Date(),
					},
				},
			);
		});

		it("should generate unified reporting data", async () => {
			const filter = {
				startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
				endDate: new Date().toISOString(),
			};

			const unifiedData =
				await adaptyAnalyticsIntegration.getUnifiedReporting(filter);

			expect(unifiedData).toHaveProperty("subscriptionMetrics");
			expect(unifiedData).toHaveProperty("revenueMetrics");
			expect(unifiedData).toHaveProperty("conversionMetrics");
			expect(unifiedData).toHaveProperty("churnMetrics");
			expect(unifiedData).toHaveProperty("cohortAnalysis");

			expect(unifiedData.subscriptionMetrics.totalSubscriptions).toBe(50);
			expect(unifiedData.revenueMetrics.totalRevenue).toBe(500);
			expect(unifiedData.conversionMetrics.overallConversionRate).toBe(0.8);
		});

		it("should combine CMS and Adapty analytics data", async () => {
			// Mock CMS analytics data
			const mockCMSData = {
				paywallMetrics: {
					impressions: 1000,
					conversions: 50,
					conversionRate: 0.05,
				},
				userEngagement: {
					averageSessionDuration: 300,
					bounceRate: 0.3,
				},
			};

			jest
				.spyOn(adaptyAnalyticsIntegration as any, "getCMSAnalytics")
				.mockResolvedValue(mockCMSData);

			const filter = {
				startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
				endDate: new Date().toISOString(),
			};

			const unifiedData =
				await adaptyAnalyticsIntegration.getUnifiedReporting(filter);

			expect(unifiedData.userEngagement.averageSessionDuration).toBe(300);
			expect(unifiedData.userEngagement.bounceRate).toBe(0.3);
		});
	});

	describe("Performance Monitoring and Alerts", () => {
		it("should detect conversion rate drops and create alerts", async () => {
			// Create historical data with higher conversion rate
			await strapi.entityService.create(
				"api::subscription-metrics.subscription-metrics",
				{
					data: {
						type: "overall",
						identifier: "previous_period",
						timestamp: new Date(Date.now() - 48 * 60 * 60 * 1000),
						conversionRate: 0.1, // 10% conversion rate
						revenue: 1000,
						subscriptions: 100,
						publishedAt: new Date(),
					},
				},
			);

			// Create current data with lower conversion rate
			await strapi.entityService.create(
				"api::subscription-metrics.subscription-metrics",
				{
					data: {
						type: "overall",
						identifier: "current_period",
						timestamp: new Date(),
						conversionRate: 0.05, // 5% conversion rate (50% drop)
						revenue: 500,
						subscriptions: 50,
						publishedAt: new Date(),
					},
				},
			);

			// Check for performance anomalies
			const alerts =
				await adaptyAnalyticsIntegration.checkPerformanceAnomalies();

			expect(alerts).toHaveLength(1);
			expect(alerts[0].type).toBe("conversion_drop");
			expect(alerts[0].severity).toBe("high");
			expect(alerts[0].affectedMetrics).toContain("conversion_rate");
		});

		it("should detect revenue anomalies", async () => {
			// Create data showing significant revenue change
			await strapi.entityService.create(
				"api::subscription-metrics.subscription-metrics",
				{
					data: {
						type: "overall",
						identifier: "previous_revenue",
						timestamp: new Date(Date.now() - 48 * 60 * 60 * 1000),
						revenue: 1000,
						subscriptions: 100,
						publishedAt: new Date(),
					},
				},
			);

			await strapi.entityService.create(
				"api::subscription-metrics.subscription-metrics",
				{
					data: {
						type: "overall",
						identifier: "current_revenue",
						timestamp: new Date(),
						revenue: 2500, // 150% increase
						subscriptions: 100,
						publishedAt: new Date(),
					},
				},
			);

			const alerts =
				await adaptyAnalyticsIntegration.checkPerformanceAnomalies();

			expect(alerts).toHaveLength(1);
			expect(alerts[0].type).toBe("revenue_spike");
			expect(alerts[0].severity).toBe("high");
		});

		it("should store alerts in database", async () => {
			const mockAlert = {
				id: "test_alert_123",
				type: "conversion_drop",
				severity: "high",
				message: "Test alert",
				currentValue: 0.05,
				previousValue: 0.1,
				threshold: 15,
				timestamp: new Date().toISOString(),
				affectedMetrics: ["conversion_rate"],
				recommendedActions: ["Review paywall changes"],
			};

			await (adaptyAnalyticsIntegration as any).storePerformanceAlert(
				mockAlert,
			);

			const storedAlerts = await strapi.entityService.findMany(
				"api::performance-alert.performance-alert",
			);
			expect(storedAlerts).toHaveLength(1);
			expect(storedAlerts[0].alertId).toBe("test_alert_123");
			expect(storedAlerts[0].type).toBe("conversion_drop");
			expect(storedAlerts[0].status).toBe("active");
		});
	});

	describe("Data Export", () => {
		beforeEach(async () => {
			// Create test data for export
			await strapi.entityService.create(
				"api::subscription-metrics.subscription-metrics",
				{
					data: {
						type: "overall",
						identifier: "export_test",
						timestamp: new Date(),
						revenue: 1000,
						subscriptions: 100,
						trials: 50,
						conversions: 80,
						conversionRate: 0.8,
						trialConversionRate: 0.6,
						averageRevenuePerUser: 10,
						publishedAt: new Date(),
					},
				},
			);
		});

		it("should export analytics data in JSON format", async () => {
			const filter = {
				startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
				endDate: new Date().toISOString(),
			};

			const exportResult = await adaptyAnalyticsIntegration.exportAnalyticsData(
				filter,
				"json",
			);

			expect(exportResult.mimeType).toBe("application/json");
			expect(exportResult.filename).toMatch(
				/adapty-analytics-\d{4}-\d{2}-\d{2}\.json/,
			);

			const parsedData = JSON.parse(exportResult.data);
			expect(parsedData).toHaveProperty("subscriptionMetrics");
			expect(parsedData).toHaveProperty("revenueMetrics");
		});

		it("should export analytics data in CSV format", async () => {
			const filter = {
				startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
				endDate: new Date().toISOString(),
			};

			const exportResult = await adaptyAnalyticsIntegration.exportAnalyticsData(
				filter,
				"csv",
			);

			expect(exportResult.mimeType).toBe("text/csv");
			expect(exportResult.filename).toMatch(
				/adapty-analytics-\d{4}-\d{2}-\d{2}\.csv/,
			);
			expect(exportResult.data).toContain("Date,Total Revenue,Subscriptions");
		});
	});

	describe("Integration Tests", () => {
		it("should run comprehensive integration tests", async () => {
			// Mock successful operations
			jest
				.spyOn(adaptyAnalyticsIntegration, "syncSubscriptionData")
				.mockResolvedValue();
			jest
				.spyOn(adaptyAnalyticsIntegration, "generateCohortAnalysis")
				.mockResolvedValue();
			jest
				.spyOn(adaptyAnalyticsIntegration, "checkPerformanceAnomalies")
				.mockResolvedValue([]);

			const testResults =
				await adaptyAnalyticsIntegration.runIntegrationTests();

			expect(testResults.passed).toBe(4);
			expect(testResults.failed).toBe(0);
			expect(testResults.results).toHaveLength(4);

			const testNames = testResults.results.map((r) => r.test);
			expect(testNames).toContain("Subscription Data Sync");
			expect(testNames).toContain("Cohort Analysis Generation");
			expect(testNames).toContain("Unified Reporting");
			expect(testNames).toContain("Performance Monitoring");
		});

		it("should handle test failures gracefully", async () => {
			// Mock a failure
			jest
				.spyOn(adaptyAnalyticsIntegration, "syncSubscriptionData")
				.mockRejectedValue(new Error("Sync failed"));

			const testResults =
				await adaptyAnalyticsIntegration.runIntegrationTests();

			expect(testResults.failed).toBeGreaterThan(0);
			expect(testResults.results.some((r) => r.status === "failed")).toBe(true);
		});
	});
});
