/**
 * Integration tests for admin interface workflows
 */

import {
	beforeAll,
	beforeEach,
	describe,
	expect,
	it,
	jest,
} from "@jest/globals";

// Mock Strapi admin components and hooks
const mockStrapi = {
	entityService: {
		findMany: jest.fn(),
		findOne: jest.fn(),
		create: jest.fn(),
		update: jest.fn(),
		delete: jest.fn(),
	},
	service: jest.fn(),
	log: {
		error: jest.fn(),
		info: jest.fn(),
	},
	admin: {
		services: {
			permission: {
				check: jest.fn().mockResolvedValue(true),
			},
		},
	},
};

// Mock React and Strapi helper components
jest.mock("react", () => ({
	...jest.requireActual("react"),
	useState: jest.fn(),
	useEffect: jest.fn(),
}));

jest.mock("@strapi/helper-plugin", () => ({
	useCMEditViewDataManager: jest.fn(),
	useNotification: jest.fn(),
	request: jest.fn(),
}));

// Mock paywall data
const mockPaywallData = {
	id: 1,
	name: "Test Paywall",
	placement_id: "test_placement_001",
	title: "Premium Features",
	subtitle: "Unlock all features",
	description_text: "Get access to premium features",
	cta_text: "Subscribe Now",
	cta_secondary_text: "Start Free Trial",
	status: "draft",
	adapty_sync_status: "pending",
	theme: {
		name: "Default Theme",
		primary_color: "#007bff",
		background_color: "#ffffff",
		text_color: "#000000",
		button_style: "rounded",
		header_style: "hero",
		product_display_style: "list",
		show_features: true,
		show_testimonials: false,
	},
	features: [
		{
			id: 1,
			icon: "star",
			title: "Premium Support",
			description: "24/7 customer support",
			order: 1,
			is_highlighted: true,
		},
		{
			id: 2,
			icon: "shield",
			title: "Advanced Security",
			description: "Enterprise-grade security",
			order: 2,
			is_highlighted: false,
		},
	],
	testimonials: [
		{
			id: 1,
			author_name: "John Doe",
			author_title: "CEO",
			content: "Great product!",
			rating: 5,
			company: "Tech Corp",
			order: 1,
		},
	],
	product_labels: [
		{
			id: 1,
			product_id: "premium_monthly",
			badge_text: "Most Popular",
			badge_color: "#ff6b35",
			highlight: true,
			savings_percentage: 20,
			badge_position: "top-right",
		},
	],
};

describe("Admin Interface Integration Tests", () => {
	beforeAll(() => {
		global.strapi = mockStrapi;
	});

	beforeEach(() => {
		jest.clearAllMocks();
	});

	describe("Paywall Content Management Workflow", () => {
		it("should create a new paywall with complete workflow", async () => {
			// Mock the creation workflow
			const createPaywallWorkflow = async (paywallData: any) => {
				// Step 1: Validate required fields
				const requiredFields = ["name", "placement_id", "title", "cta_text"];
				for (const field of requiredFields) {
					if (!paywallData[field]) {
						throw new Error(`${field} is required`);
					}
				}

				// Step 2: Create paywall
				const createdPaywall = await mockStrapi.entityService.create(
					"api::paywall.paywall",
					{
						data: paywallData,
					},
				);

				// Step 3: Create theme component
				if (paywallData.theme) {
					await mockStrapi.entityService.create("shared.theme", {
						data: paywallData.theme,
					});
				}

				// Step 4: Create features
				if (paywallData.features) {
					for (const feature of paywallData.features) {
						await mockStrapi.entityService.create("shared.feature", {
							data: feature,
						});
					}
				}

				return createdPaywall;
			};

			// Mock successful creation
			mockStrapi.entityService.create.mockResolvedValue({
				id: 1,
				...mockPaywallData,
			});

			const result = await createPaywallWorkflow(mockPaywallData);

			expect(mockStrapi.entityService.create).toHaveBeenCalledWith(
				"api::paywall.paywall",
				{
					data: mockPaywallData,
				},
			);
			expect(result.id).toBe(1);
			expect(result.name).toBe("Test Paywall");
		});

		it("should handle validation errors during paywall creation", async () => {
			const invalidPaywallData = {
				name: "", // Missing required field
				placement_id: "test_placement_001",
				title: "Premium Features",
				// Missing cta_text
			};

			const createPaywallWithValidation = async (data: any) => {
				const errors: string[] = [];

				if (!data.name) errors.push("Name is required");
				if (!data.cta_text) errors.push("CTA text is required");
				if (data.placement_id && !/^[a-zA-Z0-9_]+$/.test(data.placement_id)) {
					errors.push("Placement ID format is invalid");
				}

				if (errors.length > 0) {
					throw new Error(`Validation failed: ${errors.join(", ")}`);
				}

				return await mockStrapi.entityService.create("api::paywall.paywall", {
					data,
				});
			};

			await expect(
				createPaywallWithValidation(invalidPaywallData),
			).rejects.toThrow(
				"Validation failed: Name is required, CTA text is required",
			);
		});

		it("should update paywall with theme changes", async () => {
			const updatedTheme = {
				...mockPaywallData.theme,
				primary_color: "#ff5733",
				background_color: "#f8f9fa",
			};

			const updatePaywallTheme = async (paywallId: number, theme: any) => {
				// Validate color format
				const colorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
				if (!colorRegex.test(theme.primary_color)) {
					throw new Error("Invalid primary color format");
				}
				if (!colorRegex.test(theme.background_color)) {
					throw new Error("Invalid background color format");
				}

				return await mockStrapi.entityService.update(
					"api::paywall.paywall",
					paywallId,
					{
						data: { theme },
					},
				);
			};

			mockStrapi.entityService.update.mockResolvedValue({
				...mockPaywallData,
				theme: updatedTheme,
			});

			const result = await updatePaywallTheme(1, updatedTheme);

			expect(mockStrapi.entityService.update).toHaveBeenCalledWith(
				"api::paywall.paywall",
				1,
				{
					data: { theme: updatedTheme },
				},
			);
			expect(result.theme.primary_color).toBe("#ff5733");
		});
	});

	describe("Feature Management Workflow", () => {
		it("should handle drag-and-drop feature reordering", async () => {
			const reorderFeatures = async (paywallId: number, features: any[]) => {
				// Update order for each feature
				const updatePromises = features.map((feature, index) =>
					mockStrapi.entityService.update("shared.feature", feature.id, {
						data: { order: index + 1 },
					}),
				);

				await Promise.all(updatePromises);

				// Update paywall with new feature order
				return await mockStrapi.entityService.update(
					"api::paywall.paywall",
					paywallId,
					{
						data: { features },
					},
				);
			};

			const reorderedFeatures = [
				{ ...mockPaywallData.features[1], order: 1 }, // Move second feature first
				{ ...mockPaywallData.features[0], order: 2 }, // Move first feature second
			];

			mockStrapi.entityService.update.mockResolvedValue({
				...mockPaywallData,
				features: reorderedFeatures,
			});

			const result = await reorderFeatures(1, reorderedFeatures);

			expect(mockStrapi.entityService.update).toHaveBeenCalledTimes(3); // 2 features + 1 paywall
			expect(result.features[0].title).toBe("Advanced Security");
			expect(result.features[1].title).toBe("Premium Support");
		});

		it("should add new feature with proper validation", async () => {
			const addFeature = async (paywallId: number, feature: any) => {
				// Validate feature data
				if (!feature.title) throw new Error("Feature title is required");
				if (!feature.description)
					throw new Error("Feature description is required");
				if (!feature.icon) throw new Error("Feature icon is required");

				// Get current features to determine order
				const paywall = await mockStrapi.entityService.findOne(
					"api::paywall.paywall",
					paywallId,
				);
				const nextOrder = (paywall.features?.length || 0) + 1;

				const newFeature = {
					...feature,
					order: nextOrder,
				};

				// Create feature
				const createdFeature = await mockStrapi.entityService.create(
					"shared.feature",
					{
						data: newFeature,
					},
				);

				// Update paywall to include new feature
				const updatedFeatures = [...(paywall.features || []), createdFeature];
				await mockStrapi.entityService.update(
					"api::paywall.paywall",
					paywallId,
					{
						data: { features: updatedFeatures },
					},
				);

				return createdFeature;
			};

			const newFeature = {
				icon: "lightning",
				title: "Fast Performance",
				description: "Lightning-fast loading times",
				is_highlighted: false,
			};

			mockStrapi.entityService.findOne.mockResolvedValue(mockPaywallData);
			mockStrapi.entityService.create.mockResolvedValue({
				id: 3,
				...newFeature,
				order: 3,
			});

			const result = await addFeature(1, newFeature);

			expect(result.order).toBe(3);
			expect(result.title).toBe("Fast Performance");
			expect(mockStrapi.entityService.create).toHaveBeenCalledWith(
				"shared.feature",
				{
					data: { ...newFeature, order: 3 },
				},
			);
		});
	});

	describe("Bulk Operations Workflow", () => {
		it("should execute bulk publish operation", async () => {
			const bulkPublish = async (paywallIds: number[]) => {
				const results = [];
				const errors = [];

				for (const id of paywallIds) {
					try {
						const result = await mockStrapi.entityService.update(
							"api::paywall.paywall",
							id,
							{
								data: { publishedAt: new Date() },
							},
						);
						results.push(result);
					} catch (error) {
						errors.push({ id, error: error.message });
					}
				}

				return { results, errors };
			};

			const paywallIds = [1, 2, 3];
			mockStrapi.entityService.update.mockResolvedValue({
				id: 1,
				publishedAt: new Date(),
			});

			const result = await bulkPublish(paywallIds);

			expect(mockStrapi.entityService.update).toHaveBeenCalledTimes(3);
			expect(result.results).toHaveLength(3);
			expect(result.errors).toHaveLength(0);
		});

		it("should handle partial failures in bulk operations", async () => {
			const bulkDelete = async (paywallIds: number[]) => {
				const results = [];
				const errors = [];

				for (const id of paywallIds) {
					try {
						await mockStrapi.entityService.delete("api::paywall.paywall", id);
						results.push({ id, status: "deleted" });
					} catch (error) {
						errors.push({ id, error: error.message });
					}
				}

				return { results, errors };
			};

			const paywallIds = [1, 2, 3];

			// Mock partial failure
			mockStrapi.entityService.delete
				.mockResolvedValueOnce(undefined) // Success for ID 1
				.mockRejectedValueOnce(new Error("Permission denied")) // Failure for ID 2
				.mockResolvedValueOnce(undefined); // Success for ID 3

			const result = await bulkDelete(paywallIds);

			expect(result.results).toHaveLength(2);
			expect(result.errors).toHaveLength(1);
			expect(result.errors[0].id).toBe(2);
			expect(result.errors[0].error).toBe("Permission denied");
		});
	});

	describe("Preview Functionality Workflow", () => {
		it("should generate paywall preview data", async () => {
			const generatePreview = async (paywallData: any) => {
				// Transform paywall data for preview
				const previewData = {
					id: paywallData.id,
					title: paywallData.title,
					subtitle: paywallData.subtitle,
					description: paywallData.description_text,
					cta: {
						primary: paywallData.cta_text,
						secondary: paywallData.cta_secondary_text,
					},
					theme: {
						colors: {
							primary: paywallData.theme?.primary_color || "#007bff",
							background: paywallData.theme?.background_color || "#ffffff",
							text: paywallData.theme?.text_color || "#000000",
						},
						layout: {
							headerStyle: paywallData.theme?.header_style || "hero",
							buttonStyle: paywallData.theme?.button_style || "rounded",
							productDisplay:
								paywallData.theme?.product_display_style || "list",
						},
					},
					features:
						paywallData.features?.map((feature: any) => ({
							icon: feature.icon,
							title: feature.title,
							description: feature.description,
							highlighted: feature.is_highlighted,
						})) || [],
					testimonials:
						paywallData.testimonials?.map((testimonial: any) => ({
							author: testimonial.author_name,
							content: testimonial.content,
							rating: testimonial.rating,
							company: testimonial.company,
						})) || [],
				};

				return previewData;
			};

			const preview = await generatePreview(mockPaywallData);

			expect(preview.title).toBe("Premium Features");
			expect(preview.theme.colors.primary).toBe("#007bff");
			expect(preview.features).toHaveLength(2);
			expect(preview.features[0].highlighted).toBe(true);
			expect(preview.testimonials).toHaveLength(1);
		});

		it("should validate preview data completeness", async () => {
			const validatePreviewData = (previewData: any) => {
				const warnings = [];
				const errors = [];

				// Check required fields
				if (!previewData.title) errors.push("Title is required for preview");
				if (!previewData.cta?.primary)
					errors.push("Primary CTA is required for preview");

				// Check recommendations
				if (!previewData.features || previewData.features.length === 0) {
					warnings.push(
						"No features defined - consider adding features to highlight value",
					);
				}
				if (
					!previewData.testimonials ||
					previewData.testimonials.length === 0
				) {
					warnings.push("No testimonials - consider adding social proof");
				}
				if (previewData.features && previewData.features.length > 8) {
					warnings.push("Too many features may overwhelm users on mobile");
				}

				return { errors, warnings, isValid: errors.length === 0 };
			};

			const incompleteData = { ...mockPaywallData, title: "", features: [] };
			const preview = await generatePreview(incompleteData);
			const validation = validatePreviewData(preview);

			expect(validation.isValid).toBe(false);
			expect(validation.errors).toContain("Title is required for preview");
			expect(validation.warnings).toContain(
				"No features defined - consider adding features to highlight value",
			);
		});
	});

	describe("Form Validation Workflow", () => {
		it("should validate color format in theme configuration", () => {
			const validateColorFormat = (color: string) => {
				const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
				return hexColorRegex.test(color);
			};

			expect(validateColorFormat("#FF5733")).toBe(true);
			expect(validateColorFormat("#fff")).toBe(true);
			expect(validateColorFormat("FF5733")).toBe(false); // Missing #
			expect(validateColorFormat("#GG5733")).toBe(false); // Invalid characters
			expect(validateColorFormat("#FF57")).toBe(false); // Invalid length
		});

		it("should validate placement ID format", () => {
			const validatePlacementId = (placementId: string) => {
				const placementIdRegex = /^[a-zA-Z0-9_]+$/;
				return placementIdRegex.test(placementId) && placementId.length >= 3;
			};

			expect(validatePlacementId("test_placement_001")).toBe(true);
			expect(validatePlacementId("TestPlacement")).toBe(true);
			expect(validatePlacementId("test-placement")).toBe(false); // Contains hyphen
			expect(validatePlacementId("test placement")).toBe(false); // Contains space
			expect(validatePlacementId("ab")).toBe(false); // Too short
		});

		it("should validate feature ordering", () => {
			const validateFeatureOrdering = (features: any[]) => {
				if (!features || features.length === 0) return true;

				const orders = features
					.map((f) => f.order)
					.filter((o) => o !== undefined);
				const uniqueOrders = new Set(orders);

				// Check if all features have order values
				const allHaveOrder = features.every(
					(f) => f.order !== undefined && f.order !== null,
				);

				// Check if orders are unique
				const ordersAreUnique = orders.length === uniqueOrders.size;

				// Check if orders are sequential starting from 1
				const sortedOrders = [...orders].sort((a, b) => a - b);
				const isSequential = sortedOrders.every(
					(order, index) => order === index + 1,
				);

				return {
					valid: allHaveOrder && ordersAreUnique && isSequential,
					errors: [
						...(!allHaveOrder ? ["All features must have order values"] : []),
						...(!ordersAreUnique ? ["Feature orders must be unique"] : []),
						...(!isSequential
							? ["Feature orders should be sequential starting from 1"]
							: []),
					],
				};
			};

			const validFeatures = [
				{ id: 1, title: "Feature 1", order: 1 },
				{ id: 2, title: "Feature 2", order: 2 },
				{ id: 3, title: "Feature 3", order: 3 },
			];

			const invalidFeatures = [
				{ id: 1, title: "Feature 1", order: 1 },
				{ id: 2, title: "Feature 2", order: 1 }, // Duplicate order
				{ id: 3, title: "Feature 3" }, // Missing order
			];

			const validResult = validateFeatureOrdering(validFeatures);
			const invalidResult = validateFeatureOrdering(invalidFeatures);

			expect(validResult.valid).toBe(true);
			expect(invalidResult.valid).toBe(false);
			expect(invalidResult.errors).toContain("Feature orders must be unique");
			expect(invalidResult.errors).toContain(
				"All features must have order values",
			);
		});
	});

	describe("Error Handling and Recovery", () => {
		it("should handle network errors gracefully", async () => {
			const handleNetworkError = async (operation: () => Promise<any>) => {
				try {
					return await operation();
				} catch (error) {
					if (error.message.includes("Network")) {
						return {
							success: false,
							error:
								"Network connection failed. Please check your internet connection and try again.",
							retry: true,
						};
					}
					throw error;
				}
			};

			const failingOperation = () =>
				Promise.reject(new Error("Network request failed"));
			const result = await handleNetworkError(failingOperation);

			expect(result.success).toBe(false);
			expect(result.retry).toBe(true);
			expect(result.error).toContain("Network connection failed");
		});

		it("should provide user-friendly error messages", () => {
			const formatErrorMessage = (error: any) => {
				const errorMessages = {
					ValidationError:
						"Please check your input and fix any validation errors.",
					PermissionError: "You do not have permission to perform this action.",
					NotFoundError: "The requested paywall could not be found.",
					DuplicateError: "A paywall with this placement ID already exists.",
					NetworkError:
						"Connection failed. Please check your internet connection.",
				};

				return (
					errorMessages[error.name] ||
					"An unexpected error occurred. Please try again."
				);
			};

			expect(formatErrorMessage({ name: "ValidationError" })).toBe(
				"Please check your input and fix any validation errors.",
			);
			expect(formatErrorMessage({ name: "UnknownError" })).toBe(
				"An unexpected error occurred. Please try again.",
			);
		});
	});
});
