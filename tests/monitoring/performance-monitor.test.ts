/**
 * Performance Monitor Tests
 * Tests for the performance monitoring system
 */

import { performanceMonitor } from "../../src/services/monitoring/performance-monitor";
import { cleanupStrapi, setupStrapi } from "../setup";

describe("Performance Monitoring System", () => {
	let strapi: any;

	beforeAll(async () => {
		strapi = await setupStrapi();
	});

	afterAll(async () => {
		await cleanupStrapi();
	});

	beforeEach(async () => {
		// Clean up test data
		await strapi.db.query("api::system-health.system-health").deleteMany({});
		await strapi.db
			.query("api::api-performance.api-performance")
			.deleteMany({});
		await strapi.db.query("api::sync-operation.sync-operation").deleteMany({});
		await strapi.db
			.query("api::performance-alert.performance-alert")
			.deleteMany({});
	});

	describe("System Health Monitoring", () => {
		it("should collect and store system health metrics", async () => {
			// Initialize monitoring
			await performanceMonitor.initialize(1); // 1 second interval for testing

			// Wait for at least one metric collection
			await new Promise((resolve) => setTimeout(resolve, 1500));

			// Check if metrics were stored
			const healthMetrics = await strapi.entityService.findMany(
				"api::system-health.system-health",
			);
			expect(healthMetrics).toHaveLength(1);

			const metric = healthMetrics[0];
			expect(metric).toHaveProperty("timestamp");
			expect(metric).toHaveProperty("uptime");
			expect(metric).toHaveProperty("memoryPercentage");
			expect(metric).toHaveProperty("cpuUsage");
			expect(metric).toHaveProperty("responseTime");
			expect(metric).toHaveProperty("status");

			performanceMonitor.stop();
		});

		it("should determine correct system status based on metrics", async () => {
			// Mock system health collection to return specific values
			const mockMetrics = {
				timestamp: new Date(),
				uptime: 3600,
				memoryUsage: {
					used: **********, // 1GB
					total: **********, // 8GB
					percentage: 12.5,
				},
				cpuUsage: 25,
				responseTime: 150,
				activeConnections: 10,
				errorRate: 2,
				status: "healthy" as const,
			};

			jest
				.spyOn(performanceMonitor as any, "collectSystemHealthMetrics")
				.mockResolvedValue(mockMetrics);

			await performanceMonitor.initialize(1);
			await new Promise((resolve) => setTimeout(resolve, 1500));

			const healthMetrics = await strapi.entityService.findMany(
				"api::system-health.system-health",
			);
			expect(healthMetrics[0].status).toBe("healthy");

			performanceMonitor.stop();
		});

		it("should create alerts for critical system health issues", async () => {
			// Mock critical system health
			const criticalMetrics = {
				timestamp: new Date(),
				uptime: 3600,
				memoryUsage: {
					used: **********, // 7.6GB
					total: **********, // 8GB
					percentage: 95,
				},
				cpuUsage: 90,
				responseTime: 6000, // 6 seconds
				activeConnections: 100,
				errorRate: 20,
				status: "critical" as const,
			};

			jest
				.spyOn(performanceMonitor as any, "collectSystemHealthMetrics")
				.mockResolvedValue(criticalMetrics);

			await performanceMonitor.initialize(1);
			await new Promise((resolve) => setTimeout(resolve, 1500));

			// Check if alerts were created
			const alerts = await strapi.entityService.findMany(
				"api::performance-alert.performance-alert",
			);
			expect(alerts.length).toBeGreaterThan(0);

			const criticalAlerts = alerts.filter(
				(alert) => alert.severity === "critical",
			);
			expect(criticalAlerts.length).toBeGreaterThan(0);

			performanceMonitor.stop();
		});
	});

	describe("API Performance Monitoring", () => {
		it("should record API performance metrics", async () => {
			const apiService = strapi.service("api::api-performance.api-performance");

			const metricData = {
				endpoint: "/api/test",
				method: "GET",
				responseTime: 250,
				statusCode: 200,
				timestamp: new Date(),
				userAgent: "test-agent",
				ipAddress: "127.0.0.1",
				requestSize: 1024,
				responseSize: 2048,
			};

			await apiService.recordMetric(metricData);

			const metrics = await strapi.entityService.findMany(
				"api::api-performance.api-performance",
			);
			expect(metrics).toHaveLength(1);
			expect(metrics[0].endpoint).toBe("/api/test");
			expect(metrics[0].responseTime).toBe(250);
			expect(metrics[0].statusCode).toBe(200);
		});

		it("should calculate performance summary correctly", async () => {
			const apiService = strapi.service("api::api-performance.api-performance");

			// Create test metrics
			const testMetrics = [
				{
					endpoint: "/api/test1",
					method: "GET",
					responseTime: 100,
					statusCode: 200,
				},
				{
					endpoint: "/api/test2",
					method: "POST",
					responseTime: 200,
					statusCode: 200,
				},
				{
					endpoint: "/api/test3",
					method: "GET",
					responseTime: 150,
					statusCode: 404,
				},
				{
					endpoint: "/api/test4",
					method: "PUT",
					responseTime: 300,
					statusCode: 500,
				},
			];

			for (const metric of testMetrics) {
				await apiService.recordMetric({
					...metric,
					timestamp: new Date(),
					userAgent: "test",
					ipAddress: "127.0.0.1",
					requestSize: 1024,
					responseSize: 2048,
				});
			}

			const summary = await apiService.getPerformanceSummary(1);

			expect(summary.totalRequests).toBe(4);
			expect(summary.averageResponseTime).toBe(187); // (100+200+150+300)/4
			expect(summary.errorRate).toBe(50); // 2 errors out of 4 requests
		});

		it("should identify top error endpoints", async () => {
			const apiService = strapi.service("api::api-performance.api-performance");

			// Create error metrics
			const errorMetrics = [
				{ endpoint: "/api/error-prone", method: "GET", statusCode: 500 },
				{ endpoint: "/api/error-prone", method: "POST", statusCode: 500 },
				{ endpoint: "/api/another-error", method: "GET", statusCode: 404 },
				{ endpoint: "/api/working", method: "GET", statusCode: 200 },
			];

			for (const metric of errorMetrics) {
				await apiService.recordMetric({
					...metric,
					responseTime: 100,
					timestamp: new Date(),
					userAgent: "test",
					ipAddress: "127.0.0.1",
					requestSize: 1024,
					responseSize: 2048,
				});
			}

			const topErrors = await apiService.getTopErrorEndpoints(1, 5);

			expect(topErrors).toHaveLength(2);
			expect(topErrors[0].endpoint).toBe("GET /api/error-prone");
			expect(topErrors[0].errorCount).toBe(2);
		});
	});

	describe("Sync Operation Monitoring", () => {
		it("should track sync operation lifecycle", async () => {
			const syncService = strapi.service("api::sync-operation.sync-operation");

			// Start sync operation
			const operation = await syncService.startSyncOperation(
				"adapty_sync",
				"test_sync_123",
			);
			expect(operation.status).toBe("running");
			expect(operation.operationType).toBe("adapty_sync");
			expect(operation.operationId).toBe("test_sync_123");

			// Update progress
			await syncService.updateSyncProgress("test_sync_123", 50, 2);

			const updated = await syncService.getSyncOperationStatus("test_sync_123");
			expect(updated.recordsProcessed).toBe(50);
			expect(updated.errorCount).toBe(2);
			expect(updated.successRate).toBe(96); // (50-2)/50 * 100

			// Complete operation
			await syncService.completeSyncOperation("test_sync_123", 100, 5);

			const completed =
				await syncService.getSyncOperationStatus("test_sync_123");
			expect(completed.status).toBe("completed");
			expect(completed.recordsProcessed).toBe(100);
			expect(completed.errorCount).toBe(5);
			expect(completed.successRate).toBe(95);
			expect(completed.duration).toBeGreaterThan(0);
		});

		it("should handle sync operation failures", async () => {
			const syncService = strapi.service("api::sync-operation.sync-operation");

			// Start and fail sync operation
			await syncService.startSyncOperation("analytics_sync", "failed_sync_456");
			await syncService.failSyncOperation("failed_sync_456", [
				"Connection timeout",
				"Invalid API response",
			]);

			const failed =
				await syncService.getSyncOperationStatus("failed_sync_456");
			expect(failed.status).toBe("failed");
			expect(failed.errorDetails).toContain("Connection timeout");
			expect(failed.successRate).toBe(0);
		});

		it("should detect and handle stuck operations", async () => {
			const syncService = strapi.service("api::sync-operation.sync-operation");

			// Create a stuck operation (started more than timeout ago)
			const stuckStartTime = new Date(Date.now() - 2 * 60 * 60 * 1000); // 2 hours ago
			await strapi.entityService.create("api::sync-operation.sync-operation", {
				data: {
					operationType: "stuck_sync",
					operationId: "stuck_operation_789",
					startTime: stuckStartTime,
					status: "running",
					recordsProcessed: 0,
					errorCount: 0,
					successRate: 0,
					publishedAt: new Date(),
				},
			});

			// Check for stuck operations with 1 hour timeout
			const stuckOps = await syncService.checkForStuckOperations(60);

			expect(stuckOps).toHaveLength(1);
			expect(stuckOps[0].operationId).toBe("stuck_operation_789");

			// Verify it was marked as failed
			const updated = await syncService.getSyncOperationStatus(
				"stuck_operation_789",
			);
			expect(updated.status).toBe("failed");
			expect(updated.errorDetails[0]).toContain("timed out");
		});

		it("should calculate sync statistics correctly", async () => {
			const syncService = strapi.service("api::sync-operation.sync-operation");

			// Create test sync operations
			await syncService.startSyncOperation("test_sync", "sync_1");
			await syncService.completeSyncOperation("sync_1", 100, 0);

			await syncService.startSyncOperation("test_sync", "sync_2");
			await syncService.completeSyncOperation("sync_2", 200, 10);

			await syncService.startSyncOperation("test_sync", "sync_3");
			await syncService.failSyncOperation("sync_3", ["Test failure"]);

			const stats = await syncService.getSyncStatistics(1);

			expect(stats.totalOperations).toBe(3);
			expect(stats.completedOperations).toBe(2);
			expect(stats.failedOperations).toBe(1);
			expect(stats.totalRecordsProcessed).toBe(300);
			expect(stats.totalErrors).toBe(10);
			expect(stats.successRate).toBe(66.67); // 2 out of 3 operations succeeded
		});
	});

	describe("Performance Summary and Analytics", () => {
		it("should generate comprehensive performance summary", async () => {
			// Create test data
			await strapi.entityService.create("api::system-health.system-health", {
				data: {
					timestamp: new Date(),
					uptime: 3600,
					memoryUsed: **********,
					memoryTotal: **********,
					memoryPercentage: 12.5,
					cpuUsage: 25,
					responseTime: 150,
					activeConnections: 10,
					errorRate: 2,
					status: "healthy",
					publishedAt: new Date(),
				},
			});

			const summary = await performanceMonitor.getPerformanceSummary(1);

			expect(summary).toHaveProperty("systemHealth");
			expect(summary).toHaveProperty("apiPerformance");
			expect(summary).toHaveProperty("syncOperations");
			expect(summary.systemHealth.currentStatus).toBe("healthy");
		});

		it("should handle empty metrics gracefully", async () => {
			const summary = await performanceMonitor.getPerformanceSummary(1);

			expect(summary.systemHealth).toBeNull();
			expect(summary.apiPerformance).toBeNull();
			expect(summary.syncOperations).toBeNull();
		});
	});

	describe("Alert System Integration", () => {
		it("should create alerts for performance issues", async () => {
			// Simulate performance issue
			await (performanceMonitor as any).createPerformanceAlert({
				type: "response_time_critical",
				severity: "critical",
				message: "Response time exceeded critical threshold",
				currentValue: 5000,
				threshold: 1000,
			});

			const alerts = await strapi.entityService.findMany(
				"api::performance-alert.performance-alert",
			);
			expect(alerts).toHaveLength(1);
			expect(alerts[0].type).toBe("response_time_critical");
			expect(alerts[0].severity).toBe("critical");
			expect(alerts[0].status).toBe("active");
		});

		it("should provide recommended actions for alerts", async () => {
			const actions = (performanceMonitor as any).getRecommendedActions(
				"response_time_critical",
			);

			expect(actions).toBeInstanceOf(Array);
			expect(actions.length).toBeGreaterThan(0);
			expect(actions[0]).toContain("database");
		});
	});

	describe("Monitoring Configuration", () => {
		it("should allow updating monitoring thresholds", () => {
			const newThresholds = {
				responseTimeWarning: 2000,
				responseTimeCritical: 8000,
			};

			performanceMonitor.updateThresholds(newThresholds);

			const status = performanceMonitor.getStatus();
			expect(status.thresholds.responseTimeWarning).toBe(2000);
			expect(status.thresholds.responseTimeCritical).toBe(8000);
		});

		it("should report monitoring status correctly", () => {
			const status = performanceMonitor.getStatus();

			expect(status).toHaveProperty("isMonitoring");
			expect(status).toHaveProperty("thresholds");
			expect(status).toHaveProperty("bufferSizes");
		});
	});

	describe("Cleanup Operations", () => {
		it("should clean up old metrics data", async () => {
			// Create old metrics
			const oldDate = new Date(Date.now() - 35 * 24 * 60 * 60 * 1000); // 35 days ago

			await strapi.entityService.create("api::system-health.system-health", {
				data: {
					timestamp: oldDate,
					uptime: 3600,
					memoryUsed: **********,
					memoryTotal: **********,
					memoryPercentage: 12.5,
					cpuUsage: 25,
					responseTime: 150,
					activeConnections: 10,
					errorRate: 2,
					status: "healthy",
					publishedAt: oldDate,
				},
			});

			await strapi.entityService.create(
				"api::api-performance.api-performance",
				{
					data: {
						endpoint: "/api/old",
						method: "GET",
						responseTime: 100,
						statusCode: 200,
						timestamp: oldDate,
						requestSize: 1024,
						responseSize: 2048,
						publishedAt: oldDate,
					},
				},
			);

			// Run cleanup
			await (performanceMonitor as any).cleanupOldMetrics();

			// Verify old data was removed
			const healthMetrics = await strapi.entityService.findMany(
				"api::system-health.system-health",
			);
			const apiMetrics = await strapi.entityService.findMany(
				"api::api-performance.api-performance",
			);

			expect(healthMetrics).toHaveLength(0);
			expect(apiMetrics).toHaveLength(0);
		});
	});
});
