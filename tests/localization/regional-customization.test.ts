/**
 * Regional Customization Service Tests
 */

import {
	regionalABTestingService,
	regionalCustomizationService,
	regionalSchedulingService,
} from "../../src/services/localization";

describe("Regional Customization Service", () => {
	beforeEach(() => {
		// Reset any state if needed
	});

	describe("Regional Pricing", () => {
		it("should format US pricing correctly", () => {
			const formatted = regionalCustomizationService.formatRegionalPrice(
				99.99,
				"US",
			);
			expect(formatted).toBe("$99.99");
		});

		it("should format EU pricing correctly", () => {
			const formatted = regionalCustomizationService.formatRegionalPrice(
				99.99,
				"EU",
			);
			expect(formatted).toBe("99,99 €");
		});

		it("should format Japanese pricing correctly", () => {
			const formatted = regionalCustomizationService.formatRegionalPrice(
				9999,
				"JP",
			);
			expect(formatted).toBe("¥9,999");
		});

		it("should handle invalid region gracefully", () => {
			const formatted = regionalCustomizationService.formatRegionalPrice(
				99.99,
				"INVALID",
			);
			expect(formatted).toBe("99.99");
		});

		it("should format large numbers with thousands separators", () => {
			const formatted = regionalCustomizationService.formatRegionalPrice(
				1234567.89,
				"US",
			);
			expect(formatted).toBe("$1,234,567.89");
		});
	});

	describe("Cultural Adaptations", () => {
		it("should return US cultural adaptation", () => {
			const adaptation =
				regionalCustomizationService.getCulturalAdaptation("US");
			expect(adaptation).toBeDefined();
			expect(adaptation?.region).toBe("US");
			expect(adaptation?.colorScheme.primary).toBe("#007AFF");
			expect(adaptation?.layout.direction).toBe("ltr");
		});

		it("should return Japanese cultural adaptation", () => {
			const adaptation =
				regionalCustomizationService.getCulturalAdaptation("JP");
			expect(adaptation).toBeDefined();
			expect(adaptation?.region).toBe("JP");
			expect(adaptation?.colorScheme.primary).toBe("#BC002D");
			expect(adaptation?.layout.spacing).toBe("compact");
		});

		it("should return null for invalid region", () => {
			const adaptation =
				regionalCustomizationService.getCulturalAdaptation("INVALID");
			expect(adaptation).toBeNull();
		});

		it("should apply cultural adaptations to theme", () => {
			const baseTheme = {
				colors: { primary: "#000000", secondary: "#FFFFFF" },
				typography: { fontFamily: "Arial", fontSize: { medium: "16px" } },
				layout: { direction: "ltr", spacing: "normal" },
			};

			const adaptedTheme =
				regionalCustomizationService.applyCulturalAdaptations(baseTheme, "JP");
			expect(adaptedTheme.colors.primary).toBe("#BC002D");
			expect(adaptedTheme.layout.spacing).toBe("compact");
		});
	});

	describe("Compliance Requirements", () => {
		it("should return EU compliance requirements", () => {
			const compliance =
				regionalCustomizationService.getComplianceRequirements("EU");
			expect(compliance).toBeDefined();
			expect(compliance?.gdprRequired).toBe(true);
			expect(compliance?.cookieConsentRequired).toBe(true);
			expect(compliance?.rightToDeleteEnabled).toBe(true);
		});

		it("should return US compliance requirements", () => {
			const compliance =
				regionalCustomizationService.getComplianceRequirements("US");
			expect(compliance).toBeDefined();
			expect(compliance?.ccpaRequired).toBe(true);
			expect(compliance?.coppaRequired).toBe(true);
			expect(compliance?.gdprRequired).toBe(false);
		});

		it("should return null for unsupported region", () => {
			const compliance =
				regionalCustomizationService.getComplianceRequirements("UNSUPPORTED");
			expect(compliance).toBeNull();
		});
	});

	describe("Timezone Configuration", () => {
		it("should return US timezone config", () => {
			const config = regionalCustomizationService.getTimezoneConfig("US");
			expect(config).toBeDefined();
			expect(config?.timezone).toBe("America/New_York");
			expect(config?.dstObserved).toBe(true);
			expect(config?.businessHours.start).toBe("09:00");
		});

		it("should return Japanese timezone config", () => {
			const config = regionalCustomizationService.getTimezoneConfig("JP");
			expect(config).toBeDefined();
			expect(config?.timezone).toBe("Asia/Tokyo");
			expect(config?.dstObserved).toBe(false);
		});

		it("should check business hours correctly", () => {
			// Mock a weekday during business hours
			const businessDay = new Date("2024-01-15T14:00:00Z"); // Monday 2 PM UTC
			const isBusinessHours = regionalCustomizationService.isBusinessHours(
				"US",
				businessDay,
			);
			// Note: This test might need adjustment based on actual timezone conversion
			expect(typeof isBusinessHours).toBe("boolean");
		});

		it("should get optimal deployment time", () => {
			const now = new Date();
			const optimalTime = regionalCustomizationService.getOptimalDeploymentTime(
				"US",
				now,
			);
			expect(optimalTime).toBeInstanceOf(Date);
			expect(optimalTime.getTime()).toBeGreaterThanOrEqual(now.getTime());
		});
	});

	describe("Regional A/B Testing Configuration", () => {
		it("should return US A/B test config", () => {
			const config = regionalCustomizationService.getRegionalABTestConfig("US");
			expect(config).toBeDefined();
			expect(config?.minSampleSize).toBe(1000);
			expect(config?.confidenceLevel).toBe(0.95);
			expect(config?.culturalVariations.colorTests).toBe(true);
		});

		it("should return Japanese A/B test config with restrictions", () => {
			const config = regionalCustomizationService.getRegionalABTestConfig("JP");
			expect(config).toBeDefined();
			expect(config?.minSampleSize).toBe(500);
			expect(config?.culturalVariations.layoutTests).toBe(false);
			expect(config?.culturalVariations.pricingTests).toBe(false);
		});
	});

	describe("Regional Analytics", () => {
		it("should update regional analytics", () => {
			const analyticsData = {
				conversionRate: 0.15,
				averageRevenuePerUser: 25.5,
				churnRate: 0.05,
			};

			regionalCustomizationService.updateRegionalAnalytics("US", analyticsData);
			const analytics = regionalCustomizationService.getRegionalAnalytics("US");

			expect(analytics).toBeDefined();
			expect(analytics?.conversionRate).toBe(0.15);
			expect(analytics?.averageRevenuePerUser).toBe(25.5);
		});

		it("should return null for region without analytics", () => {
			const analytics =
				regionalCustomizationService.getRegionalAnalytics("UNKNOWN");
			expect(analytics).toBeNull();
		});
	});

	describe("Region Validation", () => {
		it("should validate supported regions", () => {
			expect(regionalCustomizationService.isValidRegion("US")).toBe(true);
			expect(regionalCustomizationService.isValidRegion("EU")).toBe(true);
			expect(regionalCustomizationService.isValidRegion("JP")).toBe(true);
			expect(regionalCustomizationService.isValidRegion("INVALID")).toBe(false);
		});

		it("should return supported regions list", () => {
			const regions = regionalCustomizationService.getSupportedRegions();
			expect(regions).toContain("US");
			expect(regions).toContain("EU");
			expect(regions).toContain("JP");
			expect(regions.length).toBeGreaterThan(0);
		});

		it("should get region from locale", () => {
			const region = regionalCustomizationService.getRegionFromLocale("en");
			expect(region).toBe("US");

			const jpRegion = regionalCustomizationService.getRegionFromLocale("ja");
			expect(jpRegion).toBe("JP");
		});
	});
});

describe("Regional Scheduling Service", () => {
	beforeEach(() => {
		// Reset scheduling state
	});

	describe("Deployment Scheduling", () => {
		it("should schedule deployment successfully", async () => {
			const deployment = {
				contentId: "paywall-123",
				contentType: "paywall" as const,
				regions: ["US"],
				scheduledTime: new Date(Date.now() + 60000), // 1 minute from now
				deploymentStrategy: "optimal" as const,
				notifications: {
					onSuccess: ["<EMAIL>"],
					onFailure: ["<EMAIL>"],
					beforeDeployment: 5,
				},
			};

			const deploymentId =
				await regionalSchedulingService.scheduleDeployment(deployment);
			expect(deploymentId).toBeDefined();
			expect(deploymentId).toMatch(/^deploy_/);
		});

		it("should reject deployment with invalid region", async () => {
			const deployment = {
				contentId: "paywall-123",
				contentType: "paywall" as const,
				regions: ["INVALID_REGION"],
				scheduledTime: new Date(Date.now() + 60000),
				deploymentStrategy: "immediate" as const,
				notifications: {
					onSuccess: [],
					onFailure: [],
					beforeDeployment: 0,
				},
			};

			await expect(
				regionalSchedulingService.scheduleDeployment(deployment),
			).rejects.toThrow("Invalid region: INVALID_REGION");
		});

		it("should get deployment status", async () => {
			const deployment = {
				contentId: "paywall-456",
				contentType: "paywall" as const,
				regions: ["US"],
				scheduledTime: new Date(Date.now() + 60000),
				deploymentStrategy: "immediate" as const,
				notifications: {
					onSuccess: [],
					onFailure: [],
					beforeDeployment: 0,
				},
			};

			const deploymentId =
				await regionalSchedulingService.scheduleDeployment(deployment);
			const status =
				regionalSchedulingService.getDeploymentStatus(deploymentId);

			expect(status).toBeDefined();
			expect(status?.id).toBe(deploymentId);
			expect(status?.status).toBe("pending");
		});

		it("should cancel scheduled deployment", async () => {
			const deployment = {
				contentId: "paywall-789",
				contentType: "paywall" as const,
				regions: ["US"],
				scheduledTime: new Date(Date.now() + 60000),
				deploymentStrategy: "immediate" as const,
				notifications: {
					onSuccess: [],
					onFailure: [],
					beforeDeployment: 0,
				},
			};

			const deploymentId =
				await regionalSchedulingService.scheduleDeployment(deployment);
			const cancelled =
				regionalSchedulingService.cancelDeployment(deploymentId);

			expect(cancelled).toBe(true);

			const status =
				regionalSchedulingService.getDeploymentStatus(deploymentId);
			expect(status?.status).toBe("cancelled");
		});
	});

	describe("Deployment Statistics", () => {
		it("should return deployment statistics for region", () => {
			const stats = regionalSchedulingService.getRegionalDeploymentStats(
				"US",
				30,
			);
			expect(stats).toBeDefined();
			expect(stats.totalDeployments).toBeGreaterThanOrEqual(0);
			expect(stats.successfulDeployments).toBeGreaterThanOrEqual(0);
			expect(stats.failedDeployments).toBeGreaterThanOrEqual(0);
			expect(stats.optimalTimeUsage).toBeGreaterThanOrEqual(0);
			expect(stats.optimalTimeUsage).toBeLessThanOrEqual(1);
		});
	});
});

describe("Regional A/B Testing Service", () => {
	beforeEach(() => {
		// Reset A/B testing state
	});

	describe("Test Creation", () => {
		it("should create regional A/B test successfully", async () => {
			const testConfig = {
				name: "US Color Test",
				description: "Testing color preferences in US market",
				regions: ["US"],
				locales: ["en"],
				startDate: new Date(),
				testType: "color" as const,
				hypothesis: "Blue colors will perform better than red",
				successMetrics: ["conversion_rate", "revenue"],
				minimumDetectableEffect: 0.05,
				culturalConsiderations: {
					avoidedElements: [],
					preferredElements: ["professional-colors"],
					complianceRequirements: ["accessibility"],
				},
				status: "draft" as const,
			};

			const testId =
				await regionalABTestingService.createRegionalTest(testConfig);
			expect(testId).toBeDefined();
			expect(testId).toMatch(/^regional_test_/);
		});

		it("should reject test with invalid region", async () => {
			const testConfig = {
				name: "Invalid Region Test",
				description: "Test with invalid region",
				regions: ["INVALID_REGION"],
				locales: ["en"],
				startDate: new Date(),
				testType: "color" as const,
				hypothesis: "Test hypothesis",
				successMetrics: ["conversion_rate"],
				minimumDetectableEffect: 0.05,
				culturalConsiderations: {
					avoidedElements: [],
					preferredElements: [],
					complianceRequirements: [],
				},
				status: "draft" as const,
			};

			await expect(
				regionalABTestingService.createRegionalTest(testConfig),
			).rejects.toThrow("Invalid region: INVALID_REGION");
		});

		it("should reject test with invalid locale", async () => {
			const testConfig = {
				name: "Invalid Locale Test",
				description: "Test with invalid locale",
				regions: ["US"],
				locales: ["invalid_locale"],
				startDate: new Date(),
				testType: "messaging" as const,
				hypothesis: "Test hypothesis",
				successMetrics: ["conversion_rate"],
				minimumDetectableEffect: 0.05,
				culturalConsiderations: {
					avoidedElements: [],
					preferredElements: [],
					complianceRequirements: [],
				},
				status: "draft" as const,
			};

			await expect(
				regionalABTestingService.createRegionalTest(testConfig),
			).rejects.toThrow("Invalid locale: invalid_locale");
		});
	});

	describe("Test Management", () => {
		it("should start regional test", async () => {
			const testConfig = {
				name: "Messaging Test",
				description: "Testing messaging variations",
				regions: ["US"],
				locales: ["en"],
				startDate: new Date(),
				testType: "messaging" as const,
				hypothesis: "Personalized messaging will increase conversions",
				successMetrics: ["conversion_rate"],
				minimumDetectableEffect: 0.05,
				culturalConsiderations: {
					avoidedElements: [],
					preferredElements: [],
					complianceRequirements: [],
				},
				status: "draft" as const,
			};

			const testId =
				await regionalABTestingService.createRegionalTest(testConfig);
			await regionalABTestingService.startRegionalTest(testId);

			const test = regionalABTestingService.getRegionalTest(testId);
			expect(test?.status).toBe("active");
			expect(test?.variations.every((v) => v.status === "active")).toBe(true);
		});

		it("should update variation performance", async () => {
			const testConfig = {
				name: "Performance Test",
				description: "Testing performance tracking",
				regions: ["US"],
				locales: ["en"],
				startDate: new Date(),
				testType: "color" as const,
				hypothesis: "Performance tracking works correctly",
				successMetrics: ["conversion_rate"],
				minimumDetectableEffect: 0.05,
				culturalConsiderations: {
					avoidedElements: [],
					preferredElements: [],
					complianceRequirements: [],
				},
				status: "draft" as const,
			};

			const testId =
				await regionalABTestingService.createRegionalTest(testConfig);
			const test = regionalABTestingService.getRegionalTest(testId);

			if (test && test.variations.length > 0) {
				const variationId = test.variations[0].id;
				const performance = {
					impressions: 1000,
					conversions: 150,
					conversionRate: 0.15,
					revenue: 2250,
					averageRevenuePerUser: 15,
					culturalInsights: {
						colorPerformance: { "#007AFF": 0.15 },
						messagingPerformance: {},
						layoutPerformance: {},
					},
					statisticalSignificance: 0.95,
					confidenceInterval: { lower: 0.12, upper: 0.18 },
				};

				regionalABTestingService.updateVariationPerformance(
					variationId,
					performance,
				);

				const updatedTest = regionalABTestingService.getRegionalTest(testId);
				const updatedVariation = updatedTest?.variations.find(
					(v) => v.id === variationId,
				);

				expect(updatedVariation?.performance?.conversionRate).toBe(0.15);
				expect(updatedVariation?.performance?.revenue).toBe(2250);
			}
		});
	});

	describe("Test Results", () => {
		it("should complete regional test and generate results", async () => {
			const testConfig = {
				name: "Completion Test",
				description: "Testing test completion",
				regions: ["US"],
				locales: ["en"],
				startDate: new Date(),
				testType: "comprehensive" as const,
				hypothesis: "Test completion works correctly",
				successMetrics: ["conversion_rate", "revenue"],
				minimumDetectableEffect: 0.05,
				culturalConsiderations: {
					avoidedElements: [],
					preferredElements: [],
					complianceRequirements: [],
				},
				status: "draft" as const,
			};

			const testId =
				await regionalABTestingService.createRegionalTest(testConfig);
			await regionalABTestingService.startRegionalTest(testId);

			// Add some performance data
			const test = regionalABTestingService.getRegionalTest(testId);
			if (test && test.variations.length > 0) {
				test.variations.forEach((variation, index) => {
					const performance = {
						impressions: 1000,
						conversions: 100 + index * 20,
						conversionRate: 0.1 + index * 0.02,
						revenue: 1500 + index * 300,
						averageRevenuePerUser: 15 + index * 3,
						culturalInsights: {
							colorPerformance: {},
							messagingPerformance: {},
							layoutPerformance: {},
						},
						statisticalSignificance: 0.9 + index * 0.02,
						confidenceInterval: { lower: 0.08, upper: 0.15 },
					};

					regionalABTestingService.updateVariationPerformance(
						variation.id,
						performance,
					);
				});
			}

			const results =
				await regionalABTestingService.completeRegionalTest(testId);

			expect(results).toBeDefined();
			expect(results.testId).toBe(testId);
			expect(results.winningVariation).toBeDefined();
			expect(results.recommendations).toBeDefined();
			expect(results.recommendations.length).toBeGreaterThan(0);
		});
	});
});
