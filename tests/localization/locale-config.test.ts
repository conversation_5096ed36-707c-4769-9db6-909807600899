/**
 * Locale Configuration Service Tests
 */

import { describe, expect, it } from "@jest/globals";
import { localeConfigService } from "../../src/services/localization/locale-config";

describe("LocaleConfigService", () => {
	describe("getActiveLocales", () => {
		it("should return all active locales", () => {
			const activeLocales = localeConfigService.getActiveLocales();
			expect(activeLocales).toBeDefined();
			expect(activeLocales.length).toBeGreaterThan(0);
			expect(activeLocales.every((locale) => locale.isActive)).toBe(true);
		});
	});

	describe("getLocaleConfig", () => {
		it("should return locale config for valid locale code", () => {
			const config = localeConfigService.getLocaleConfig("en");
			expect(config).toBeDefined();
			expect(config?.code).toBe("en");
			expect(config?.name).toBe("English");
		});

		it("should return null for invalid locale code", () => {
			const config = localeConfigService.getLocaleConfig("invalid");
			expect(config).toBeNull();
		});
	});

	describe("getDefaultLocale", () => {
		it("should return the default locale", () => {
			const defaultLocale = localeConfigService.getDefaultLocale();
			expect(defaultLocale).toBeDefined();
			expect(defaultLocale.isDefault).toBe(true);
			expect(defaultLocale.code).toBe("en");
		});
	});

	describe("getFallbackChain", () => {
		it("should return correct fallback chain for Spanish", () => {
			const chain = localeConfigService.getFallbackChain("es");
			expect(chain).toEqual(["es", "en"]);
		});

		it("should return single item for default locale", () => {
			const chain = localeConfigService.getFallbackChain("en");
			expect(chain).toEqual(["en"]);
		});

		it("should include default locale as final fallback", () => {
			const chain = localeConfigService.getFallbackChain("fr");
			expect(chain[chain.length - 1]).toBe("en");
		});
	});

	describe("formatCurrency", () => {
		it("should format currency for US locale", () => {
			const formatted = localeConfigService.formatCurrency(99.99, "en");
			expect(formatted).toMatch(/\$99\.99/);
		});

		it("should format currency for Euro locale", () => {
			const formatted = localeConfigService.formatCurrency(99.99, "es");
			expect(formatted).toMatch(/99,99\s*€/);
		});

		it("should handle invalid locale gracefully", () => {
			const formatted = localeConfigService.formatCurrency(99.99, "invalid");
			expect(formatted).toBe("99.99");
		});
	});

	describe("formatDate", () => {
		it("should format date for US locale", () => {
			const date = new Date("2024-01-15");
			const formatted = localeConfigService.formatDate(date, "en");
			expect(formatted).toMatch(/1\/15\/2024/);
		});

		it("should format date for European locale", () => {
			const date = new Date("2024-01-15");
			const formatted = localeConfigService.formatDate(date, "es");
			expect(formatted).toMatch(/15\/1\/2024/);
		});
	});

	describe("getLocaleThemeCustomization", () => {
		it("should return theme customization for valid locale", () => {
			const customization =
				localeConfigService.getLocaleThemeCustomization("en");
			expect(customization).toBeDefined();
			expect(customization.locale).toBe("en");
			expect(customization.layoutDirection).toBe("ltr");
		});

		it("should return default customization for invalid locale", () => {
			const customization =
				localeConfigService.getLocaleThemeCustomization("invalid");
			expect(customization).toBeDefined();
			expect(customization.locale).toBe("invalid");
			expect(customization.layoutDirection).toBe("ltr");
		});
	});

	describe("isValidLocale", () => {
		it("should return true for valid active locale", () => {
			expect(localeConfigService.isValidLocale("en")).toBe(true);
			expect(localeConfigService.isValidLocale("es")).toBe(true);
		});

		it("should return false for invalid locale", () => {
			expect(localeConfigService.isValidLocale("invalid")).toBe(false);
		});
	});

	describe("getSupportedLocaleCodes", () => {
		it("should return array of supported locale codes", () => {
			const codes = localeConfigService.getSupportedLocaleCodes();
			expect(Array.isArray(codes)).toBe(true);
			expect(codes).toContain("en");
			expect(codes).toContain("es");
			expect(codes.length).toBeGreaterThan(0);
		});
	});
});
