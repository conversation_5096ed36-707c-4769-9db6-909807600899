/**
 * Translation Status Service Tests
 */

import { beforeEach, describe, expect, it, jest } from "@jest/globals";

// Mock Strapi
const mockStrapi = {
	entityService: {
		findOne: jest.fn(),
		findMany: jest.fn(),
		create: jest.fn(),
		update: jest.fn(),
	},
	getModel: jest.fn(),
	log: {
		info: jest.fn(),
		error: jest.fn(),
	},
};

global.strapi = mockStrapi as any;

import { translationStatusService } from "../../src/services/localization/translation-status";

describe("TranslationStatusService", () => {
	beforeEach(() => {
		jest.clearAllMocks();
	});

	describe("getTranslationStatus", () => {
		it("should return translation status for existing entity", async () => {
			const mockEntity = {
				id: 1,
				title: "Test Title",
				description: "Test Description",
				updatedAt: new Date(),
			};

			const mockSchema = {
				attributes: {
					title: {
						pluginOptions: {
							i18n: { localized: true },
						},
					},
					description: {
						pluginOptions: {
							i18n: { localized: true },
						},
					},
					status: {
						pluginOptions: {
							i18n: { localized: false },
						},
					},
				},
			};

			mockStrapi.entityService.findOne.mockResolvedValue(mockEntity);
			mockStrapi.getModel.mockReturnValue(mockSchema);

			const status = await translationStatusService.getTranslationStatus(
				"api::paywall.paywall",
				1,
				"en",
			);

			expect(status).toBeDefined();
			expect(status.locale).toBe("en");
			expect(status.contentType).toBe("api::paywall.paywall");
			expect(status.entityId).toBe(1);
			expect(status.totalFields).toBe(2);
			expect(status.translatedFields).toBe(2);
			expect(status.completionPercentage).toBe(100);
			expect(status.status).toBe("completed");
		});

		it("should return empty status for non-existing entity", async () => {
			mockStrapi.entityService.findOne.mockResolvedValue(null);

			const status = await translationStatusService.getTranslationStatus(
				"api::paywall.paywall",
				999,
				"en",
			);

			expect(status).toBeDefined();
			expect(status.locale).toBe("en");
			expect(status.entityId).toBe(999);
			expect(status.totalFields).toBe(0);
			expect(status.translatedFields).toBe(0);
			expect(status.completionPercentage).toBe(0);
			expect(status.status).toBe("not_started");
		});

		it("should handle partial translations", async () => {
			const mockEntity = {
				id: 1,
				title: "Test Title",
				description: "", // Empty field
				updatedAt: new Date(),
			};

			const mockSchema = {
				attributes: {
					title: {
						pluginOptions: {
							i18n: { localized: true },
						},
					},
					description: {
						pluginOptions: {
							i18n: { localized: true },
						},
					},
				},
			};

			mockStrapi.entityService.findOne.mockResolvedValue(mockEntity);
			mockStrapi.getModel.mockReturnValue(mockSchema);

			const status = await translationStatusService.getTranslationStatus(
				"api::paywall.paywall",
				1,
				"es",
			);

			expect(status.totalFields).toBe(2);
			expect(status.translatedFields).toBe(1);
			expect(status.completionPercentage).toBe(50);
			expect(status.status).toBe("in_progress");
			expect(status.missingFields).toContain("description");
		});
	});

	describe("getContentTypeTranslationOverview", () => {
		it("should return overview for content type", async () => {
			const mockEntities = [{ id: 1 }, { id: 2 }];

			mockStrapi.entityService.findMany.mockResolvedValue(mockEntities);

			// Mock getTranslationStatus calls
			const originalGetTranslationStatus =
				translationStatusService.getTranslationStatus;
			translationStatusService.getTranslationStatus = jest
				.fn()
				.mockResolvedValueOnce({
					completionPercentage: 100,
					locale: "en",
				})
				.mockResolvedValueOnce({
					completionPercentage: 50,
					locale: "es",
				})
				.mockResolvedValueOnce({
					completionPercentage: 100,
					locale: "en",
				})
				.mockResolvedValueOnce({
					completionPercentage: 75,
					locale: "es",
				});

			const overview =
				await translationStatusService.getContentTypeTranslationOverview(
					"api::paywall.paywall",
				);

			expect(overview).toBeDefined();
			expect(overview.totalEntities).toBe(2);
			expect(overview.localeCompletionRates.en).toBe(100);
			expect(overview.localeCompletionRates.es).toBe(62.5);
			expect(overview.overallCompletionRate).toBe(81.25);

			// Restore original method
			translationStatusService.getTranslationStatus =
				originalGetTranslationStatus;
		});
	});

	describe("startBulkTranslation", () => {
		it("should start bulk translation operation", async () => {
			const operation = await translationStatusService.startBulkTranslation(
				"api::paywall.paywall",
				"en",
				["es", "fr"],
				[1, 2],
			);

			expect(operation).toBeDefined();
			expect(operation.contentType).toBe("api::paywall.paywall");
			expect(operation.sourceLocale).toBe("en");
			expect(operation.targetLocales).toEqual(["es", "fr"]);
			expect(operation.entityIds).toEqual([1, 2]);
			expect(operation.status).toBe("pending");
			expect(operation.progress).toBe(0);
			expect(operation.errors).toEqual([]);
		});
	});
});
