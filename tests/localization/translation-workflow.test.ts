/**
 * Translation Workflow System Tests
 * Tests for translation assignment, quality assurance, and approval workflows
 */

import {
	translationApprovalWorkflow,
	translationAssignmentService,
	translationQualityService,
} from "../../src/services/localization";

// Mock Strapi
const mockStrapi = {
	entityService: {
		findOne: jest.fn(),
		findMany: jest.fn(),
		findPage: jest.fn(),
		create: jest.fn(),
		update: jest.fn(),
		delete: jest.fn(),
	},
	getModel: jest.fn(),
	log: {
		info: jest.fn(),
		error: jest.fn(),
		warn: jest.fn(),
	},
};

global.strapi = mockStrapi as any;

describe("Translation Assignment Service", () => {
	beforeEach(() => {
		jest.clearAllMocks();
	});

	describe("createAssignment", () => {
		it("should create a translation assignment successfully", async () => {
			const mockSourceEntity = {
				id: 1,
				title: "Test Paywall",
				subtitle: "Test Subtitle",
				description_text: "Test Description",
			};

			const mockSchema = {
				attributes: {
					title: { pluginOptions: { i18n: { localized: true } } },
					subtitle: { pluginOptions: { i18n: { localized: true } } },
					description_text: { pluginOptions: { i18n: { localized: true } } },
				},
			};

			const mockAssignment = {
				id: 1,
				content_type: "api::paywall.paywall",
				entity_id: 1,
				source_locale: "en",
				target_locale: "es",
				status: "pending",
				priority: "medium",
				estimated_words: 15,
				progress_percentage: 0,
				fields_to_translate: ["title", "subtitle", "description_text"],
				completed_fields: [],
				createdAt: new Date(),
				updatedAt: new Date(),
			};

			mockStrapi.entityService.findOne.mockResolvedValue(mockSourceEntity);
			mockStrapi.getModel.mockReturnValue(mockSchema);
			mockStrapi.entityService.create.mockResolvedValue(mockAssignment);

			const result = await translationAssignmentService.createAssignment(
				"api::paywall.paywall",
				1,
				"en",
				"es",
				{ priority: "medium" },
			);

			expect(result).toBeDefined();
			expect(result.contentType).toBe("api::paywall.paywall");
			expect(result.sourceLocale).toBe("en");
			expect(result.targetLocale).toBe("es");
			expect(result.status).toBe("pending");
			expect(result.fieldsToTranslate).toEqual([
				"title",
				"subtitle",
				"description_text",
			]);
		});

		it("should throw error when source entity not found", async () => {
			mockStrapi.entityService.findOne.mockResolvedValue(null);

			await expect(
				translationAssignmentService.createAssignment(
					"api::paywall.paywall",
					999,
					"en",
					"es",
				),
			).rejects.toThrow("Source entity not found: 999 in locale en");
		});
	});

	describe("assignTranslator", () => {
		it("should assign translator to assignment successfully", async () => {
			const mockAssignment = {
				id: 1,
				contentType: "api::paywall.paywall",
				entityId: 1,
				sourceLocale: "en",
				targetLocale: "es",
				status: "pending",
				priority: "medium",
				assignedTranslator: null,
				fieldsToTranslate: ["title"],
			};

			const mockUpdatedAssignment = {
				...mockAssignment,
				assigned_translator: 123,
				status: "assigned",
			};

			jest
				.spyOn(translationAssignmentService, "getAssignment")
				.mockResolvedValue(mockAssignment as any);
			jest
				.spyOn(
					translationAssignmentService as any,
					"canTranslatorHandleAssignment",
				)
				.mockResolvedValue(true);
			jest
				.spyOn(translationAssignmentService as any, "notifyTranslator")
				.mockResolvedValue(undefined);
			mockStrapi.entityService.update.mockResolvedValue(mockUpdatedAssignment);

			const result = await translationAssignmentService.assignTranslator(
				1,
				123,
			);

			expect(result.assignedTranslator).toBe(123);
			expect(result.status).toBe("assigned");
			expect(mockStrapi.entityService.update).toHaveBeenCalledWith(
				"api::translation-assignment.translation-assignment",
				1,
				{
					data: {
						assigned_translator: 123,
						status: "assigned",
					},
				},
			);
		});

		it("should throw error when translator cannot handle assignment", async () => {
			const mockAssignment = {
				id: 1,
				contentType: "api::paywall.paywall",
				sourceLocale: "en",
				targetLocale: "es",
			};

			jest
				.spyOn(translationAssignmentService, "getAssignment")
				.mockResolvedValue(mockAssignment as any);
			jest
				.spyOn(
					translationAssignmentService as any,
					"canTranslatorHandleAssignment",
				)
				.mockResolvedValue(false);

			await expect(
				translationAssignmentService.assignTranslator(1, 123),
			).rejects.toThrow("Translator 123 cannot handle this assignment");
		});
	});

	describe("updateProgress", () => {
		it("should update translation progress successfully", async () => {
			const mockAssignment = {
				id: 1,
				assignedTranslator: 123,
				fieldsToTranslate: ["title", "subtitle", "description_text"],
				progressPercentage: 0,
			};

			const mockUpdatedAssignment = {
				...mockAssignment,
				completed_fields: ["title", "subtitle"],
				progress_percentage: 67,
			};

			jest
				.spyOn(translationAssignmentService, "getAssignment")
				.mockResolvedValue(mockAssignment as any);
			mockStrapi.entityService.update.mockResolvedValue(mockUpdatedAssignment);

			const result = await translationAssignmentService.updateProgress(1, 123, {
				completedFields: ["title", "subtitle"],
			});

			expect(result.progressPercentage).toBe(67);
			expect(result.completedFields).toEqual(["title", "subtitle"]);
		});

		it("should mark assignment as completed when progress reaches 100%", async () => {
			const mockAssignment = {
				id: 1,
				assignedTranslator: 123,
				fieldsToTranslate: ["title"],
				progressPercentage: 0,
			};

			const mockUpdatedAssignment = {
				...mockAssignment,
				progress_percentage: 100,
				status: "completed",
				completed_at: new Date(),
			};

			jest
				.spyOn(translationAssignmentService, "getAssignment")
				.mockResolvedValue(mockAssignment as any);
			jest
				.spyOn(translationAssignmentService as any, "triggerReviewWorkflow")
				.mockResolvedValue(undefined);
			mockStrapi.entityService.update.mockResolvedValue(mockUpdatedAssignment);

			const result = await translationAssignmentService.updateProgress(1, 123, {
				progressPercentage: 100,
			});

			expect(result.status).toBe("completed");
			expect(result.completedAt).toBeDefined();
		});
	});
});

describe("Translation Quality Service", () => {
	beforeEach(() => {
		jest.clearAllMocks();
	});

	describe("runQualityAssurance", () => {
		it("should run quality checks and generate report", async () => {
			const mockAssignment = {
				id: 1,
				content_type: "api::paywall.paywall",
				entity_id: 1,
				source_locale: "en",
				target_locale: "es",
				fields_to_translate: ["title", "description_text"],
			};

			const mockSourceEntity = {
				title: "Test Title",
				description_text: "Test description with {{placeholder}}",
			};

			const mockTargetEntity = {
				title: "Título de Prueba",
				description_text: "Descripción de prueba con {{placeholder}}",
			};

			mockStrapi.entityService.findOne
				.mockResolvedValueOnce(mockAssignment)
				.mockResolvedValueOnce(mockSourceEntity)
				.mockResolvedValueOnce(mockTargetEntity);

			mockStrapi.entityService.findMany.mockResolvedValue([]);
			mockStrapi.entityService.create.mockResolvedValue({});

			const report = await translationQualityService.runQualityAssurance(1);

			expect(report).toBeDefined();
			expect(report.assignmentId).toBe(1);
			expect(report.overallScore).toBeGreaterThan(0);
			expect(report.issues).toBeDefined();
			expect(report.passedChecks).toBeDefined();
			expect(report.recommendations).toBeDefined();
		});

		it("should detect missing translations", async () => {
			const mockAssignment = {
				id: 1,
				content_type: "api::paywall.paywall",
				entity_id: 1,
				source_locale: "en",
				target_locale: "es",
				fields_to_translate: ["title", "description_text"],
			};

			const mockSourceEntity = {
				title: "Test Title",
				description_text: "Test description",
			};

			const mockTargetEntity = {
				title: "Título de Prueba",
				description_text: "", // Missing translation
			};

			mockStrapi.entityService.findOne
				.mockResolvedValueOnce(mockAssignment)
				.mockResolvedValueOnce(mockSourceEntity)
				.mockResolvedValueOnce(mockTargetEntity);

			mockStrapi.entityService.findMany.mockResolvedValue([]);
			mockStrapi.entityService.create.mockResolvedValue({});

			const report = await translationQualityService.runQualityAssurance(1);

			const completenessIssues = report.issues.filter(
				(issue) => issue.checkId === "completeness",
			);
			expect(completenessIssues.length).toBeGreaterThan(0);
			expect(completenessIssues[0].field).toBe("description_text");
			expect(completenessIssues[0].severity).toBe("error");
		});

		it("should detect placeholder preservation issues", async () => {
			const mockAssignment = {
				id: 1,
				content_type: "api::paywall.paywall",
				entity_id: 1,
				source_locale: "en",
				target_locale: "es",
				fields_to_translate: ["description_text"],
			};

			const mockSourceEntity = {
				description_text: "Welcome {{username}} to our service",
			};

			const mockTargetEntity = {
				description_text: "Bienvenido a nuestro servicio", // Missing placeholder
			};

			mockStrapi.entityService.findOne
				.mockResolvedValueOnce(mockAssignment)
				.mockResolvedValueOnce(mockSourceEntity)
				.mockResolvedValueOnce(mockTargetEntity);

			mockStrapi.entityService.findMany.mockResolvedValue([]);
			mockStrapi.entityService.create.mockResolvedValue({});

			const report = await translationQualityService.runQualityAssurance(1);

			const placeholderIssues = report.issues.filter(
				(issue) => issue.checkId === "placeholder-preservation",
			);
			expect(placeholderIssues.length).toBeGreaterThan(0);
			expect(placeholderIssues[0].message).toContain("{{username}}");
		});
	});

	describe("getQualityChecks", () => {
		it("should return available quality checks", () => {
			const checks = translationQualityService.getQualityChecks();

			expect(checks).toBeDefined();
			expect(checks.length).toBeGreaterThan(0);
			expect(checks[0]).toHaveProperty("id");
			expect(checks[0]).toHaveProperty("name");
			expect(checks[0]).toHaveProperty("type");
			expect(checks[0]).toHaveProperty("severity");
		});
	});
});

describe("Translation Approval Workflow", () => {
	beforeEach(() => {
		jest.clearAllMocks();
	});

	describe("startApprovalWorkflow", () => {
		it("should start approval workflow for paywall assignment", async () => {
			const mockAssignment = {
				id: 1,
				contentType: "api::paywall.paywall",
				entityId: 1,
				sourceLocale: "en",
				targetLocale: "es",
				priority: "medium",
				estimatedWords: 100,
			};

			jest
				.spyOn(translationAssignmentService, "getAssignment")
				.mockResolvedValue(mockAssignment as any);
			jest
				.spyOn(translationQualityService, "runQualityAssurance")
				.mockResolvedValue({
					assignmentId: 1,
					overallScore: 85,
					issues: [],
					passedChecks: ["completeness", "placeholder-preservation"],
					failedChecks: [],
					recommendations: [],
					generatedAt: new Date(),
				});

			const instance =
				await translationApprovalWorkflow.startApprovalWorkflow(1);

			expect(instance).toBeDefined();
			expect(instance?.assignmentId).toBe(1);
			expect(instance?.workflowId).toBe("standard-paywall");
			expect(instance?.status).toBe("pending");
			expect(instance?.steps.length).toBeGreaterThan(0);
		});

		it("should start urgent fast-track workflow for urgent assignments", async () => {
			const mockAssignment = {
				id: 1,
				contentType: "api::paywall.paywall",
				entityId: 1,
				sourceLocale: "en",
				targetLocale: "es",
				priority: "urgent",
				estimatedWords: 50,
			};

			jest
				.spyOn(translationAssignmentService, "getAssignment")
				.mockResolvedValue(mockAssignment as any);
			jest
				.spyOn(translationQualityService, "runQualityAssurance")
				.mockResolvedValue({
					assignmentId: 1,
					overallScore: 80,
					issues: [],
					passedChecks: ["completeness"],
					failedChecks: [],
					recommendations: [],
					generatedAt: new Date(),
				});

			const instance =
				await translationApprovalWorkflow.startApprovalWorkflow(1);

			expect(instance).toBeDefined();
			expect(instance?.workflowId).toBe("urgent-fast-track");
			expect(instance?.steps.length).toBe(2); // Fewer steps for urgent workflow
		});

		it("should return null when no matching workflow found", async () => {
			const mockAssignment = {
				id: 1,
				contentType: "api::unknown.unknown",
				entityId: 1,
				sourceLocale: "en",
				targetLocale: "es",
				priority: "low",
				estimatedWords: 10,
			};

			jest
				.spyOn(translationAssignmentService, "getAssignment")
				.mockResolvedValue(mockAssignment as any);

			const instance =
				await translationApprovalWorkflow.startApprovalWorkflow(1);

			expect(instance).toBeNull();
		});
	});

	describe("approveStep", () => {
		it("should approve workflow step successfully", async () => {
			const mockAssignment = {
				id: 1,
				contentType: "api::paywall.paywall",
				priority: "medium",
			};

			jest
				.spyOn(translationAssignmentService, "getAssignment")
				.mockResolvedValue(mockAssignment as any);
			jest
				.spyOn(translationQualityService, "runQualityAssurance")
				.mockResolvedValue({
					assignmentId: 1,
					overallScore: 85,
					issues: [],
					passedChecks: ["completeness", "placeholder-preservation"],
					failedChecks: [],
					recommendations: [],
					generatedAt: new Date(),
				});

			// Start workflow first
			const instance =
				await translationApprovalWorkflow.startApprovalWorkflow(1);
			expect(instance).toBeDefined();

			// Mock step assignment
			instance?.steps[1].assignedTo = 123; // Assign manual review step
			instance!.currentStep = 1; // Move to manual review step

			const _isComplete = await translationApprovalWorkflow.approveStep(
				instance?.id,
				1,
				123,
				"Translation looks good",
			);

			const updatedInstance = translationApprovalWorkflow.getApprovalInstance(
				instance?.id,
			);
			expect(updatedInstance?.steps[1].status).toBe("approved");
			expect(updatedInstance?.steps[1].comments).toBe("Translation looks good");
			expect(updatedInstance?.currentStep).toBe(2);
		});

		it("should reject step with reason", async () => {
			const mockAssignment = {
				id: 1,
				contentType: "api::paywall.paywall",
				priority: "medium",
			};

			jest
				.spyOn(translationAssignmentService, "getAssignment")
				.mockResolvedValue(mockAssignment as any);
			jest
				.spyOn(translationQualityService, "runQualityAssurance")
				.mockResolvedValue({
					assignmentId: 1,
					overallScore: 85,
					issues: [],
					passedChecks: ["completeness", "placeholder-preservation"],
					failedChecks: [],
					recommendations: [],
					generatedAt: new Date(),
				});

			// Start workflow first
			const instance =
				await translationApprovalWorkflow.startApprovalWorkflow(1);
			expect(instance).toBeDefined();

			// Mock step assignment
			instance?.steps[1].assignedTo = 123;
			instance!.currentStep = 1;

			await translationApprovalWorkflow.rejectStep(
				instance?.id,
				1,
				123,
				"Translation needs improvement",
			);

			const updatedInstance = translationApprovalWorkflow.getApprovalInstance(
				instance?.id,
			);
			expect(updatedInstance?.steps[1].status).toBe("rejected");
			expect(updatedInstance?.steps[1].comments).toBe(
				"Translation needs improvement",
			);
			expect(updatedInstance?.status).toBe("rejected");
		});
	});
});

describe("Integration Tests", () => {
	beforeEach(() => {
		jest.clearAllMocks();
	});

	it("should complete full translation workflow from assignment to approval", async () => {
		// Mock data
		const mockSourceEntity = {
			id: 1,
			title: "Premium Features",
			subtitle: "Unlock advanced capabilities",
			description_text: "Get access to {{feature_count}} premium features",
		};

		const mockSchema = {
			attributes: {
				title: { pluginOptions: { i18n: { localized: true } } },
				subtitle: { pluginOptions: { i18n: { localized: true } } },
				description_text: { pluginOptions: { i18n: { localized: true } } },
			},
		};

		const mockAssignment = {
			id: 1,
			content_type: "api::paywall.paywall",
			entity_id: 1,
			source_locale: "en",
			target_locale: "es",
			status: "pending",
			priority: "medium",
			assigned_translator: 123,
			estimated_words: 20,
			progress_percentage: 0,
			fields_to_translate: ["title", "subtitle", "description_text"],
			completed_fields: [],
			createdAt: new Date(),
			updatedAt: new Date(),
		};

		mockStrapi.entityService.findOne.mockResolvedValue(mockSourceEntity);
		mockStrapi.getModel.mockReturnValue(mockSchema);
		mockStrapi.entityService.create.mockResolvedValue(mockAssignment);
		mockStrapi.entityService.update.mockResolvedValue({
			...mockAssignment,
			status: "approved",
		});
		mockStrapi.entityService.findMany.mockResolvedValue([]);

		// 1. Create assignment
		const assignment = await translationAssignmentService.createAssignment(
			"api::paywall.paywall",
			1,
			"en",
			"es",
			{ priority: "medium" },
		);

		expect(assignment.status).toBe("pending");

		// 2. Assign translator
		jest
			.spyOn(translationAssignmentService, "getAssignment")
			.mockResolvedValue(assignment);
		jest
			.spyOn(
				translationAssignmentService as any,
				"canTranslatorHandleAssignment",
			)
			.mockResolvedValue(true);
		jest
			.spyOn(translationAssignmentService as any, "notifyTranslator")
			.mockResolvedValue(undefined);

		const assignedAssignment =
			await translationAssignmentService.assignTranslator(1, 123);
		expect(assignedAssignment.assignedTranslator).toBe(123);

		// 3. Update progress to completion
		jest
			.spyOn(translationAssignmentService, "getAssignment")
			.mockResolvedValue(assignedAssignment);
		jest
			.spyOn(translationAssignmentService as any, "triggerReviewWorkflow")
			.mockResolvedValue(undefined);

		const completedAssignment =
			await translationAssignmentService.updateProgress(1, 123, {
				progressPercentage: 100,
			});

		expect(completedAssignment.status).toBe("completed");

		// 4. Run quality assurance
		mockStrapi.entityService.findOne
			.mockResolvedValueOnce({ ...mockAssignment, status: "completed" })
			.mockResolvedValueOnce(mockSourceEntity)
			.mockResolvedValueOnce({
				title: "Características Premium",
				subtitle: "Desbloquea capacidades avanzadas",
				description_text:
					"Obtén acceso a {{feature_count}} características premium",
			});

		const qualityReport =
			await translationQualityService.runQualityAssurance(1);
		expect(qualityReport.overallScore).toBeGreaterThan(80);

		// 5. Start approval workflow
		jest
			.spyOn(translationAssignmentService, "getAssignment")
			.mockResolvedValue(completedAssignment);

		const approvalInstance =
			await translationApprovalWorkflow.startApprovalWorkflow(1);
		expect(approvalInstance).toBeDefined();
		expect(approvalInstance?.status).toBe("pending");

		strapi.log.info(
			"Full translation workflow integration test completed successfully",
		);
	});
});

describe("Error Handling", () => {
	beforeEach(() => {
		jest.clearAllMocks();
	});

	it("should handle database errors gracefully", async () => {
		mockStrapi.entityService.findOne.mockRejectedValue(
			new Error("Database connection failed"),
		);

		await expect(
			translationAssignmentService.createAssignment(
				"api::paywall.paywall",
				1,
				"en",
				"es",
			),
		).rejects.toThrow("Database connection failed");
	});

	it("should handle quality check failures", async () => {
		mockStrapi.entityService.findOne.mockRejectedValue(
			new Error("Quality check service unavailable"),
		);

		await expect(
			translationQualityService.runQualityAssurance(1),
		).rejects.toThrow("Quality check service unavailable");
	});

	it("should handle workflow errors", async () => {
		jest
			.spyOn(translationAssignmentService, "getAssignment")
			.mockRejectedValue(new Error("Assignment service error"));

		await expect(
			translationApprovalWorkflow.startApprovalWorkflow(1),
		).rejects.toThrow("Assignment service error");
	});
});
