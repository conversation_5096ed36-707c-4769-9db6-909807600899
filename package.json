{"name": "admin-strapi", "version": "0.1.0", "private": true, "description": "A Strapi application", "scripts": {"build": "strapi build", "console": "strapi console", "deploy": "strapi deploy", "dev": "strapi develop --bundler vite --watch-admin", "dev:debug": "strapi develop --bundler vite --watch-admin --debug", "dev:debug:enhanced": "./scripts/enhanced-dev-debug.sh", "dev:debug:legacy": "./scripts/dev-with-sourcemaps.sh", "dev:webpack": "strapi develop --bundler webpack --watch-admin", "setup:types": "./scripts/setup-product-content-type.sh", "dev:env": "node scripts/dev-environment-manager.js", "dev:setup": "./scripts/enhanced-setup-dev.sh", "dev:db": "./scripts/enhanced-database-manager.sh", "develop": "strapi develop --bundler vite --watch-admin", "seed:example": "node ./scripts/seed.js", "start": "strapi start", "strapi": "strapi", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:e2e:ui": "playwright test --ui", "test:e2e:report": "playwright show-report", "upgrade": "npx @strapi/upgrade latest", "upgrade:dry": "npx @strapi/upgrade latest --dry"}, "dependencies": {"@strapi/plugin-i18n": "^4.25.23", "@strapi/plugin-upload": "4.25.2", "@strapi/plugin-users-permissions": "^4.25.23", "@strapi/strapi": "^4.25.23", "ajv": "^8.17.1", "axios": "^1.8.4", "better-sqlite3": "11.3.0", "fs-extra": "^10.0.0", "ioredis": "^5.7.0", "mime-types": "^2.1.27", "pg": "^8.11.0", "react": "^18.0.0", "react-beautiful-dnd": "^13.1.1", "react-color": "^2.19.3", "react-dom": "^18.0.0", "react-router-dom": "^5.3.4", "sharp": "^0.33.0", "styled-components": "^5.3.11"}, "devDependencies": {"@faker-js/faker": "^9.9.0", "@jest/globals": "^29.7.0", "@playwright/test": "^1.54.2", "@types/ioredis": "^5.0.0", "@types/jest": "^29.5.5", "@types/minimatch": "^6.0.0", "@types/node": "^20", "@types/pg": "^8.10.0", "@types/react": "^18", "@types/react-beautiful-dnd": "^13.1.4", "@types/react-color": "^3.0.6", "@types/react-dom": "^18", "@types/sharp": "^0.32.0", "esbuild": "^0.25.8", "esbuild-loader": "^4.3.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "typescript": "^5", "webpack": "^5.101.0"}, "engines": {"node": ">=18.0.0 <=22.x.x", "npm": ">=6.0.0"}, "strapi": {"uuid": "************************************", "installId": "6dc8362bcb086a1f417b3facdb46b5e880f5a39b03540b6a1016768059398425"}}