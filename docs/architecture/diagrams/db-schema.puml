@startuml db-schema
!define T<PERSON><PERSON>(name,desc) class name as "desc" << (T,#FFAAAA) >>
!define COMPONENT(name,desc) class name as "desc" << (C,#AAFFAA) >>
!define ENUM(name,desc) class name as "desc" << (E,#AAAAFF) >>
!define METRICS(name,desc) class name as "desc" << (M,#FFAA88) >>
!define SYSTEM(name,desc) class name as "desc" << (S,#88AAFF) >>
!define CONTENT(name,desc) class name as "desc" << (CT,#FFAAFF) >>

title Strapi Adapty CMS - Database Schema\nEntity Relationship Diagram

' Core Paywall Management
TABLE(paywalls, "Paywalls\n====\nid: PK\nplacement_id: UK\nname: string\ndescription: text\nstatus: enum\ntitle: string\nsubtitle: string\ndescription_text: text\ncta_text: string\ncta_secondary_text: string\nadapty_sync_status: enum\nadapty_last_sync: datetime\nadapty_remote_config_id: string\n--\nTheme Configuration\nFeatures List\nTestimonials\nProduct Labels")

TABLE(paywall_variations, "Paywall Variations\n====\nid: PK\nname: string\ndescription: text\nvariation_type: enum\ntraffic_percentage: decimal\n--\nContent Overrides:\ntitle_override: string\nsubtitle_override: string\ndescription_override: text\ncta_text_override: string\ncta_secondary_text_override: string\n--\nPerformance Metrics:\nconversion_rate: decimal\nrevenue_per_user: decimal\ntotal_impressions: bigint\ntotal_conversions: bigint\ntotal_revenue: decimal\nunique_users: bigint\nconfidence_interval_lower: decimal\nconfidence_interval_upper: decimal\nstatistical_significance: decimal\nis_winner: boolean\npromoted_at: datetime\n--\nAdapty Integration:\nadapty_variation_id: string\nadapty_remote_config_id: string\nlast_metrics_update: datetime")

TABLE(ab_tests, "A/B Tests\n====\nid: PK\nname: string\ndescription: text\nhypothesis: text\nstatus: enum\ntest_type: enum\nprimary_metric: enum\nsecondary_metrics: json\n--\nScheduling:\nstart_date: datetime\nend_date: datetime\nduration_days: integer\n--\nStatistical Configuration:\ntraffic_allocation: decimal\nminimum_sample_size: integer\nconfidence_level: decimal\nstatistical_power: decimal\nminimum_detectable_effect: decimal\n--\nTargeting:\naudience_targeting: json\ngeographic_targeting: json\ndevice_targeting: json\nuser_segment_filters: json\n--\nResults:\nwinner_selected_at: datetime\nwinner_selection_method: enum\nauto_promote_winner: boolean\ncurrent_results: json\nstatistical_significance: decimal\np_value: decimal\neffect_size: decimal\nconfidence_interval: json\n--\nAdapty Integration:\nadapty_test_id: string\nadapty_sync_status: enum\nadapty_last_sync: datetime")

' Mobile Integration
TABLE(mobile_apps, "Mobile Apps\n====\nid: PK\napp_id: UK\nname: string\nplatform: enum\nbundle_id: string\napi_key: UK\nsecret_key: string\nmin_version: string\nmax_version: string\npermissions: json\nrate_limits: json\nis_active: boolean\ndescription: text\nwebhook_url: string\nwebhook_secret: string\nlast_used: datetime\nusage_stats: json")

' Product Management
TABLE(products, "Products\n====\nid: PK\nadapty_product_id: UK\nvendor_product_id: string\nname: string\ntype: enum\nstore: enum\nprice_amount: decimal\nprice_currency: string\nprice_localized: string\nsubscription_period_unit: enum\nsubscription_period_count: integer\nlast_synced: datetime\nis_active: boolean\nmetadata: json")

' Remote Configuration
TABLE(remote_config_versions, "Remote Config Versions\n====\nid: PK\nversion_id: UK\nversion_number: string\nconfig_data: json\nstatus: enum\ndeployment_notes: text\ncreated_by: string\ndeployed_at: datetime\nplacement_id: string\nlocale: string")

' Analytics & Metrics
METRICS(analytics, "Analytics\n====\nid: PK\nevent_type: enum\npaywall_id: string\nuser_id: string\nsession_id: string\nplacement_id: string\napp_id: string\nplatform: enum\ndevice_type: enum\nregion: string\nlocale: string\ntimestamp: datetime\nmetadata: json\nvalue: decimal\ncount: integer")

METRICS(paywall_metrics, "Paywall Metrics\n====\nid: PK\npaywallId: string\ntimestamp: datetime\nimpressions: integer\nconversions: integer\nconversionRate: decimal\nrevenue: decimal\naverageRevenuePerUser: decimal\nbounceRate: decimal\ntimeToConversion: integer\nuserSegment: string\nregion: string\nlocale: string\ndeviceType: enum\nplatform: enum")

METRICS(subscription_metrics, "Subscription Metrics\n====\nid: PK\ntype: enum\nidentifier: string\ntimestamp: datetime\nrevenue: decimal\nsubscriptions: integer\ntrials: integer\nconversions: integer\nimpressions: integer\nconversionRate: decimal\ntrialConversionRate: decimal\naverageRevenuePerUser: decimal")

METRICS(user_engagement, "User Engagement\n====\nid: PK\npaywallId: string\ntimestamp: datetime\nsessionDuration: integer\npageViews: integer\nclickThroughRate: decimal\nscrollDepth: decimal\ninteractionEvents: json\nheatmapData: json")

' System Monitoring
SYSTEM(system_health, "System Health\n====\nid: PK\ntimestamp: datetime\nuptime: biginteger\nmemoryUsed: biginteger\nmemoryTotal: biginteger\nmemoryPercentage: decimal\ncpuUsage: decimal\nresponseTime: integer\nactiveConnections: integer\nerrorRate: decimal\nstatus: enum")

SYSTEM(performance_alerts, "Performance Alerts\n====\nid: PK\nalertId: UK\ntype: enum\nseverity: enum\nmessage: text\ncurrentValue: decimal\npreviousValue: decimal\nthreshold: decimal\ntimestamp: datetime\naffectedMetrics: json\nrecommendedActions: json\nstatus: enum\nacknowledgedBy: string\nacknowledgedAt: datetime\nresolvedBy: string\nresolvedAt: datetime\nnotes: text")

SYSTEM(api_performance, "API Performance\n====\nid: PK\nendpoint: string\nmethod: enum\nresponseTime: integer\nstatusCode: integer\ntimestamp: datetime\nuserAgent: text\nipAddress: string\nrequestSize: integer\nresponseSize: integer\nerrorMessage: text")

SYSTEM(cache_metrics, "Cache Metrics\n====\nid: PK\nendpoint: string\nmethod: enum\ncache_type: enum\napp_id: string\nresponse_time: integer\ncache_ttl: integer\ncache_hit: boolean\ncache_size: integer\ntimestamp: datetime")

SYSTEM(cache_invalidations, "Cache Invalidations\n====\nid: PK\ncontent_type: string\nevent: enum\ntags_invalidated: integer\ncache_keys: json\nreason: text\ntriggered_by: string\nsuccess: boolean\nerror_message: text\nprocessing_time: integer\ntimestamp: datetime")

SYSTEM(mobile_api_usages, "Mobile API Usage\n====\nid: PK\napp_id: string\nuser_id: string\nplatform: enum\napp_version: string\nendpoint: string\nmethod: enum\nstatus_code: integer\nresponse_time: integer\nrequest_size: integer\nresponse_size: integer\nip_address: string\nuser_agent: string\nerror_message: text\ncache_hit: boolean\ntimestamp: datetime\nrequest_params: json\nsession_id: string")

' Operations & Deployment
SYSTEM(deployment_logs, "Deployment Logs\n====\nid: PK\ntype: enum\naction: enum\nversion_id: string\nprevious_version_id: string\nreason: text\ntimestamp: datetime\nperformed_by: string\nsuccess: boolean\nerror_message: text\nplacement_id: string\nlocale: string\ntest_id: integer\nsync_type: enum\nretry_count: integer\noccurred_at: datetime\nstatus: string\ndata: json")

SYSTEM(sync_operations, "Sync Operations\n====\nid: PK\noperationType: enum\noperationId: UK\nstartTime: datetime\nendTime: datetime\nduration: integer\nstatus: enum\nrecordsProcessed: integer\nerrorCount: integer\nsuccessRate: decimal\nerrorDetails: json")

SYSTEM(sync_status, "Sync Status\n====\nid: PK\n(Additional sync tracking)")

' Content Management
CONTENT(articles, "Articles\n====\nid: PK\ntitle: string\ndescription: text\nslug: uid\ncover: media\nblocks: dynamiczone")

CONTENT(authors, "Authors\n====\nid: PK\nname: string\navatar: media\nemail: string")

CONTENT(categories, "Categories\n====\nid: PK\nname: string\nslug: uid\ndescription: text")

CONTENT(globals, "Global Settings\n====\nid: PK\nsiteName: string\nfavicon: media\nsiteDescription: text\ndefaultSeo: component")

' Localization Management
TABLE(translation_assignments, "Translation Assignments\n====\nid: PK\ncontent_type: string\nentity_id: integer\nsource_locale: string\ntarget_locale: string\nstatus: enum\npriority: enum\ndeadline: datetime\nestimated_words: integer\nactual_words: integer\nprogress_percentage: integer\nfields_to_translate: json\ncompleted_fields: json\nnotes: text\nreviewer_notes: text\nquality_score: integer\nstarted_at: datetime\ncompleted_at: datetime\nreviewed_at: datetime\napproved_at: datetime\ntranslation_memory_matches: json\nexternal_service_data: json")

TABLE(translation_memory, "Translation Memory\n====\nid: PK\n(Translation memory storage)")

TABLE(cohort_analysis, "Cohort Analysis\n====\nid: PK\n(Cohort analysis data)")

TABLE(preview_config, "Preview Config\n====\nid: PK\n(Preview configurations)")

TABLE(about, "About\n====\nid: PK\n(About page content)")

' Shared Components
COMPONENT(theme, "Theme Component\n====\nname: string\nprimary_color: string\nbackground_color: string\ntext_color: string\nbutton_style: enum\ngradient_colors: json\nheader_style: enum\nproduct_display_style: enum\nshow_features: boolean\nshow_testimonials: boolean\ncustom_styles: text\nbackground_image: media\nlogo: media")

COMPONENT(feature, "Feature Component\n====\nicon: string\ntitle: string\ndescription: text\norder: integer\nis_highlighted: boolean\nicon_image: media")

COMPONENT(testimonial, "Testimonial Component\n====\nauthor_name: string\nauthor_title: string\ncontent: text\nrating: integer\nauthor_avatar: media\ncompany: string\norder: integer")

COMPONENT(product_label, "Product Label Component\n====\nproduct_id: string\nbadge_text: string\nbadge_color: string\nsubtitle: string\nhighlight: boolean\nsavings_percentage: integer\nbadge_position: enum")

COMPONENT(seo, "SEO Component\n====\nmetaTitle: string\nmetaDescription: text\nshareImage: media")

' Relationships
ab_tests ||--o{ paywall_variations : "has variations"
ab_tests ||--o| paywall_variations : "winner variation"
paywall_variations }o--|| paywalls : "base paywall"
paywalls ||--o{ remote_config_versions : "versions"

' Content relationships
articles }o--|| authors : "written by"
articles }o--|| categories : "categorized"
categories ||--o{ articles : "contains"
authors ||--o{ articles : "writes"

' Component relationships
paywalls ||--|| theme : "uses theme"
paywalls ||--o{ feature : "has features"
paywalls ||--o{ testimonial : "has testimonials"
paywalls ||--o{ product_label : "has labels"
paywall_variations ||--o| theme : "theme override"
paywall_variations ||--o{ feature : "feature override"
paywall_variations ||--o{ testimonial : "testimonial override"
paywall_variations ||--o{ product_label : "label override"
globals ||--|| seo : "default SEO"

' Translation relationships
translation_assignments }o--|| "admin::user" : "translator"
translation_assignments }o--|| "admin::user" : "reviewer"

' Metrics relationships (conceptual - no direct FK but logical connection)
analytics -.-> paywalls : "tracks paywall events"
paywall_metrics -.-> paywalls : "paywall performance"
user_engagement -.-> paywalls : "engagement metrics"
subscription_metrics -.-> products : "product performance"
mobile_api_usages -.-> mobile_apps : "app usage"
api_performance -.-> mobile_apps : "API performance"
cache_metrics -.-> mobile_apps : "cache performance"
performance_alerts -.-> paywall_metrics : "monitors metrics"

' System monitoring relationships
deployment_logs -.-> remote_config_versions : "deployment tracking"
sync_operations -.-> paywalls : "sync operations"
sync_operations -.-> ab_tests : "A/B test sync"
cache_invalidations -.-> paywalls : "cache invalidation"

' Legend
note top of paywalls : **Core Paywall Domain**\nManages paywall content,\nA/B testing, and variations

note top of analytics : **Analytics & Metrics Domain**\nTracks performance, user behavior,\nand subscription metrics

note top of mobile_apps : **Mobile Integration Domain**\nHandles mobile app registration,\nAPI access, and usage tracking

note top of system_health : **System Operations Domain**\nMonitors health, performance,\nand deployment operations

note top of articles : **Content Management Domain**\nTraditional CMS content\nfor blog and marketing pages

note top of translation_assignments : **Localization Domain**\nManages multi-language content\nand translation workflows

note bottom of theme : **Shared Components**\nReusable components used\nacross different content types

' Styling
skinparam class {
  BackgroundColor<<T>> #FFEEEE
  BackgroundColor<<C>> #EEFFEE
  BackgroundColor<<E>> #EEEEFF
  BackgroundColor<<M>> #FFEEAA
  BackgroundColor<<S>> #AAEEFF
  BackgroundColor<<CT>> #FFEEFF
  BorderColor Black
  ArrowColor Black
}

skinparam note {
  BackgroundColor #FFFACD
  BorderColor #DDD
}

@enduml