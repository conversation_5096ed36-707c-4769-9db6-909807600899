@startuml Analytics Dashboard Query Journey
!theme amiga
title Analytics Dashboard Query - Complex Data Aggregation Flow

actor "Admin User" as Admin
participant "Web Browser" as Browser
participant "Strapi Admin" as StrapiAdmin
participant "Auth Middleware" as Auth
participant "Analytics Controller" as Controller
participant "Analytics Service" as AnalyticsService
participant "Database" as DB
participant "Cache Layer" as Cache
participant "Adapty Client" as Adapty
participant "Background Jobs" as BgJobs

== Request Phase ==
Admin -> Browser: Open Analytics Dashboard
Browser -> StrapiAdmin: GET /admin/analytics/dashboard
StrapiAdmin -> Auth: Validate admin session
note right: **Admin Authentication:**\n- Session validation\n- Permission checks\n- Role verification (analytics_viewer)

Auth -> Controller: Authorized admin request

== Dashboard Data Request ==
StrapiAdmin -> Controller: POST /api/analytics/dashboard
note right: **Dashboard Query:**\n{\n  "dateRange": {\n    "start": "2024-01-01T00:00:00Z",\n    "end": "2024-01-31T23:59:59Z"\n  },\n  "paywallIds": ["paywall_123", "paywall_456"],\n  "regions": ["US", "EU"],\n  "locales": ["en", "fr"],\n  "deviceTypes": ["mobile", "tablet"],\n  "platforms": ["ios", "android"],\n  "userSegments": ["new_users", "returning_users"]\n}

Controller -> Controller: Validate query parameters
note right: **Parameter Validation:**\n- Date range validation\n- Maximum 90-day range\n- Paywall ID format check\n- Filter array limits

== Cache Check ==
Controller -> Cache: Check cached dashboard data
note right: **Cache Key Pattern:**\ndashboard_{hash(filters)}_{date_range}\nTTL: 15 minutes for real-time\nTTL: 1 hour for historical

Cache --> Controller: Cache miss / expired

== Analytics Service Layer ==
Controller -> AnalyticsService: getDashboardData(filters)

== Primary Metrics Queries ==
AnalyticsService -> DB: Query paywall impressions
note right: **Impressions Query:**\nSELECT\n  pi.paywall_id,\n  DATE(pi.timestamp) as date,\n  pi.device_type,\n  pi.locale,\n  COUNT(*) as impressions,\n  COUNT(DISTINCT pi.user_id) as unique_users\nFROM paywall_impressions pi\nWHERE pi.timestamp BETWEEN ? AND ?\n  AND pi.paywall_id IN (?)\n  AND pi.device_type IN (?)\n  AND pi.locale IN (?)\nGROUP BY pi.paywall_id, DATE(pi.timestamp),\n         pi.device_type, pi.locale

DB --> AnalyticsService: Impression metrics

AnalyticsService -> DB: Query user engagement
note right: **Engagement Query:**\nSELECT\n  ue.paywall_id,\n  DATE(ue.timestamp) as date,\n  ue.interaction_type,\n  ue.device_type,\n  COUNT(*) as interaction_count,\n  COUNT(DISTINCT ue.user_id) as unique_users,\n  AVG(CASE WHEN ue.interaction_type = 'view' \n      THEN UNIX_TIMESTAMP(ue.timestamp) END) as avg_view_time\nFROM user_engagement ue\nWHERE ue.timestamp BETWEEN ? AND ?\n  AND ue.paywall_id IN (?)\n  AND ue.device_type IN (?)\nGROUP BY ue.paywall_id, DATE(ue.timestamp),\n         ue.interaction_type, ue.device_type

DB --> AnalyticsService: Engagement metrics

AnalyticsService -> DB: Query conversion data
note right: **Conversion Query:**\nSELECT\n  ue.paywall_id,\n  DATE(ue.timestamp) as date,\n  ue.device_type,\n  COUNT(*) as conversions,\n  COUNT(DISTINCT ue.user_id) as converted_users,\n  SUM(JSON_EXTRACT(ue.additional_data, '$.revenue')) as revenue,\n  AVG(JSON_EXTRACT(ue.additional_data, '$.revenue')) as avg_revenue\nFROM user_engagement ue\nWHERE ue.interaction_type = 'purchase_complete'\n  AND ue.timestamp BETWEEN ? AND ?\n  AND ue.paywall_id IN (?)\n  AND ue.device_type IN (?)\nGROUP BY ue.paywall_id, DATE(ue.timestamp), ue.device_type

DB --> AnalyticsService: Conversion metrics

== A/B Test Performance ==
AnalyticsService -> DB: Query A/B test results
note right: **A/B Test Query:**\nSELECT\n  at.id as test_id,\n  at.name as test_name,\n  pv.id as variation_id,\n  pv.name as variation_name,\n  pv.is_control,\n  pv.total_impressions,\n  pv.total_conversions,\n  pv.conversion_rate,\n  pv.total_revenue,\n  pv.avg_revenue_per_user,\n  at.status,\n  at.confidence_level\nFROM ab_tests at\nJOIN paywall_variations pv ON at.id = pv.ab_test_id\nWHERE at.created_at BETWEEN ? AND ?\n  AND at.status IN ('running', 'completed')\nORDER BY at.created_at DESC

DB --> AnalyticsService: A/B test metrics

== Complex Analytics Calculations ==
AnalyticsService -> AnalyticsService: Calculate derived metrics
note right: **Derived Calculations:**\n- Conversion rates per paywall\n- Revenue per user (RPU)\n- Customer lifetime value (CLV)\n- Funnel conversion rates\n- Time-series trends\n- Statistical significance\n- Cohort analysis\n- Device/platform performance

== User Journey Analysis ==
AnalyticsService -> DB: Query user journeys
note right: **Journey Query:**\nSELECT\n  uj.user_id,\n  uj.paywall_id,\n  uj.first_interaction,\n  uj.last_interaction,\n  uj.total_interactions,\n  uj.interaction_sequence,\n  uj.converted,\n  TIMESTAMPDIFF(MINUTE, uj.first_interaction, \n                uj.last_interaction) as journey_duration\nFROM user_journeys uj\nWHERE uj.first_interaction BETWEEN ? AND ?\n  AND uj.paywall_id IN (?)\nORDER BY uj.first_interaction DESC

DB --> AnalyticsService: User journey data

== External Data Enhancement ==
alt Include Adapty Data
    AnalyticsService -> Adapty: getAnalyticsData(filters)
    note right: **Adapty API Call:**\nGET /v1/analytics/paywalls\n{\n  "placement_ids": ["premium_ios", "premium_android"],\n  "start_date": "2024-01-01",\n  "end_date": "2024-01-31",\n  "metrics": ["impressions", "conversions", "revenue"],\n  "group_by": ["placement_id", "date"]\n}

    Adapty --> AnalyticsService: External analytics data
    
    AnalyticsService -> AnalyticsService: Merge internal + external data
    note right: **Data Reconciliation:**\n- Match placement IDs\n- Align date ranges\n- Resolve discrepancies\n- Flag data quality issues
end

== Response Data Assembly ==
AnalyticsService -> AnalyticsService: Aggregate all metrics
note right: **Dashboard Response:**\n{\n  "overview": {\n    "totalImpressions": 125000,\n    "totalConversions": 3750,\n    "overallConversionRate": 3.0,\n    "totalRevenue": 37500.00,\n    "averageRevenuePerUser": 10.00,\n    "uniqueUsers": 98000\n  },\n  "timeSeriesData": [...],\n  "paywallPerformance": [...],\n  "abTestResults": [...],\n  "userSegmentAnalysis": [...],\n  "deviceBreakdown": {...},\n  "geographicDistribution": {...}\n}

AnalyticsService --> Controller: Comprehensive dashboard data

== Response Caching ==
Controller -> Cache: Store dashboard results
note right: **Cache Strategy:**\n- Store computed results\n- 15-minute TTL for recent data\n- 1-hour TTL for historical data\n- Invalidate on new data arrival

== Response to Client ==
Controller --> StrapiAdmin: Dashboard JSON response
StrapiAdmin -> Browser: Render dashboard
note right: **Dashboard Components:**\n- Overview metrics cards\n- Time-series charts\n- Paywall performance table\n- A/B test results\n- User journey visualization\n- Export options

Browser --> Admin: Interactive dashboard

== Real-time Updates ==
note over BgJobs, AnalyticsService: **Background Processing**

BgJobs -> DB: Continuous metrics aggregation
note right: **Real-time Jobs:**\n- 5-minute metric rollups\n- Hourly trend calculations\n- Daily cohort updates\n- A/B test significance monitoring

BgJobs -> Cache: Update cache preemptively
note right: **Cache Warming:**\n- Pre-calculate common queries\n- Update popular dashboards\n- Refresh A/B test results

== Performance Optimization ==
alt Large Dataset Query
    AnalyticsService -> DB: Use pre-aggregated tables
    note right: **Optimization Strategy:**\n- Query hourly_metrics for speed\n- Use materialized views\n- Implement query result pagination\n- Apply data sampling for large ranges

    AnalyticsService -> AnalyticsService: Apply data sampling
    note right: **Sampling Logic:**\n- For >90 days: sample every hour\n- For >30 days: sample every 15 minutes  \n- For <7 days: use raw data\n- Maintain statistical significance
end

== Error Handling ==
alt Query Timeout
    DB --> AnalyticsService: Query timeout (30s)
    AnalyticsService -> Cache: Fallback to last cached result
    note right: **Fallback Strategy:**\n- Return cached data with warning\n- Trigger background refresh\n- Log performance issue
    
    Cache --> AnalyticsService: Stale cached data
    AnalyticsService -> AnalyticsService: Add data freshness warning
end

alt External API Failure
    Adapty --> AnalyticsService: API timeout/error
    AnalyticsService -> AnalyticsService: Continue with internal data
    note right: **Graceful Degradation:**\n- Show internal metrics only\n- Display data source warning\n- Queue retry for external data
end

@enduml
