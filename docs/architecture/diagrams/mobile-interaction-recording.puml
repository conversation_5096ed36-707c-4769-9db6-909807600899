@startuml Mobile Interaction Recording Journey
!theme amiga
title Mobile Client Records Paywall Interaction - Analytics Data Flow

actor "Mobile App" as Mobile
participant "Rate Limiter" as RateLimit
participant "Auth Middleware" as Auth
participant "Mobile Controller" as Controller
participant "Analytics Service" as AnalyticsService
participant "A/B Test Manager" as ABTest
participant "Database" as DB
participant "Adapty Client" as Adapty
participant "Performance Monitor" as PerfMonitor
participant "Alert System" as AlertSystem

== Request Phase ==
Mobile -> RateLimit: POST /mobile/v1/paywalls/{placementId}/interactions
note right: **Request Body:**\n{\n  "interaction_type": "purchase_complete",\n  "user_id": "user_123",\n  "device_type": "mobile",\n  "ab_test_id": "test_456",\n  "variation_id": "var_789",\n  "additional_data": {\n    "revenue": 9.99,\n    "product_id": "premium_monthly",\n    "currency": "USD"\n  }\n}

RateLimit -> Auth: Validate request
note right: Check rate limits:\n- 120 interactions/minute\n- Per app_id restrictions

Auth -> Controller: Authorized request
note right: Extract JWT claims:\n- app_id, platform\n- user permissions

== Controller Validation ==
Controller -> Controller: Validate interaction data
note right: **Validation Rules:**\n- interaction_type ∈ [view, click, close,\n  purchase_start, purchase_complete]\n- user_id required\n- additional_data format check

== Analytics Recording ==
Controller -> AnalyticsService: recordUserEngagement(data)
note right: **Analytics Data:**\n- paywallId: placementId\n- userId, interactionType\n- deviceType, abTestId\n- variationId, additionalData\n- timestamp: current time

AnalyticsService -> DB: INSERT user_engagement
note right: **Database Write:**\nINSERT INTO user_engagement (\n  paywall_id, user_id, interaction_type,\n  device_type, ab_test_id, variation_id,\n  additional_data, timestamp, session_id,\n  ip_address, user_agent, app_id, platform\n) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), ?, ?, ?, ?, ?)

DB --> AnalyticsService: Insert confirmation

== A/B Test Conversion Recording ==
alt Conversion Event (purchase_complete)
    Controller -> ABTest: recordVariationConversion()
    note right: **Conversion Data:**\n- testId: ab_test_id\n- variationId: variation_id\n- userId, placementId\n- deviceType, revenue

    ABTest -> DB: INSERT ab_test_conversions
    note right: **A/B Test Metrics:**\nINSERT INTO ab_test_conversions (\n  test_id, variation_id, user_id,\n  placement_id, device_type, revenue,\n  conversion_timestamp, session_id\n) VALUES (?, ?, ?, ?, ?, ?, NOW(), ?)

    ABTest -> DB: UPDATE variation_metrics
    note right: **Metrics Update:**\nUPDATE paywall_variations SET\n  total_conversions = total_conversions + 1,\n  total_revenue = total_revenue + ?,\n  conversion_rate = (total_conversions / total_impressions),\n  avg_revenue_per_user = (total_revenue / total_conversions),\n  updated_at = NOW()\nWHERE id = ?

    DB --> ABTest: Update confirmation
    ABTest --> Controller: Conversion recorded

    == Statistical Analysis Trigger ==
    ABTest -> ABTest: checkTestSignificance()
    note right: **Statistical Checks:**\n- Sample size sufficiency\n- Statistical significance\n- Confidence intervals\n- Effect size calculations

    alt Significant Results Found
        ABTest -> AlertSystem: createSignificanceAlert()
        note right: **Alert Data:**\n- Test ready for conclusion\n- Winner recommendation\n- Confidence level achieved

        AlertSystem -> DB: INSERT performance_alerts
        note right: INSERT INTO performance_alerts (\n  alert_id, type, severity, message,\n  test_id, variation_id, significance_level,\n  recommended_actions, status, timestamp\n)
    end
end

== Real-time Metrics Update ==
AnalyticsService -> DB: UPDATE paywall_metrics
note right: **Real-time Aggregation:**\nINSERT INTO paywall_metrics (\n  paywall_id, metric_date, metric_hour,\n  total_interactions, purchase_completions,\n  revenue, unique_users, device_breakdown\n) VALUES (?, CURDATE(), HOUR(NOW()), 1, ?, ?, 1, ?)\nON DUPLICATE KEY UPDATE\n  total_interactions = total_interactions + 1,\n  purchase_completions = purchase_completions + ?,\n  revenue = revenue + ?,\n  unique_users = unique_users + ?

== Performance Monitoring ==
AnalyticsService -> PerfMonitor: recordInteractionMetrics()
note right: **Performance Data:**\n- Processing time\n- Database write latency\n- Queue depth (if async)\n- Error rates

PerfMonitor -> DB: INSERT interaction_performance
note right: INSERT INTO interaction_performance (\n  endpoint, processing_time, db_write_time,\n  queue_depth, timestamp, app_id\n)

== External Synchronization ==
alt Adapty Integration Enabled
    AnalyticsService -> Adapty: syncConversionEvent()
    note right: **Adapty Event:**\n{\n  "user_id": "user_123",\n  "event_type": "purchase",\n  "placement_id": placementId,\n  "variation_id": variation_id,\n  "revenue": 9.99,\n  "currency": "USD",\n  "timestamp": ISO_timestamp\n}

    Adapty --> AnalyticsService: Sync confirmation
    
    alt Sync Failed
        AnalyticsService -> DB: INSERT sync_failures
        note right: **Failure Tracking:**\nINSERT INTO adapty_sync_failures (\n  event_id, event_type, retry_count,\n  error_message, next_retry, payload\n)
    end
end

== Response Generation ==
Controller -> Controller: Build success response
note right: **Response Structure:**\n{\n  "success": true,\n  "message": "Interaction recorded successfully",\n  "interaction_id": "generated_id",\n  "timestamp": "2024-01-15T10:30:00Z"\n}

Controller --> Mobile: 200 OK
note right: **Response Headers:**\nContent-Type: application/json\nX-Request-ID: abc123

== Asynchronous Processing ==
note over AnalyticsService, AlertSystem: **Background Processing**

AnalyticsService -> AnalyticsService: triggerAggregationJob()
note right: **Batch Aggregation:**\n- Hourly metrics rollup\n- Daily summary calculations\n- User journey reconstruction\n- Cohort analysis updates

alt High Error Rate Detected
    PerfMonitor -> AlertSystem: createPerformanceAlert()
    note right: **Error Rate Alert:**\n- Threshold: >5% failures\n- Time window: 5 minutes\n- Affected endpoints\n- Recommended actions

    AlertSystem -> DB: INSERT performance_alerts
end

== Data Quality Checks ==
AnalyticsService -> AnalyticsService: validateDataIntegrity()
note right: **Integrity Checks:**\n- Duplicate detection\n- Data consistency validation\n- Referential integrity\n- Anomaly detection

alt Data Quality Issues
    AnalyticsService -> AlertSystem: createDataQualityAlert()
    AlertSystem -> DB: INSERT data_quality_alerts
end

@enduml
