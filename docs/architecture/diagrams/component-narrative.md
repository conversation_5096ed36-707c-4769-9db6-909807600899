# Component Architecture Analysis

## Overview

This document provides a comprehensive analysis of the Strapi Adapty CMS component architecture, mapping the major components and their interactions. The system is built on Strapi v5.18.1 as the foundation CMS, with extensive custom domains for mobile API delivery, analytics, A/B testing, and Adapty integration.

---

## Strapi Core & Framework Components

### Strapi Framework
- **Role**: Core CMS framework providing the foundation
- **Version**: 5.18.1
- **Key Features**: 
  - Headless CMS architecture
  - RESTful API generation
  - Admin panel interface
  - Plugin ecosystem

### Entity Service
- **Purpose**: Data abstraction layer for CRUD operations
- **Integration**: Direct interface with all content types
- **Features**: Query building, relation population, lifecycle hooks

### Content Manager
- **Function**: Content administration and workflow management
- **Capabilities**: Publishing, drafts, content versioning

### Database Layer
- **Primary**: SQLite with better-sqlite3 v11.3.0
- **Production Support**: PostgreSQL ready
- **Features**: ACID compliance, migration support

---

## Core Plugins

### i18n Plugin
- **Internationalization Support**:
  - **Supported Locales**: English, Spanish, French, German, Japanese, Korean, Chinese
  - **Default Locale**: English (en)
  - **Features**: Content localization, locale-specific routing, region-based content delivery
  - **Integration**: Deep integration with paywall content for global markets

### Upload Plugin (Enhanced)
- **Media Management**:
  - **Size Limit**: 50MB for paywall assets
  - **Breakpoints**: xlarge (1920px), large (1000px), medium (750px), small (500px), xsmall (64px)
  - **CDN Integration**: AWS S3, Cloudinary support
  - **Optimization**: Progressive JPEG, 85% quality, responsive images
  - **Custom Extensions**: Media validation middleware, folder organization

### Users-Permissions Plugin
- **Authentication & Authorization**:
  - **JWT Configuration**: 7-day token expiry
  - **Role Management**: Admin, Editor, Viewer roles
  - **API Security**: Token-based authentication for mobile endpoints

---

## Custom Domain: Mobile API v1

### Architecture
The Mobile API v1 is a specialized REST API layer optimized for React Native applications, providing:

#### Mobile Controllers
- **Paywall Controller**: Handles paywall retrieval and A/B test assignment
- **Analytics Controller**: Processes user interaction events
- **Health Controller**: System status and connectivity checks

#### Key Endpoints
```
GET    /mobile/v1/health                           # Health check
GET    /mobile/v1/paywalls/:placementId           # Single paywall
POST   /mobile/v1/paywalls/batch                  # Batch paywall retrieval
GET    /mobile/v1/paywalls/:id/ab-test            # A/B test variant
POST   /mobile/v1/paywalls/:id/interactions       # Analytics events
GET    /mobile/v1/paywalls/:id/preview            # Preview mode
```

#### Mobile Services
- **Transformation Layer**: Converts Strapi entities to mobile-optimized JSON
- **Device Optimization**: Responsive content based on device type (mobile/tablet)
- **Locale Handling**: Automatic content localization
- **Performance**: Optimized for mobile network conditions

---

## Custom Domain: Analytics Service

### Core Analytics Engine
Comprehensive analytics system built for paywall performance monitoring:

#### Metrics Collection
- **Paywall Metrics**: Impressions, conversions, conversion rates, revenue
- **User Engagement**: Session duration, click-through rates, scroll depth, heatmap data
- **Revenue Analytics**: Total/recurring/one-time revenue, ARPU, CLV, churn rates
- **Real-time Processing**: 5-minute cache expiry for live metrics

#### Dashboard Data Processing
- **Overview Calculations**: Total metrics, conversion rates, top performers
- **Trend Analysis**: Daily/weekly/monthly performance trends
- **Regional Performance**: Geography-based analytics
- **Statistical Analysis**: Confidence intervals, significance testing

#### Export Capabilities
- **Formats**: CSV, JSON, PDF
- **Customization**: Date ranges, metric selection, grouping options
- **Automation**: Scheduled reports, email delivery

---

## Custom Domain: Adapty Sync

### Adapty API Client
Robust integration with Adapty's subscription platform:

#### Connection Management
- **Circuit Breaker Pattern**: Automatic failover when Adapty is unavailable
- **Retry Logic**: Exponential backoff with maximum 30-second delays
- **Token Management**: Automatic refresh with 5-minute threshold
- **Rate Limiting**: Compliance with Adapty's API limits

#### API Coverage
- **Placements**: CRUD operations for paywall placements
- **Products**: App store product synchronization
- **Paywalls**: Paywall configuration management
- **Remote Configs**: Dynamic configuration updates
- **A/B Tests**: Test creation, management, and results
- **Analytics**: Performance data retrieval

#### Error Handling
- **Specific Error Types**: Authentication, validation, rate limiting, service unavailable
- **Contextual Responses**: Detailed error messages with recovery suggestions
- **Logging**: Comprehensive request/response logging with configurable levels

---

## Custom Domain: A/B Testing

### A/B Test Manager
Advanced testing framework for paywall optimization:

#### Test Lifecycle Management
- **Creation**: Hypothesis-driven test setup with variation configuration
- **Validation**: Pre-flight checks for test readiness
- **Execution**: Automated traffic allocation and user assignment
- **Monitoring**: Real-time performance tracking
- **Conclusion**: Statistical analysis and winner selection

#### Statistical Engine
- **Significance Testing**: p-value calculations, confidence intervals
- **Power Analysis**: Sample size recommendations, effect size calculations
- **Bayesian Methods**: Probability distributions for conversion rates
- **Multi-variant Support**: Beyond simple A/B to A/B/C/D testing

#### Winner Selection & Promotion
- **Automated Promotion**: Configurable auto-promotion based on statistical significance
- **Manual Override**: Expert judgment capabilities
- **Rollback Support**: Quick reversion if winner underperforms
- **Adapty Sync**: Seamless promotion to production in Adapty

---

## Custom Domain: Rate Limiting

### Granular Rate Limiting Strategy
Multi-tier rate limiting to ensure API stability:

#### Rate Limit Policies
- **Default Endpoints**: 1000 requests per 15 minutes
- **Paywall Endpoints**: 60 requests per minute (high-frequency access)
- **Analytics Events**: 120 requests per minute (event burst handling)
- **Batch Operations**: 10 requests per minute (resource-intensive)
- **Health Checks**: 30 requests per minute

#### Implementation Features
- **Key Generation**: App-based and IP-based limiting
- **Memory Store**: In-memory storage with automated cleanup
- **Headers**: Standard X-RateLimit-* response headers
- **Violation Tracking**: Analytics integration for abuse monitoring
- **Graceful Degradation**: Continues operation on rate limiter errors

---

## Custom Domain: Caching (Redis)

### Multi-Layer Caching Architecture
Comprehensive caching strategy for optimal performance:

#### Redis Cache Service
- **Configuration**: Configurable host, port, password, database selection
- **Connection Management**: Lazy connection, retry logic, keep-alive settings
- **Key Prefix**: Namespace isolation (strapi:cache:)
- **Monitoring**: Hit/miss statistics, performance metrics

#### Cache Features
- **Compression**: Automatic gzip compression for payloads >1KB
- **TTL Management**: Configurable time-to-live with extension capabilities
- **Tag-based Invalidation**: Logical grouping for bulk invalidation
- **Memory Optimization**: Automatic cleanup of expired entries

#### Cache Warming
- **Paywall Pre-loading**: Automatic cache population for all published paywalls
- **Multi-locale Support**: Pre-warming for all supported languages
- **Device Variants**: Separate caching for mobile and tablet optimizations
- **Scheduled Updates**: Regular cache refresh based on content changes

#### CDN Integration
- **AWS S3**: Direct integration for asset delivery
- **Cloudinary**: Image optimization and transformation
- **Edge Caching**: Global distribution for reduced latency

---

## Content Types & API Layer

### Rich Content Schema
Comprehensive content model supporting complex paywall configurations:

#### Core Content Types
- **Paywall**: Main entity with themes, features, testimonials, product labels
- **AB Test**: Test configuration with variations and statistical parameters
- **Analytics**: Metrics storage for performance data
- **Categories & Authors**: Content organization and attribution
- **Global Settings**: System-wide configuration

#### Shared Components
- **Theme Component**: Background images, colors, typography
- **Feature Component**: Feature lists with icons and descriptions
- **Testimonial Component**: Customer testimonials with avatars
- **Product Label Component**: Pricing and product information
- **Media Component**: Rich media with responsive breakpoints
- **SEO Component**: Meta tags and search optimization

---

## Admin Interface Extensions

### Custom React Components
Tailored admin interface for paywall management:

#### Specialized Components
- **ABTestManager**: Complete A/B test lifecycle management
- **ABTestDashboard**: Visual test performance monitoring
- **PaywallPreview**: Real-time paywall preview with device switching
- **AnalyticsDashboard**: Comprehensive metrics visualization
- **PerformanceMonitoringDashboard**: System health and API performance
- **BulkPaywallOperations**: Batch operations for efficiency

#### User Experience Features
- **Form Validation**: Real-time validation for paywall configurations
- **Color Picker**: Visual theme customization
- **Variation Comparison**: Side-by-side A/B test comparison
- **Test Results Visualization**: Charts and graphs for test performance

---

## Middleware Stack

### Request Processing Pipeline
Layered middleware for security, performance, and monitoring:

#### Middleware Components
1. **Authentication Middleware**: JWT verification and user context
2. **Rate Limiting Middleware**: API abuse prevention
3. **Cache Middleware**: Response caching and cache headers
4. **Analytics Middleware**: Request/response event tracking
5. **Performance Monitoring**: Response time and error tracking
6. **Media Validation**: Upload security and format validation

#### Integration Points
- **Strapi Core**: Seamless integration with Strapi's middleware stack
- **Mobile API**: Specialized middleware for mobile endpoints
- **Admin Interface**: Admin-specific middleware for enhanced functionality

---

## External System Integration

### Adapty API
- **Connection**: RESTful API integration with comprehensive error handling
- **Synchronization**: Bi-directional sync for paywalls, tests, and analytics
- **Reliability**: Circuit breaker and retry patterns for high availability

### Redis Server
- **Deployment**: External Redis instance for production scalability
- **Configuration**: Environment-based configuration for different deployment stages
- **Monitoring**: Connection health and performance monitoring

### CDN Services
- **AWS S3**: Primary media storage with global distribution
- **Cloudinary**: Advanced image processing and optimization
- **Multi-provider**: Flexible provider switching based on requirements

---

## Architecture Benefits

### Scalability
- **Horizontal Scaling**: Stateless services enable easy horizontal scaling
- **Caching Strategy**: Multi-layer caching reduces database load
- **CDN Integration**: Global content delivery for optimal performance

### Reliability
- **Circuit Breaker**: Prevents cascade failures in external integrations
- **Graceful Degradation**: System continues operating during partial failures
- **Comprehensive Monitoring**: Real-time health and performance monitoring

### Maintainability
- **Modular Design**: Clear separation of concerns between components
- **TypeScript**: Strong typing for better code quality and IDE support
- **Comprehensive Testing**: Unit, integration, and E2E test coverage

### Performance
- **Optimized APIs**: Mobile-specific optimizations for reduced payload
- **Intelligent Caching**: Cache warming and invalidation strategies
- **Rate Limiting**: Prevents API abuse while ensuring legitimate access

---

This architecture provides a robust, scalable foundation for managing paywall content and A/B testing while maintaining high performance and reliability standards required for mobile applications.