@startuml Strapi Adapty CMS Components
!define RECTANGLE class
!theme blueprint

title Strapi Adapty CMS - Component Architecture Diagram

package "Strapi Core & Framework" {
  [Strapi Framework] as strapi_core
  [Entity Service] as entity_service
  [Content Manager] as content_manager
  [Database Layer] as database
  
  strapi_core --> entity_service
  strapi_core --> content_manager
  entity_service --> database
}

package "Core Plugins" {
  [i18n Plugin] as i18n
  note right of i18n
    • Multi-language support
    • Locales: en, es, fr, de, ja, ko, zh
    • Content localization
  end note
  
  [Upload Plugin] as upload
  note right of upload
    • Media management
    • CDN integration (AWS S3, Cloudinary)
    • Image optimization & breakpoints
    • 50MB size limit
  end note
  
  [Users-Permissions] as users_permissions
  note right of users_permissions
    • JWT authentication
    • Role-based access control
    • 7-day token expiry
  end note
}

package "Mobile API v1" {
  [Mobile Controllers] as mobile_controllers
  [Mobile Services] as mobile_services
  [Mobile Routes] as mobile_routes
  
  mobile_routes --> mobile_controllers
  mobile_controllers --> mobile_services
  
  note top of mobile_controllers
    RESTful endpoints optimized for mobile:
    • GET /mobile/v1/paywalls/:placementId
    • POST /mobile/v1/paywalls/batch
    • GET /mobile/v1/paywalls/:id/ab-test
    • POST /mobile/v1/paywalls/:id/interactions
  end note
}

package "Analytics Service" {
  [Analytics Service] as analytics_service
  [Paywall Metrics] as paywall_metrics
  [User Engagement] as user_engagement
  [Revenue Metrics] as revenue_metrics
  [Dashboard Data] as dashboard_data
  
  analytics_service --> paywall_metrics
  analytics_service --> user_engagement
  analytics_service --> revenue_metrics
  analytics_service --> dashboard_data
  
  note top of analytics_service
    Comprehensive analytics system:
    • Real-time metrics collection
    • Conversion rate tracking
    • Revenue analytics
    • User engagement heatmaps
    • Export to CSV/JSON/PDF
  end note
}

package "Adapty Sync" {
  [Adapty Client] as adapty_client
  [Remote Config] as remote_config
  [Strapi Integration] as strapi_integration
  [AB Test Integration] as ab_test_integration
  
  adapty_client --> remote_config
  adapty_client --> strapi_integration
  adapty_client --> ab_test_integration
  
  note top of adapty_client
    Full Adapty API integration:
    • Circuit breaker pattern
    • Exponential backoff retry
    • Token refresh management
    • Rate limiting compliance
    • Error transformation
  end note
}

package "A/B Testing" {
  [AB Test Manager] as ab_test_manager
  [Statistical Engine] as statistical_engine
  [Winner Selection] as winner_selection
  [Test Validation] as test_validation
  
  ab_test_manager --> statistical_engine
  ab_test_manager --> winner_selection
  ab_test_manager --> test_validation
  
  note top of ab_test_manager
    Advanced A/B testing capabilities:
    • Statistical significance calculation
    • Automated winner promotion
    • Multi-variant testing
    • Confidence intervals & p-values
    • Test lifecycle management
  end note
}

package "Rate Limiting" {
  [Rate Limiter] as rate_limiter
  [Endpoint Policies] as endpoint_policies
  [Violation Tracking] as violation_tracking
  
  rate_limiter --> endpoint_policies
  rate_limiter --> violation_tracking
  
  note top of rate_limiter
    Granular rate limiting:
    • 1000 req/15min (default)
    • 60 req/min (paywall endpoints)
    • 120 req/min (analytics)
    • 10 req/min (batch operations)
    • IP and app-based limiting
  end note
}

package "Caching (Redis)" {
  [Redis Cache Service] as redis_cache
  [Cache Invalidation] as cache_invalidation
  [Cache Warming] as cache_warming
  [CDN Service] as cdn_service
  
  redis_cache --> cache_invalidation
  redis_cache --> cache_warming
  redis_cache --> cdn_service
  
  note top of redis_cache
    Multi-layer caching strategy:
    • Redis with compression
    • Tag-based invalidation
    • Cache warming for paywalls
    • CDN integration
    • Hit rate monitoring
  end note
}

package "Content Types & API" {
  [Paywall API] as paywall_api
  [AB Test API] as ab_test_api
  [Analytics API] as analytics_api
  [Content Types] as content_types
  
  paywall_api --> content_types
  ab_test_api --> content_types
  analytics_api --> content_types
  
  note right of content_types
    Rich content schema:
    • Paywall (themes, features, testimonials)
    • AB Test (variations, metrics)
    • Analytics (metrics, user engagement)
    • Product Labels & Categories
    • Localization content
  end note
}

package "Admin Interface Extensions" {
  [React Components] as react_components
  [Custom Admin Views] as admin_views
  [Dashboard Widgets] as dashboard_widgets
  
  react_components --> admin_views
  admin_views --> dashboard_widgets
  
  note right of react_components
    Custom admin components:
    • ABTestManager & Dashboard
    • PaywallPreview & Validation
    • AnalyticsDashboard
    • PerformanceMonitoring
    • BulkOperations
  end note
}

package "Middleware Stack" {
  [Authentication] as auth_middleware
  [Cache Middleware] as cache_middleware
  [Analytics Middleware] as analytics_middleware
  [Performance Monitoring] as perf_monitoring
  [Media Validation] as media_validation
  
  note right of auth_middleware
    Middleware pipeline:
    • JWT authentication
    • Request/response caching
    • Analytics event collection
    • Performance monitoring
    • Media upload validation
  end note
}

' Component Relationships
strapi_core --> i18n
strapi_core --> upload
strapi_core --> users_permissions

mobile_services --> analytics_service
mobile_services --> redis_cache
mobile_services --> ab_test_manager
mobile_services --> adapty_client

analytics_service --> database
ab_test_manager --> adapty_client
redis_cache --> database

paywall_api --> mobile_services
ab_test_api --> ab_test_manager
analytics_api --> analytics_service

rate_limiter --> analytics_service
auth_middleware --> users_permissions
cache_middleware --> redis_cache
analytics_middleware --> analytics_service

admin_views --> paywall_api
admin_views --> ab_test_api
admin_views --> analytics_api

' External Systems
cloud "External Services" {
  [Adapty API] as adapty_external
  [Redis Server] as redis_external
  [CDN (AWS S3/Cloudinary)] as cdn_external
}

adapty_client --> adapty_external
redis_cache --> redis_external
upload --> cdn_external

@enduml