@startuml Mobile Client Paywall Request Journey
!theme amiga
title Mobile Client Requests Paywall - Complete Data Flow

actor "Mobile App" as Mobile
participant "Rate Limiter" as RateLimit
participant "Auth Middleware" as Auth
participant "Cache Middleware" as Cache
participant "Analytics Middleware" as Analytics
participant "Mobile Controller" as Controller
participant "Mobile Service" as Service
participant "Entity Service" as EntityService
participant "Database" as DB
participant "Adapty Client" as Adapty
participant "Analytics Service" as AnalyticsService
participant "A/B Test Manager" as ABTest
participant "CDN/Image Service" as CDN

== Request Phase ==
Mobile -> RateLimit: GET /mobile/v1/paywalls/{placementId}\n?locale=en&device_type=mobile&user_id=123&app_version=1.2.0
note right: Rate limit check\n(60 req/min)

RateLimit -> Auth: Forward request
note right: Validate JWT token\nExtract app credentials

Auth -> Cache: Authorized request
note right: Check cached response\nETag validation

Cache -> Analytics: Cache miss/expired
note right: Record API usage metrics\nPerformance monitoring

Analytics -> Controller: Track request start
note right: Start performance timer\nRecord request metadata

== Controller Layer ==
Controller -> Controller: Parse parameters
note right: Extract:\n- placementId\n- locale (default: en)\n- device_type (default: mobile)\n- app_version, user_id

Controller -> Service: getPaywallForMobile(params)
note right: Delegate to service layer

== Service Layer - Data Retrieval ==
Service -> EntityService: findMany("api::paywall.paywall")
note right: Query filters:\n- placement_id: placementId\n- publishedAt: not null\n- locale: en

EntityService -> DB: SELECT query with joins
note right: **Database Query:**\nSELECT p.*, t.*, f.*, ts.*, pl.*\nFROM paywalls p\nLEFT JOIN themes t ON p.theme_id = t.id\nLEFT JOIN features f ON p.id = f.paywall_id\nLEFT JOIN testimonials ts ON p.id = ts.paywall_id\nLEFT JOIN product_labels pl ON p.id = pl.paywall_id\nWHERE p.placement_id = ? \nAND p.published_at IS NOT NULL\nAND p.locale = ?\nLIMIT 1

DB --> EntityService: Raw paywall data with relations
EntityService --> Service: Structured paywall entity

== Service Layer - Data Transformation ==
Service -> Service: transformForMobile()
note right: **Data Transformations:**\n1. Optimize images for device\n2. Limit features (max 6 for mobile)\n3. Limit testimonials (max 3)\n4. Apply device-specific styling\n5. Generate optimized URLs

Service -> CDN: getOptimizedImageUrl()
note right: Get device-optimized images:\n- Background images\n- Feature icons\n- Testimonial avatars

CDN --> Service: Optimized image URLs

Service --> Controller: Transformed paywall data

== Controller Layer - Analytics Recording ==
Controller -> AnalyticsService: recordPaywallImpression()
note right: **Analytics Data:**\n- paywallId, placementId\n- userId, deviceType, locale\n- appVersion, timestamp

AnalyticsService -> DB: INSERT into analytics tables
note right: **Database Writes:**\nINSERT INTO paywall_impressions\n(paywall_id, placement_id, user_id,\n device_type, locale, app_version,\n timestamp, ip_address, user_agent)

DB --> AnalyticsService: Insert confirmation
AnalyticsService --> Controller: Analytics recorded

== Response Phase ==
Controller -> Controller: Build response
note right: **Response Structure:**\n{\n  "data": transformed_paywall,\n  "meta": {\n    "version": "latest",\n    "locale": "en",\n    "device_type": "mobile",\n    "cached_at": timestamp,\n    "expires_at": timestamp + 5min\n  }\n}

Controller --> Analytics: Response ready
note right: Calculate response time\nRecord performance metrics

Analytics -> DB: INSERT performance_logs
note right: **Performance Metrics:**\nINSERT INTO api_performance\n(endpoint, method, response_time,\n status_code, request_size,\n response_size, timestamp)

Analytics --> Cache: Update cache
note right: Cache response with TTL\nSet ETag header

Cache --> RateLimit: Cached response
RateLimit --> Mobile: 200 OK + JSON response
note right: **Response Headers:**\nContent-Type: application/json\nX-Cache: MISS\nETag: "abc123"\nCache-Control: max-age=300

== Post-Response Analytics (Async) ==
Analytics -> DB: INSERT mobile_api_usage
note right: **Async Analytics:**\nINSERT INTO mobile_api_usage\n(app_id, user_id, platform,\n endpoint, method, status_code,\n response_time, timestamp)

== Error Scenarios ==
note over Controller, DB: **Error Handling:**\n- 404: Paywall not found\n- 429: Rate limit exceeded\n- 500: Database/service errors\n- 503: External service unavailable

@enduml
