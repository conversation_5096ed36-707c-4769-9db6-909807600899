# Source Code Inventory

## Project Overview
- **Project Name**: admin-strapi
- **Version**: 0.1.0
- **Strapi Version**: 4.25.23
- **Total Files**: 214 (excluding node_modules)
- **Language**: TypeScript/JavaScript

## Directory Tree and File Counts per Layer

### Core Layers
- **API Layer**: 106 files (controllers, routes, services, schemas)
- **Services Layer**: 31 files (business logic services)
- **Middlewares Layer**: 2 files (request/response processing)
- **Config Layer**: 13 files (configuration files)
- **Admin Layer**: 20 files (admin panel customizations)
- **Tests Layer**: 25 files (unit, integration, e2e tests)

### Directory Structure
```
.
├── .bmad-core/              # BMad automation core
├── .claude/                 # Claude AI configurations
├── .cursor/                 # Cursor IDE configurations
├── .docs/                   # Documentation states
├── .git/                    # Git version control
├── .github/                 # GitHub workflows
├── .kiro/                   # Kiro specifications
├── .rovodev/                # RovoDev configurations
├── .serena/                 # Serena AI configurations
├── .strapi/                 # Strapi internal files
├── .vscode/                 # VSCode configurations
├── config/                  # Configuration files
│   ├── env/                 # Environment-specific configs
│   └── utils/               # Configuration utilities
├── data/                    # Data files and uploads
├── database/                # Database migrations and init
├── dist/                    # Build output
├── docs/                    # Project documentation
├── public/                  # Static assets
├── scripts/                 # Build and deployment scripts
├── src/                     # Source code
│   ├── admin/               # Admin panel customizations
│   ├── api/                 # API endpoints (29 content types)
│   ├── components/          # Shared components
│   ├── extensions/          # Plugin extensions
│   ├── middlewares/         # Custom middlewares
│   └── services/            # Business logic services
├── tests/                   # Test suites
│   ├── ab-testing/          # A/B testing tests
│   ├── adapty/              # Adapty integration tests
│   ├── admin/               # Admin panel tests
│   ├── analytics/           # Analytics tests
│   ├── e2e/                 # End-to-end tests
│   ├── localization/        # i18n tests
│   ├── media/               # Media handling tests
│   ├── mobile-api/          # Mobile API tests
│   ├── monitoring/          # System monitoring tests
│   └── paywall/             # Paywall functionality tests
└── types/                   # TypeScript type definitions
    └── generated/           # Auto-generated types
```

## Strapi Plugins

### Core Plugins
- **@strapi/plugin-i18n** (v4.25.23): Internationalization plugin supporting 7 languages (en, es, fr, de, ja, ko, zh)
- **@strapi/plugin-upload** (v4.25.2): Enhanced upload plugin with CDN integration, 50MB limit, responsive images
- **@strapi/plugin-users-permissions** (v4.25.23): User authentication and permissions with 7-day JWT expiration

### Plugin Configurations
- **Upload Provider**: Configurable (local/AWS S3/Cloudinary)
- **Image Optimization**: Quality 85, progressive JPEG, responsive breakpoints
- **CDN Integration**: AWS S3 and Cloudinary support
- **Multi-language**: 7 supported locales with English as default

## Custom Content Types (29 Total)

### Content Management
- **ab-test**: A/B testing configurations
- **about**: About page content
- **analytics**: Analytics data tracking
- **article**: Blog/news articles
- **author**: Content authors
- **category**: Content categorization
- **global**: Global site settings
- **product**: Product information

### Mobile & API
- **mobile**: Mobile-specific content
- **mobile-api-usage**: API usage tracking
- **mobile-app**: Mobile app configurations
- **api-performance**: API performance metrics

### Monetization
- **paywall**: Paywall configurations
- **paywall-metrics**: Paywall performance data
- **paywall-variation**: Paywall A/B test variations
- **subscription-metrics**: Subscription analytics

### System Management
- **cache-invalidation**: Cache management
- **cache-metrics**: Cache performance data
- **deployment-log**: Deployment tracking
- **performance-alert**: System alerts
- **preview-config**: Preview configurations
- **remote-config-version**: Remote configuration versioning
- **sync-operation**: Data synchronization
- **sync-status**: Sync status tracking
- **system-health**: Health monitoring

### Localization & Analytics
- **translation-assignment**: Translation workflows
- **translation-memory**: Translation cache
- **user-engagement**: User interaction analytics
- **cohort-analysis**: User cohort data

## Controllers, Routes, and Services Structure

Each content type follows Strapi's standard structure:
```
src/api/{content-type}/
├── content-types/{content-type}/
│   └── schema.json           # Content type definition
├── controllers/
│   └── {content-type}.js     # Request handlers
├── routes/
│   └── {content-type}.js     # Route definitions
└── services/
    └── {content-type}.js     # Business logic
```

## Dependency Graph (npm ls --depth=0)

### Core Dependencies
```
admin-strapi@0.1.0
├── @strapi/strapi@4.25.23          # Core Strapi framework
├── @strapi/plugin-i18n@4.25.23     # Internationalization
├── @strapi/plugin-upload@4.25.2    # File upload handling
├── @strapi/plugin-users-permissions@4.25.23  # User management
├── better-sqlite3@11.3.0           # SQLite database driver
├── pg@8.16.3                       # PostgreSQL driver
├── ioredis@5.7.0                   # Redis client
├── axios@1.11.0                    # HTTP client
├── sharp@0.33.5                    # Image processing
├── react@18.3.1                    # React for admin panel
├── react-dom@18.3.1                # React DOM
├── react-router-dom@5.3.4          # Client-side routing
├── styled-components@5.3.11        # CSS-in-JS styling
└── mime-types@2.1.35               # MIME type utilities
```

### Development Dependencies
```
├── @playwright/test@1.54.2         # E2E testing framework
├── @jest/globals@29.7.0            # Testing framework
├── jest@29.7.0                     # Unit testing
├── ts-jest@29.4.0                  # TypeScript Jest support
├── typescript@5.8.3                # TypeScript compiler
├── esbuild@0.25.8                  # Fast bundler
├── webpack@5.101.0                 # Module bundler
└── @types/*                        # TypeScript definitions
```

### External Libraries & Integrations
- **Database**: SQLite (development), PostgreSQL (production)
- **Caching**: Redis with ioredis client
- **Image Processing**: Sharp for optimization and resizing
- **Testing**: Jest + Playwright for comprehensive testing
- **Build Tools**: Vite (primary), Webpack (fallback)
- **Styling**: Styled Components for UI customization

## Key Features Identified
- **Multi-database Support**: SQLite + PostgreSQL
- **Caching Layer**: Redis integration
- **CDN Integration**: AWS S3 + Cloudinary support
- **Comprehensive Testing**: Unit, Integration, E2E tests
- **Mobile-First**: Dedicated mobile API and content types
- **Monetization**: Advanced paywall and subscription management
- **Analytics**: Detailed user engagement and performance tracking
- **Internationalization**: 7-language support
- **Performance Monitoring**: Health checks and alerts
- **DevOps Ready**: Docker, CI/CD, and deployment automation

