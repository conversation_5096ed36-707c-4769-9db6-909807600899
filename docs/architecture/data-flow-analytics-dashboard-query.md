# Data Flow Analysis: Analytics Dashboard Query

## Journey Overview
This document analyzes the complete data flow for analytics dashboard queries, which aggregate complex metrics across multiple data sources for business intelligence.

**Endpoint**: `POST /api/analytics/dashboard`

**Primary Use Case**: Admin users query comprehensive analytics data for business decision-making, A/B test analysis, and performance monitoring.

## Request Flow Summary

```
Admin User → Browser → Strapi Admin → Controller → Analytics Service → Database (Multiple Queries) → External APIs → Response
```

## Data Entities Accessed

### Primary Read Entities (Analytics Queries)

| Entity | Table | Purpose | Query Complexity | Estimated Rows |
|--------|-------|---------|-----------------|----------------|
| `paywall_impressions` | `paywall_impressions` | Base impression metrics | Complex aggregation with GROUP BY | 1M+ rows/month |
| `user_engagement` | `user_engagement` | Interaction tracking | Complex filtering and aggregation | 5M+ rows/month |
| `ab_test_conversions` | `ab_test_conversions` | A/B test conversion events | JOIN with variations table | 100K+ rows/month |
| `paywall_variations` | `paywall_variations` | A/B test variation metrics | Statistical calculations | 1K+ rows |
| `ab_tests` | `ab_tests` | A/B test configurations | Status and metadata queries | 100+ rows |
| `user_journeys` | `user_journeys` | User behavior sequences | Complex temporal analysis | 500K+ rows/month |
| `paywall_hourly_metrics` | `paywall_hourly_metrics` | Pre-aggregated metrics | Fast aggregation queries | 50K+ rows/month |

### Performance Optimization Tables

| Entity | Table | Purpose | Update Frequency |
|--------|-------|---------|------------------|
| `paywall_daily_metrics` | `paywall_daily_metrics` | Daily rollup data | Once per day |
| `cohort_analysis` | `cohort_analysis` | User cohort metrics | Weekly batch job |
| `funnel_metrics` | `funnel_metrics` | Conversion funnel data | Hourly updates |
| `device_performance` | `device_performance` | Device-specific metrics | Real-time updates |

## Detailed Data Flow

### Phase 1: Request Validation & Caching

**Input Query Structure:**
```json
{
  "dateRange": {
    "start": "2024-01-01T00:00:00Z",
    "end": "2024-01-31T23:59:59Z"
  },
  "paywallIds": ["paywall_123", "paywall_456"],
  "regions": ["US", "EU", "APAC"],
  "locales": ["en", "fr", "de", "ja"],
  "deviceTypes": ["mobile", "tablet", "desktop"],
  "platforms": ["ios", "android", "web"],
  "userSegments": ["new_users", "returning_users", "premium_users"],
  "metrics": ["impressions", "conversions", "revenue", "engagement"],
  "groupBy": ["date", "paywall", "device_type"],
  "includeABTests": true,
  "includeRealtime": false
}
```

**Cache Strategy:**
```javascript
// Cache key generation
const cacheKey = `dashboard_${generateHash({
  dateRange,
  filters: {paywallIds, regions, locales, deviceTypes, platforms, userSegments},
  groupBy
})}_v2`;

// Cache TTL logic
const cacheTTL = isRealtime ? 300 : // 5 minutes for real-time
                 isRecent ? 900 :   // 15 minutes for recent data
                 3600;              // 1 hour for historical data
```

### Phase 2: Primary Metrics Aggregation

**1. Paywall Impressions Query:**
```sql
SELECT 
    pi.paywall_id,
    DATE(pi.timestamp) as metric_date,
    pi.device_type,
    pi.locale,
    pi.platform,
    pi.region,
    -- Impression metrics
    COUNT(*) as total_impressions,
    COUNT(DISTINCT pi.user_id) as unique_users,
    COUNT(DISTINCT pi.session_id) as unique_sessions,
    -- Time-based analysis
    COUNT(CASE WHEN HOUR(pi.timestamp) BETWEEN 6 AND 12 THEN 1 END) as morning_impressions,
    COUNT(CASE WHEN HOUR(pi.timestamp) BETWEEN 12 AND 18 THEN 1 END) as afternoon_impressions,
    COUNT(CASE WHEN HOUR(pi.timestamp) BETWEEN 18 AND 24 THEN 1 END) as evening_impressions,
    -- User segment analysis
    COUNT(CASE WHEN pi.user_segment = 'new_users' THEN 1 END) as new_user_impressions,
    COUNT(CASE WHEN pi.user_segment = 'returning_users' THEN 1 END) as returning_user_impressions,
    -- Performance metrics
    AVG(pi.load_time_ms) as avg_load_time,
    COUNT(CASE WHEN pi.load_time_ms > 3000 THEN 1 END) as slow_loads
FROM paywall_impressions pi
WHERE pi.timestamp BETWEEN ? AND ?
  AND pi.paywall_id IN (?, ?, ?)
  AND pi.device_type IN (?, ?, ?)
  AND pi.locale IN (?, ?, ?, ?)
  AND pi.region IN (?, ?, ?)
  AND pi.platform IN (?, ?, ?)
GROUP BY pi.paywall_id, DATE(pi.timestamp), 
         pi.device_type, pi.locale, pi.platform, pi.region
ORDER BY metric_date ASC, pi.paywall_id;
```

**2. User Engagement Analysis:**
```sql
SELECT 
    ue.paywall_id,
    DATE(ue.timestamp) as metric_date,
    ue.device_type,
    ue.interaction_type,
    -- Engagement metrics
    COUNT(*) as total_interactions,
    COUNT(DISTINCT ue.user_id) as unique_users,
    COUNT(DISTINCT ue.session_id) as unique_sessions,
    -- Interaction-specific metrics
    COUNT(CASE WHEN ue.interaction_type = 'view' THEN 1 END) as views,
    COUNT(CASE WHEN ue.interaction_type = 'click' THEN 1 END) as clicks,
    COUNT(CASE WHEN ue.interaction_type = 'close' THEN 1 END) as closes,
    COUNT(CASE WHEN ue.interaction_type = 'purchase_start' THEN 1 END) as purchase_starts,
    COUNT(CASE WHEN ue.interaction_type = 'purchase_complete' THEN 1 END) as conversions,
    -- Timing analysis
    AVG(CASE WHEN ue.interaction_type = 'view' 
        THEN JSON_EXTRACT(ue.additional_data, '$.duration_seconds') END) as avg_view_duration,
    -- Revenue metrics
    SUM(CASE WHEN ue.interaction_type = 'purchase_complete'
        THEN JSON_EXTRACT(ue.additional_data, '$.revenue') ELSE 0 END) as total_revenue,
    AVG(CASE WHEN ue.interaction_type = 'purchase_complete'
        THEN JSON_EXTRACT(ue.additional_data, '$.revenue') END) as avg_revenue_per_conversion
FROM user_engagement ue
WHERE ue.timestamp BETWEEN ? AND ?
  AND ue.paywall_id IN (?, ?, ?)
  AND ue.device_type IN (?, ?, ?)
GROUP BY ue.paywall_id, DATE(ue.timestamp), 
         ue.device_type, ue.interaction_type
ORDER BY metric_date ASC, ue.paywall_id;
```

**3. A/B Test Performance Analysis:**
```sql
SELECT 
    at.id as test_id,
    at.name as test_name,
    at.status,
    at.hypothesis,
    at.primary_metric,
    at.confidence_level,
    at.start_date,
    at.end_date,
    -- Control variation metrics
    control.id as control_variation_id,
    control.name as control_variation_name,
    control.total_impressions as control_impressions,
    control.total_conversions as control_conversions,
    control.conversion_rate as control_conversion_rate,
    control.total_revenue as control_revenue,
    control.avg_revenue_per_user as control_arpu,
    -- Variant metrics
    variant.id as variant_variation_id,
    variant.name as variant_variation_name,
    variant.total_impressions as variant_impressions,
    variant.total_conversions as variant_conversions,
    variant.conversion_rate as variant_conversion_rate,
    variant.total_revenue as variant_revenue,
    variant.avg_revenue_per_user as variant_arpu,
    -- Statistical analysis
    CASE WHEN control.total_impressions > 0 AND variant.total_impressions > 0 THEN
        (variant.conversion_rate - control.conversion_rate) / control.conversion_rate * 100
    END as conversion_rate_lift_percent,
    CASE WHEN control.avg_revenue_per_user > 0 AND variant.avg_revenue_per_user > 0 THEN
        (variant.avg_revenue_per_user - control.avg_revenue_per_user) / control.avg_revenue_per_user * 100
    END as arpu_lift_percent,
    -- Significance calculation (simplified)
    CASE WHEN control.total_impressions >= 1000 AND variant.total_impressions >= 1000 THEN
        ABS(variant.conversion_rate - control.conversion_rate) / 
        SQRT((control.conversion_rate * (1 - control.conversion_rate) / control.total_impressions) +
             (variant.conversion_rate * (1 - variant.conversion_rate) / variant.total_impressions))
    END as z_score
FROM ab_tests at
LEFT JOIN paywall_variations control ON at.id = control.ab_test_id AND control.is_control = true
LEFT JOIN paywall_variations variant ON at.id = variant.ab_test_id AND variant.is_control = false
WHERE at.created_at BETWEEN ? AND ?
  AND at.status IN ('running', 'completed', 'paused')
ORDER BY at.created_at DESC;
```

### Phase 3: Advanced Analytics Calculations

**User Journey Funnel Analysis:**
```sql
WITH journey_steps AS (
    SELECT 
        ue.user_id,
        ue.paywall_id,
        ue.session_id,
        MIN(CASE WHEN ue.interaction_type = 'view' THEN ue.timestamp END) as first_view,
        MIN(CASE WHEN ue.interaction_type = 'click' THEN ue.timestamp END) as first_click,
        MIN(CASE WHEN ue.interaction_type = 'purchase_start' THEN ue.timestamp END) as purchase_start,
        MIN(CASE WHEN ue.interaction_type = 'purchase_complete' THEN ue.timestamp END) as purchase_complete,
        MAX(CASE WHEN ue.interaction_type = 'close' THEN ue.timestamp END) as last_close
    FROM user_engagement ue
    WHERE ue.timestamp BETWEEN ? AND ?
      AND ue.paywall_id IN (?, ?, ?)
    GROUP BY ue.user_id, ue.paywall_id, ue.session_id
),
funnel_metrics AS (
    SELECT 
        paywall_id,
        COUNT(*) as total_sessions,
        COUNT(first_view) as viewed_sessions,
        COUNT(first_click) as clicked_sessions,
        COUNT(purchase_start) as purchase_started_sessions,
        COUNT(purchase_complete) as converted_sessions,
        -- Conversion rates
        COUNT(first_click) * 100.0 / NULLIF(COUNT(first_view), 0) as view_to_click_rate,
        COUNT(purchase_start) * 100.0 / NULLIF(COUNT(first_click), 0) as click_to_start_rate,
        COUNT(purchase_complete) * 100.0 / NULLIF(COUNT(purchase_start), 0) as start_to_complete_rate,
        COUNT(purchase_complete) * 100.0 / NULLIF(COUNT(first_view), 0) as overall_conversion_rate,
        -- Time analysis
        AVG(CASE WHEN purchase_complete IS NOT NULL 
            THEN TIMESTAMPDIFF(SECOND, first_view, purchase_complete) END) as avg_conversion_time_seconds
    FROM journey_steps
    GROUP BY paywall_id
)
SELECT * FROM funnel_metrics;
```

**Cohort Analysis Query:**
```sql
WITH user_cohorts AS (
    SELECT 
        user_id,
        paywall_id,
        DATE(MIN(timestamp)) as cohort_date,
        MIN(timestamp) as first_interaction,
        MAX(CASE WHEN interaction_type = 'purchase_complete' THEN timestamp END) as first_conversion
    FROM user_engagement
    WHERE timestamp BETWEEN ? AND ?
    GROUP BY user_id, paywall_id
),
cohort_metrics AS (
    SELECT 
        c.cohort_date,
        c.paywall_id,
        COUNT(*) as cohort_size,
        COUNT(c.first_conversion) as converted_users,
        COUNT(c.first_conversion) * 100.0 / COUNT(*) as cohort_conversion_rate,
        -- Retention analysis (users who returned)
        COUNT(CASE WHEN r.return_visit IS NOT NULL THEN 1 END) as retained_users,
        COUNT(CASE WHEN r.return_visit IS NOT NULL THEN 1 END) * 100.0 / COUNT(*) as retention_rate
    FROM user_cohorts c
    LEFT JOIN (
        SELECT DISTINCT user_id, paywall_id, 1 as return_visit
        FROM user_engagement ue2
        WHERE ue2.timestamp > DATE_ADD(?, INTERVAL 7 DAY) -- Return visits after week 1
    ) r ON c.user_id = r.user_id AND c.paywall_id = r.paywall_id
    GROUP BY c.cohort_date, c.paywall_id
)
SELECT * FROM cohort_metrics ORDER BY cohort_date DESC;
```

### Phase 4: External Data Integration

**Adapty Analytics API Integration:**
```javascript
// API call to Adapty
const adaptyResponse = await adaptyClient.getAnalytics({
  placement_ids: paywallIds.map(id => placementIdMapping[id]),
  start_date: dateRange.start,
  end_date: dateRange.end,
  metrics: ['impressions', 'trial_activations', 'purchases', 'revenue'],
  group_by: ['placement_id', 'date', 'country'],
  currency: 'USD'
});

// Data reconciliation
const reconciledData = {
  internal: internalMetrics,
  external: adaptyResponse.data,
  discrepancies: [],
  reconciliation_summary: {
    impression_variance: calculateVariance(
      internalMetrics.impressions, 
      adaptyResponse.data.impressions
    ),
    revenue_variance: calculateVariance(
      internalMetrics.revenue,
      adaptyResponse.data.revenue
    ),
    data_quality_score: calculateDataQualityScore()
  }
};
```

### Phase 5: Response Assembly

**Dashboard Response Structure:**
```json
{
  "overview": {
    "totalImpressions": 125000,
    "totalConversions": 3750,
    "overallConversionRate": 3.0,
    "totalRevenue": 37500.00,
    "averageRevenuePerUser": 10.00,
    "uniqueUsers": 98000,
    "averageSessionDuration": 145.5,
    "bounceRate": 65.2
  },
  "timeSeriesData": {
    "impressions": [
      {"date": "2024-01-01", "value": 4200, "trend": "****%"},
      {"date": "2024-01-02", "value": 4450, "trend": "+6.0%"}
    ],
    "conversions": [
      {"date": "2024-01-01", "value": 126, "trend": "+8.1%"},
      {"date": "2024-01-02", "value": 134, "trend": "****%"}
    ],
    "revenue": [
      {"date": "2024-01-01", "value": 1260.00, "trend": "+12.5%"},
      {"date": "2024-01-02", "value": 1340.00, "trend": "****%"}
    ]
  },
  "paywallPerformance": [
    {
      "paywallId": "paywall_123",
      "name": "Premium Monthly Offer",
      "impressions": 45000,
      "conversions": 1350,
      "conversionRate": 3.0,
      "revenue": 13500.00,
      "arpu": 10.00,
      "trend": "****%",
      "deviceBreakdown": {
        "mobile": {"impressions": 36000, "conversions": 1080, "rate": 3.0},
        "tablet": {"impressions": 7200, "conversions": 216, "rate": 3.0},
        "desktop": {"impressions": 1800, "conversions": 54, "rate": 3.0}
      }
    }
  ],
  "abTestResults": [
    {
      "testId": "test_456",
      "testName": "Price Point Optimization",
      "status": "running",
      "hypothesis": "Higher price point will increase ARPU without significantly reducing conversion rate",
      "startDate": "2024-01-15",
      "endDate": null,
      "confidence": 85.2,
      "significance": "approaching",
      "variations": [
        {
          "variationId": "control",
          "name": "Original Price ($9.99)",
          "isControl": true,
          "impressions": 15000,
          "conversions": 450,
          "conversionRate": 3.0,
          "revenue": 4495.50,
          "arpu": 9.99
        },
        {
          "variationId": "variant_1",
          "name": "Higher Price ($12.99)",
          "isControl": false,
          "impressions": 15200,
          "conversions": 410,
          "conversionRate": 2.7,
          "revenue": 5324.90,
          "arpu": 12.99,
          "lift": {
            "conversionRate": -10.0,
            "arpu": +30.0,
            "revenue": +18.4
          }
        }
      ],
      "recommendation": "Continue test to achieve statistical significance",
      "recommendedActions": [
        "Monitor for 7+ more days",
        "Ensure balanced traffic allocation",
        "Prepare deployment plan for winner"
      ]
    }
  ],
  "userSegmentAnalysis": {
    "newUsers": {
      "count": 65000,
      "conversionRate": 2.1,
      "arpu": 8.50,
      "avgTimeToConversion": 450
    },
    "returningUsers": {
      "count": 33000,
      "conversionRate": 4.5,
      "arpu": 12.75,
      "avgTimeToConversion": 180
    }
  },
  "geographicDistribution": {
    "US": {"impressions": 50000, "conversions": 1600, "revenue": 16000},
    "EU": {"impressions": 35000, "conversions": 1050, "revenue": 10500},
    "APAC": {"impressions": 25000, "conversions": 750, "revenue": 7500},
    "Other": {"impressions": 15000, "conversions": 350, "revenue": 3500}
  },
  "funnelAnalysis": {
    "stages": [
      {"name": "Impression", "users": 98000, "rate": 100.0},
      {"name": "View", "users": 85000, "rate": 86.7},
      {"name": "Engage", "users": 45000, "rate": 52.9},
      {"name": "Intent", "users": 12000, "rate": 26.7},
      {"name": "Purchase", "users": 3750, "rate": 31.3}
    ],
    "dropoffAnalysis": {
      "highestDropoff": "View to Engage",
      "dropoffRate": 47.1,
      "improvementOpportunity": "Optimize paywall content and CTAs"
    }
  },
  "meta": {
    "queryExecutionTime": 1450,
    "dataFreshness": "2024-01-15T10:45:00Z",
    "cacheStatus": "miss",
    "recordsProcessed": 2500000,
    "dataQuality": {
      "completeness": 98.5,
      "accuracy": 97.2,
      "consistency": 99.1,
      "discrepancies": []
    },
    "exportOptions": {
      "formats": ["csv", "json", "pdf"],
      "scheduledReports": true,
      "realTimeAlerts": true
    }
  }
}
```

## Performance Optimization Strategies

### Query Optimization

**1. Pre-aggregated Tables:**
```sql
-- Hourly metrics table for fast queries
CREATE TABLE paywall_hourly_metrics (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    paywall_id VARCHAR(255),
    metric_hour DATETIME,
    device_type VARCHAR(50),
    locale VARCHAR(10),
    impressions INT DEFAULT 0,
    unique_users INT DEFAULT 0,
    conversions INT DEFAULT 0,
    revenue DECIMAL(10,2) DEFAULT 0,
    engagement_score FLOAT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_paywall_hour (paywall_id, metric_hour),
    INDEX idx_hour_device (metric_hour, device_type),
    INDEX idx_performance (impressions, conversions, revenue)
);
```

**2. Materialized Views for Complex Queries:**
```sql
-- Create materialized view for funnel analysis
CREATE OR REPLACE VIEW paywall_funnel_metrics AS
SELECT 
    paywall_id,
    DATE(timestamp) as metric_date,
    COUNT(DISTINCT CASE WHEN interaction_type = 'view' THEN user_id END) as users_viewed,
    COUNT(DISTINCT CASE WHEN interaction_type = 'click' THEN user_id END) as users_clicked,
    COUNT(DISTINCT CASE WHEN interaction_type = 'purchase_start' THEN user_id END) as users_started,
    COUNT(DISTINCT CASE WHEN interaction_type = 'purchase_complete' THEN user_id END) as users_converted,
    -- Calculated conversion rates
    COUNT(DISTINCT CASE WHEN interaction_type = 'click' THEN user_id END) * 100.0 / 
    NULLIF(COUNT(DISTINCT CASE WHEN interaction_type = 'view' THEN user_id END), 0) as view_to_click_rate,
    COUNT(DISTINCT CASE WHEN interaction_type = 'purchase_complete' THEN user_id END) * 100.0 / 
    NULLIF(COUNT(DISTINCT CASE WHEN interaction_type = 'view' THEN user_id END), 0) as overall_conversion_rate
FROM user_engagement
WHERE timestamp >= DATE_SUB(CURDATE(), INTERVAL 90 DAY)
GROUP BY paywall_id, DATE(timestamp);
```

**3. Indexed Query Patterns:**
```sql
-- Optimized indexes for dashboard queries
CREATE INDEX idx_impressions_dashboard ON paywall_impressions 
    (timestamp, paywall_id, device_type, locale) 
    INCLUDE (user_id, session_id, region);

CREATE INDEX idx_engagement_dashboard ON user_engagement 
    (timestamp, paywall_id, interaction_type, device_type) 
    INCLUDE (user_id, additional_data);

CREATE INDEX idx_ab_test_performance ON paywall_variations 
    (ab_test_id, updated_at) 
    INCLUDE (total_impressions, total_conversions, conversion_rate);
```

### Caching Strategy

**Multi-layer Caching:**
```javascript
// L1: In-memory cache for recent queries (Redis)
const cacheKey = `dashboard_${queryHash}`;
const cachedResult = await redis.get(cacheKey);

// L2: Database query result cache
if (!cachedResult) {
    const queryResult = await executeOptimizedQuery(filters);
    await redis.setex(cacheKey, ttl, JSON.stringify(queryResult));
}

// L3: Pre-computed dashboard cache warming
const popularDashboards = [
    'last_30_days_all_paywalls',
    'current_week_ab_tests',
    'real_time_performance'
];

for (const dashboard of popularDashboards) {
    await scheduleBackgroundRefresh(dashboard);
}
```

## Error Handling & Fallbacks

### Query Timeout Handling
```javascript
const executeWithTimeout = async (query, timeout = 30000) => {
    try {
        return await Promise.race([
            database.query(query),
            new Promise((_, reject) => 
                setTimeout(() => reject(new Error('Query timeout')), timeout)
            )
        ]);
    } catch (error) {
        if (error.message === 'Query timeout') {
            // Fallback to cached data
            const fallbackData = await getCachedFallback(query);
            return {
                ...fallbackData,
                meta: { 
                    ...fallbackData.meta,
                    warning: 'Data served from cache due to query timeout',
                    dataFreshness: fallbackData.meta.cacheTimestamp
                }
            };
        }
        throw error;
    }
};
```

### Graceful Degradation
```javascript
const buildDashboardWithFallbacks = async (filters) => {
    const results = {};
    const errors = [];

    // Core metrics (required)
    try {
        results.overview = await getOverviewMetrics(filters);
    } catch (error) {
        errors.push({ section: 'overview', error: error.message });
        results.overview = await getFallbackOverview();
    }

    // A/B test data (optional)
    try {
        results.abTestResults = await getABTestResults(filters);
    } catch (error) {
        errors.push({ section: 'abTests', error: error.message });
        results.abTestResults = [];
    }

    // External data (optional)
    try {
        results.externalData = await getAdaptyData(filters);
    } catch (error) {
        errors.push({ section: 'external', error: error.message });
        results.externalData = null;
    }

    return {
        ...results,
        meta: {
            errors,
            dataQuality: calculateDataQuality(results, errors),
            fallbacksUsed: errors.length > 0
        }
    };
};
```

## Performance Characteristics

### Expected Performance Metrics
- **Simple queries** (7 days, 1-2 paywalls): 200-500ms
- **Medium queries** (30 days, multiple filters): 1-3 seconds
- **Complex queries** (90 days, all filters): 3-10 seconds
- **Cache hit response**: < 100ms
- **Maximum acceptable timeout**: 30 seconds

### Scaling Considerations
- **Database partitioning** by date (monthly partitions)
- **Read replicas** for analytics queries
- **Connection pooling** with 50+ concurrent connections
- **Query result pagination** for large datasets
- **Background pre-aggregation** jobs
- **CDN caching** for static dashboard components

## Security & Access Control

### Authentication & Authorization
- Admin session validation required
- Role-based access control (analytics_viewer, analytics_admin)
- API rate limiting (100 requests/hour per user)
- Data access logging for audit trails

### Data Privacy
- PII masking in exported data
- Aggregated data only (no individual user details)
- GDPR compliance for EU data
- Data retention policies enforcement
