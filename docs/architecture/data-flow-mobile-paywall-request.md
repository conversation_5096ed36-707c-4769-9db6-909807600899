# Data Flow Analysis: Mobile Client Paywall Request

## Journey Overview
This document analyzes the complete data flow for a mobile client requesting paywall content through the Strapi-Adapty CMS API.

**Endpoint**: `GET /mobile/v1/paywalls/{placementId}`

**Primary Use Case**: Mobile applications fetch paywall configuration for display to users.

## Request Flow Summary

```
Mobile App → Rate Limiter → Auth → Cache → Analytics → Controller → Service → Database → Response
```

## Data Entities Accessed

### Primary Entities (Read Operations)

| Entity | Table | Purpose | Query Pattern |
|--------|-------|---------|---------------|
| `paywall` | `paywalls` | Main paywall configuration | `SELECT * WHERE placement_id = ? AND published_at IS NOT NULL AND locale = ?` |
| `theme` | `themes` | Visual styling configuration | `LEFT JOIN themes ON paywalls.theme_id = themes.id` |
| `features` | `features` | Paywall feature list | `LEFT JOIN features ON paywalls.id = features.paywall_id` |
| `testimonials` | `testimonials` | User testimonials | `LEFT JOIN testimonials ON paywalls.id = testimonials.paywall_id` |
| `product_labels` | `product_labels` | Product badge configurations | `LEFT JOIN product_labels ON paywalls.id = product_labels.paywall_id` |
| `media` | `files` | Image assets (background, icons, avatars) | Referenced through relation fields |

### Analytics Entities (Write Operations)

| Entity | Table | Purpose | Data Written |
|--------|-------|---------|--------------|
| `paywall_impressions` | `paywall_impressions` | Track paywall views | `paywall_id, placement_id, user_id, device_type, locale, app_version, timestamp, ip_address, user_agent` |
| `api_performance` | `api_performance` | API performance metrics | `endpoint, method, response_time, status_code, request_size, response_size, timestamp` |
| `mobile_api_usage` | `mobile_api_usage` | Mobile-specific API usage | `app_id, user_id, platform, endpoint, method, status_code, response_time, timestamp` |

## Detailed Data Flow

### Phase 1: Request Processing & Validation

**Input Parameters:**
- `placementId` (required): Adapty placement identifier
- `locale` (optional, default: "en"): Language code
- `device_type` (optional, default: "mobile"): Device category
- `app_version` (optional): Client app version
- `user_id` (optional): User identifier for analytics

**Middleware Chain Processing:**

1. **Rate Limiting**: Validates request rate (60 requests/minute)
2. **Authentication**: JWT token validation, extracts app credentials
3. **Caching**: ETag validation, cache lookup with key pattern: `paywall_${placementId}_${locale}_${device_type}`
4. **Analytics**: Request metadata collection, performance timer start

### Phase 2: Data Retrieval

**Primary Database Query:**
```sql
SELECT 
    p.id, p.placement_id, p.title, p.subtitle, p.description_text,
    p.cta_text, p.cta_secondary_text, p.adapty_version, p.updated_at,
    t.primary_color, t.secondary_color, t.background_color, t.text_color,
    t.button_style, t.layout_style, t.background_image_id, t.logo_id,
    f.id as feature_id, f.title as feature_title, f.description as feature_desc,
    f.icon_image_id, f.order as feature_order,
    ts.id as testimonial_id, ts.content, ts.author_name, ts.author_title,
    ts.rating, ts.author_avatar_id,
    pl.id as label_id, pl.text, pl.style, pl.color, pl.background_color, pl.position
FROM paywalls p
LEFT JOIN themes t ON p.theme_id = t.id
LEFT JOIN features f ON p.id = f.paywall_id
LEFT JOIN testimonials ts ON p.id = ts.paywall_id  
LEFT JOIN product_labels pl ON p.id = pl.paywall_id
WHERE p.placement_id = ? 
  AND p.published_at IS NOT NULL 
  AND p.locale = ?
ORDER BY f.order ASC, ts.id ASC, pl.id ASC
LIMIT 1;
```

**Image Asset Queries:**
```sql
-- For each image reference
SELECT url, mime, width, height, formats 
FROM files 
WHERE id IN (background_image_id, logo_id, icon_image_id, author_avatar_id);

-- Optimization lookup
SELECT device_type, optimized_url 
FROM file_optimizations 
WHERE file_id = ? AND device_type = ?;
```

### Phase 3: Data Transformation

**Mobile Optimization Rules:**
- **Features**: Limit to 6 items for mobile devices (first 6 by order)
- **Testimonials**: Limit to 3 items for mobile devices  
- **Images**: Use device-optimized versions when available
- **Styling**: Apply mobile-specific font and padding scaling

**Transformation Output:**
```json
{
  "id": 123,
  "placement_id": "premium_paywall_ios",
  "title": "Unlock Premium Features",
  "subtitle": "Get unlimited access",
  "description": "Transform your experience with premium features",
  "cta_text": "Start Free Trial",
  "cta_secondary_text": "Restore Purchase",
  "theme": {
    "primary_color": "#007AFF",
    "secondary_color": "#34C759", 
    "background_color": "#FFFFFF",
    "text_color": "#000000",
    "button_style": "rounded",
    "layout_style": "vertical",
    "background_image": "https://cdn.example.com/bg_mobile_optimized.jpg",
    "logo": "https://cdn.example.com/logo_mobile.png",
    "mobile_adjustments": {
      "font_scale": 0.9,
      "padding_scale": 0.8
    }
  },
  "features": [
    {
      "id": 1,
      "title": "Unlimited Downloads",
      "description": "Download as many files as you want",
      "icon_url": "https://cdn.example.com/icon_download_mobile.png",
      "order": 1
    }
    // ... up to 6 features
  ],
  "testimonials": [
    {
      "id": 1,
      "content": "This app changed my life!",
      "author_name": "John Doe",
      "author_title": "Power User",
      "rating": 5,
      "avatar_url": "https://cdn.example.com/avatar_mobile.jpg"
    }
    // ... up to 3 testimonials
  ],
  "product_labels": [
    {
      "id": 1,
      "text": "Most Popular",
      "style": "badge",
      "color": "#FFFFFF",
      "background_color": "#FF3B30",
      "position": "top_right"
    }
  ],
  "locale": "en",
  "last_updated": "2024-01-15T10:30:00Z",
  "version": "1.0.0"
}
```

### Phase 4: Analytics Recording

**Paywall Impression Analytics:**
```sql
INSERT INTO paywall_impressions (
    paywall_id, placement_id, user_id, device_type, locale, 
    app_version, timestamp, ip_address, user_agent, session_id
) VALUES (?, ?, ?, ?, ?, ?, NOW(), ?, ?, ?);
```

**API Performance Metrics:**
```sql
INSERT INTO api_performance (
    endpoint, method, response_time, status_code, 
    request_size, response_size, timestamp, app_id
) VALUES (
    '/mobile/v1/paywalls/{placementId}', 'GET', ?, 200, 
    ?, ?, NOW(), ?
);
```

### Phase 5: Response Caching

**Cache Configuration:**
- **TTL**: 5 minutes for paywall content
- **Cache Key**: `mobile_paywall_${placementId}_${locale}_${device_type}`
- **ETag**: Generated from content hash and last_updated timestamp
- **Headers**: `Cache-Control: max-age=300, must-revalidate`

## Performance Characteristics

### Expected Response Times
- **Cache Hit**: < 50ms
- **Cache Miss**: 150-300ms
- **With Complex Joins**: 200-500ms

### Database Impact
- **Read Queries**: 1 primary + N image queries (typically 3-8 total)
- **Write Queries**: 2-3 analytics inserts (asynchronous)
- **Indexes Required**: `(placement_id, published_at, locale)`, `(paywall_id)` on related tables

### External Dependencies
- **CDN**: Image optimization and delivery
- **Cache Store**: Redis/Memory cache for response caching
- **Analytics Store**: Time-series data for performance metrics

## Error Scenarios

| Error Code | Scenario | Response | Data Impact |
|------------|----------|----------|-------------|
| 404 | Paywall not found | `{"error": "Paywall not found"}` | Analytics logged with 404 status |
| 429 | Rate limit exceeded | `{"error": "Too many requests"}` | Request blocked, minimal logging |
| 500 | Database error | `{"error": "Internal server error"}` | Error logged, performance alert triggered |
| 503 | CDN unavailable | Fallback to original images | Degraded experience, monitoring alert |

## Monitoring & Alerts

### Performance Thresholds
- **Warning**: Response time > 2 seconds
- **Critical**: Response time > 5 seconds
- **Error Rate**: > 5% errors in 5-minute window

### Key Metrics Tracked
- Request volume per app/placement
- Response time percentiles (p50, p95, p99)
- Cache hit ratio
- Error rates by type
- Data transformation success rate

## Security Considerations

- JWT token validation for all requests
- Rate limiting per app/user
- Query parameter sanitization
- Sensitive data filtering in logs
- CORS policy enforcement
