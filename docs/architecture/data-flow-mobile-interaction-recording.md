# Data Flow Analysis: Mobile Interaction Recording

## Journey Overview
This document analyzes the complete data flow for mobile clients recording paywall interactions, which is critical for analytics and A/B testing.

**Endpoint**: `POST /mobile/v1/paywalls/{placementId}/interactions`

**Primary Use Case**: Mobile applications record user interactions with paywalls (views, clicks, conversions) for analytics and A/B test measurement.

## Request Flow Summary

```
Mobile App → Rate Limiter → Auth → Controller → Analytics Service → Database → A/B Test Manager → External APIs
```

## Data Entities Accessed

### Primary Write Entities

| Entity | Table | Purpose | Data Written |
|--------|-------|---------|--------------|
| `user_engagement` | `user_engagement` | Core interaction tracking | `paywall_id, user_id, interaction_type, device_type, ab_test_id, variation_id, additional_data, timestamp, session_id, ip_address, user_agent, app_id, platform` |
| `ab_test_conversions` | `ab_test_conversions` | A/B test conversion events | `test_id, variation_id, user_id, placement_id, device_type, revenue, conversion_timestamp, session_id` |
| `paywall_variations` | `paywall_variations` | A/B test variation metrics | Updates: `total_conversions, total_revenue, conversion_rate, avg_revenue_per_user, updated_at` |
| `paywall_metrics` | `paywall_metrics` | Real-time aggregated metrics | `paywall_id, metric_date, metric_hour, total_interactions, purchase_completions, revenue, unique_users, device_breakdown` |

### Analytics and Monitoring Entities

| Entity | Table | Purpose | Data Written |
|--------|-------|---------|--------------|
| `interaction_performance` | `interaction_performance` | API performance tracking | `endpoint, processing_time, db_write_time, queue_depth, timestamp, app_id` |
| `performance_alerts` | `performance_alerts` | System alerts | `alert_id, type, severity, message, test_id, variation_id, significance_level, recommended_actions, status, timestamp` |
| `adapty_sync_failures` | `adapty_sync_failures` | External sync failure tracking | `event_id, event_type, retry_count, error_message, next_retry, payload` |
| `data_quality_alerts` | `data_quality_alerts` | Data integrity issues | `alert_id, issue_type, severity, affected_records, detection_time, resolution_status` |

## Detailed Data Flow

### Phase 1: Request Validation

**Input Data Structure:**
```json
{
  "interaction_type": "purchase_complete",
  "user_id": "user_123",
  "device_type": "mobile",
  "ab_test_id": "test_456",
  "variation_id": "var_789", 
  "additional_data": {
    "revenue": 9.99,
    "product_id": "premium_monthly",
    "currency": "USD",
    "subscription_period": "monthly",
    "trial_period": 7,
    "source": "paywall_v2"
  }
}
```

**Validation Rules:**
- `interaction_type` ∈ ["view", "click", "close", "purchase_start", "purchase_complete"]
- `user_id` required and non-empty
- `additional_data` must be valid JSON object
- Rate limiting: 120 interactions per minute per app

### Phase 2: Core Analytics Recording

**Primary Interaction Insert:**
```sql
INSERT INTO user_engagement (
    paywall_id, user_id, interaction_type, device_type,
    ab_test_id, variation_id, additional_data, timestamp,
    session_id, ip_address, user_agent, app_id, platform,
    event_id, referrer, screen_resolution, app_version
) VALUES (
    ?, ?, ?, ?,
    ?, ?, ?, NOW(),
    ?, ?, ?, ?, ?,
    UUID(), ?, ?, ?
);
```

**Real-time Metrics Aggregation:**
```sql
INSERT INTO paywall_metrics (
    paywall_id, metric_date, metric_hour,
    total_interactions, purchase_completions, revenue,
    unique_users, device_breakdown, conversion_events
) VALUES (?, CURDATE(), HOUR(NOW()), 1, ?, ?, 1, ?, 1)
ON DUPLICATE KEY UPDATE
    total_interactions = total_interactions + 1,
    purchase_completions = purchase_completions + CASE 
        WHEN ? = 'purchase_complete' THEN 1 ELSE 0 END,
    revenue = revenue + COALESCE(JSON_EXTRACT(?, '$.revenue'), 0),
    unique_users = unique_users + CASE 
        WHEN user_first_interaction_today THEN 1 ELSE 0 END,
    device_breakdown = JSON_SET(device_breakdown, 
        CONCAT('$.', ?), 
        COALESCE(JSON_EXTRACT(device_breakdown, CONCAT('$.', ?)), 0) + 1),
    conversion_events = conversion_events + CASE 
        WHEN ? = 'purchase_complete' THEN 1 ELSE 0 END,
    last_updated = NOW();
```

### Phase 3: A/B Test Conversion Tracking

**Conversion Event Recording (for purchase_complete):**
```sql
INSERT INTO ab_test_conversions (
    test_id, variation_id, user_id, placement_id,
    device_type, revenue, currency, product_id,
    conversion_timestamp, session_id, user_segment,
    attribution_data, funnel_step, time_to_conversion
) VALUES (
    ?, ?, ?, ?,
    ?, ?, ?, ?,
    NOW(), ?, ?, ?, ?, ?
);
```

**Variation Metrics Update:**
```sql
UPDATE paywall_variations SET
    total_conversions = total_conversions + 1,
    total_revenue = total_revenue + ?,
    conversion_rate = (total_conversions + 1) / GREATEST(total_impressions, 1),
    avg_revenue_per_user = (total_revenue + ?) / (total_conversions + 1),
    last_conversion_at = NOW(),
    updated_at = NOW()
WHERE id = ? AND ab_test_id = ?;
```

**Statistical Significance Check:**
```sql
SELECT 
    v1.id as control_id,
    v1.total_impressions as control_impressions,
    v1.total_conversions as control_conversions,
    v1.conversion_rate as control_rate,
    v2.id as variant_id,
    v2.total_impressions as variant_impressions,
    v2.total_conversions as variant_conversions,
    v2.conversion_rate as variant_rate,
    -- Statistical calculations
    SQRT((v1.conversion_rate * (1 - v1.conversion_rate) / v1.total_impressions) +
         (v2.conversion_rate * (1 - v2.conversion_rate) / v2.total_impressions)) as standard_error,
    ABS(v2.conversion_rate - v1.conversion_rate) / 
    SQRT((v1.conversion_rate * (1 - v1.conversion_rate) / v1.total_impressions) +
         (v2.conversion_rate * (1 - v2.conversion_rate) / v2.total_impressions)) as z_score
FROM paywall_variations v1
JOIN paywall_variations v2 ON v1.ab_test_id = v2.ab_test_id
WHERE v1.ab_test_id = ? 
  AND v1.is_control = true 
  AND v2.is_control = false
  AND v1.total_impressions >= 100 
  AND v2.total_impressions >= 100;
```

### Phase 4: Performance Monitoring

**API Performance Metrics:**
```sql
INSERT INTO interaction_performance (
    endpoint, method, processing_time, db_write_time,
    queue_depth, error_count, timestamp, app_id,
    request_size, response_size, cache_hit_ratio,
    external_api_time, validation_time
) VALUES (
    '/mobile/v1/paywalls/{placementId}/interactions',
    'POST', ?, ?, ?, 0, NOW(), ?,
    ?, ?, ?, ?, ?
);
```

**Alert Generation (when thresholds exceeded):**
```sql
INSERT INTO performance_alerts (
    alert_id, type, severity, message, source_entity,
    current_value, threshold_value, affected_metrics,
    recommended_actions, status, created_at, app_id
) VALUES (
    CONCAT('perf_', UNIX_TIMESTAMP(), '_', ?),
    'high_response_time', -- or 'high_error_rate', 'test_significance'
    'warning', -- or 'critical', 'info'
    ?, -- Generated message
    'mobile_interaction_api',
    ?, ?, -- current_value, threshold_value
    JSON_ARRAY('response_time', 'error_rate'),
    JSON_ARRAY('Scale database', 'Optimize queries', 'Check external dependencies'),
    'active',
    NOW(),
    ?
);
```

### Phase 5: External API Synchronization

**Adapty Event Sync:**
```json
{
  "event_type": "purchase_completed",
  "user_id": "user_123",
  "placement_id": "premium_paywall_ios",
  "variation_id": "var_789",
  "revenue": {
    "amount": 9.99,
    "currency": "USD"
  },
  "product": {
    "id": "premium_monthly",
    "type": "subscription",
    "period": "monthly"
  },
  "attribution": {
    "ab_test_id": "test_456",
    "variation_name": "high_price_variant",
    "source": "mobile_app"
  },
  "metadata": {
    "device_type": "mobile",
    "app_version": "1.2.0",
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

**Sync Failure Tracking:**
```sql
INSERT INTO adapty_sync_failures (
    event_id, event_type, user_id, placement_id,
    retry_count, max_retries, error_message,
    next_retry_at, created_at, payload, status
) VALUES (
    ?, 'purchase_completed', ?, ?,
    0, 3, ?,
    DATE_ADD(NOW(), INTERVAL 5 MINUTE),
    NOW(), ?, 'pending'
);
```

## Response Structure

**Success Response:**
```json
{
  "success": true,
  "message": "Interaction recorded successfully",
  "data": {
    "interaction_id": "int_1642234200_abc123",
    "timestamp": "2024-01-15T10:30:00Z",
    "processing_time_ms": 45
  },
  "meta": {
    "ab_test_recorded": true,
    "metrics_updated": true,
    "external_sync_status": "pending"
  }
}
```

## Asynchronous Processing

### Background Jobs Triggered

1. **Metrics Aggregation Job** (Every 5 minutes):
```sql
-- Hourly rollups
INSERT INTO paywall_hourly_metrics 
SELECT 
    paywall_id,
    DATE_FORMAT(timestamp, '%Y-%m-%d %H:00:00') as hour_bucket,
    COUNT(*) as total_interactions,
    COUNT(DISTINCT user_id) as unique_users,
    SUM(CASE WHEN interaction_type = 'purchase_complete' THEN 1 ELSE 0 END) as conversions,
    SUM(CASE WHEN interaction_type = 'purchase_complete' 
        THEN JSON_EXTRACT(additional_data, '$.revenue') ELSE 0 END) as revenue
FROM user_engagement 
WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
GROUP BY paywall_id, hour_bucket;
```

2. **User Journey Reconstruction** (Every 15 minutes):
```sql
-- Reconstruct user journeys
INSERT INTO user_journeys
SELECT 
    user_id,
    paywall_id,
    MIN(timestamp) as first_interaction,
    MAX(timestamp) as last_interaction,
    COUNT(*) as total_interactions,
    GROUP_CONCAT(interaction_type ORDER BY timestamp) as interaction_sequence,
    MAX(CASE WHEN interaction_type = 'purchase_complete' THEN 1 ELSE 0 END) as converted
FROM user_engagement
WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 15 MINUTE)
GROUP BY user_id, paywall_id;
```

3. **A/B Test Statistical Analysis** (Every 10 minutes):
- Calculate confidence intervals
- Update statistical significance
- Trigger winner recommendations
- Generate test conclusion alerts

### Data Quality Validation

**Duplicate Detection:**
```sql
-- Find potential duplicates
SELECT user_id, paywall_id, interaction_type, 
       COUNT(*) as duplicate_count,
       GROUP_CONCAT(id) as interaction_ids
FROM user_engagement 
WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
GROUP BY user_id, paywall_id, interaction_type, 
         DATE_FORMAT(timestamp, '%Y-%m-%d %H:%i')
HAVING COUNT(*) > 1;
```

**Data Consistency Checks:**
```sql
-- Verify A/B test data consistency
SELECT ab_test_id, variation_id, 
       COUNT(DISTINCT user_id) as unique_users,
       COUNT(*) as total_interactions
FROM user_engagement 
WHERE ab_test_id IS NOT NULL 
  AND timestamp >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
GROUP BY ab_test_id, variation_id;
```

## Performance Characteristics

### Expected Performance
- **Write Latency**: < 100ms (p95)
- **Processing Time**: < 50ms (p95)
- **Throughput**: 1000+ interactions/second
- **External Sync**: < 500ms (when enabled)

### Database Impact
- **Primary Writes**: 1-3 INSERT operations
- **Aggregation Updates**: 1-2 UPDATE operations
- **Background Jobs**: Periodic batch processing
- **Index Requirements**: 
  - `(user_id, timestamp)` for user journeys
  - `(paywall_id, timestamp)` for metrics
  - `(ab_test_id, variation_id)` for A/B tests

### Scaling Considerations
- **Write Scaling**: Partition by date/hour
- **Read Scaling**: Separate analytics read replicas
- **Queue Processing**: Redis/SQS for async operations
- **External API**: Circuit breaker patterns

## Error Handling

| Error Scenario | HTTP Code | Response | Data Impact |
|----------------|-----------|----------|-------------|
| Invalid interaction type | 400 | `{"error": "Invalid interaction_type"}` | No data written |
| Missing user_id | 400 | `{"error": "user_id required"}` | No data written |
| Rate limit exceeded | 429 | `{"error": "Rate limit exceeded"}` | Request blocked |
| Database write failure | 500 | `{"error": "Failed to record interaction"}` | Retry mechanism triggered |
| A/B test update failure | 500 | Partial success response | Core interaction saved, A/B metrics may be inconsistent |
| External API timeout | 200 | Success with sync_status: "failed" | Local data saved, sync queued for retry |

## Monitoring & Alerting

### Key Metrics
- **Interaction Volume**: Interactions per second/minute/hour
- **Conversion Rates**: By paywall, A/B test, device type
- **Processing Performance**: Response times, error rates
- **Data Quality**: Duplicate rates, consistency scores
- **External Sync**: Success rates, retry queues

### Alert Conditions
- Processing time > 100ms (p95)
- Error rate > 1% over 5 minutes
- Duplicate interaction rate > 0.1%
- External sync failure rate > 5%
- A/B test sample size anomalies
