# Database Schema Documentation

## Overview

This document provides a comprehensive overview of the Strapi Adapty CMS database schema, including all content types, their relationships, field descriptions, constraints, and indexing strategies.

## Core Architecture

The database follows a Strapi v5 schema structure with the following key architectural patterns:

- **Content Types**: Main entity collections
- **Components**: Reusable field groups (shared components)
- **Relations**: Foreign key relationships between entities
- **Internationalization (i18n)**: Multi-language support
- **Draft & Publish**: Content lifecycle management

## Entity Relationship Overview

The schema is organized around several core domain areas:

1. **Paywall Management**: Core paywall configuration and content
2. **A/B Testing**: Experiment configuration and variation management
3. **Analytics & Metrics**: Performance tracking and analytics data
4. **Mobile Integration**: Mobile app registration and API usage
5. **Content Management**: Articles, authors, and categories
6. **System Operations**: Health monitoring, deployment logs, and sync operations
7. **Localization**: Translation management and assignments

---

## Content Types

### 1. Paywall (paywalls)

**Purpose**: Core paywall configuration for Adapty integration

**Collection Name**: `paywalls`
**Draft & Publish**: ✅ Enabled
**Internationalization**: ✅ Enabled

#### Fields

| Field | Type | Required | Constraints | Description | i18n |
|-------|------|----------|-------------|-------------|------|
| `name` | string | ✅ | maxLength: 255 | Paywall identifier name | ❌ |
| `description` | text | ❌ | - | Paywall description | ✅ |
| `placement_id` | string | ✅ | maxLength: 100, unique | Adapty placement identifier | ❌ |
| `status` | enumeration | ❌ | enum: [draft, review, approved, published, archived] | Paywall lifecycle status | ❌ |
| `title` | string | ✅ | maxLength: 255 | Display title | ✅ |
| `subtitle` | string | ❌ | maxLength: 500 | Display subtitle | ✅ |
| `description_text` | text | ❌ | - | Main description text | ✅ |
| `cta_text` | string | ✅ | maxLength: 100 | Call-to-action button text | ✅ |
| `cta_secondary_text` | string | ❌ | maxLength: 200 | Secondary CTA text | ✅ |
| `theme` | component | ✅ | component: shared.theme | Theme configuration | ❌ |
| `features` | component | ❌ | repeatable, component: shared.feature | Feature list | ❌ |
| `testimonials` | component | ❌ | repeatable, component: shared.testimonial | Customer testimonials | ❌ |
| `product_labels` | component | ❌ | repeatable, component: shared.product-label | Product badges | ❌ |
| `adapty_sync_status` | enumeration | ❌ | enum: [pending, synced, error] | Sync status with Adapty | ❌ |
| `adapty_last_sync` | datetime | ❌ | - | Last successful sync timestamp | ❌ |
| `adapty_remote_config_id` | string | ❌ | maxLength: 100 | Adapty remote config ID | ❌ |

#### Relations

- `remote_config_versions` → `remote-config-version` (oneToMany, mappedBy: paywall)

#### Indexing Strategy

- **Primary Index**: `id` (auto)
- **Unique Index**: `placement_id`
- **Composite Index**: `status`, `adapty_sync_status`
- **Localized Index**: `locale`

---

### 2. Paywall Variation (paywall_variations)

**Purpose**: Individual variations for A/B testing with performance tracking

**Collection Name**: `paywall_variations`
**Draft & Publish**: ✅ Enabled
**Internationalization**: ✅ Enabled

#### Fields

| Field | Type | Required | Constraints | Description | i18n |
|-------|------|----------|-------------|-------------|------|
| `name` | string | ✅ | maxLength: 255 | Variation name | ❌ |
| `description` | text | ❌ | - | Variation description | ✅ |
| `variation_type` | enumeration | ✅ | enum: [control, variant, challenger] | Type of variation | ❌ |
| `traffic_percentage` | decimal | ✅ | min: 0.1, max: 100.0, default: 50.0 | Traffic allocation percentage | ❌ |
| `title_override` | string | ❌ | maxLength: 255 | Override title | ✅ |
| `subtitle_override` | string | ❌ | maxLength: 500 | Override subtitle | ✅ |
| `description_override` | text | ❌ | - | Override description | ✅ |
| `cta_text_override` | string | ❌ | maxLength: 100 | Override CTA text | ✅ |
| `cta_secondary_text_override` | string | ❌ | maxLength: 200 | Override secondary CTA text | ✅ |
| `theme_override` | component | ❌ | component: shared.theme | Theme override | ❌ |
| `features_override` | component | ❌ | repeatable, component: shared.feature | Feature list override | ❌ |
| `testimonials_override` | component | ❌ | repeatable, component: shared.testimonial | Testimonials override | ❌ |
| `product_labels_override` | component | ❌ | repeatable, component: shared.product-label | Product labels override | ❌ |
| `pricing_override` | json | ❌ | - | Pricing configuration override | ❌ |
| `custom_properties` | json | ❌ | - | Custom properties | ❌ |
| `performance_metrics` | json | ❌ | - | Performance metrics data | ❌ |
| `conversion_rate` | decimal | ❌ | min: 0.0, max: 100.0 | Conversion rate percentage | ❌ |
| `revenue_per_user` | decimal | ❌ | min: 0.0 | Revenue per user | ❌ |
| `total_impressions` | biginteger | ❌ | min: 0 | Total impressions count | ❌ |
| `total_conversions` | biginteger | ❌ | min: 0 | Total conversions count | ❌ |
| `total_revenue` | decimal | ❌ | min: 0.0 | Total revenue generated | ❌ |
| `unique_users` | biginteger | ❌ | min: 0 | Unique users count | ❌ |
| `confidence_interval_lower` | decimal | ❌ | - | Statistical confidence interval lower bound | ❌ |
| `confidence_interval_upper` | decimal | ❌ | - | Statistical confidence interval upper bound | ❌ |
| `statistical_significance` | decimal | ❌ | min: 0.0, max: 100.0 | Statistical significance percentage | ❌ |
| `is_winner` | boolean | ❌ | default: false | Whether this variation is the winner | ❌ |
| `promoted_at` | datetime | ❌ | - | Timestamp when promoted to winner | ❌ |
| `adapty_variation_id` | string | ❌ | maxLength: 100 | Adapty variation identifier | ❌ |
| `adapty_remote_config_id` | string | ❌ | maxLength: 100 | Adapty remote config ID | ❌ |
| `last_metrics_update` | datetime | ❌ | - | Last metrics update timestamp | ❌ |
| `notes` | text | ❌ | - | Additional notes | ✅ |

#### Relations

- `ab_test` → `ab-test` (manyToOne, inversedBy: variations)
- `base_paywall` → `paywall` (manyToOne)

#### Indexing Strategy

- **Primary Index**: `id` (auto)
- **Composite Index**: `ab_test`, `variation_type`
- **Performance Index**: `is_winner`, `statistical_significance`
- **Adapty Index**: `adapty_variation_id`

---

### 3. A/B Test (ab_tests)

**Purpose**: A/B test configuration with metrics and audience targeting

**Collection Name**: `ab_tests`
**Draft & Publish**: ✅ Enabled
**Internationalization**: ✅ Enabled

#### Fields

| Field | Type | Required | Constraints | Description | i18n |
|-------|------|----------|-------------|-------------|------|
| `name` | string | ✅ | maxLength: 255 | Test name | ❌ |
| `description` | text | ❌ | - | Test description | ✅ |
| `hypothesis` | text | ✅ | - | Test hypothesis | ✅ |
| `status` | enumeration | ✅ | enum: [draft, scheduled, running, paused, completed, cancelled] | Test status | ❌ |
| `test_type` | enumeration | ✅ | enum: [paywall_content, pricing, theme, features, full_paywall] | Type of test | ❌ |
| `primary_metric` | enumeration | ✅ | enum: [conversion_rate, revenue_per_user, trial_conversion, retention_rate, ltv] | Primary success metric | ❌ |
| `secondary_metrics` | json | ❌ | - | Secondary metrics configuration | ❌ |
| `start_date` | datetime | ✅ | - | Test start date | ❌ |
| `end_date` | datetime | ❌ | - | Test end date | ❌ |
| `duration_days` | integer | ❌ | min: 1, max: 365 | Test duration in days | ❌ |
| `traffic_allocation` | decimal | ✅ | min: 0.1, max: 100.0, default: 100.0 | Traffic allocation percentage | ❌ |
| `minimum_sample_size` | integer | ✅ | min: 100, default: 1000 | Minimum sample size | ❌ |
| `confidence_level` | decimal | ✅ | min: 80.0, max: 99.9, default: 95.0 | Statistical confidence level | ❌ |
| `statistical_power` | decimal | ✅ | min: 50.0, max: 99.0, default: 80.0 | Statistical power | ❌ |
| `minimum_detectable_effect` | decimal | ✅ | min: 0.1, max: 100.0, default: 5.0 | Minimum detectable effect | ❌ |
| `audience_targeting` | json | ❌ | - | Audience targeting configuration | ❌ |
| `geographic_targeting` | json | ❌ | - | Geographic targeting | ❌ |
| `device_targeting` | json | ❌ | - | Device targeting | ❌ |
| `user_segment_filters` | json | ❌ | - | User segment filters | ❌ |
| `winner_selected_at` | datetime | ❌ | - | Winner selection timestamp | ❌ |
| `winner_selection_method` | enumeration | ❌ | enum: [manual, automatic, scheduled] | How winner was selected | ❌ |
| `auto_promote_winner` | boolean | ❌ | default: false | Auto-promote winner flag | ❌ |
| `current_results` | json | ❌ | - | Current test results | ❌ |
| `statistical_significance` | decimal | ❌ | min: 0.0, max: 100.0 | Statistical significance | ❌ |
| `p_value` | decimal | ❌ | min: 0.0, max: 1.0 | P-value | ❌ |
| `effect_size` | decimal | ❌ | - | Effect size | ❌ |
| `confidence_interval` | json | ❌ | - | Confidence interval data | ❌ |
| `adapty_test_id` | string | ❌ | maxLength: 100 | Adapty test identifier | ❌ |
| `adapty_sync_status` | enumeration | ❌ | enum: [pending, synced, error] | Adapty sync status | ❌ |
| `adapty_last_sync` | datetime | ❌ | - | Last Adapty sync timestamp | ❌ |
| `created_by_user` | string | ❌ | - | User who created the test | ❌ |
| `notes` | text | ❌ | - | Additional notes | ✅ |

#### Relations

- `variations` → `paywall-variation` (oneToMany, mappedBy: ab_test)
- `winner_variation` → `paywall-variation` (oneToOne)

#### Indexing Strategy

- **Primary Index**: `id` (auto)
- **Status Index**: `status`
- **Date Range Index**: `start_date`, `end_date`
- **Adapty Index**: `adapty_test_id`
- **Performance Index**: `statistical_significance`

---

### 4. Mobile App (mobile_apps)

**Purpose**: Mobile application registrations for API access

**Collection Name**: `mobile_apps`
**Draft & Publish**: ❌ Disabled
**Internationalization**: ❌ Disabled

#### Fields

| Field | Type | Required | Constraints | Description |
|-------|------|----------|-------------|-------------|
| `app_id` | string | ✅ | maxLength: 50, unique | Application identifier |
| `name` | string | ✅ | maxLength: 100 | Application name |
| `platform` | enumeration | ✅ | enum: [ios, android, react_native] | Platform type |
| `bundle_id` | string | ✅ | maxLength: 200 | Bundle/package identifier |
| `api_key` | string | ✅ | maxLength: 100, unique | API access key |
| `secret_key` | string | ✅ | maxLength: 200 | Secret key for authentication |
| `min_version` | string | ✅ | maxLength: 20 | Minimum supported version |
| `max_version` | string | ❌ | maxLength: 20 | Maximum supported version |
| `permissions` | json | ✅ | default: ["paywall:read"] | API permissions |
| `rate_limits` | json | ✅ | default: {requests_per_minute: 60, burst_limit: 100} | Rate limiting configuration |
| `is_active` | boolean | ✅ | default: true | Active status |
| `description` | text | ❌ | - | Application description |
| `webhook_url` | string | ❌ | maxLength: 500 | Webhook endpoint URL |
| `webhook_secret` | string | ❌ | maxLength: 100 | Webhook secret |
| `last_used` | datetime | ❌ | - | Last API usage timestamp |
| `usage_stats` | json | ❌ | default: {total_requests: 0, last_24h_requests: 0, error_rate: 0} | Usage statistics |

#### Indexing Strategy

- **Primary Index**: `id` (auto)
- **Unique Indexes**: `app_id`, `api_key`
- **Platform Index**: `platform`
- **Active Index**: `is_active`

---

### 5. Analytics (analytics)

**Purpose**: General analytics data storage for various metrics and events

**Collection Name**: `analytics`
**Draft & Publish**: ❌ Disabled
**Internationalization**: ❌ Disabled

#### Fields

| Field | Type | Required | Constraints | Description |
|-------|------|----------|-------------|-------------|
| `event_type` | enumeration | ✅ | enum: [paywall_impression, user_engagement, conversion, interaction, page_view, session_start, session_end] | Type of analytics event |
| `paywall_id` | string | ❌ | - | Related paywall ID |
| `user_id` | string | ❌ | - | User identifier |
| `session_id` | string | ❌ | - | Session identifier |
| `placement_id` | string | ❌ | - | Placement identifier |
| `app_id` | string | ❌ | - | Mobile app identifier |
| `platform` | enumeration | ✅ | enum: [ios, android, web] | Platform |
| `device_type` | enumeration | ✅ | enum: [mobile, tablet, desktop] | Device type |
| `region` | string | ✅ | - | Geographic region |
| `locale` | string | ✅ | default: "en" | Locale code |
| `timestamp` | datetime | ✅ | - | Event timestamp |
| `metadata` | json | ❌ | - | Additional event metadata |
| `value` | decimal | ❌ | default: 0 | Event value |
| `count` | integer | ❌ | default: 1 | Event count |

#### Indexing Strategy

- **Primary Index**: `id` (auto)
- **Event Index**: `event_type`
- **Time Series Index**: `timestamp`
- **Composite Index**: `paywall_id`, `event_type`, `timestamp`
- **Platform Index**: `platform`, `device_type`
- **Region Index**: `region`, `locale`

---

### 6. Paywall Metrics (paywall_metrics)

**Purpose**: Analytics data for paywall performance tracking

**Collection Name**: `paywall_metrics`
**Draft & Publish**: ❌ Disabled
**Internationalization**: ❌ Disabled

#### Fields

| Field | Type | Required | Constraints | Description |
|-------|------|----------|-------------|-------------|
| `paywallId` | string | ✅ | - | Paywall identifier |
| `timestamp` | datetime | ✅ | - | Metrics timestamp |
| `impressions` | integer | ✅ | default: 0 | Number of impressions |
| `conversions` | integer | ✅ | default: 0 | Number of conversions |
| `conversionRate` | decimal | ✅ | default: 0 | Conversion rate |
| `revenue` | decimal | ✅ | default: 0 | Revenue generated |
| `averageRevenuePerUser` | decimal | ✅ | default: 0 | ARPU |
| `bounceRate` | decimal | ✅ | default: 0 | Bounce rate |
| `timeToConversion` | integer | ✅ | default: 0 | Time to conversion (seconds) |
| `userSegment` | string | ✅ | - | User segment |
| `region` | string | ✅ | - | Geographic region |
| `locale` | string | ✅ | - | Locale code |
| `deviceType` | enumeration | ✅ | enum: [mobile, tablet, desktop] | Device type |
| `platform` | enumeration | ✅ | enum: [ios, android, web] | Platform |

#### Indexing Strategy

- **Primary Index**: `id` (auto)
- **Time Series Index**: `paywallId`, `timestamp`
- **Segment Index**: `userSegment`
- **Platform Index**: `platform`, `deviceType`
- **Region Index**: `region`, `locale`

---

### 7. System Health (system_health)

**Purpose**: System health monitoring metrics

**Collection Name**: `system_health`
**Draft & Publish**: ❌ Disabled
**Internationalization**: ❌ Disabled

#### Fields

| Field | Type | Required | Constraints | Description |
|-------|------|----------|-------------|-------------|
| `timestamp` | datetime | ✅ | - | Health check timestamp |
| `uptime` | biginteger | ✅ | default: 0 | System uptime (milliseconds) |
| `memoryUsed` | biginteger | ✅ | default: 0 | Memory used (bytes) |
| `memoryTotal` | biginteger | ✅ | default: 0 | Total memory (bytes) |
| `memoryPercentage` | decimal | ✅ | default: 0 | Memory usage percentage |
| `cpuUsage` | decimal | ✅ | default: 0 | CPU usage percentage |
| `responseTime` | integer | ✅ | default: 0 | Response time (milliseconds) |
| `activeConnections` | integer | ✅ | default: 0 | Active connections count |
| `errorRate` | decimal | ✅ | default: 0 | Error rate percentage |
| `status` | enumeration | ✅ | enum: [healthy, warning, critical, down], default: "healthy" | System status |

#### Indexing Strategy

- **Primary Index**: `id` (auto)
- **Time Series Index**: `timestamp`
- **Status Index**: `status`
- **Performance Index**: `cpuUsage`, `memoryPercentage`

---

### 8. Product (products)

**Purpose**: Products synced from Adapty for paywall configuration

**Collection Name**: `products`
**Draft & Publish**: ❌ Disabled
**Internationalization**: ❌ Disabled

#### Fields

| Field | Type | Required | Constraints | Description |
|-------|------|----------|-------------|-------------|
| `adapty_product_id` | string | ✅ | maxLength: 100, unique | Adapty product identifier |
| `vendor_product_id` | string | ✅ | maxLength: 255 | Vendor (App Store/Play Store) product ID |
| `name` | string | ✅ | maxLength: 255 | Product name |
| `type` | enumeration | ✅ | enum: [subscription, consumable, non_consumable] | Product type |
| `store` | enumeration | ✅ | enum: [app_store, play_store, stripe] | Store platform |
| `price_amount` | decimal | ❌ | - | Price amount |
| `price_currency` | string | ❌ | maxLength: 3 | Currency code (ISO 4217) |
| `price_localized` | string | ❌ | maxLength: 50 | Localized price string |
| `subscription_period_unit` | enumeration | ❌ | enum: [day, week, month, year] | Subscription period unit |
| `subscription_period_count` | integer | ❌ | min: 1 | Subscription period count |
| `last_synced` | datetime | ❌ | - | Last sync timestamp |
| `is_active` | boolean | ❌ | default: true | Active status |
| `metadata` | json | ❌ | - | Additional product metadata |

#### Indexing Strategy

- **Primary Index**: `id` (auto)
- **Unique Index**: `adapty_product_id`
- **Store Index**: `store`
- **Type Index**: `type`
- **Active Index**: `is_active`

---

### 9. Remote Config Version (remote_config_versions)

**Purpose**: Track versions of remote configurations for Adapty deployment

**Collection Name**: `remote_config_versions`
**Draft & Publish**: ❌ Disabled
**Internationalization**: ❌ Disabled

#### Fields

| Field | Type | Required | Constraints | Description |
|-------|------|----------|-------------|-------------|
| `version_id` | string | ✅ | unique | Version identifier |
| `version_number` | string | ✅ | - | Version number |
| `config_data` | json | ✅ | - | Configuration data |
| `status` | enumeration | ✅ | enum: [draft, deployed, rolled_back], default: "draft" | Version status |
| `deployment_notes` | text | ❌ | - | Deployment notes |
| `created_by` | string | ✅ | - | Creator user ID |
| `deployed_at` | datetime | ❌ | - | Deployment timestamp |
| `placement_id` | string | ✅ | - | Placement identifier |
| `locale` | string | ❌ | default: "en" | Locale code |

#### Relations

- `paywall` → `paywall` (manyToOne, inversedBy: remote_config_versions)

#### Indexing Strategy

- **Primary Index**: `id` (auto)
- **Unique Index**: `version_id`
- **Status Index**: `status`
- **Placement Index**: `placement_id`
- **Foreign Key Index**: `paywall`

---

## Shared Components

### 1. Theme (components_shared_themes)

**Purpose**: Theme configuration for paywall appearance and styling

#### Fields

| Field | Type | Required | Constraints | Description |
|-------|------|----------|-------------|-------------|
| `name` | string | ✅ | maxLength: 255 | Theme name |
| `primary_color` | string | ✅ | regex: ^#([A-Fa-f0-9]{6}\|[A-Fa-f0-9]{3})$ | Primary color (hex) |
| `background_color` | string | ✅ | regex: ^#([A-Fa-f0-9]{6}\|[A-Fa-f0-9]{3})$ | Background color (hex) |
| `text_color` | string | ✅ | regex: ^#([A-Fa-f0-9]{6}\|[A-Fa-f0-9]{3})$ | Text color (hex) |
| `button_style` | enumeration | ❌ | enum: [rounded, square], default: "rounded" | Button style |
| `gradient_colors` | json | ❌ | - | Gradient color configuration |
| `header_style` | enumeration | ❌ | enum: [minimal, hero, gradient], default: "hero" | Header style |
| `product_display_style` | enumeration | ❌ | enum: [list, grid, carousel], default: "list" | Product display style |
| `show_features` | boolean | ❌ | default: true | Show features flag |
| `show_testimonials` | boolean | ❌ | default: false | Show testimonials flag |
| `custom_styles` | text | ❌ | - | Custom CSS styles |
| `background_image` | media | ❌ | allowedTypes: [images] | Background image |
| `logo` | media | ❌ | allowedTypes: [images] | Logo image |

### 2. Feature (components_shared_features)

**Purpose**: Feature item for paywall feature lists
**Internationalization**: ✅ Enabled

#### Fields

| Field | Type | Required | Constraints | Description | i18n |
|-------|------|----------|-------------|-------------|------|
| `icon` | string | ✅ | maxLength: 100 | Icon identifier | ❌ |
| `title` | string | ✅ | maxLength: 255 | Feature title | ✅ |
| `description` | text | ✅ | - | Feature description | ✅ |
| `order` | integer | ✅ | default: 0 | Display order | ❌ |
| `is_highlighted` | boolean | ❌ | default: false | Highlight flag | ❌ |
| `icon_image` | media | ❌ | allowedTypes: [images] | Icon image | ❌ |

### 3. Testimonial (components_shared_testimonials)

**Purpose**: Customer testimonial for paywall social proof
**Internationalization**: ✅ Enabled

#### Fields

| Field | Type | Required | Constraints | Description | i18n |
|-------|------|----------|-------------|-------------|------|
| `author_name` | string | ✅ | maxLength: 255 | Author name | ✅ |
| `author_title` | string | ❌ | maxLength: 255 | Author title/position | ✅ |
| `content` | text | ✅ | - | Testimonial content | ✅ |
| `rating` | integer | ❌ | min: 1, max: 5 | Star rating | ❌ |
| `author_avatar` | media | ❌ | allowedTypes: [images] | Author avatar | ❌ |
| `company` | string | ❌ | maxLength: 255 | Company name | ❌ |
| `order` | integer | ✅ | default: 0 | Display order | ❌ |

### 4. Product Label (components_shared_product_labels)

**Purpose**: Custom product badges and highlights for paywall products
**Internationalization**: ✅ Enabled

#### Fields

| Field | Type | Required | Constraints | Description | i18n |
|-------|------|----------|-------------|-------------|------|
| `product_id` | string | ✅ | maxLength: 100 | Product identifier | ❌ |
| `badge_text` | string | ❌ | maxLength: 50 | Badge text | ✅ |
| `badge_color` | string | ❌ | regex: ^#([A-Fa-f0-9]{6}\|[A-Fa-f0-9]{3})$ | Badge color (hex) | ❌ |
| `subtitle` | string | ❌ | maxLength: 255 | Product subtitle | ✅ |
| `highlight` | boolean | ❌ | default: false | Highlight flag | ❌ |
| `savings_percentage` | integer | ❌ | min: 0, max: 100 | Savings percentage | ❌ |
| `badge_position` | enumeration | ❌ | enum: [top-left, top-right, bottom-left, bottom-right], default: "top-right" | Badge position | ❌ |

---

## Content Management Entities

### Article (articles)

**Purpose**: Blog content management
**Collection Name**: `articles`
**Draft & Publish**: ✅ Enabled

#### Fields

| Field | Type | Required | Constraints | Description |
|-------|------|----------|-------------|-------------|
| `title` | string | ❌ | - | Article title |
| `description` | text | ❌ | maxLength: 80 | Short description |
| `slug` | uid | ❌ | targetField: "title" | URL slug |
| `cover` | media | ❌ | allowedTypes: [images, files, videos] | Cover image |
| `blocks` | dynamiczone | ❌ | components: [shared.media, shared.quote, shared.rich-text, shared.slider] | Article content blocks |

#### Relations

- `author` → `author` (manyToOne, inversedBy: articles)
- `category` → `category` (manyToOne, inversedBy: articles)

### Author (authors)

**Purpose**: Content authors
**Collection Name**: `authors`
**Draft & Publish**: ❌ Disabled

#### Fields

| Field | Type | Required | Constraints | Description |
|-------|------|----------|-------------|-------------|
| `name` | string | ❌ | - | Author name |
| `avatar` | media | ❌ | allowedTypes: [images, files, videos] | Author avatar |
| `email` | string | ❌ | - | Author email |

#### Relations

- `articles` → `article` (oneToMany, mappedBy: author)

---

## Monitoring & Operations

### Performance Alert (performance_alerts)

**Purpose**: Automated performance alerts from Adapty analytics integration
**Collection Name**: `performance_alerts`
**Draft & Publish**: ❌ Disabled

#### Fields

| Field | Type | Required | Constraints | Description |
|-------|------|----------|-------------|-------------|
| `alertId` | string | ✅ | unique | Alert identifier |
| `type` | enumeration | ✅ | enum: [conversion_drop, revenue_spike, revenue_drop, churn_increase, trial_conversion_drop] | Alert type |
| `severity` | enumeration | ✅ | enum: [low, medium, high, critical] | Alert severity |
| `message` | text | ✅ | - | Alert message |
| `currentValue` | decimal | ✅ | - | Current metric value |
| `previousValue` | decimal | ✅ | - | Previous metric value |
| `threshold` | decimal | ✅ | - | Alert threshold |
| `timestamp` | datetime | ✅ | - | Alert timestamp |
| `affectedMetrics` | json | ✅ | - | Affected metrics data |
| `recommendedActions` | json | ✅ | - | Recommended actions |
| `status` | enumeration | ✅ | enum: [active, acknowledged, resolved, dismissed], default: "active" | Alert status |
| `acknowledgedBy` | string | ❌ | - | User who acknowledged |
| `acknowledgedAt` | datetime | ❌ | - | Acknowledgment timestamp |
| `resolvedBy` | string | ❌ | - | User who resolved |
| `resolvedAt` | datetime | ❌ | - | Resolution timestamp |
| `notes` | text | ❌ | - | Additional notes |

---

## Localization Management

### Translation Assignment (translation_assignments)

**Purpose**: Manages translation assignments and progress tracking
**Collection Name**: `translation_assignments`
**Draft & Publish**: ❌ Disabled

#### Key Features

- **Workflow Management**: Tracks translation status from pending to approved
- **Quality Control**: Includes reviewer assignments and quality scoring
- **Progress Tracking**: Word count estimation and completion percentage
- **Translation Memory**: Integration with translation memory systems

#### Fields

| Field | Type | Required | Constraints | Description |
|-------|------|----------|-------------|-------------|
| `content_type` | string | ✅ | maxLength: 255 | Content type being translated |
| `entity_id` | integer | ✅ | - | Entity ID being translated |
| `source_locale` | string | ✅ | maxLength: 10 | Source language locale |
| `target_locale` | string | ✅ | maxLength: 10 | Target language locale |
| `status` | enumeration | ❌ | enum: [pending, assigned, in_progress, completed, reviewed, approved, rejected], default: "pending" | Assignment status |
| `priority` | enumeration | ❌ | enum: [low, medium, high, urgent], default: "medium" | Assignment priority |
| `deadline` | datetime | ❌ | - | Translation deadline |
| `estimated_words` | integer | ❌ | min: 0 | Estimated word count |
| `actual_words` | integer | ❌ | min: 0 | Actual word count |
| `progress_percentage` | integer | ❌ | min: 0, max: 100, default: 0 | Completion percentage |
| `fields_to_translate` | json | ❌ | - | Fields requiring translation |
| `completed_fields` | json | ❌ | - | Completed fields |
| `notes` | text | ❌ | - | Translator notes |
| `reviewer_notes` | text | ❌ | - | Reviewer notes |
| `quality_score` | integer | ❌ | min: 1, max: 10 | Quality score (1-10) |
| `started_at` | datetime | ❌ | - | Work start timestamp |
| `completed_at` | datetime | ❌ | - | Completion timestamp |
| `reviewed_at` | datetime | ❌ | - | Review timestamp |
| `approved_at` | datetime | ❌ | - | Approval timestamp |
| `translation_memory_matches` | json | ❌ | - | Translation memory matches |
| `external_service_data` | json | ❌ | - | External service integration data |

#### Relations

- `assigned_translator` → `admin::user` (manyToOne)
- `assigned_reviewer` → `admin::user` (manyToOne)

---

## Database Optimization & Indexing Strategy

### Primary Indexes

All tables have auto-generated primary key indexes on the `id` field.

### Unique Constraints

- `paywalls.placement_id`
- `mobile_apps.app_id`
- `mobile_apps.api_key`
- `products.adapty_product_id`
- `remote_config_versions.version_id`
- `performance_alerts.alertId`

### Composite Indexes

#### Performance-Critical Queries

```sql
-- Paywall status and sync tracking
CREATE INDEX idx_paywall_status_sync ON paywalls(status, adapty_sync_status);

-- A/B test variations by test and type
CREATE INDEX idx_variation_test_type ON paywall_variations(ab_test, variation_type);

-- Analytics time-series queries
CREATE INDEX idx_analytics_paywall_time ON analytics(paywall_id, event_type, timestamp);
CREATE INDEX idx_metrics_paywall_time ON paywall_metrics(paywallId, timestamp);

-- System health monitoring
CREATE INDEX idx_health_time_status ON system_health(timestamp, status);

-- Mobile API usage tracking
CREATE INDEX idx_mobile_usage_app_time ON mobile_api_usages(app_id, timestamp);
```

#### Localization Indexes

```sql
-- All i18n-enabled content types need locale indexes
CREATE INDEX idx_paywall_locale ON paywalls(locale);
CREATE INDEX idx_paywall_variation_locale ON paywall_variations(locale);
CREATE INDEX idx_ab_test_locale ON ab_tests(locale);
```

#### Translation Management

```sql
-- Translation assignment workflow tracking
CREATE INDEX idx_translation_status ON translation_assignments(status);
CREATE INDEX idx_translation_locale_pair ON translation_assignments(source_locale, target_locale);
CREATE INDEX idx_translation_deadline ON translation_assignments(deadline);
```

### Time-Series Optimizations

#### Partitioning Strategy (Recommended)

For high-volume time-series tables, consider monthly partitioning:

- `analytics` - partition by timestamp (monthly)
- `paywall_metrics` - partition by timestamp (monthly)
- `cache_metrics` - partition by timestamp (monthly)
- `system_health` - partition by timestamp (weekly)
- `api_performance` - partition by timestamp (weekly)
- `mobile_api_usages` - partition by timestamp (monthly)

#### Data Retention Policies

```sql
-- Example retention policies
-- Analytics: 2 years
-- Metrics: 1 year
-- System Health: 6 months
-- Performance Logs: 3 months
```

### Foreign Key Constraints

#### Critical Relations

```sql
-- Paywall variations to A/B tests
ALTER TABLE paywall_variations 
ADD CONSTRAINT fk_variation_ab_test 
FOREIGN KEY (ab_test) REFERENCES ab_tests(id) ON DELETE CASCADE;

-- Remote config versions to paywalls
ALTER TABLE remote_config_versions 
ADD CONSTRAINT fk_config_paywall 
FOREIGN KEY (paywall) REFERENCES paywalls(id) ON DELETE CASCADE;

-- Translation assignments to users
ALTER TABLE translation_assignments 
ADD CONSTRAINT fk_translation_translator 
FOREIGN KEY (assigned_translator) REFERENCES admin_users(id) ON DELETE SET NULL;
```

### Caching Strategy

#### Redis Cache Keys

```
paywall:{placement_id}:{locale}         # TTL: 1 hour
product:{adapty_product_id}             # TTL: 24 hours
ab_test:{test_id}:variations            # TTL: 30 minutes
mobile_app:{app_id}:permissions         # TTL: 24 hours
analytics:dashboard:{date_range}        # TTL: 15 minutes
```

#### CDN Cache Strategy

- **Paywall Content**: 1 hour TTL, purge on content update
- **Product Information**: 24 hour TTL, purge on sync
- **Static Assets**: 7 days TTL
- **API Responses**: 15 minutes TTL for read operations

---

## Performance Considerations

### Query Optimization Guidelines

1. **Always use indexes** for WHERE, ORDER BY, and JOIN conditions
2. **Limit result sets** using pagination for large datasets
3. **Use composite indexes** for multi-column queries
4. **Avoid N+1 queries** by using Strapi's populate feature
5. **Cache frequently accessed data** using Redis

### Monitoring Queries

```sql
-- Slow query identification
SELECT query, mean_time, calls, total_time 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;

-- Index usage analysis
SELECT schemaname, tablename, attname, n_distinct, correlation 
FROM pg_stats 
WHERE tablename IN ('paywalls', 'paywall_variations', 'analytics')
ORDER BY n_distinct DESC;
```

### Scaling Recommendations

1. **Read Replicas**: For analytics queries and reporting
2. **Connection Pooling**: Use PgBouncer for connection management
3. **Partitioning**: Implement for time-series tables
4. **Archiving**: Move old analytics data to cold storage
5. **Caching**: Multi-layer caching strategy (Redis + CDN)

---

## Conclusion

This database schema provides a comprehensive foundation for the Strapi Adapty CMS, supporting:

- **Scalable paywall management** with A/B testing capabilities
- **Multi-language content** with efficient translation workflows
- **Real-time analytics** and performance monitoring
- **Mobile API integration** with rate limiting and security
- **Operational monitoring** with health checks and alerting

The schema is designed for performance with proper indexing strategies and supports horizontal scaling through partitioning and caching mechanisms.
