# Strapi Adapty CMS Debug Mode Guide

## Table of Contents
1. [Overview](#overview)
2. [Quick Start](#quick-start)
3. [Debug Mode Options](#debug-mode-options)
4. [IDE Setup](#ide-setup)
5. [Troubleshooting](#troubleshooting)
6. [Performance Optimization](#performance-optimization)
7. [Advanced Configuration](#advanced-configuration)
8. [FAQ](#faq)

## Overview

The Strapi Adapty CMS debug mode provides comprehensive debugging capabilities with intelligent fallback mechanisms, enhanced error handling, and multi-tier configuration options. This guide covers everything you need to know to effectively debug your application.

### Key Features
- **Intelligent Fallback System**: 4-tier configuration (Enhanced → Standard → Minimal → Safe)
- **Node.js Compatibility**: Optimized for v18.x, v20.x, and v22.x
- **Automatic Error Recovery**: Self-healing debug sessions
- **Comprehensive Diagnostics**: Built-in health checks and troubleshooting
- **IDE Integration**: Support for VS Code, WebStorm, and other IDEs

### System Requirements
- **Node.js**: 18.x or higher (20.x recommended)
- **Memory**: Minimum 2GB RAM (4GB recommended for enhanced mode)
- **Disk Space**: At least 1GB free space
- **Network**: Port 1337 available (configurable)

## Quick Start

### 1. Basic Debug Mode
```bash
# Start intelligent debug mode (recommended)
yarn dev:debug

# This will:
# - Run pre-flight checks
# - Apply optimal configuration for your system
# - Start debug mode with automatic fallback
# - Provide real-time feedback and troubleshooting
```

### 2. Alternative Debug Options
```bash
# Enhanced debug mode (full features)
yarn dev:debug:enhanced

# Legacy debug mode (backward compatibility)
yarn dev:debug:legacy

# Manual debug mode
yarn develop --debug
```

### 3. Environment Setup
```bash
# Setup development environment (if not done)
yarn dev:setup

# Check environment health
yarn dev:env check

# Run diagnostics if issues occur
yarn dev:env doctor
```

## Debug Mode Options

### Intelligent Debug Mode (Recommended)
**Command**: `yarn dev:debug`

**Features**:
- Automatic system detection and optimization
- 4-tier fallback system for maximum reliability
- Real-time error detection and recovery
- Comprehensive logging and diagnostics

**Best For**: Daily development, team environments, CI/CD

### Enhanced Debug Mode
**Command**: `yarn dev:debug:enhanced`

**Features**:
- Full source map support
- TypeScript debugging with inline breakpoints
- Hot reload with fast refresh
- Performance monitoring
- Advanced error tracking

**Best For**: Complex debugging sessions, performance analysis

**Requirements**:
- Node.js 18.x or higher
- Minimum 4GB RAM
- Fast SSD storage

### Standard Debug Mode
**Automatic Fallback Level**

**Features**:
- Source map support (reduced quality)
- TypeScript debugging
- Hot reload
- Basic error tracking

**Best For**: Systems with limited resources, older hardware

### Minimal Debug Mode
**Automatic Fallback Level**

**Features**:
- Basic debugging capabilities
- Hot reload only
- Essential error tracking
- Minimal resource usage

**Best For**: Low-resource environments, emergency debugging

### Safe Debug Mode
**Automatic Fallback Level**

**Features**:
- Maximum compatibility
- No source maps
- Basic functionality only
- Minimal resource usage

**Best For**: Compatibility issues, emergency situations

## IDE Setup

### Visual Studio Code

#### 1. Install Extensions
```bash
# Recommended extensions
code --install-extension ms-vscode.vscode-typescript-next
code --install-extension bradlc.vscode-tailwindcss
code --install-extension esbenp.prettier-vscode
code --install-extension ms-vscode.vscode-json
```

#### 2. Launch Configuration
Create `.vscode/launch.json`:
```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Strapi",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/node_modules/.bin/strapi",
      "args": ["develop", "--debug"],
      "env": {
        "NODE_ENV": "development",
        "GENERATE_SOURCEMAP": "true",
        "DEV_SERVER_SOURCEMAP": "true"
      },
      "console": "integratedTerminal",
      "restart": true,
      "protocol": "inspector",
      "sourceMaps": true,
      "outFiles": ["${workspaceFolder}/dist/**/*.js"],
      "skipFiles": ["<node_internals>/**"]
    },
    {
      "name": "Debug Strapi (Enhanced)",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/scripts/debug-manager.js",
      "console": "integratedTerminal",
      "restart": true,
      "protocol": "inspector",
      "sourceMaps": true,
      "env": {
        "DEBUG_MODE": "enhanced"
      }
    }
  ]
}
```

#### 3. Tasks Configuration
Create `.vscode/tasks.json`:
```json
{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "Start Debug Mode",
      "type": "shell",
      "command": "yarn",
      "args": ["dev:debug"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "new"
      },
      "problemMatcher": []
    },
    {
      "label": "Environment Check",
      "type": "shell",
      "command": "yarn",
      "args": ["dev:env", "check"],
      "group": "test",
      "presentation": {
        "echo": true,
        "reveal": "always"
      }
    },
    {
      "label": "Run Diagnostics",
      "type": "shell",
      "command": "yarn",
      "args": ["dev:env", "doctor"],
      "group": "test",
      "presentation": {
        "echo": true,
        "reveal": "always"
      }
    }
  ]
}
```

#### 4. Settings Configuration
Create `.vscode/settings.json`:
```json
{
  "typescript.preferences.includePackageJsonAutoImports": "on",
  "typescript.suggest.autoImports": true,
  "typescript.updateImportsOnFileMove.enabled": "always",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true,
    "source.organizeImports": true
  },
  "files.associations": {
    "*.json": "jsonc"
  },
  "search.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/.cache": true,
    "**/build": true
  }
}
```

### WebStorm/IntelliJ IDEA

#### 1. Run Configuration
1. Go to **Run** → **Edit Configurations**
2. Click **+** → **Node.js**
3. Configure:
   - **Name**: Debug Strapi
   - **Node interpreter**: (auto-detected)
   - **Working directory**: `$PROJECT_DIR$`
   - **JavaScript file**: `node_modules/.bin/strapi`
   - **Application parameters**: `develop --debug`
   - **Environment variables**:
     ```
     NODE_ENV=development
     GENERATE_SOURCEMAP=true
     DEV_SERVER_SOURCEMAP=true
     ```

#### 2. TypeScript Configuration
1. Go to **File** → **Settings** → **Languages & Frameworks** → **TypeScript**
2. Enable **TypeScript Language Service**
3. Set **TypeScript**: Use TypeScript from project
4. Enable **Recompile on changes**

### Other IDEs

#### Vim/Neovim
Install language server support:
```bash
# Install TypeScript language server
npm install -g typescript-language-server

# Add to your vim config
" TypeScript support
Plug 'neoclide/coc.nvim', {'branch': 'release'}
Plug 'leafgarland/typescript-vim'
```

#### Sublime Text
Install packages:
- TypeScript
- LSP
- LSP-typescript

## Troubleshooting

### Common Issues and Solutions

#### 1. Debug Mode Crashes on Startup

**Symptoms**:
```
TypeError: callback is not a function
    at handleIncomingPacket (/node_modules/esbuild-loader/node_modules/esbuild/lib/main.js:756:9)
```

**Solution**:
```bash
# Check if dependencies are updated
yarn dev:env deps check

# Update dependencies if needed
yarn add --dev esbuild-loader@^4.3.0 esbuild@^0.25.8

# Verify the fix
yarn dev:debug
```

**Root Cause**: Outdated ESBuild loader incompatible with Node.js v20.x

#### 2. Port Already in Use

**Symptoms**:
```
Error: listen EADDRINUSE: address already in use :::1337
```

**Solution**:
```bash
# Check what's using the port
lsof -i :1337

# Kill the process (replace PID)
kill -9 <PID>

# Or use a different port
echo "PORT=3000" >> .env

# Verify port availability
yarn dev:env check
```

#### 3. Out of Memory Errors

**Symptoms**:
```
FATAL ERROR: Ineffective mark-compacts near heap limit Allocation failed - JavaScript heap out of memory
```

**Solution**:
```bash
# Check system memory
yarn dev:env check

# The debug manager will automatically apply memory optimizations
yarn dev:debug

# Manual memory increase (if needed)
export NODE_OPTIONS="--max-old-space-size=4096"
yarn develop --debug
```

#### 4. Source Maps Not Working

**Symptoms**:
- Breakpoints not hitting
- Variables showing as undefined
- Stack traces pointing to compiled code

**Solution**:
```bash
# Verify source map configuration
grep -E "SOURCEMAP|sourcemap" .env

# Should show:
# GENERATE_SOURCEMAP=true
# DEV_SERVER_SOURCEMAP=true

# Clear cache and restart
yarn dev:env clean cache
yarn dev:debug:enhanced
```

#### 5. TypeScript Errors in Debug Mode

**Symptoms**:
```
TS2307: Cannot find module '@/components/MyComponent'
```

**Solution**:
```bash
# Check TypeScript configuration
cat tsconfig.json | grep -A 10 "paths"

# Verify path mappings are correct
# Update tsconfig.json if needed

# Clear TypeScript cache
rm -rf .tsbuildinfo
yarn dev:debug
```

#### 6. Hot Reload Not Working

**Symptoms**:
- Changes not reflected automatically
- Need to manually refresh browser

**Solution**:
```bash
# Check if running in enhanced mode
yarn dev:debug:enhanced

# Verify file watching
yarn dev:env check

# Clear cache if needed
yarn dev:env clean cache
```

#### 7. Database Connection Issues

**Symptoms**:
```
Error: connect ECONNREFUSED 127.0.0.1:5432
```

**Solution**:
```bash
# Check database status
yarn dev:db status

# Reset database if needed
yarn dev:db reset

# Verify database configuration
cat .env | grep DATABASE
```

### Diagnostic Commands

#### Environment Health Check
```bash
# Comprehensive environment check
yarn dev:env check

# Sample output:
✅ Node.js v20.12.1 is supported
✅ Dependencies are compatible
✅ Environment configuration found
✅ Port 1337 is available
✅ Memory: 2.1GB free of 8.0GB total
```

#### Comprehensive Diagnostics
```bash
# Run full diagnostics
yarn dev:env doctor

# This generates a detailed report including:
# - System information
# - Node.js configuration
# - Dependency analysis
# - Environment validation
# - Performance metrics
# - Saved to .cache/diagnostics.json
```

#### Debug State Analysis
```bash
# Check debug session state
cat .cache/debug-state.json

# Shows:
# - Current debug level
# - Error history
# - Fallback attempts
# - System diagnostics
```

### Error Code Reference

| Error Code | Description | Solution |
|------------|-------------|----------|
| `EADDRINUSE` | Port already in use | Change port or kill process |
| `ECONNREFUSED` | Database connection failed | Check database status |
| `ENOMEM` | Out of memory | Increase Node.js heap size |
| `ENOENT` | File not found | Check file paths and permissions |
| `EPIPE` | Broken pipe | Restart debug session |
| `ETIMEDOUT` | Connection timeout | Check network and firewall |

## Performance Optimization

### System Requirements by Debug Level

#### Enhanced Mode
- **CPU**: 4+ cores recommended
- **Memory**: 4GB+ RAM
- **Storage**: SSD recommended
- **Network**: Stable connection for hot reload

#### Standard Mode
- **CPU**: 2+ cores
- **Memory**: 2GB+ RAM
- **Storage**: Any
- **Network**: Basic connection

#### Minimal Mode
- **CPU**: Any
- **Memory**: 1GB+ RAM
- **Storage**: Any
- **Network**: Optional

### Performance Tuning

#### Memory Optimization
```bash
# Automatic optimization (recommended)
yarn dev:debug

# Manual optimization for Node.js v20.x
export NODE_OPTIONS="--max-old-space-size=4096"

# For systems with limited memory
export NODE_OPTIONS="--max-old-space-size=2048"
```

#### Build Performance
```bash
# Clear cache for fresh start
yarn dev:env clean cache

# Use filesystem caching (enhanced mode)
yarn dev:debug:enhanced

# Disable source maps for faster builds (if needed)
echo "GENERATE_SOURCEMAP=false" >> .env
```

#### Network Optimization
```bash
# Disable unnecessary network features
echo "DEV_SERVER_SOURCEMAP=false" >> .env

# Use local database for faster access
echo "DATABASE_CLIENT=sqlite" >> .env
```

## Advanced Configuration

### Environment Variables

#### Debug Configuration
```bash
# Core debug settings
NODE_ENV=development
DEBUG=strapi:*
STRAPI_LOG_LEVEL=debug

# Source map settings
GENERATE_SOURCEMAP=true
DEV_SERVER_SOURCEMAP=true

# TypeScript settings
TS_NODE_OPTIONS="--transpile-only --files --esm"

# Performance settings
NODE_OPTIONS="--max-old-space-size=4096"
UV_THREADPOOL_SIZE=128
```

#### Webpack Configuration
```javascript
// config/webpack.debug.js
module.exports = {
  mode: 'development',
  devtool: 'eval-cheap-module-source-map',
  optimization: {
    minimize: false,
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
          priority: 10,
        }
      }
    }
  },
  cache: {
    type: 'filesystem',
    buildDependencies: {
      config: [__filename],
    }
  }
};
```

### Custom Debug Scripts

#### Create Custom Debug Configuration
```bash
# Create custom debug script
cat > scripts/my-debug.sh << 'EOF'
#!/bin/bash
export NODE_ENV=development
export DEBUG=strapi:admin,strapi:api
export GENERATE_SOURCEMAP=true
yarn develop --debug
EOF

chmod +x scripts/my-debug.sh

# Add to package.json
"scripts": {
  "dev:my-debug": "./scripts/my-debug.sh"
}
```

#### Custom Fallback Configuration
```javascript
// config/my-debug-fallback.js
const DebugFallbackManager = require('./debug-fallback');

class MyDebugFallback extends DebugFallbackManager {
  getCustomConfig() {
    return {
      environment: {
        NODE_ENV: 'development',
        DEBUG: 'myapp:*',
        CUSTOM_SETTING: 'true'
      },
      features: {
        customFeature: true
      }
    };
  }
}

module.exports = MyDebugFallback;
```

## FAQ

### General Questions

**Q: Which debug mode should I use?**
A: Use `yarn dev:debug` for daily development. It automatically selects the best configuration for your system and provides fallback if issues occur.

**Q: Why does debug mode take longer to start?**
A: Debug mode includes source map generation, TypeScript compilation, and additional tooling. The enhanced debug manager optimizes this process and provides progress feedback.

**Q: Can I use debug mode in production?**
A: No, debug mode is for development only. Use `yarn build` and `yarn start` for production.

### Technical Questions

**Q: How does the fallback system work?**
A: The system tries Enhanced → Standard → Minimal → Safe configurations automatically when issues are detected. Each level reduces features to improve compatibility.

**Q: What's the difference between the debug modes?**
A: 
- `yarn dev:debug`: Intelligent mode with automatic fallback
- `yarn dev:debug:enhanced`: Full features, requires more resources
- `yarn dev:debug:legacy`: Original script for compatibility

**Q: How do I debug TypeScript files?**
A: Use enhanced or standard debug mode with source maps enabled. Set breakpoints in your TypeScript files, and they'll work correctly.

### Troubleshooting Questions

**Q: Debug mode works but breakpoints don't hit**
A: Check that source maps are enabled (`GENERATE_SOURCEMAP=true`) and clear your cache (`yarn dev:env clean cache`).

**Q: Getting memory errors during debug**
A: The debug manager automatically optimizes memory. If issues persist, try minimal mode or increase system RAM.

**Q: Port 1337 is already in use**
A: Run `yarn dev:env check` to identify the conflict, or change the port in your `.env` file.

### Performance Questions

**Q: Debug mode is slow on my system**
A: Run `yarn dev:env doctor` to check system resources. The fallback system will automatically use a lighter configuration if needed.

**Q: How can I speed up the build process?**
A: Use filesystem caching (automatic in enhanced mode), ensure you have an SSD, and consider disabling source maps if not needed.

**Q: Can I run multiple debug sessions?**
A: Yes, but use different ports for each session. The debug manager will detect port conflicts and suggest alternatives.

---

## Getting Help

### Support Resources
- **Environment Check**: `yarn dev:env check`
- **Diagnostics**: `yarn dev:env doctor`
- **Documentation**: `.docs/` directory
- **Debug State**: `.cache/debug-state.json`

### Reporting Issues
When reporting debug issues, please include:
1. Output from `yarn dev:env doctor`
2. Your `.env` configuration (without secrets)
3. Error messages and stack traces
4. Steps to reproduce the issue

### Community Resources
- Check existing issues in the project repository
- Review the troubleshooting guide above
- Run diagnostics before asking for help
- Include system information in bug reports

---

*This guide is automatically updated with each release. For the latest version, check the docs/ directory in your project.*