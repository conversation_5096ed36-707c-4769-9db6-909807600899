# Story 1.2: Secondary Entity CRUD Coverage & Relationship Testing

## Status
Draft

## Story

**As a** QA Engineer and System Administrator,
**I want** comprehensive E2E CRUD testing for secondary entities (cache-metrics, performance-alert, sync-operation, user-engagement, translation-memory, etc.) with cross-entity relationship validation and bulk operations support,
**so that** I can ensure complete system reliability and data consistency across all content types and their interdependencies.

## Acceptance Criteria

1. **Secondary Entity CRUD Coverage**: Complete testing for remaining 17+ entities (cache-metrics, performance-alert, sync-operation, user-engagement, translation-memory, deployment-log, etc.)
2. **Cross-Entity Relationship Testing**: Validate entity relationships and dependencies (e.g., paywall-variation to paywall, analytics to ab-test)
3. **Bulk Operations Testing**: Test bulk create, update, delete operations for entities that support batch processing
4. **Relationship Integrity**: Existing entity relationships and data integrity constraints continue to function correctly
5. **Framework Extension**: New tests extend Story 1.1 framework without modifying core testing utilities
6. **Admin Interface Compatibility**: Integration with all admin interface forms maintains current user experience and functionality
7. **Complete Coverage**: All secondary entities have complete CRUD test coverage with relationship validation
8. **Bulk Operation Validation**: Bulk operation testing includes proper error handling and rollback verification
9. **Dependency Testing**: Cross-entity dependency testing ensures referential integrity is maintained

## Tasks / Subtasks

- [ ] Task 1: Extend Testing Framework for Secondary Entities (AC: 1, 4, 5)
  - [ ] Create page objects for cache-metrics, performance-alert, sync-operation entities
  - [ ] Implement page objects for user-engagement, translation-memory, deployment-log entities
  - [ ] Extend EntityDataFactory with secondary entity data generators
  - [ ] Create specialized test utilities for complex entity types
  - [ ] Implement relationship mapping and validation utilities

- [ ] Task 2: Implement Cross-Entity Relationship Testing (AC: 2, 4, 9)
  - [ ] Create RelationshipTester utility for one-to-many relationships
  - [ ] Implement many-to-many relationship validation
  - [ ] Develop cascade delete testing for dependent entities
  - [ ] Create referential integrity validation tests
  - [ ] Implement relationship constraint testing

- [ ] Task 3: Develop Bulk Operations Testing Framework (AC: 3, 8)
  - [ ] Create BulkOperationTester utility class
  - [ ] Implement bulk create operations testing
  - [ ] Develop bulk update operations with validation
  - [ ] Create bulk delete operations with rollback testing
  - [ ] Implement error handling and partial failure scenarios

- [ ] Task 4: Secondary Entity Test Suite Implementation (AC: 1, 6, 7)
  - [ ] Create cache-metrics-crud.test.ts with performance validation
  - [ ] Implement performance-alert-crud.test.ts with threshold testing
  - [ ] Develop sync-operation-crud.test.ts with status tracking
  - [ ] Create user-engagement-crud.test.ts with analytics integration
  - [ ] Implement translation-memory-crud.test.ts with localization testing

- [ ] Task 5: Integration and Comprehensive Validation (AC: 4, 6, 9)
  - [ ] Execute full relationship integrity testing
  - [ ] Validate admin interface compatibility across all entities
  - [ ] Perform comprehensive regression testing
  - [ ] Verify referential integrity constraints
  - [ ] Execute end-to-end workflow testing

## Dev Notes

### Previous Story Dependencies
This story builds directly on Story 1.1 (Enhanced CRUD Testing Framework) and requires:
- BaseEntityPage class and core utilities from Story 1.1
- EntityDataFactory base implementation
- CRUDTestRunner and DatabaseCleaner utilities
- Existing Playwright configuration and authentication setup

### Data Models
[Source: docs/architecture/database-schema.md#secondary-entities]

**Secondary Entities for CRUD Testing**:

**Performance & Monitoring Entities**:
- **Cache Metrics**: Performance tracking for caching layer
  - Fields: cache_key, hit_rate, miss_rate, response_time, timestamp
  - Relationships: Links to system-health for performance correlation
- **Performance Alert**: Automated alerting for system performance
  - Fields: alert_type, threshold, current_value, status, created_at
  - Enum fields: alert_type (cpu, memory, response_time), status (active, resolved)
- **System Health**: Overall system status monitoring
  - Fields: cpu_usage, memory_usage, disk_usage, response_time, status
  - Time-series data with aggregation capabilities

**Synchronization & Operations**:
- **Sync Operation**: Adapty synchronization tracking
  - Fields: operation_type, status, started_at, completed_at, error_message
  - Relationships: Links to mobile-app for sync context
- **Sync Status**: Current synchronization state
  - Fields: last_sync, next_sync, sync_frequency, status
  - Enum fields: status (pending, in_progress, completed, failed)
- **Deployment Log**: Application deployment tracking
  - Fields: version, environment, deployed_at, deployed_by, status
  - Relationships: Links to system-health for deployment impact

**Analytics & Engagement**:
- **User Engagement**: User interaction tracking
  - Fields: user_id, session_duration, page_views, interactions, timestamp
  - Relationships: Links to analytics for engagement correlation
- **Cohort Analysis**: User behavior analysis over time
  - Fields: cohort_name, start_date, retention_rate, conversion_rate
  - Relationships: Links to user-engagement for cohort data
- **Subscription Metrics**: Subscription performance tracking
  - Fields: subscription_type, conversion_rate, churn_rate, revenue
  - Relationships: Links to paywall for subscription attribution

**Localization & Content**:
- **Translation Memory**: Translation management system
  - Fields: source_text, target_text, language_pair, quality_score
  - Relationships: Links to translation-assignment for workflow
- **Translation Assignment**: Translation workflow management
  - Fields: content_id, translator_id, status, assigned_at, completed_at
  - Enum fields: status (assigned, in_progress, review, completed)

### Relationship Specifications
[Source: docs/architecture/database-schema.md#entity-relationships]

**Critical Relationships to Test**:

**One-to-Many Relationships**:
- `paywall` → `paywall-variation` (1:N)
- `ab-test` → `analytics` (1:N)
- `mobile-app` → `sync-operation` (1:N)
- `system-health` → `performance-alert` (1:N)

**Many-to-Many Relationships**:
- `translation-memory` ↔ `translation-assignment` (M:N)
- `user-engagement` ↔ `cohort-analysis` (M:N)
- `cache-metrics` ↔ `api-performance` (M:N)

**Cascade Delete Behavior**:
- Deleting `paywall` should handle `paywall-variation` dependencies
- Deleting `ab-test` should preserve historical `analytics` data
- Deleting `mobile-app` should archive related `sync-operation` records

### API Specifications
[Source: docs/architecture/patterns-review.md#bulk-operations]

**Bulk Operation Endpoints**:
- `POST /admin/content-manager/collection-types/api::{entity}.{entity}/actions/bulkDelete`
- `PUT /admin/content-manager/collection-types/api::{entity}.{entity}/actions/bulkUpdate`
- `POST /admin/content-manager/collection-types/api::{entity}.{entity}/actions/bulkCreate`

**Relationship Management Endpoints**:
- `PUT /admin/content-manager/collection-types/api::{entity}.{entity}/{id}/relations/{relationField}`
- `DELETE /admin/content-manager/collection-types/api::{entity}.{entity}/{id}/relations/{relationField}/{relatedId}`

### Component Specifications
[Source: docs/e2e-implementation-summary.md#admin-components]

**Admin Interface Components for Secondary Entities**:
- **BulkPaywallOperations**: Batch operations interface
  - Features: Multi-select, bulk actions, progress tracking
  - Testing focus: Selection state, operation confirmation, error handling
- **PerformanceMonitoringDashboard**: Real-time performance metrics
  - Features: Live data updates, threshold configuration, alert management
  - Testing focus: Data loading, real-time updates, alert triggers
- **AnalyticsDashboard**: Comprehensive analytics interface
  - Features: Data visualization, export functionality, filtering
  - Testing focus: Chart rendering, data accuracy, export formats

### File Locations
[Source: Story 1.1 framework extension]

**Extended Test Framework Structure**:
```
tests/e2e/crud/
├── secondary/
│   ├── page-objects/
│   │   ├── CacheMetricsPage.ts
│   │   ├── PerformanceAlertPage.ts
│   │   ├── SyncOperationPage.ts
│   │   ├── UserEngagementPage.ts
│   │   └── TranslationMemoryPage.ts
│   ├── utilities/
│   │   ├── RelationshipTester.ts
│   │   ├── BulkOperationTester.ts
│   │   └── CrossEntityValidator.ts
│   └── tests/
│       ├── cache-metrics-crud.test.ts
│       ├── performance-alert-crud.test.ts
│       ├── relationship-validation.test.ts
│       └── bulk-operations.test.ts
```

### Testing Requirements
[Source: Story 1.1 framework, relationship testing patterns]

**Relationship Testing Standards**:
- **One-to-Many**: Create parent, create child with reference, verify relationship, test cascade behavior
- **Many-to-Many**: Create entities, establish relationships, verify bidirectional links, test relationship removal
- **Referential Integrity**: Test foreign key constraints, orphan record handling, cascade delete behavior
- **Bulk Operations**: Test batch create/update/delete with partial failures and rollback scenarios

**Performance Testing Requirements**:
- Bulk operations should complete within reasonable time limits
- Relationship queries should be optimized for performance
- Large dataset testing for entities with high volume (cache-metrics, user-engagement)
- Memory usage optimization for bulk operations

### Technical Constraints
[Source: docs/architecture/patterns-review.md#performance-considerations]

**Relationship Complexity Considerations**:
- Some entities have complex nested relationships requiring careful test ordering
- Bulk operations may have transaction limits requiring chunked processing
- Real-time entities (cache-metrics, performance-alert) may require time-based testing
- Translation entities require locale-specific data handling

**Data Volume Considerations**:
- Cache metrics and user engagement entities may have large datasets
- Performance testing should account for realistic data volumes
- Bulk operations should be tested with various batch sizes
- Cleanup procedures must handle large datasets efficiently

### Integration Points
[Source: docs/e2e-implementation-summary.md#integration-testing]

**External Service Integration**:
- **Adapty Sync**: sync-operation and sync-status entities integrate with external Adapty API
- **Performance Monitoring**: cache-metrics and performance-alert integrate with system monitoring
- **Analytics Services**: user-engagement and cohort-analysis integrate with analytics pipeline
- **Translation Services**: translation-memory integrates with external translation providers

**Admin Interface Integration**:
- Bulk operations interface requires complex UI interaction testing
- Real-time dashboard components need WebSocket or polling testing
- Relationship management interfaces require drag-and-drop and selection testing
- Export functionality requires file download and format validation testing

## Testing

### Unit Testing Requirements
- RelationshipTester utility should have comprehensive unit tests
- BulkOperationTester should be tested with mock data and operations
- CrossEntityValidator should validate relationship constraints accurately
- Data factories for secondary entities should generate valid test data

### Integration Testing Requirements
- Full relationship workflows should be tested end-to-end
- Bulk operations should be tested with realistic data volumes
- Cross-entity dependencies should be validated comprehensively
- Admin interface integration should cover all secondary entity forms

### Relationship Testing Scenarios
- Create parent entity, create child with relationship, verify link
- Update relationship references, verify referential integrity
- Delete parent entity, verify cascade behavior or constraint enforcement
- Test orphan record handling and cleanup procedures

### Bulk Operation Testing Scenarios
- Bulk create with valid data, verify all entities created
- Bulk update with mixed valid/invalid data, verify partial success handling
- Bulk delete with relationship constraints, verify constraint enforcement
- Test transaction rollback on bulk operation failures

## Change Log

| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2024-12-19 | 1.0 | Initial story creation for secondary entity CRUD and relationship testing | Scrum Master |

## Dev Agent Record

### Agent Model Used
_To be populated by development agent_

### Debug Log References
_To be populated by development agent_

### Completion Notes List
_To be populated by development agent_

### File List
_To be populated by development agent_

## QA Results
_To be populated by QA agent_