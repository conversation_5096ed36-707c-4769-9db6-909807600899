# Mobile API v1 Documentation

## Overview

The Strapi-Adapty Mobile API v1 provides RESTful endpoints optimized for mobile app paywall content delivery. This API enables React Native, iOS, and Android applications to dynamically load paywall configurations without requiring app updates.

## Base URL

```
https://your-domain.com/api/mobile/v1
```

## Authentication

All API endpoints (except health check and documentation) require Bearer token authentication.

### Token Format
```
Authorization: Bearer <your-jwt-token>
```

### Getting a Token

Tokens are generated server-side for registered mobile applications. Contact your system administrator to register your mobile app and receive API credentials.

## Rate Limits

| Endpoint Type | Limit |
|---------------|-------|
| Default | 1000 requests per 15 minutes |
| Paywall | 60 requests per minute |
| Analytics | 120 requests per minute |
| Batch | 10 requests per minute |
| Health | 30 requests per minute |

Rate limit headers are included in all responses:
- `X-RateLimit-Limit`: Maximum requests allowed
- `X-RateLimit-Remaining`: Remaining requests in current window
- `X-RateLimit-Reset`: When the rate limit resets

## Endpoints

### Health Check

Check API health status.

```http
GET /health
```

**Response:**
```json
{
  "status": "healthy",
  "version": "v1",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "services": {
    "database": true,
    "cache": true,
    "adapty": true
  }
}
```

### Get API Documentation

Retrieve comprehensive API documentation.

```http
GET /docs
```

### Get Version Information

Get API version and feature information.

```http
GET /version
```

### Get Paywall by Placement ID

Retrieve a single paywall configuration.

```http
GET /paywalls/{placementId}
```

**Parameters:**
- `placementId` (path, required): Adapty placement ID
- `locale` (query, optional): Language code (default: "en")
- `device_type` (query, optional): Device type ("mobile", "tablet")
- `app_version` (query, optional): App version
- `user_id` (query, optional): User identifier

**Response:**
```json
{
  "data": {
    "paywall": {
      "id": "123",
      "name": "Premium Subscription",
      "placement_id": "premium_paywall",
      "content": {
        "title": "Unlock Premium Features",
        "subtitle": "Get unlimited access",
        "description": "Access all premium features...",
        "cta_text": "Subscribe Now",
        "cta_secondary_text": "Maybe Later"
      },
      "theme": {
        "primary_color": "#007AFF",
        "background_color": "#FFFFFF",
        "text_color": "#000000",
        "button_style": "rounded",
        "background_image": "https://cdn.example.com/bg.jpg"
      },
      "features": [
        {
          "id": 1,
          "icon": "star",
          "title": "Premium Feature",
          "description": "Access to premium content",
          "is_highlighted": true,
          "order": 1
        }
      ],
      "testimonials": [
        {
          "id": 1,
          "author_name": "John Doe",
          "content": "Amazing app!",
          "rating": 5,
          "order": 1
        }
      ],
      "product_labels": {
        "premium_monthly": {
          "badge_text": "Most Popular",
          "badge_color": "#FF6B35",
          "highlight": true
        }
      },
      "layout": {
        "show_features": true,
        "show_testimonials": false,
        "header_style": "hero",
        "product_display_style": "list"
      }
    },
    "cache_info": {
      "version": 1,
      "etag": "abc123",
      "expires_at": "2024-01-01T12:05:00.000Z"
    }
  }
}
```

### Get Batch Paywalls

Retrieve multiple paywalls in a single request.

```http
POST /paywalls/batch
```

**Body:**
```json
{
  "placement_ids": ["placement1", "placement2"],
  "locale": "en",
  "device_type": "mobile",
  "user_id": "user123"
}
```

### Get Paywalls by Multiple Placement IDs

Retrieve paywalls using comma-separated placement IDs in URL.

```http
GET /paywalls/placements/{placementIds}
```

**Parameters:**
- `placementIds` (path, required): Comma-separated placement IDs
- `locale` (query, optional): Language code
- `device_type` (query, optional): Device type
- `user_id` (query, optional): User identifier

### Get Paywall with A/B Testing

Retrieve paywall with A/B test variation.

```http
GET /paywalls/{placementId}/ab-test
```

**Parameters:**
- `placementId` (path, required): Adapty placement ID
- `locale` (query, optional): Language code
- `device_type` (query, optional): Device type
- `user_id` (query, required): User identifier for A/B test assignment

### Record Paywall Interaction

Record user interaction with paywall.

```http
POST /paywalls/{placementId}/interactions
```

**Body:**
```json
{
  "interaction_type": "view|click|close|purchase",
  "user_id": "user123",
  "metadata": {
    "source": "home_screen",
    "duration": 5000
  }
}
```

### Bulk Operations

Perform multiple operations in a single request.

```http
POST /paywalls/bulk
```

**Body:**
```json
{
  "operations": [
    {
      "id": 1,
      "type": "get_paywall",
      "placement_id": "premium_paywall",
      "locale": "en"
    },
    {
      "id": 2,
      "type": "record_interaction",
      "placement_id": "premium_paywall",
      "interaction_type": "view",
      "user_id": "user123"
    }
  ]
}
```

### Record Batch Interactions

Record multiple interactions in a single request.

```http
POST /interactions/batch
```

**Body:**
```json
{
  "interactions": [
    {
      "id": 1,
      "placement_id": "premium_paywall",
      "interaction_type": "view",
      "user_id": "user123",
      "timestamp": "2024-01-01T12:00:00.000Z"
    }
  ]
}
```

### Prefetch Paywalls

Prefetch paywalls for offline use.

```http
POST /paywalls/prefetch
```

**Body:**
```json
{
  "placement_ids": ["placement1", "placement2"],
  "locale": "en",
  "device_type": "mobile"
}
```

## Error Handling

The API uses standard HTTP status codes:

- `200` - Success
- `400` - Bad Request (invalid parameters)
- `401` - Unauthorized (invalid or missing token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found (resource not found)
- `429` - Too Many Requests (rate limit exceeded)
- `500` - Internal Server Error

**Error Response Format:**
```json
{
  "error": "Error message",
  "message": "Detailed error description",
  "statusCode": 400
}
```

## Caching

The API implements intelligent caching to optimize performance:

- **ETag Support**: Use `If-None-Match` header for conditional requests
- **Cache Headers**: Responses include appropriate cache control headers
- **CDN Integration**: Static assets are served via CDN

### Cache Headers

- `Cache-Control`: Caching directives
- `ETag`: Entity tag for cache validation
- `Last-Modified`: Last modification timestamp

## Mobile Optimization

The API is optimized for mobile consumption:

- **Compressed Responses**: Automatic gzip compression
- **Minimal Payloads**: Only essential data is included
- **Batch Operations**: Reduce number of requests
- **Offline Support**: Prefetch capabilities for offline use

## SDK Examples

### React Native

```javascript
import { AdaptyMobileAPI } from '@your-company/adapty-mobile-sdk';

const api = new AdaptyMobileAPI({
  baseURL: 'https://your-domain.com/api/mobile/v1',
  token: 'your-jwt-token'
});

// Get paywall
const paywall = await api.getPaywall('premium_paywall', {
  locale: 'en',
  deviceType: 'mobile',
  userId: 'user123'
});

// Record interaction
await api.recordInteraction('premium_paywall', {
  interactionType: 'view',
  userId: 'user123'
});
```

### iOS (Swift)

```swift
import AdaptyMobileSDK

let api = AdaptyMobileAPI(
    baseURL: "https://your-domain.com/api/mobile/v1",
    token: "your-jwt-token"
)

// Get paywall
api.getPaywall(placementId: "premium_paywall") { result in
    switch result {
    case .success(let paywall):
        // Handle paywall data
    case .failure(let error):
        // Handle error
    }
}
```

### Android (Kotlin)

```kotlin
import com.yourcompany.adapty.AdaptyMobileAPI

val api = AdaptyMobileAPI(
    baseURL = "https://your-domain.com/api/mobile/v1",
    token = "your-jwt-token"
)

// Get paywall
api.getPaywall("premium_paywall") { result ->
    result.onSuccess { paywall ->
        // Handle paywall data
    }.onFailure { error ->
        // Handle error
    }
}
```

## Best Practices

1. **Token Management**: Store tokens securely and refresh when needed
2. **Error Handling**: Implement proper error handling and retry logic
3. **Caching**: Use ETags and cache headers to minimize bandwidth
4. **Batch Operations**: Use batch endpoints when possible
5. **Rate Limiting**: Respect rate limits and implement backoff strategies
6. **Offline Support**: Use prefetch for critical paywalls

## Support

For API support, contact: <EMAIL>

## Changelog

### v1.0.0 (2024-01-01)
- Initial release
- Basic paywall delivery endpoints
- Authentication and rate limiting
- A/B testing support
- Analytics tracking
- Batch operations
- Prefetch capabilities