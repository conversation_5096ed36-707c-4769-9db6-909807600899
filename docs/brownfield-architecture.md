# Strapi-Adapty CMS Brownfield Architecture Document

## Executive Summary

The Strapi-Adapty CMS is a sophisticated content management system built on Strapi 4.25.2 that provides a comprehensive solution for managing dynamic paywall configurations integrated with Adapty's subscription platform. This brownfield system enables non-technical stakeholders to create, manage, and deploy paywall content, A/B tests, and localized experiences without requiring code changes.

## System Overview

### Core Purpose
- **Primary Function**: Content management for DIY paywall configurations
- **Target Users**: Marketing teams, product managers, content creators, and growth managers
- **Integration**: Seamless synchronization with Adapty's server API
- **Deployment**: Mobile-first API delivery for React Native, iOS, and Android applications

### Key Capabilities
- Dynamic paywall content management with real-time preview
- Advanced A/B testing with statistical analysis
- Multi-language localization support (7 languages)
- Comprehensive analytics and performance monitoring
- Mobile-optimized API endpoints with caching and rate limiting
- Workflow management with approval processes

## Architecture Overview

### High-Level System Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        A[Strapi Admin Dashboard]
        B[Custom React Components]
        C[Mobile Preview Interface]
    end
    
    subgraph "API Layer"
        D[Strapi Core API]
        E[Mobile API v1]
        F[Analytics API]
        G[Webhook Handlers]
    end
    
    subgraph "Service Layer"
        H[Adapty Integration Service]
        I[A/B Testing Manager]
        J[Localization Service]
        K[Analytics Service]
        L[Caching Service]
    end
    
    subgraph "Data Layer"
        M[PostgreSQL/SQLite Database]
        N[Redis Cache]
        O[CDN Storage]
    end
    
    subgraph "External Integrations"
        P[Adapty Server API]
        Q[Mobile Applications]
        R[Translation Services]
        S[Analytics Platforms]
    end
    
    A --> D
    B --> D
    C --> E
    D --> H
    E --> I
    F --> J
    G --> K
    H --> P
    I --> M
    J --> N
    K --> O
    E --> Q
    H --> R
    F --> S
```

## Technology Stack

### Core Framework
- **Backend**: Strapi 4.25.2 (Node.js-based headless CMS)
- **Frontend**: React 18 with TypeScript
- **Styling**: Styled Components 6.0
- **Database**: PostgreSQL (production) / SQLite (development)
- **Caching**: Redis for session and API caching

### Key Dependencies
- **HTTP Client**: Axios 1.8.4 for API communications
- **Image Processing**: Sharp 0.33.0 for media optimization
- **Testing**: Jest 29.7.0 with TypeScript support
- **Build Tools**: Webpack 5.101.0, ESBuild 0.25.8
- **UI Components**: Strapi Design System, React Beautiful DnD

### Development Tools
- **TypeScript**: Full type safety across the codebase
- **Docker**: Containerized development and deployment
- **Environment Management**: Multiple environment configurations
- **Testing**: Comprehensive test suite with coverage reporting

## Core Components

### 1. Content Management System

#### Content Types (24 schemas)
The system manages 24 distinct content types organized into functional categories:

**Paywall Management**:
- `paywall`: Core paywall configurations with i18n support
- `paywall-variation`: A/B test variations
- `paywall-metrics`: Performance tracking data

**A/B Testing**:
- `ab-test`: Test configurations and parameters
- `cohort-analysis`: User segmentation data

**Analytics & Monitoring**:
- `analytics`: Performance metrics and KPIs
- `user-engagement`: User interaction tracking
- `api-performance`: API response time monitoring
- `system-health`: Infrastructure health checks

**Localization**:
- `translation-assignment`: Workflow management
- `translation-memory`: Translation cache

**Configuration Management**:
- `remote-config-version`: Version control for configurations
- `preview-config`: Preview environment settings

#### Shared Components
Reusable component schemas for consistent data modeling:
- `theme`: Visual styling and branding
- `feature`: Product feature listings
- `testimonial`: Customer feedback display
- `product-label`: Promotional badges and highlights
- `media`: Optimized asset management

### 2. Adapty Integration Layer

#### AdaptyApiClient (`src/services/adapty/client.ts`)
Comprehensive TypeScript client with:
- **Authentication**: JWT token management with refresh capabilities
- **Error Handling**: Typed error responses with retry logic
- **Circuit Breaker**: Fault tolerance for external API calls
- **Rate Limiting**: Respect for Adapty API limits
- **Logging**: Structured logging for debugging and monitoring

#### Core Integration Services
- **Placement Management**: Sync paywall placements
- **Product Synchronization**: Real-time product catalog updates
- **Remote Config**: Configuration deployment to Adapty
- **Analytics Integration**: Bidirectional data flow

### 3. A/B Testing Engine

#### ABTestManager (`src/services/ab-testing/ab-test-manager.ts`)
Advanced testing capabilities:
- **Statistical Engine**: Confidence intervals and significance testing
- **Traffic Allocation**: Percentage-based user distribution
- **Performance Monitoring**: Real-time conversion tracking
- **Winner Determination**: Automated test conclusion workflows

#### Features
- Multi-variant testing support
- Audience targeting integration
- Real-time performance dashboards
- Statistical significance calculations

### 4. Mobile API Layer

#### Mobile API v1 (`src/api/mobile/v1/`)
Optimized endpoints for mobile consumption:

**Core Endpoints**:
- `GET /mobile/v1/paywalls/{placementId}`: Single paywall retrieval
- `POST /mobile/v1/paywalls/batch`: Bulk paywall requests
- `GET /mobile/v1/paywalls/{placementId}/ab-test`: A/B test variations
- `POST /mobile/v1/paywalls/{placementId}/interactions`: Analytics tracking

**Middleware Stack**:
- **Authentication**: JWT token validation
- **Rate Limiting**: Tiered limits by endpoint type
- **Caching**: Redis-based response caching with ETags
- **Analytics**: Request tracking and performance monitoring

#### Performance Optimizations
- **Compression**: Automatic gzip compression
- **CDN Integration**: Asset delivery optimization
- **Batch Operations**: Reduced request overhead
- **Offline Support**: Prefetch capabilities

### 5. Admin Interface

#### Custom React Components (16 components)
Specialized UI components for enhanced user experience:

**Dashboard Components**:
- `ABTestDashboard`: Real-time A/B test monitoring
- `AnalyticsDashboard`: Performance metrics visualization
- `PerformanceMonitoringDashboard`: System health overview

**Content Management**:
- `PaywallFormValidation`: Input validation and error handling
- `PaywallPreview`: Real-time preview functionality
- `BulkPaywallOperations`: Batch content operations

**Testing & Analytics**:
- `ABTestInterface`: Test configuration interface
- `TestResultsVisualization`: Statistical analysis display
- `VariationComparison`: Side-by-side test comparison

**Workflow Management**:
- `TestConclusionWorkflow`: Automated test completion
- `FeatureManager`: Feature flag management
- `ProductLabelManager`: Promotional content management

## Data Architecture

### Database Schema Design

#### Core Entities
```sql
-- Paywall entity with localization support
paywalls {
  id: PRIMARY KEY
  name: VARCHAR(255) NOT NULL
  placement_id: VARCHAR(100) UNIQUE NOT NULL
  status: ENUM('draft', 'review', 'approved', 'published', 'archived')
  title: VARCHAR(255) -- Localized
  subtitle: VARCHAR(500) -- Localized
  description_text: TEXT -- Localized
  cta_text: VARCHAR(100) -- Localized
  adapty_sync_status: ENUM('pending', 'synced', 'error')
  adapty_last_sync: DATETIME
  theme: JSON -- Component reference
  features: JSON[] -- Component array
  testimonials: JSON[] -- Component array
  created_at: TIMESTAMP
  updated_at: TIMESTAMP
}

-- A/B Test configuration
ab_tests {
  id: PRIMARY KEY
  name: VARCHAR(255) NOT NULL
  status: ENUM('draft', 'running', 'paused', 'completed')
  placement_id: VARCHAR(100) NOT NULL
  traffic_allocation: JSON
  success_metrics: JSON
  statistical_config: JSON
  start_date: DATETIME
  end_date: DATETIME
  winner_variation_id: VARCHAR(100)
}

-- Analytics and metrics
paywall_metrics {
  id: PRIMARY KEY
  placement_id: VARCHAR(100) NOT NULL
  date: DATE NOT NULL
  impressions: INTEGER DEFAULT 0
  conversions: INTEGER DEFAULT 0
  revenue: DECIMAL(10,2) DEFAULT 0
  conversion_rate: DECIMAL(5,4)
  device_type: VARCHAR(50)
  locale: VARCHAR(10)
}
```

#### Localization Schema
```sql
-- i18n support for content
localizations {
  id: PRIMARY KEY
  entity_id: INTEGER NOT NULL
  entity_type: VARCHAR(100) NOT NULL
  locale: VARCHAR(10) NOT NULL
  field_name: VARCHAR(100) NOT NULL
  field_value: TEXT
}
```

### Caching Strategy

#### Redis Cache Structure
```
paywall:{placement_id}:{locale} -> Cached paywall data (TTL: 1 hour)
ab_test:{placement_id}:{user_id} -> User test assignment (TTL: 24 hours)
analytics:{placement_id}:{date} -> Daily metrics (TTL: 6 hours)
sync_status:{placement_id} -> Adapty sync status (TTL: 5 minutes)
```

## Integration Patterns

### 1. Adapty Synchronization

#### Bidirectional Sync Flow
```mermaid
sequenceDiagram
    participant CMS as Strapi CMS
    participant Adapty as Adapty API
    participant Mobile as Mobile App
    
    CMS->>Adapty: Sync paywall configuration
    Adapty-->>CMS: Confirm sync status
    CMS->>CMS: Update sync status
    Mobile->>CMS: Request paywall
    CMS->>Adapty: Fetch latest config
    Adapty-->>CMS: Return config
    CMS-->>Mobile: Deliver paywall
```

#### Webhook Integration
- **Incoming Webhooks**: Adapty product updates, subscription events
- **Outgoing Webhooks**: Content publication notifications
- **Error Handling**: Retry logic with exponential backoff

### 2. Mobile API Integration

#### Request Flow
```mermaid
sequenceDiagram
    participant App as Mobile App
    participant API as Mobile API v1
    participant Cache as Redis Cache
    participant DB as Database
    participant Adapty as Adapty API
    
    App->>API: GET /paywalls/{placementId}
    API->>Cache: Check cache
    alt Cache Hit
        Cache-->>API: Return cached data
    else Cache Miss
        API->>DB: Query paywall
        API->>Adapty: Fetch remote config
        API->>Cache: Store result
    end
    API-->>App: Return paywall data
```

## Security Architecture

### Authentication & Authorization

#### Multi-tier Security Model
1. **Admin Access**: Strapi's built-in role-based access control
2. **API Authentication**: JWT tokens for mobile API access
3. **Adapty Integration**: Secure API key management
4. **Database Security**: Connection encryption and access controls

#### Security Features
- **Token Management**: Automatic refresh and expiration handling
- **Rate Limiting**: Protection against abuse and DDoS
- **Input Validation**: Comprehensive data sanitization
- **Audit Logging**: Complete action tracking for compliance

### Data Protection
- **Encryption**: At-rest and in-transit data encryption
- **Backup Strategy**: Automated database backups with retention policies
- **Access Logs**: Detailed logging for security monitoring
- **Environment Isolation**: Separate configurations for dev/staging/production

## Performance Characteristics

### Scalability Metrics

#### Current Performance Benchmarks
- **API Response Time**: < 200ms for cached requests
- **Database Queries**: Optimized with proper indexing
- **Concurrent Users**: Supports 1000+ simultaneous admin users
- **Mobile API Throughput**: 10,000+ requests per minute

#### Optimization Strategies
- **Database Indexing**: Strategic indexes on frequently queried fields
- **Query Optimization**: Efficient joins and data fetching
- **CDN Integration**: Global asset distribution
- **Horizontal Scaling**: Load balancer ready architecture

### Monitoring & Observability

#### Performance Monitoring
- **API Performance**: Response time tracking per endpoint
- **Database Performance**: Query execution time monitoring
- **Cache Hit Rates**: Redis performance metrics
- **Error Tracking**: Comprehensive error logging and alerting

#### Health Checks
- **System Health**: `/api/system-health/health-check` endpoint
- **Database Connectivity**: Connection pool monitoring
- **External API Status**: Adapty API availability checks
- **Cache Status**: Redis connectivity and performance

## Deployment Architecture

### Environment Configuration

#### Multi-Environment Setup
```yaml
# Development
- Database: SQLite (local development)
- Cache: Local Redis instance
- Storage: Local file system
- Adapty: Sandbox environment

# Staging
- Database: PostgreSQL (managed service)
- Cache: Redis cluster
- Storage: AWS S3 with CloudFront
- Adapty: Staging environment

# Production
- Database: PostgreSQL (high availability)
- Cache: Redis cluster (multi-AZ)
- Storage: CDN with global distribution
- Adapty: Production environment
```

#### Docker Configuration
- **Development**: `docker-compose.dev.yml` with hot reloading
- **Production**: Optimized multi-stage builds
- **Environment Variables**: Comprehensive configuration management

### CI/CD Pipeline

#### Automated Workflows
1. **Code Quality**: ESLint, TypeScript compilation, test execution
2. **Security Scanning**: Dependency vulnerability checks
3. **Database Migrations**: Automated schema updates
4. **Deployment**: Blue-green deployment strategy
5. **Monitoring**: Post-deployment health checks

## Maintenance & Operations

### Backup & Recovery

#### Data Protection Strategy
- **Database Backups**: Daily automated backups with 30-day retention
- **Media Assets**: CDN-based redundancy with versioning
- **Configuration Backups**: Version-controlled environment configs
- **Disaster Recovery**: RTO: 4 hours, RPO: 1 hour

### Monitoring & Alerting

#### Operational Metrics
- **System Performance**: CPU, memory, disk usage
- **Application Metrics**: Request rates, error rates, response times
- **Business Metrics**: Conversion rates, revenue tracking
- **Security Events**: Failed authentication attempts, suspicious activity

#### Alert Configuration
- **Critical Alerts**: System downtime, database connectivity issues
- **Warning Alerts**: High error rates, performance degradation
- **Business Alerts**: Significant conversion rate changes

## Development Workflow

### Code Organization

#### Project Structure
```
src/
├── admin/                 # Custom admin interface
│   ├── extensions/        # Custom React components (16 files)
│   └── app.tsx           # Admin configuration
├── api/                  # API endpoints (24 content types)
│   ├── mobile/v1/        # Mobile-optimized API
│   └── [entity]/         # Standard Strapi API structure
├── services/             # Business logic services
│   ├── adapty/           # Adapty integration
│   ├── ab-testing/       # A/B testing engine
│   ├── analytics/        # Analytics processing
│   └── localization/     # i18n management
├── components/           # Shared component schemas
└── middlewares/          # Custom middleware
```

#### Development Scripts
```json
{
  "dev": "strapi develop",
  "dev:debug": "Enhanced debugging with sourcemaps",
  "dev:env": "Environment management utilities",
  "test": "jest --coverage",
  "build": "strapi build"
}
```

### Testing Strategy

#### Test Coverage
- **Unit Tests**: Service layer and utility functions
- **Integration Tests**: API endpoints and database operations
- **Component Tests**: React component functionality
- **E2E Tests**: Critical user workflows

#### Test Categories
```
tests/
├── ab-testing/           # A/B testing functionality
├── adapty/              # Adapty integration
├── admin/               # Admin interface
├── analytics/           # Analytics processing
├── localization/        # i18n features
├── media/               # Media management
├── mobile-api/          # Mobile API endpoints
├── monitoring/          # Performance monitoring
└── paywall/             # Core paywall functionality
```

## Future Considerations

### Scalability Roadmap
1. **Microservices Migration**: Gradual decomposition of monolithic services
2. **Event-Driven Architecture**: Implement event sourcing for better scalability
3. **GraphQL API**: Enhanced query capabilities for mobile applications
4. **Machine Learning**: Automated A/B test optimization and personalization

### Technology Evolution
- **Strapi v5 Migration**: Planning for next major version upgrade
- **React 19**: Frontend framework updates
- **Database Sharding**: Horizontal scaling for large datasets
- **Edge Computing**: CDN-based API endpoints for global performance

## Conclusion

The Strapi-Adapty CMS represents a mature, production-ready system that successfully bridges the gap between technical infrastructure and business user needs. Its brownfield architecture demonstrates sophisticated integration patterns, comprehensive testing strategies, and robust operational practices.

### Key Strengths
- **Comprehensive Integration**: Seamless Adapty API integration with robust error handling
- **User Experience**: Intuitive admin interface with 16 custom React components
- **Performance**: Optimized mobile API with caching and rate limiting
- **Scalability**: Well-architected for horizontal scaling and high availability
- **Maintainability**: Clean code organization with comprehensive testing

### Architectural Excellence
- **Separation of Concerns**: Clear boundaries between presentation, business logic, and data layers
- **Extensibility**: Plugin-based architecture supporting custom functionality
- **Reliability**: Circuit breaker patterns and comprehensive error handling
- **Security**: Multi-layered security with proper authentication and authorization
- **Observability**: Comprehensive monitoring and logging throughout the system

This architecture document serves as a foundation for understanding the current system capabilities and planning future enhancements while maintaining the robust, scalable foundation that has been established.