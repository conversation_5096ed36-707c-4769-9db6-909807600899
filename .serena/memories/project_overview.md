# Strapi Adapty CMS Project Overview

## Purpose
This project implements a comprehensive Content Management System (CMS) using Strapi to manage DIY paywall configurations that integrate with the Adapty server API. The system allows marketing teams, product managers, and other non-technical stakeholders to create, manage, and deploy paywall content without requiring code changes.

## Key Features
- Paywall content management with themes, features, and testimonials
- Adapty API integration for subscription management
- A/B testing and variation management
- Multi-language and localization support
- Analytics and performance monitoring
- Mobile API endpoints for React Native apps
- Content workflow and approval processes

## Tech Stack
- **Strapi**: v5.18.1 - Headless CMS framework
- **Node.js**: >=18.0.0 <=22.x.x
- **TypeScript**: ^5.x for type safety
- **React**: ^18.0.0 for admin interface
- **Database**: SQLite (better-sqlite3 v11.3.0) with PostgreSQL support
- **Testing**: Jest with TypeScript support

## Current Status
- Project foundation is complete
- Paywall content type and components are implemented
- Ready to proceed with media management and admin interface customizations