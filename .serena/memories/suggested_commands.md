# Suggested Commands for Strapi Adapty CMS

## Development Commands
```bash
# Start development server with auto-reload
yarn develop
npm run develop

# Start production server
yarn start
npm run start

# Build admin panel
yarn build
npm run build
```

## Data Management
```bash
# Seed example data
yarn seed:example
npm run seed:example

# Access Strapi console
yarn console
npm run console
```

## Testing Commands
```bash
# Run all tests
yarn test
npm test

# Run tests in watch mode
yarn test:watch
npm run test:watch

# Run tests with coverage
yarn test:coverage
npm run test:coverage
```

## Deployment Commands
```bash
# Deploy application
yarn deploy
npm run deploy

# Upgrade Strapi (dry run first)
yarn upgrade:dry
npm run upgrade:dry

# Upgrade Strapi
yarn upgrade
npm run upgrade
```

## Docker Commands
```bash
# Start development environment
docker-compose -f docker-compose.dev.yml up

# Start production environment
docker-compose up

# Build and start
docker-compose up --build
```

## System Commands (macOS)
```bash
# File operations
ls -la          # List files with details
find . -name    # Find files by name
grep -r         # Search in files recursively

# Process management
ps aux          # List running processes
kill -9 <pid>   # Force kill process

# Git operations
git status      # Check repository status
git log --oneline # View commit history
```