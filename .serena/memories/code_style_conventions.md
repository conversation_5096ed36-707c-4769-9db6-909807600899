# Code Style and Conventions

## TypeScript Standards
- Use TypeScript for all new code
- Provide explicit type definitions for interfaces and functions
- Use proper JSDoc comments for complex functions
- Follow camelCase for variables and functions, PascalCase for classes and interfaces

## Strapi Conventions
- Follow Strapi's conventional structure for content types
- Use kebab-case for content type names (e.g., `paywall`, `ab-test`)
- Place content type schemas in `src/api/[content-type]/content-types/[content-type]/schema.json`
- Implement controllers, services, and routes in respective directories
- Use shared components for reusable content structures

## File Organization
- Content types: `src/api/[content-type]/`
- Shared components: `src/components/shared/`
- Admin customizations: `src/admin/`
- Extensions: `src/extensions/`
- Tests: `tests/[feature]/`

## API Design
- Use RESTful endpoints for standard operations
- Implement custom routes for specialized functionality
- Add proper error handling and validation
- Use middleware for cross-cutting concerns (caching, authentication)
- Follow mobile-first API design principles

## Database Conventions
- Use descriptive field names with underscores (e.g., `placement_id`, `cta_text`)
- Implement proper relationships between content types
- Add validation rules in schema definitions
- Use enumerations for fixed value sets

## Testing Standards
- Write unit tests for all business logic
- Create integration tests for API endpoints
- Use descriptive test names that explain the scenario
- Mock external dependencies (Adapty API)
- Maintain test coverage above 80%