# Docker Compose configuration for Chrome MCP Monitoring Dashboard
# Implements Story 2.1: Chrome MCP Server Health Monitoring Dashboard

version: '3.8'

services:
  # Main Strapi application with Chrome MCP monitoring
  strapi-monitoring:
    build:
      context: .
      dockerfile: docker/chrome-mcp-monitoring.dockerfile
    container_name: strapi-chrome-mcp-monitoring
    restart: unless-stopped
    env_file: .env
    environment:
      # Database configuration
      DATABASE_CLIENT: postgres
      DATABASE_HOST: postgres
      DATABASE_PORT: 5432
      DATABASE_NAME: ${DATABASE_NAME:-strapi}
      DATABASE_USERNAME: ${DATABASE_USERNAME:-strapi}
      DATABASE_PASSWORD: ${DATABASE_PASSWORD:-strapi}
      
      # Chrome MCP monitoring configuration
      CHROME_MCP_MONITORING_ENABLED: true
      CHROME_MCP_MONITORING_INTERVAL: 30000
      CHROME_MCP_MONITORING_AUTO_START: true
      CHROME_MCP_MONITORING_DATA_DIR: /opt/app/data/monitoring
      
      # Alert configuration
      CHROME_MCP_ALERT_RESPONSE_TIME_WARNING: 2000
      CHROME_MCP_ALERT_RESPONSE_TIME_CRITICAL: 5000
      CHROME_MCP_ALERT_CONNECTION_RATE_WARNING: 90
      CHROME_MCP_ALERT_CONNECTION_RATE_CRITICAL: 75
      CHROME_MCP_ALERT_ERROR_COUNT_WARNING: 3
      CHROME_MCP_ALERT_ERROR_COUNT_CRITICAL: 10
      
      # Security
      APP_KEYS: ${APP_KEYS}
      API_TOKEN_SALT: ${API_TOKEN_SALT}
      ADMIN_JWT_SECRET: ${ADMIN_JWT_SECRET}
      TRANSFER_TOKEN_SALT: ${TRANSFER_TOKEN_SALT}
      JWT_SECRET: ${JWT_SECRET}
      
      # Chrome configuration for headless mode
      CHROME_ARGS: "--no-sandbox,--disable-setuid-sandbox,--disable-dev-shm-usage,--disable-gpu,--no-first-run,--no-zygote,--single-process"
    volumes:
      - monitoring_data:/opt/app/data/monitoring
      - ./uploads:/opt/app/public/uploads
    ports:
      - "1337:1337"
    depends_on:
      - postgres
      - redis
    networks:
      - strapi-monitoring
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:1337/api/chrome-mcp-monitoring/status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # PostgreSQL database
  postgres:
    image: postgres:15-alpine
    container_name: strapi-postgres-monitoring
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${DATABASE_NAME:-strapi}
      POSTGRES_USER: ${DATABASE_USERNAME:-strapi}
      POSTGRES_PASSWORD: ${DATABASE_PASSWORD:-strapi}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - strapi-monitoring
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DATABASE_USERNAME:-strapi}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for caching and session storage
  redis:
    image: redis:7-alpine
    container_name: strapi-redis-monitoring
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - strapi-monitoring
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Nginx reverse proxy with monitoring endpoints
  nginx:
    image: nginx:alpine
    container_name: strapi-nginx-monitoring
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/monitoring.conf:/etc/nginx/conf.d/default.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - strapi-monitoring
    networks:
      - strapi-monitoring
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus for metrics collection (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: strapi-prometheus-monitoring
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - strapi-monitoring
    profiles:
      - monitoring

  # Grafana for advanced monitoring dashboards (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: strapi-grafana-monitoring
    restart: unless-stopped
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD:-admin}
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning
    ports:
      - "3000:3000"
    depends_on:
      - prometheus
    networks:
      - strapi-monitoring
    profiles:
      - monitoring

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  monitoring_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  strapi-monitoring:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
